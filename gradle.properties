# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
android.enableJetifier=true
android.nonFinalResIds=false
android.nonTransitiveRClass=false
android.useAndroidX=true
org.gradle.jvmargs=-Xmx4608m
org.gradle.unsafe.configuration-cache-problems=warn
systemProp.sonar.host.url=https://sonarqube-enterprise.bri.co.id
systemProp.sonar.login=squ_995e5f9f07df74287aa571ef1af92b65f9bc97d2
kapt.include.compile.classpath=false
##android.enableDexingArtifactTransform=false
org.gradle.allow-insecure-protocol=true
org.gradle.configuration-cache=true

# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
CHUCKER_REPO_AUTH=jp_c8nh0sc66kd3crm4bhlajc2bb3
