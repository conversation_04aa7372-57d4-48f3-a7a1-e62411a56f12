plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-parcelize'
}

android {
    namespace 'id.co.bri.brimo.nfcpayment'
    compileSdk 33

    defaultConfig {
        minSdk 23

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.5.0-alpha01'
    implementation 'androidx.appcompat:appcompat:1.3.0-alpha01'
    implementation 'androidx.activity:activity:1.5.1'
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.0.0'

    testImplementation 'junit:junit:4.12'
}