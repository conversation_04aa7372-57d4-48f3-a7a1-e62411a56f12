<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <dimen name="one_px">1dp</dimen>
    <dimen name="one_point_five_px">1.5dp</dimen>
    <dimen name="textSize">17dp</dimen>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="item_offset">0dp</dimen>
    <dimen name="margin_from_bottom_layout">87dp</dimen>
    <dimen name="margin_from_top_layout">17.5dp</dimen>
    <dimen name="margin_pin_cardview">12dp</dimen>
    <dimen name="design_bottom_navigation_text_size" tools:override="true">11dp</dimen>
    <dimen name="design_bottom_navigation_height" tools:override="true">50dp</dimen>
    <dimen name="design_bottom_navigation_icon_size" tools:override="true">20dp</dimen>
    <dimen name="fab_margin">16dp</dimen>

    <!-- segmented tab -->
    <dimen name="margin_top_tab">19dp</dimen>
    <dimen name="margin_top_webview">-19dp</dimen>
    <dimen name="margin_horizontal_tab">57dp</dimen>
    <dimen name="margin_horizontal_new_tab">37dp</dimen>
    <dimen name="margin_horizontal_parent_tab">70dp</dimen>
    <dimen name="height_size_tab">26dp</dimen>
    <dimen name="height_size_parent_tab">26dp</dimen>
    <dimen name="new_height_size_parent_tab">36dp</dimen>
    <dimen name="height_size_pulsa_tab">48dp</dimen>
    <dimen name="econize_edittext_padding_bottom">15dp</dimen>
    <dimen name="margin_elevation_card">4dp</dimen>

    <!-- size font -->
    <dimen name="size_pinnumber_text">24dp</dimen>
    <dimen name="size_tab_text">10.5dp</dimen>
    <dimen name="size_item_category_text">13.5dp</dimen>
    <dimen name="size_pfm_empty_text">13dp</dimen>
    <dimen name="size_pfm_subtitle_text">12dp</dimen>
    <dimen name="size_pfm_amount_text">11dp</dimen>
    <dimen name="size_pfm_amount_anggaran_total">10dp</dimen>
    <dimen name="size_konfirmasi_subtitle">14dp</dimen>

    <dimen name="size_text_18sp">18dp</dimen>
    <dimen name="size_text_16sp">16dp</dimen>
    <dimen name="size_text_14sp">14dp</dimen>
    <dimen name="size_text_12sp">12dp</dimen>

    <dimen name="size_saved_title_trx">26dp</dimen>
    <dimen name="size_saved_subtitle_trx">24dp</dimen>

    <dimen name="size_default_15sp">15dp</dimen>
    <dimen name="size_default_14sp">14dp</dimen>
    <dimen name="size_default_13sp">13dp</dimen>
    <dimen name="size_default_12sp">12dp</dimen>

    <dimen name="size_default_error">13dp</dimen>
    <dimen name="size_default_title">14dp</dimen>
    <dimen name="size_default_list1">13dp</dimen>
    <dimen name="size_default_list2">13dp</dimen>
    <dimen name="size_default_initial">14dp</dimen>
    <dimen name="size_default_edittext">18dp</dimen>
    <dimen name="size_default_receipt">12dp</dimen>
    <dimen name="size_default_subtitle_receipt">14dp</dimen>

    <dimen name="size_paket_data_title">13dp</dimen>
    <dimen name="size_paket_data_subtitle">11dp</dimen>
    <dimen name="size_paket_data_amount">14dp</dimen>

    <dimen name="size_menu_name">12dp</dimen>
    <dimen name="size_common_dashboard">12dp</dimen>
    <dimen name="size_lihatlebih_dashboard">11dp</dimen>
    <dimen name="size_rekening_lain">11dp</dimen>
    <dimen name="size_rekening_utama">13dp</dimen>

    <!-- size icon -->
    <dimen name="width_icon">24dp</dimen>
    <dimen name="left_margin_icon">22dp</dimen>
    <dimen name="top_margin_icon">21dp</dimen>
    <dimen name="right_margin_field">28dp</dimen>

    <!-- size stikcy header -->
    <dimen name="recycler_section_header_height">56dp</dimen>


    <!-- radius size -->
    <dimen name="corner_radius_cardview">8dp</dimen>
    <dimen name="radius_dashboard_cardview">5dp</dimen>
    <dimen name="radius_submit_button">6dp</dimen>
    <dimen name="radius_waiting_cardview">8.5dp</dimen>
    <dimen name="radius_search_view">6dp</dimen>
    <dimen name="radius_button_ns_full">50dp</dimen>
    <dimen name="radius_text_input">21dp</dimen>
    <dimen name="corner_radius_15dp">15dp</dimen>
    <dimen name="corner_radius_25dp">25dp</dimen>


    <!-- margin button -->
    <dimen name="horizontal_submit_button_fast">@dimen/_25sdp</dimen>
    <dimen name="horizontal_submit_button">@dimen/_15sdp</dimen>
    <dimen name="list_item_spacing">16dp</dimen>
    <dimen name="list_item_spacing_half">8dp</dimen>
    <!--
    Because the window insets on round devices are larger than 15dp, this padding only applies
    to square screens.
    -->
    <dimen name="box_inset_layout_padding">0dp</dimen>

    <!--
    This padding applies to both square and round screens. The total padding between the buttons
    and the window insets is box_inset_layout_padding (above variable) on square screens and
    inner_frame_layout_padding (below variable) on round screens.
    -->
    <dimen name="inner_frame_layout_padding">5dp</dimen>

    <!--Text size dimen-->
    <dimen name="h1_text">96dp</dimen>
    <dimen name="h2_text">60dp</dimen>
    <dimen name="h3_text">48dp</dimen>
    <dimen name="h4_text">34dp</dimen>
    <dimen name="h5_text">24dp</dimen>
    <dimen name="h6_text">20dp</dimen>
    <dimen name="subtitle_text">18dp</dimen>
    <dimen name="body_medium_text">16dp</dimen>
    <dimen name="body_small_text">14dp</dimen>
    <dimen name="micro_text">12dp</dimen>

    <dimen name="space_x0">0dp</dimen>
    <dimen name="size_1dp">1dp</dimen>
    <dimen name="size_2dp">2dp</dimen>
    <dimen name="size_3dp">3dp</dimen>
    <dimen name="size_4dp">4dp</dimen>
    <dimen name="size_5dp">5dp</dimen>
    <dimen name="size_6dp">6dp</dimen>
    <dimen name="size_8dp">8dp</dimen>
    <dimen name="size_9dp">9dp</dimen>
    <dimen name="size_10dp">10dp</dimen>
    <dimen name="size_11dp">11dp</dimen>
    <dimen name="size_12dp">12dp</dimen>
    <dimen name="size_14dp">14dp</dimen>
    <dimen name="size_15dp">15dp</dimen>
    <dimen name="size_16dp">16dp</dimen>
    <dimen name="size_18dp">18dp</dimen>
    <dimen name="size_20dp">20dp</dimen>
    <dimen name="size_21dp">21dp</dimen>
    <dimen name="size_22dp">22dp</dimen>
    <dimen name="size_23dp">23dp</dimen>
    <dimen name="size_24dp">24dp</dimen>
    <dimen name="size_26dp">26dp</dimen>
    <dimen name="size_27dp">27dp</dimen>
    <dimen name="size_28dp">28dp</dimen>
    <dimen name="size_30dp">30dp</dimen>
    <dimen name="size_25dp">25dp</dimen>
    <dimen name="size_32dp">32dp</dimen>
    <dimen name="size_34dp">34dp</dimen>
    <dimen name="size_35dp">35dp</dimen>
    <dimen name="size_36dp">36dp</dimen>
    <dimen name="size_38dp">38dp</dimen>
    <dimen name="size_40dp">40dp</dimen>
    <dimen name="size_42dp">42dp</dimen>
    <dimen name="size_48dp">48dp</dimen>
    <dimen name="size_49dp">49dp</dimen>
    <dimen name="size_50dp">50dp</dimen>
    <dimen name="size_52dp">52dp</dimen>
    <dimen name="size_56dp">56dp</dimen>
    <dimen name="size_58dp">58dp</dimen>
    <dimen name="size_60dp">60dp</dimen>
    <dimen name="size_64dp">64dp</dimen>
    <dimen name="size_72dp">72dp</dimen>
    <dimen name="size_74dp">74dp</dimen>
    <dimen name="size_76dp">76dp</dimen>
    <dimen name="size_81dp">81dp</dimen>
    <dimen name="size_83dp">83dp</dimen>
    <dimen name="size_86dp">86dp</dimen>
    <dimen name="size_88dp">88dp</dimen>
    <dimen name="size_90dp">90dp</dimen>
    <dimen name="size_92dp">92dp</dimen>
    <dimen name="size_96dp">96dp</dimen>
    <dimen name="size_98dp">98dp</dimen>
    <dimen name="size_100dp">100dp</dimen>
    <dimen name="size_104dp">104dp</dimen>
    <dimen name="size_110dp">110dp</dimen>
    <dimen name="size_120dp">120dp</dimen>
    <dimen name="size_123dp">123dp</dimen>
    <dimen name="size_128dp">128dp</dimen>
    <dimen name="size_118dp">118dp</dimen>
    <dimen name="size_140dp">140dp</dimen>
    <dimen name="size_148dp">148dp</dimen>
    <dimen name="size_134dp">134dp</dimen>
    <dimen name="size_135dp">135dp</dimen>
    <dimen name="size_150dp">150dp</dimen>
    <dimen name="size_154dp">154dp</dimen>
    <dimen name="size_160dp">160dp</dimen>
    <dimen name="size_170dp">170dp</dimen>
    <dimen name="size_174dp">174dp</dimen>
    <dimen name="size_180dp">180dp</dimen>
    <dimen name="size_182dp">182dp</dimen>
    <dimen name="size_187dp">187dp</dimen>
    <dimen name="size_190dp">190dp</dimen>
    <dimen name="size_195dp">195dp</dimen>
    <dimen name="size_198dp">198dp</dimen>
    <dimen name="size_200dp">200dp</dimen>
    <dimen name="size_220dp">220dp</dimen>
    <dimen name="size_240dp">240dp</dimen>
    <dimen name="size_246dp">246dp</dimen>
    <dimen name="size_280dp">280dp</dimen>
    <dimen name="size_256dp">256dp</dimen>
    <dimen name="size_264dp">264dp</dimen>
    <dimen name="size_300dp">300dp</dimen>
    <dimen name="size_312dp">312dp</dimen>
    <dimen name="size_360dp">360dp</dimen>
    <dimen name="size_380dp">380dp</dimen>
    <dimen name="size_420dp">420dp</dimen>
    <dimen name="size_500dp">500dp</dimen>

    <dimen name="space_half">4dp</dimen>
    <dimen name="space_x1">8dp</dimen>
    <dimen name="space_minx1">-8dp</dimen>
    <dimen name="space_minx1_half">-10dp</dimen>
    <dimen name="space_x1_half">12dp</dimen>
    <dimen name="space_x2">16dp</dimen>
    <dimen name="space_minx2">-16dp</dimen>
    <dimen name="space_minx2_half">-16dp</dimen>
    <dimen name="space_x2_half">20dp</dimen>
    <dimen name="space_x3">24dp</dimen>
    <dimen name="space_minx3">-24dp</dimen>
    <dimen name="icon_size_bottom_navigation">26dp</dimen>
    <dimen name="space_x3_half">28dp</dimen>
    <dimen name="space_minx3_half">-28dp</dimen>
    <dimen name="space_x4">32dp</dimen>
    <dimen name="space_minx4">-32dp</dimen>
    <dimen name="space_x4_half">36dp</dimen>
    <dimen name="space_x5">40dp</dimen>
    <dimen name="space_minx5_half">-44dp</dimen>
    <dimen name="space_x5_half">44dp</dimen>
    <dimen name="space_x6">48dp</dimen>
    <dimen name="space_x6_half">52dp</dimen>
    <dimen name="space_x7">56dp</dimen>
    <dimen name="space_x7_half">60dp</dimen>
    <dimen name="space_minx7_half">-60dp</dimen>
    <dimen name="space_x8">64dp</dimen>
    <dimen name="space_x8_half">66dp</dimen>
    <dimen name="space_x9">72dp</dimen>
    <dimen name="space_x9_half">76dp</dimen>
    <dimen name="space_minx9_half">-76dp</dimen>
    <dimen name="space_x10">80dp</dimen>
    <dimen name="space_x10_half">84dp</dimen>
    <dimen name="space_x11">88dp</dimen>
    <dimen name="space_minx11">-88dp</dimen>
    <dimen name="space_x11_half">92dp</dimen>
    <dimen name="space_x11_3quarter">94dp</dimen>
    <dimen name="space_x12">96dp</dimen>
    <dimen name="space_x12_half">100dp</dimen>
    <dimen name="space_x13">104dp</dimen>
    <dimen name="space_x13_half">108dp</dimen>
    <dimen name="space_x14">112dp</dimen>
    <dimen name="space_x14_half">116dp</dimen>
    <dimen name="space_x15">120dp</dimen>
    <dimen name="space_x16">128dp</dimen>
    <dimen name="space_x18">144dp</dimen>
    <dimen name="space_x18_half">148dp</dimen>
    <dimen name="space_x19">152dp</dimen>
    <dimen name="space_x19_half">156dp</dimen>
    <dimen name="space_x20">160dp</dimen>
    <dimen name="space_x21">168dp</dimen>
    <dimen name="space_x21_half">172dp</dimen>
    <dimen name="space_x22">176dp</dimen>
    <dimen name="space_x22_half">180dp</dimen>
    <dimen name="space_x23">184dp</dimen>
    <dimen name="space_x23_half">188dp</dimen>
    <dimen name="space_x24">192dp</dimen>
    <dimen name="space_x25">200dp</dimen>
    <dimen name="space_x26">208dp</dimen>
    <dimen name="space_x26_half">212dp</dimen>
    <dimen name="space_x27_half">220dp</dimen>
    <dimen name="space_x29">232dp</dimen>
    <dimen name="space_x30">240dp</dimen>
    <dimen name="space_x31_half">252dp</dimen>
    <dimen name="space_x32_half">260dp</dimen>
    <dimen name="space_x33_half">268dp</dimen>
    <dimen name="space_x34">272dp</dimen>
    <dimen name="space_x36">288dp</dimen>
    <dimen name="space_x36_half">292dp</dimen>
    <dimen name="space_x37">296dp</dimen>
    <dimen name="space_x37_half">300dp</dimen>
    <dimen name="space_x38_half">308dp</dimen>
    <dimen name="space_x40">320dp</dimen>
    <dimen name="space_x40_half">324dp</dimen>
    <dimen name="space_x41">328dp</dimen>
    <dimen name="space_x43">344dp</dimen>
    <dimen name="space_x45">360dp</dimen>
    <dimen name="space_x50">400dp</dimen>

    <!-- TEXT SIZE REVAMP -->
    <dimen name="text_h1">28dp</dimen>
    <dimen name="text_h2">24dp</dimen>
    <dimen name="text_h3">22dp</dimen>
    <dimen name="text_h4">20dp</dimen>
    <dimen name="text_body1">18dp</dimen>
    <dimen name="text_body2">16dp</dimen>
    <dimen name="text_body3">14dp</dimen>
    <dimen name="text_caption1">12dp</dimen>
    <dimen name="text_caption2">10dp</dimen>

    <dimen name="bg_upsize">444dp</dimen>
    <dimen name="bg_downsize_x2">330dp</dimen>
    <dimen name="bg_downsize_x3">280dp</dimen>
    <dimen name="bg_downsize">180dp</dimen>
    <dimen name="bg_blank_size">0dp</dimen>

    <!--line spacing extra -->
    <dimen name="line_spacing_menu">4sp</dimen>

    <dimen name="view_gradient_height_gone">118dp</dimen>
    <dimen name="view_gradient_margin_top_gone">85dp</dimen>
    <dimen name="view_gradient_height_visible">188dp</dimen>
    <dimen name="view_gradient_margin_top_visible">30dp</dimen>
    <dimen name="size_0dp">0dp</dimen>

    <!-- Text Size Dimension (SP) -->
    <dimen name="size_10sp">10sp</dimen>
    <dimen name="size_12sp">12sp</dimen>
    <dimen name="size_14sp">14sp</dimen>
    <dimen name="size_16sp">16sp</dimen>
    <dimen name="size_20sp">20sp</dimen>
    <dimen name="size_24sp">24sp</dimen>
    <dimen name="size_25sp">25sp</dimen>
    <dimen name="size_28sp">28sp</dimen>
    <dimen name="size_32sp">32sp</dimen>
    <dimen name="size_36sp">36sp</dimen>
    <dimen name="size_40sp">40sp</dimen>

    <dimen name="design_bottom_navigation_active_text_size" tools:override="true">@dimen/design_bottom_navigation_text_size</dimen>
    <dimen name="design_bottom_navigation_inactive_text_size" tools:override="true">@dimen/design_bottom_navigation_text_size</dimen>


    <dimen name="viewpager_next_item_visible">26dp</dimen>
    <dimen name="viewpager_current_item_horizontal_margin">42dp</dimen>
</resources>