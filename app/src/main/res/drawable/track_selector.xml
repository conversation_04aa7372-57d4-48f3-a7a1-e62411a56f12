<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_checked="true">
        <shape android:dither="true" android:shape="rectangle" android:useLevel="false" android:visible="true">
            <solid android:color="@color/success80" />
            <corners android:radius="50dp" />
            <size android:width="2dp" android:height="@dimen/_15sdp" />
        </shape>
    </item>
    <item android:state_checked="false">
        <shape android:dither="true" android:shape="rectangle" android:useLevel="false" android:visible="true">

            <corners android:radius="50dp" />
            <solid android:color="@color/ns_grey_soft" />
            <size android:width="2dp" android:height="@dimen/_15sdp" />
        </shape>
    </item>
</selector>