<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_item_home"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginVertical="@dimen/space_x1_half">

    <LinearLayout
        android:id="@+id/ll_home"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:id="@+id/gambarHome"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/_10sdp"
            android:layout_marginBottom="@dimen/_8sdp"
            android:contentDescription="@null" />

        <TextView
            android:id="@+id/namaHome"
            style="@style/BodyText.Medium.SemiBold.BlackNsMain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:maxLines="2"
            tools:text="@string/data_not_found" />
    </LinearLayout>

    <View
        android:id="@+id/viewOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80FFFFFF"
        android:visibility="gone" />
</FrameLayout>
