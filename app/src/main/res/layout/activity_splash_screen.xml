<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_splash_screen_ns">

    <FrameLayout
        android:id="@+id/frameLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:paddingBottom="250dp">

        <ImageView
            android:layout_width="182dp"
            android:layout_height="183dp"
            android:layout_gravity="center"
            android:src="@drawable/logo_qitta_splash"/>

        <ImageView
            android:id="@+id/logo2"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginTop="20dp"
            android:layout_marginLeft="3dp"
            android:layout_gravity="center"
            android:visibility="gone"
            android:alpha="0"
            android:src="@drawable/itta"
            android:contentDescription="Logo 2" />

        <View
            android:id="@+id/cover"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:visibility="gone"
            android:layout_gravity="center"
            android:scaleX="1" />

        <ImageView
            android:id="@+id/logo1"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            android:visibility="gone"
            android:rotation="-90"
            android:scaleX="1"
            android:scaleY="1"
            android:src="@drawable/q"
            android:contentDescription="Logo 1" />
    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_alignParentBottom="true">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_x3"
            android:layout_marginHorizontal="@dimen/space_x1"
            android:textSize="10sp"
            android:gravity="center"
            android:fontFamily="@font/bri_digital_text_regular"
            android:textColor="@color/white"
            android:text="@string/text_splash_screen"/>

        <TextView
            android:id="@+id/tvVersi"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/space_x2"
            android:layout_marginHorizontal="@dimen/space_x1"
            style="@style/Caption1SmallText.Bold.NeutralBaseWhite"
            android:gravity="center"
            android:text="@string/version_app_splash"/>

    </LinearLayout>

</RelativeLayout>