<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/toolbar"
    android:layout_width="match_parent"
    android:layout_height="@dimen/size_64dp"
    android:background="@color/transparent"
    android:outlineProvider="bounds"
    android:textAlignment="center"
    app:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

    <TextView
        android:id="@+id/textTitle"
        style="@style/Body1LargeText.Medium.NeutralBaseWhite.Toolbar"
        android:fontFamily="@font/bri_digital_text_medium"
        android:textStyle="bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="marquee"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true" />

    <TextView
        android:id="@+id/textRight"
        style="@style/ToolbarTitleStyleRightFilter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="marquee"
        android:layout_marginEnd="@dimen/_13sdp"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true" />

</androidx.appcompat.widget.Toolbar>