<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:id="@+id/view_connect"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_rounded_primary_blue_10"
    android:backgroundTint="@color/black_ns_100"
    android:layout_marginEnd="@dimen/_8sdp"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_chip"
        style="@style/Body3SmallText.Regular"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:paddingHorizontal="@dimen/_11sdp"
        android:paddingVertical="@dimen/_8sdp"
        android:text="@string/Pembayaran"
        android:textColor="@color/black_ns_main"
        android:textSize="13sp"/>

</FrameLayout>
