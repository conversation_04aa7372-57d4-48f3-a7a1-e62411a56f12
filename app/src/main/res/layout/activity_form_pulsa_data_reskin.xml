<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <!-- Toolbar -->
        <id.co.bri.brimo.ui.widget.BaseScreenLayout
            android:id="@+id/bsl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="100dp">
            <LinearLayout
                android:id="@+id/ll_content_main"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <id.co.bri.brimo.ui.widget.input_til.BaseInputView
                        android:id="@+id/biv_no_pelanggan"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="21dp"
                        app:prefixText="+62"
                        app:expanded="false"
                        app:hintText="Nomor HP" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/one_point_five_px"
                    android:layout_marginVertical="@dimen/size_21dp"
                    android:background="#E9EEF6" />
            </LinearLayout>

            <!-- Tab Section for Favorit and Riwayat -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Search bar -->
                <androidx.appcompat.widget.SearchView
                    android:id="@+id/searchview_briva"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginBottom="24dp"
                    android:background="@drawable/bg_input_black_100_brimo_ns_rounded"
                    app:iconifiedByDefault="false"
                    android:theme="@style/AppSearchViewSmall"
                    app:queryBackground="@color/transparent"
                    app:closeIcon="@drawable/ic_cross_newskin"
                    app:queryHint="Cari nama pelanggan atau layanan" />

                <!-- Tab Layout -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp">

                    <TextView
                        android:id="@+id/tab_favorit"
                        android:layout_width="77dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Favorit"
                        android:textAlignment="center"
                        android:textColor="@color/text_brand_primary_ns"
                        android:background="@drawable/rounded_button_soft_ns"
                        android:paddingVertical="12dp"
                        android:layout_marginEnd="8dp"
                        android:clickable="true"
                        android:focusable="true" />

                    <TextView
                        android:id="@+id/tab_riwayat"
                        android:layout_width="77dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Riwayat"
                        android:textAlignment="center"
                        android:textColor="@color/black"
                        android:background="@drawable/bg_input_black_100_brimo_ns_rounded"
                        android:paddingVertical="12dp"
                        android:layout_marginStart="8dp"
                        android:clickable="true"
                        android:focusable="true" />


                </LinearLayout>

                <!-- Favorit Content (Saved List) -->
                <LinearLayout
                    android:id="@+id/content_favorit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <!-- Saved List RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_daftar_favorit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layoutAnimation="@anim/layout_animation_fade_in"
                        android:visibility="gone" />

                    <!-- No Saved Data Message -->
                    <LinearLayout
                        android:id="@+id/ll_no_data_saved"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="32dp"
                        android:visibility="visible">

                        <ImageView
                            android:layout_width="120dp"
                            android:layout_height="120dp"
                            android:src="@drawable/empty_box_3d"
                            android:layout_marginBottom="16dp" />
                        <TextView
                            android:id="@+id/no_data_saved"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Belum Ada Daftar Favorit"
                            android:textStyle="bold"
                            android:textColor="@color/black"
                            android:layout_marginTop="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Yuk, tambah favorit biar transaksi berikutnya lebih cepat."
                            android:textSize="12sp"
                            android:gravity="center"
                            android:layout_marginTop="4dp" />

                        <LinearLayout
                            android:id="@+id/ll_add_saved_list"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center"
                            android:layout_marginTop="23dp"
                            android:paddingVertical="10dp"
                            android:paddingHorizontal="16dp"
                            android:background="@drawable/rounded_button_border_blue_ns"
                            android:clickable="true"
                            android:focusable="true"
                            android:visibility="visible">
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Tambah Favorit"
                                android:textColor="@color/primary_ns_main"
                                android:textSize="12sp"
                                android:gravity="center" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>

                <!-- Riwayat Content (History) -->
                <LinearLayout
                    android:id="@+id/content_riwayat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <!-- History RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_riwayat"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layoutAnimation="@anim/layout_animation_fade_in"
                        android:visibility="visible" />

                    <!-- No History Message -->
                    <LinearLayout
                        android:id="@+id/ll_no_history"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="32dp"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="120dp"
                            android:layout_height="120dp"
                            android:src="@drawable/empty_box_3d"
                            android:layout_marginBottom="16dp" />

                        <TextView
                            android:id="@+id/tv_no_history"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Belum ada riwayat transaksi"
                            android:textColor="@color/neutral_light60"
                            android:textSize="14sp"
                            android:gravity="center" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </id.co.bri.brimo.ui.widget.BaseScreenLayout>

        <!-- Bottom Button -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_alignParentBottom="true"
            android:orientation="vertical"
            android:padding="16dp"
            android:background="@android:color/white">

            <Button
                android:id="@+id/btnSubmit"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                style="@style/CustomButtonStyle"
                android:text="Lanjutkan"
                android:textSize="16sp"
                android:textAllCaps="false"
                android:background="@drawable/rounded_button_ns"
                android:textColor="@color/selector_text_color_button_primary_ns"
                android:enabled="false" />
        </LinearLayout>
    </FrameLayout>
</layout>