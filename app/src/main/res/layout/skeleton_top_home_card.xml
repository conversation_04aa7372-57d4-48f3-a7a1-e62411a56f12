<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    app:cardCornerRadius="@dimen/space_x1">

    <LinearLayout
        android:id="@+id/llTopContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true">

            <androidx.cardview.widget.CardView
                android:id="@+id/cvAcc"
                android:layout_width="120dp"
                android:layout_height="14dp"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="8dp"
                app:cardBackgroundColor="@color/ns_primary600"
                app:cardCornerRadius="25dp"
                app:cardElevation="0dp" />

            <androidx.cardview.widget.CardView
                android:id="@+id/cvCopy"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="8dp"
                android:layout_toEndOf="@id/cvAcc"
                app:cardBackgroundColor="@color/ns_primary600"
                app:cardCornerRadius="25dp"
                app:cardElevation="0dp" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="45dp"
            android:layout_marginTop="24dp">

            <androidx.cardview.widget.CardView
                android:id="@+id/cvSaldo"
                android:layout_width="150dp"
                android:layout_height="20dp"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="8dp"
                app:cardBackgroundColor="@color/ns_primary600"
                app:cardCornerRadius="25dp"
                app:cardElevation="0dp" />

            <androidx.cardview.widget.CardView
                android:id="@+id/cvEye"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="8dp"
                android:layout_toEndOf="@id/cvSaldo"
                app:cardBackgroundColor="@color/ns_primary600"
                app:cardCornerRadius="25dp"
                app:cardElevation="0dp" />

        </RelativeLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="100dp"
            android:layout_marginTop="24dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rlMenu2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="15dp"
                android:foregroundGravity="center">

                <androidx.cardview.widget.CardView
                    android:id="@+id/cvMenu2"
                    android:layout_width="56dp"
                    android:layout_height="56dp"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="8dp"
                    app:cardBackgroundColor="@color/ns_primary600"
                    app:cardCornerRadius="15dp"
                    app:cardElevation="0dp" />

                <androidx.cardview.widget.CardView
                    android:id="@+id/cvTitle2"
                    android:layout_width="76dp"
                    android:layout_height="10dp"
                    android:layout_below="@id/cvMenu2"
                    android:layout_gravity="center_vertical"
                    app:cardBackgroundColor="@color/ns_primary600"
                    app:cardCornerRadius="15dp"
                    app:cardElevation="0dp" />
            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>