<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:fitsSystemWindows="true"
    android:background="@drawable/bg_new_skin_activity_container"
    tools:context=".ui.activities.inforekeningnewskin.TransactionNotificationSettingActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_new_skin" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:background="@drawable/bg_new_skin_activity">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:id="@+id/view_parent"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="@dimen/_18sdp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_solid_outline_corner_newskin"
                        android:orientation="horizontal"
                        android:paddingHorizontal="@dimen/_13sdp"
                        android:paddingVertical="@dimen/_13sdp">

                        <ImageView
                            android:id="@+id/iv_info"
                            android:layout_width="@dimen/_17sdp"
                            android:layout_height="@dimen/_17sdp"
                            android:scaleType="fitXY"
                            android:src="@drawable/ic_info_ns"/>

                        <TextView
                            android:id="@+id/tv_fee_desc"
                            style="@style/Body1LargeText.Medium"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_8sdp"
                            android:text="@string/txt_setting_notification_wa_fee"
                            android:textColor="@color/black_ns_main"
                            android:textSize="@dimen/_11sdp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/Body1LargeText.Medium"
                        android:layout_marginTop="@dimen/_12sdp"
                        android:text="@string/txt_nominal_minimum"
                        android:textColor="@color/black_ns_main"
                        android:textSize="@dimen/_12sdp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/txt_notification_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8sdp"
                        style="@style/Body3SmallText.Regular"
                        android:text="@string/txt_nominal_minimum_desc"
                        android:textColor="@color/black_ns_600"
                        android:textSize="@dimen/_11sdp"/>


                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp">

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/input_nominal"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/selector_input_amont_ns"
                            android:paddingHorizontal="@dimen/_8sdp"
                            android:paddingTop="@dimen/_10sdp"
                            android:paddingBottom="@dimen/_5sdp"
                            android:textColorHint="@color/black_ns_600"
                            app:boxBackgroundMode="none"
                            app:hintTextAppearance="@style/TextHint.Small"
                            app:hintTextColor="@color/black_ns_600">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/bri_digital_text_regular"
                                android:textSize="12sp"
                                android:textColor="@color/black_ns_main"
                                android:text="@string/txt_nominal_minimum"/>

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_nominal_minimum"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@android:color/transparent"
                                android:text="@string/tx_rp"
                                android:textColor="@color/black_ns_main"
                                android:textSize="24sp"
                                android:maxLines="1"
                                android:maxLength="15"
                                android:layout_marginEnd="28dp"
                                android:paddingTop="5dp"
                                android:fontFamily="@font/bri_digital_text_semi_bold"
                                app:startIconDrawable="@null"
                                android:inputType="number"
                                android:paddingStart="0dp"
                                android:paddingLeft="0dp"/>

                            </LinearLayout>


                        </com.google.android.material.textfield.TextInputLayout>

                        <ImageView
                            android:id="@+id/img_clear"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="16dp"
                            android:visibility="gone"
                            android:paddingBottom="24dp"
                            android:src="@drawable/ic_clear_input_ns"
                            android:layout_alignParentBottom="true"
                            android:layout_alignParentEnd="true"/>

                    </RelativeLayout>

                    <TextView
                        android:id="@+id/tv_nominal_desc"
                        style="@style/Body3SmallText.Regular.NeutralLight60"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_half"
                        android:textColor="@color/black_ns_600"
                        android:text="@string/txt_minimal_rp1" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/Body1LargeText.Medium"
                        android:layout_marginTop="@dimen/_16sdp"
                        android:text="@string/txt_sumber_dana"
                        android:textColor="@color/black_ns_main"
                        android:textSize="@dimen/_12sdp"
                        android:textStyle="bold" />

                    <include
                        android:id="@+id/view_source_account"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        layout="@layout/item_source_of_account" />

                </LinearLayout>

            </androidx.core.widget.NestedScrollView>

            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="@color/ns_black100"
                app:layout_constraintBottom_toTopOf="@id/layout_button"/>

            <LinearLayout
                android:id="@+id/layout_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/space_x2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_activate"
                    style="@style/ButtonPrimaryNewSkin"
                    android:layout_width="0dp"
                    android:text="@string/aktifkan2"
                    android:layout_height="@dimen/space_x6"
                    android:layout_weight="1"
                    android:enabled="false"
                    android:textAllCaps="false" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</RelativeLayout>