<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cv_item_saldo_dompet_digital"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/size_12dp"
    card_view:cardCornerRadius="@dimen/size_24dp"
    card_view:cardUseCompatPadding="false"
    android:elevation="0dp"
    android:gravity="center_vertical">

    <LinearLayout
        android:id="@+id/ll_connect_wallet_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="start"
        android:padding="20dp"
        android:background="@drawable/bg_input_black_100_brimo_ns"
        android:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_vertical">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/size_12dp"
                android:layout_weight="1">

                <ImageView
                    android:id="@+id/list_1"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_toEndOf="@+id/list_2"
                    android:src="@drawable/ic_plus_rounded_ns"
                    android:layout_marginStart="-12dp"
                    />

                <ImageView
                    android:id="@+id/list_2"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_toEndOf="@+id/list_3"
                    android:src="@drawable/ic_shopee_pay"
                    android:layout_marginStart="-12dp" />

                <ImageView
                    android:id="@+id/list_3"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_toEndOf="@+id/list_4"
                    android:src="@drawable/ic_ovo"
                    android:layout_marginStart="-12dp" />

                <ImageView
                    android:id="@+id/list_4"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_toEndOf="@+id/list_5"
                    android:src="@drawable/ic_dana"
                    android:layout_marginStart="-12dp" />

                <ImageView
                    android:id="@+id/list_5"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_link_aja" />

            </RelativeLayout>

            <Button
                android:id="@+id/btnConnect"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/CustomButtonStyle"
                android:text="Hubungkan"
                android:layout_weight="1"
                android:textSize="12sp"
                android:textStyle="bold"
                android:paddingHorizontal="@dimen/size_16dp"
                android:paddingVertical="10dp"
                android:textAllCaps="false"
                android:background="@drawable/rounded_button_ns"
                android:textColor="@color/selector_text_color_button_primary_ns" />


        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>