<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@drawable/bg_new_skin_activity_container"
    tools:context=".ui.activities.inforekeningnewskin.InformasiRekeningActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_new_skin" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:background="@drawable/bg_new_skin_activity">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:id="@+id/viewParent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_x3"
                android:layout_marginHorizontal="@dimen/_16sdp"
                android:layout_marginBottom="@dimen/space_x3"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/view_saldo_onhold"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:background="@drawable/bg_solid_outline_corner_red_ns"
                    android:paddingHorizontal="@dimen/_13sdp"
                    android:layout_marginBottom="13sp"
                    android:paddingVertical="@dimen/_8sdp">

                    <ImageView
                        android:id="@+id/iv_info"
                        android:layout_width="@dimen/_21sdp"
                        android:layout_height="@dimen/_21sdp"
                        android:scaleType="fitXY"
                        android:src="@drawable/ic_error_newns"
                        android:layout_centerVertical="true"/>

                    <TextView
                        style="@style/Body1LargeText.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_4sdp"
                        android:layout_centerVertical="true"
                        android:text="@string/saldo_tertahan"
                        android:textColor="@color/black_ns_main"
                        android:textSize="@dimen/_11sdp"
                        android:textStyle="bold"
                        android:layout_toEndOf="@id/iv_info"
                        />

                </RelativeLayout>

                <TextView
                    android:id="@+id/tv_nama_info_rekening"
                    style="@style/Body2MediumText.SemiBold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/_13sdp"
                    android:text="@string/tx_detail_rekening"
                    android:textColor="@color/black_ns_main"/>

                <LinearLayout
                    android:id="@+id/containerLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:orientation="vertical">

                </LinearLayout>

                <TextView
                    style="@style/Body2MediumText.SemiBold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:textSize="@dimen/_13sdp"
                    android:visibility="gone"
                    android:text="@string/text_kartu_terhubung"
                    android:textColor="@color/black_ns_main"/>

                <androidx.cardview.widget.CardView
                    android:id="@+id/view_connect_card"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toBottomOf="@id/tv_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:visibility="gone"
                    android:layout_marginTop="@dimen/_10sdp"
                    app:cardCornerRadius="@dimen/_13sdp"
                    app:cardElevation="0dp"
                    app:cardBackgroundColor="@color/black_ns_100">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="@dimen/_14sdp"
                        android:paddingVertical="@dimen/_13sdp">

                        <ImageView
                            android:id="@+id/ivCardLogo"
                            android:layout_width="@dimen/_53sdp"
                            android:layout_height="@dimen/_33sdp"
                            android:scaleType="fitXY"
                            android:src="@drawable/dummy_britama"
                            android:layout_alignParentStart="true"
                            android:layout_centerVertical="true" />

                        <ImageView
                            android:id="@+id/ivArrow"
                            android:layout_width="@dimen/_15sdp"
                            android:layout_height="@dimen/_15sdp"
                            android:src="@drawable/ic_arrow"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            app:tint="@color/black_ns_main" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_toEndOf="@id/ivCardLogo"
                            android:layout_toStartOf="@id/ivArrow"
                            android:orientation="vertical"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="12dp"
                            android:layout_marginEnd="4dp">

                            <TextView
                                android:id="@+id/tvAccountName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                style="@style/Body3SmallText.Regular"
                                android:text="@string/empty"
                                android:textColor="@color/ns_black900"
                                android:textSize="@dimen/_10sdp"/>

                            <TextView
                                android:id="@+id/tvAccountNumber"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                style="@style/Body1LargeText.Regular"
                                android:textColor="@color/ns_black900"
                                android:text="@string/empty"
                                android:textSize="@dimen/_10sdp"/>

                        </LinearLayout>

                    </RelativeLayout>


                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</RelativeLayout>