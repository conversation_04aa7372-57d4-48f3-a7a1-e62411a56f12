<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/rlMenu"
    android:layout_marginTop="@dimen/space_x2">

    <RelativeLayout
        android:id="@+id/view"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:background="@drawable/background_circle_white_newskin"
        android:layout_centerHorizontal="true">

        <ImageView
            android:id="@+id/iv_category"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_centerInParent="true"
            tools:background="@drawable/ic_menu_acc" />
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_category"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_below="@+id/view"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:fontFamily="@font/bri_digital_text_regular"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:layout_marginTop="8dp"
        android:text="Informasi Rekening" />

</RelativeLayout>