<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="12dp"
    card_view:cardCornerRadius="16dp"
    card_view:cardElevation="0dp"
    card_view:cardBackgroundColor="@android:color/transparent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/cardview_border"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <View
                android:layout_width="120dp"
                android:layout_height="12dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/skeleton_placeholder_rounded" />

            <View
                android:layout_width="160dp"
                android:layout_height="16dp"
                android:layout_marginBottom="6dp"
                android:background="@drawable/skeleton_placeholder_rounded" />

            <View
                android:layout_width="180dp"
                android:layout_height="16dp"
                android:layout_marginBottom="6dp"
                android:background="@drawable/skeleton_placeholder_rounded" />

            <View
                android:layout_width="150dp"
                android:layout_height="16dp"
                android:background="@drawable/skeleton_placeholder_rounded" />
        </LinearLayout>

        <View
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginStart="12dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/skeleton_placeholder_rounded" />
    </LinearLayout>
</androidx.cardview.widget.CardView>
