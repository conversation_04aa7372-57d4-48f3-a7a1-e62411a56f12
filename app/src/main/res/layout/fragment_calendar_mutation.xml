<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/rounded_dialog"
    tools:context=".ui.fragments.CalendarMutationFragment">

    <ImageView
        android:id="@+id/view_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_line_bottomsheet"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp" />

    <RelativeLayout
        android:id="@+id/toolbarCalendar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:layout_below="@id/view_line"
        android:elevation="@dimen/_4sdp"
        android:textAlignment="center">

        <TextView
            android:id="@+id/textTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/txt_select_range_date"
            style="@style/Body1LargeText.Medium"
            android:textStyle="bold"
            android:textColor="@color/ns_black900"
            android:layout_centerInParent="true"
            android:textSize="18sp" />

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="20dp"
            android:layout_centerInParent="true"
            android:layout_alignParentEnd="true"
            android:src="@drawable/ic_cancel_button_ns" />

    </RelativeLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/toolbarCalendar"
        android:background="@color/white"
        android:scrollbarAlwaysDrawVerticalTrack="true">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/rl_bulan"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_x8"
                    android:paddingHorizontal="@dimen/space_x2">

                    <ImageButton
                        android:id="@+id/ibPreviousMonthFrag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:padding="10dp"
                        android:src="@drawable/left_calendar"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:tint="@color/black_ns_main" />

                    <TextView
                        android:id="@+id/tvMonthYearFrag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Juli 2025"
                        android:textSize="16sp"
                        style="@style/Body1LargeText.Medium"
                        android:textColor="@color/ns_black900"
                        android:textStyle="bold"
                        app:layout_constraintStart_toEndOf="@id/ibPreviousMonthFrag"
                        app:layout_constraintEnd_toStartOf="@id/ibNextMonthFrag"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHorizontal_bias="0.5" />

                    <ImageButton
                        android:id="@+id/ibNextMonthFrag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:padding="10dp"
                        android:src="@drawable/right_calendar"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:tint="@color/black_ns_main" />

                </androidx.constraintlayout.widget.ConstraintLayout>


                <include
                    android:id="@+id/dayLegend"
                    layout="@layout/calendar_day_mutation"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp" />

                <com.kizitonwose.calendarview.CalendarView
                    android:id="@+id/calendarView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cv_dayViewResource="@layout/calendar_day_layout"
                    app:cv_inDateStyle="allMonths"
                    app:cv_orientation="horizontal"
                    app:cv_outDateStyle="endOfGrid"
                    app:cv_scrollMode="paged"
                    android:layout_margin="0dp"
                    android:padding="0dp"/>


                <LinearLayout
                    android:id="@+id/ll_info_border"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:layout_marginTop="@dimen/_13sdp"
                    android:background="@drawable/bg_blue_caution"
                    android:padding="@dimen/space_x2"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="@dimen/space_x2"
                        android:layout_height="@dimen/space_x2"
                        android:src="@drawable/info_biru" />

                    <TextView
                        android:id="@+id/tv_info"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/space_x1"
                        android:fontFamily="@font/bri_text_medium"
                        android:text="@string/calendar_caution"
                        android:textColor="@color/neutralDark20"
                        android:textSize="12dp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/space_x2"
                    android:layout_alignParentBottom="true"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btPilihDurasi"
                        style="@style/ButtonPrimaryNewSkin"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/space_x6_half"
                        android:layout_weight="1"
                        android:text="@string/simpan"
                        android:textAllCaps="false" />

                </LinearLayout>

            </LinearLayout>

        </RelativeLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</RelativeLayout>