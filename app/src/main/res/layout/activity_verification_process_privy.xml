<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/privy_background_verif"
    android:gravity="center"
    android:orientation="vertical">

    <View
        android:id="@+id/smile_2"
        android:layout_width="250dp"
        android:layout_height="250dp"
        android:background="@drawable/ic_clock_illustration" />

    <LinearLayout
        android:id="@+id/timerContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <TextView
            style="@style/TitleText.Large.SemiBold.NeutralBaseWhite"
            android:id="@+id/tvMinute"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:background="@drawable/bg_timer_box"
            android:gravity="center"
            android:text="04"
            android:textColor="@android:color/white"
            android:textSize="24sp"
            android:textStyle="bold" />

        <TextView
            style="@style/TitleText.Large.SemiBold.NeutralBaseWhite"
            android:layout_width="wrap_content"
            android:layout_height="52dp"
            android:gravity="center"
            android:paddingHorizontal="8dp"
            android:text=":" />

        <TextView
            style="@style/TitleText.Large.SemiBold.NeutralBaseWhite"
            android:id="@+id/tvSecond"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:background="@drawable/bg_timer_box"
            android:gravity="center"
            android:text="59"/>
    </LinearLayout>

    <TextView
        style="@style/TitleText.ExtraLarge.SemiBold.NeutralBaseWhite"
        android:layout_width="327dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:textAlignment="center"
        android:text="Sedang dalam Proses Verifikasi" />

    <TextView
        android:id="@+id/tvDesc"
        style="@style/BodyText.Large.Regular.NeutralBaseWhite"
        android:layout_width="327dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginHorizontal="@dimen/space_x2"
        android:textAlignment="center"
        android:includeFontPadding="false"
        android:text="Kami sedang melakukan verifikasi. Kamu boleh meninggalkan proses ini dan kembali melanjutkan pendaftaran BRImo." />

</LinearLayout>
