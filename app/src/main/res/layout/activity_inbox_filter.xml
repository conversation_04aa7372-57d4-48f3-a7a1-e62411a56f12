<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@drawable/bg_new_skin_activity_container"
    tools:context="id.co.bri.brimo.ui.activities.FormBrivaActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_new_skin_text_right" />


    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:background="@drawable/bg_new_skin_activity">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.core.widget.NestedScrollView
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toTopOf="@id/layout_button"
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            style="@style/Body1LargeText.SemiBold"
                            android:textColor="@color/black_ns_main"
                            android:textStyle="bold"
                            android:layout_marginStart="@dimen/space_x2"
                            android:layout_marginTop="24dp"
                            android:textSize="17sp"
                            android:text="@string/date_range" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_time_filter"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            tools:itemCount="6"
                            tools:listitem="@layout/item_category_transaction"
                            android:layout_marginTop="@dimen/space_x1"
                            android:layout_marginBottom="16dp"
                            android:clipToPadding="false"
                            android:paddingHorizontal="@dimen/space_x2" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            style="@style/Body1LargeText.SemiBold"
                            android:textColor="@color/black_ns_main"
                            android:textStyle="bold"
                            android:layout_marginStart="@dimen/space_x2"
                            android:textSize="17sp"
                            android:text="@string/status" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_main_filter"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginTop="8dp"
                            android:layout_marginBottom="16dp"
                            tools:listitem="@layout/item_select_chip"
                            tools:itemCount="4"
                            tools:layoutManager="LinearLayoutManager"
                            tools:orientation="horizontal"
                            android:clipToPadding="false"
                            android:paddingHorizontal="@dimen/space_x2" />

                        <TextView
                            style="@style/Body1LargeText.SemiBold"
                            android:textColor="@color/black_ns_main"
                            android:textStyle="bold"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textSize="17sp"
                            android:layout_marginStart="@dimen/space_x2"
                            android:text="@string/txt_select_transaction_category" />

                        <androidx.cardview.widget.CardView
                            android:id="@+id/view_category"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginTop="@dimen/_13sdp"
                            app:cardCornerRadius="@dimen/_13sdp"
                            app:cardElevation="0dp"
                            app:cardBackgroundColor="@color/black_ns_100">

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:paddingHorizontal="@dimen/_13sdp"
                                android:paddingVertical="@dimen/_13sdp">

                                <TextView
                                    android:id="@+id/tv_source_account_label"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    style="@style/Body3SmallText.Regular"
                                    android:text="@string/txt_select_transaction_category"
                                    android:textColor="@color/ns_black900"
                                    android:textSize="12sp"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    app:layout_constraintEnd_toStartOf="@id/img_arrow_status_source_rekening" />

                                <TextView
                                    android:id="@+id/tv_source_account"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    style="@style/Body1LargeText.SemiBold"
                                    android:text="@string/txt_all_transaction_category"
                                    android:textColor="@color/black_ns_main"
                                    android:textSize="14sp"
                                    android:layout_marginTop="@dimen/_2sdp"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toBottomOf="@id/tv_source_account_label"
                                    app:layout_constraintEnd_toEndOf="@id/tv_source_account_label" />

                                <ImageView
                                    android:id="@+id/img_arrow_status_source_rekening"
                                    android:layout_width="@dimen/_22sdp"
                                    android:layout_height="@dimen/_22sdp"
                                    android:src="@drawable/ic_arrow_down_ns"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="@id/tv_source_account_label"
                                    app:layout_constraintBottom_toBottomOf="@id/tv_source_account" />

                            </androidx.constraintlayout.widget.ConstraintLayout>


                        </androidx.cardview.widget.CardView>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_title_filter"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:visibility="gone"
                            android:layout_marginTop="@dimen/space_x1"
                            android:layout_marginBottom="@dimen/space_x3"
                            android:clipToPadding="false"
                            android:paddingHorizontal="@dimen/space_x2" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_below="@id/kontak_terakhir_briva"
                            android:orientation="vertical">

                            <CheckBox
                                android:id="@+id/cb_subfilter_all"
                                style="@style/BodyMediumText.Medium.Black"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/space_x2"
                                android:button="@drawable/checkbox_rec_blue"
                                android:text="@string/semua"
                                android:visibility="gone"
                                android:paddingLeft="@dimen/size_14dp"/>

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_subtitle_filter"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginHorizontal="@dimen/space_x2"
                                android:layout_marginBottom="@dimen/space_x2"
                                android:clipToPadding="false"
                                android:layoutAnimation="@anim/layout_animation_fall_down"
                                android:nestedScrollingEnabled="false" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.core.widget.NestedScrollView>

            <LinearLayout
                android:id="@+id/layout_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/space_x2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnSubmit"
                    style="@style/ButtonPrimaryNewSkin"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/space_x6"
                    android:layout_weight="1"
                    android:alpha="0.3"
                    android:enabled="false"
                    android:text="@string/simpan"
                    android:textAllCaps="false" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</RelativeLayout>