<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/rl_mutasi_bulan"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/space_x1"
            android:paddingHorizontal="@dimen/space_x2"
            android:visibility="gone"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_bulan_mutasi"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/bri_text_semibold"
                style="@style/Body3SmallText.Regular.NeutralLight60"
                android:textSize="@dimen/_12sdp"
                android:textColor="@color/ns_black500"
                android:text="14 Juni 2025"/>
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_mutasi_item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="visible"
            tools:listitem="@layout/item_mutasi"
            android:nestedScrollingEnabled="true"/>
    </LinearLayout>
</LinearLayout>