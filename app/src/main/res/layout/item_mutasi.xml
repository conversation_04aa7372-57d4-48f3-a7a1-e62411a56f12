<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/ll_item"
    android:orientation="vertical"
    android:paddingTop="@dimen/space_x1_half"
    android:paddingHorizontal="@dimen/space_x2">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/space_half"
            android:weightSum="2"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/ll_left"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_weight="1.2"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_detail_transaksi"
                    style="@style/Body1LargeText.Medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minHeight="@dimen/space_x6"
                    android:text="@string/empty"
                    android:lineSpacingExtra="@dimen/space_half"
                    android:textStyle="bold"
                    android:textSize="@dimen/_12sdp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tv_tanggal_transaksi"
                    style="@style/Body1LargeText.Medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/empty"
                    android:textStyle="bold"
                    android:textSize="@dimen/_12sdp"
                    android:lineSpacingExtra="@dimen/space_half"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tv_transaksi"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    style="@style/Body1LargeText.SemiBold"
                    android:lineSpacingExtra="@dimen/space_half"
                    android:singleLine="false"
                    android:text="Pembayaran Tokopedia 0857****896 via Qitta"
                    android:textColor="@color/ns_black900"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_right"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_weight="0.8"
                android:gravity="end"
                android:layout_marginStart="@dimen/space_x1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_nominal_mutasi"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginHorizontal="@dimen/space_half"
                    style="@style/Body1LargeText.SemiBold"
                    android:singleLine="true"
                    android:text="-Rp31.000,00"
                    android:textColor="@color/ns_black900"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_time_mutasi"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginHorizontal="@dimen/space_half"
                    android:layout_marginTop="@dimen/space_half"
                    style="@style/Body3SmallText.Regular.NeutralLight60"
                    android:singleLine="true"
                    android:text="12:30:21 WIB"
                    android:textColor="@color/ns_black500"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_1dp"
        android:background="@color/black_ns_200"
        android:layout_marginTop="@dimen/space_x1_half"/>
</LinearLayout>