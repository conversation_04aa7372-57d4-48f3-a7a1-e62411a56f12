<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_action"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="16dp">

    <ImageView
        android:id="@+id/iv_info_profile"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:scaleType="fitXY"
        android:src="@drawable/ic_change_language_n"
        app:layout_constraintBottom_toBottomOf="@id/tv_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_name" />


    <TextView
        android:id="@+id/tv_name"
        style="@style/BodyText.Large.SemiBold.BlackNsMain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginBottom="16dp"
        android:paddingVertical="@dimen/space_x1_half"
        app:layout_constraintBottom_toTopOf="@id/view"
        app:layout_constraintStart_toEndOf="@+id/iv_info_profile"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="bahasa" />

    <ImageView
        android:id="@+id/iv_arrow"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginVertical="@dimen/space_x1_half"
        tools:src="@drawable/ic_arrow_setting_n"
        app:layout_constraintBottom_toBottomOf="@id/tv_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_name" />

    <View
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_1dp"
        android:background="@color/neutralLight20"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>