<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_380dp"
        android:importantForAccessibility="no"
        android:scaleType="fitXY"
        android:src="@drawable/bg_revamp_biru_atas"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <id.co.bri.brimo.ui.customviews.toolbar.ToolbarRevampView
            android:id="@+id/toolbarActivityApplyCcConfirm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:toolbarIsTitleCentered="true"
            app:toolbarNavigationIcon="@drawable/ic_arrow_left_revamp"
            app:toolbarTitle="@string/str_konfirmasi" />

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toBottomOf="@id/toolbarActivityApplyCcConfirm"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/tvActivityApplyCcConfirmTitle"
                    style="@style/Body3SmallText.Medium.NeutralBaseWhite"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_x2"
                    android:text="@string/txt_periksa_dan_pastikan_data_berikut_sudah_sesuai_sebelum_melanjutkan_pengajuan"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.core.widget.NestedScrollView
                    android:id="@+id/nsvActivityApplyCcConfirm"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/space_x2"
                    app:layout_constraintBottom_toTopOf="@id/mcvActivityApplyCcConfirmTcnButton"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvActivityApplyCcConfirmTitle">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <androidx.cardview.widget.CardView
                            android:id="@+id/mcvActivityApplyCcConfirm"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginBottom="@dimen/space_x2"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="@dimen/space_x2">

                            <LinearLayout
                                android:id="@+id/layActivityApplyCcConfirm"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:animateLayoutChanges="true"
                                android:orientation="vertical">

                                <androidx.cardview.widget.CardView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_margin="@dimen/space_x2"
                                    app:cardCornerRadius="@dimen/space_x2">

                                    <androidx.constraintlayout.widget.ConstraintLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent">

                                        <ImageView
                                            android:id="@+id/ivActivityApplyCcConfirm"
                                            android:layout_width="match_parent"
                                            android:layout_height="@dimen/space_x13"
                                            android:scaleType="fitXY"
                                            app:layout_constraintBottom_toBottomOf="parent"
                                            app:layout_constraintEnd_toEndOf="parent"
                                            app:layout_constraintHorizontal_bias="0.5"
                                            app:layout_constraintStart_toStartOf="parent"
                                            app:layout_constraintTop_toTopOf="parent"
                                            app:layout_constraintVertical_bias="0.5"
                                            tools:ignore="ContentDescription"
                                            tools:src="@drawable/ic_bri_world_card" />

                                        <TextView
                                            android:id="@+id/tv_activity_apply_vcc_confirm_card_name"
                                            style="@style/Body3SmallText.Bold.NeutralBaseWhite"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="@dimen/space_x2"
                                            android:elevation="@dimen/space_x2"
                                            android:textColor="@color/white"
                                            app:layout_constraintBottom_toBottomOf="parent"
                                            app:layout_constraintStart_toStartOf="parent"
                                            app:layout_constraintTop_toTopOf="parent"
                                            app:layout_constraintVertical_bias="0.5"
                                            tools:text="@tools:sample/lorem" />
                                    </androidx.constraintlayout.widget.ConstraintLayout>
                                </androidx.cardview.widget.CardView>

                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/rvActivityApplyCcConfirm"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:nestedScrollingEnabled="false"
                                    android:orientation="vertical"
                                    android:paddingHorizontal="@dimen/space_x2"
                                    android:paddingBottom="@dimen/space_x2"
                                    tools:itemCount="4"
                                    tools:layoutManager="LinearLayoutManager"
                                    tools:listitem="@layout/item_apply_vcc_confirm_content" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>
                    </LinearLayout>
                </androidx.core.widget.NestedScrollView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/mcvActivityApplyCcConfirmTcnButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardElevation="@dimen/space_x1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="parent">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layActivityApplyCcConfirmTncButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <androidx.appcompat.widget.AppCompatCheckBox
                            android:id="@+id/cbActivityApplyCcRiplay"
                            android:layout_width="@dimen/space_x3"
                            android:layout_height="@dimen/space_x3"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginTop="@dimen/space_x2"
                            android:background="@null"
                            android:button="@drawable/checkbox_rec_blue"
                            android:enabled="false"
                            android:textIsSelectable="false"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:checked="false"
                            tools:ignore="TouchTargetSizeCheck" />

                        <TextView
                            android:id="@+id/tvActivityApplyCcConfirmRiplay"
                            style="@style/CaptionText.Medium.Black"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/space_x1"
                            android:layout_marginEnd="@dimen/space_x2"
                            android:gravity="center_vertical"
                            app:layout_constraintBottom_toBottomOf="@id/cbActivityApplyCcRiplay"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="0"
                            app:layout_constraintStart_toEndOf="@id/cbActivityApplyCcRiplay"
                            app:layout_constraintTop_toTopOf="@id/cbActivityApplyCcRiplay"
                            tools:ignore="TextSizeCheck"
                            android:text="@string/riplay_consent" />

                        <androidx.appcompat.widget.AppCompatCheckBox
                            android:id="@+id/cbActivityApplyCcConfirm"
                            android:layout_width="@dimen/space_x3"
                            android:layout_height="@dimen/space_x3"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginTop="@dimen/space_x1_half"
                            android:background="@null"
                            android:button="@drawable/checkbox_rec_blue"
                            android:enabled="false"
                            android:textIsSelectable="false"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/cbActivityApplyCcRiplay"
                            tools:checked="false"
                            tools:ignore="TouchTargetSizeCheck" />

                        <TextView
                            android:id="@+id/tvActivityApplyCcConfirmTnc"
                            style="@style/CaptionText.Medium.Black"
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:layout_marginStart="@dimen/space_x1"
                            android:layout_marginEnd="@dimen/space_x2"
                            android:gravity="center_vertical"
                            app:layout_constraintBottom_toBottomOf="@id/cbActivityApplyCcConfirm"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="0"
                            app:layout_constraintStart_toEndOf="@id/cbActivityApplyCcConfirm"
                            app:layout_constraintTop_toTopOf="@id/cbActivityApplyCcConfirm"
                            tools:ignore="TextSizeCheck"
                            tools:text="Saya menyetujui seluruh Syarat &amp; Ketentuan" />

                        <androidx.appcompat.widget.AppCompatButton
                            android:id="@+id/bActivityApplyCcConfirm"
                            style="@style/ButtonPrimaryRevamp"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/space_x6"
                            android:layout_margin="@dimen/space_x2"
                            android:enabled="true"
                            android:text="@string/label_buat_pengajuan"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="0.5"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/cbActivityApplyCcConfirm"
                            tools:ignore="VisualLintButtonSize,TextSizeCheck" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.cardview.widget.CardView>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>