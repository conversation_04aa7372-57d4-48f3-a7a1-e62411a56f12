<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <RelativeLayout
        android:id="@+id/rl_header"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/bg_login_revamp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <include
            android:id="@+id/toolbar_revamp"
            layout="@layout/toolbar_logo_revamp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <id.co.bri.brimo.ui.customviews.switchbutton.SwitchLanguageButtonView
            android:id="@+id/switch_button"
            android:layout_width="@dimen/space_x10"
            android:layout_height="@dimen/space_x5"
            android:layout_marginStart="@dimen/_12sdp"
            android:layout_marginTop="@dimen/_12sdp"/>

        <LinearLayout
            android:id="@+id/ll_kontak_kami"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12sdp"
            android:background="@drawable/background_cardview_noborder_half"
            android:backgroundTint="@color/blue_BRI80"
            android:orientation="horizontal"
            android:layout_alignParentEnd="true"
            android:padding="@dimen/space_half">

            <ImageView
                android:layout_width="@dimen/space_x3"
                android:layout_height="@dimen/space_x3"
                android:contentDescription="@string/information"
                android:src="@drawable/ic_call_center_outline" />

            <TextView
                style="@style/Caption2MicroText.Bold.NeutralBaseWhite"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_2dp"
                android:text="@string/contact_us_fastmenu" />
        </LinearLayout>

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/toolbar_revamp"
            android:layout_marginTop="@dimen/space_x1_half">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <include
                        android:id="@+id/safety_mode"
                        layout="@layout/safety_mode_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <include
                        android:id="@+id/alert_maintenance"
                        layout="@layout/alert_caution_item"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/space_x2"
                        android:layout_marginTop="@dimen/space_x2"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/safety_mode" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </RelativeLayout>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline8"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.25" />

    <RelativeLayout
        android:id="@+id/ll_iv"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/space_x2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/guideline8"
        app:layout_constraintBottom_toTopOf="@+id/rl_menu">

        <TextView
            android:id="@+id/tv_dynamic"
            style="@style/Body1LargeText.Bold.NeutralBaseWhite"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/space_x2"
            android:layout_marginBottom="@dimen/space_x5"
            android:gravity="center"
            android:maxLength="60"
            tools:text="@string/askSelamatDatang" />

        <ImageView
            android:id="@+id/image_view_dynamic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_dynamic"
            android:layout_centerHorizontal="true"
            android:layout_marginHorizontal="@dimen/space_x2"
            android:src="@drawable/slice_brimo_default" />
    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rl_menu"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/space_x2"
        app:layout_constraintBottom_toTopOf="@id/ll_2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/ll_info_fast_menu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/text_fast_menu"
                style="@style/Body1LargeText.Bold.PrimaryBlue80"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/fast_menu" />

            <ImageView
                android:layout_width="@dimen/size_20dp"
                android:layout_height="@dimen/size_20dp"
                android:layout_marginStart="@dimen/space_half"
                android:contentDescription="@null"
                android:src="@drawable/ic_information_blue" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <HorizontalScrollView
            android:id="@+id/hsv_fast_menu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:overScrollMode="never"
            android:paddingHorizontal="@dimen/space_x2"
            android:scrollbars="none"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_info_fast_menu">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/space_x2">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_home_menu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:overScrollMode="never"
                    android:paddingHorizontal="@dimen/size_10dp"
                    app:spanCount="5"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    tools:listitem="@layout/item_home_menu_revamp"
                    tools:itemCount="5" />

                <LinearLayout
                    android:id="@+id/ll_edit_fast"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toEndOf="@id/rv_home_menu"
                    android:gravity="center"
                    android:orientation="vertical"
                    tools:ignore="UseCompoundDrawables">

                    <ImageView
                        android:id="@+id/iv_edit"
                        android:layout_width="@dimen/size_52dp"
                        android:layout_height="@dimen/size_52dp"
                        android:layout_marginHorizontal="@dimen/space_x1_half"
                        android:layout_marginBottom="@dimen/space_x1"
                        android:contentDescription="@null"
                        android:src="@drawable/ic_menu_edit" />

                    <TextView
                        style="@style/BodySmallText.DemiBold.Black"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:letterSpacing="0.03"
                        android:lineSpacingExtra="1sp"
                        android:maxLines="2"
                        android:text="@string/edit" />
                </LinearLayout>
            </RelativeLayout>
        </HorizontalScrollView>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_seekbar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/hsv_fast_menu">

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/horizontal_seekbar_background"
                app:layout_constraintBottom_toBottomOf="@id/sb_custom"
                app:layout_constraintEnd_toEndOf="@id/sb_custom"
                app:layout_constraintStart_toStartOf="@id/sb_custom"
                app:layout_constraintTop_toTopOf="@id/sb_custom" />

            <SeekBar
                android:id="@+id/sb_custom"
                android:layout_width="@dimen/size_64dp"
                android:layout_height="@dimen/size_8dp"
                android:max="100"
                android:progressDrawable="@drawable/horizontal_seekbar"
                android:thumb="@drawable/horizontal_thumb"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ll_2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@id/cl_voice_assistant"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <RelativeLayout
            android:id="@+id/rl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16sdp"
            android:layout_marginBottom="@dimen/space_x2">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_login"
                style="@style/Body2MediumText.Bold.NeutralBaseWhite"
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_x6"
                android:layout_toStartOf="@id/btn_fingerprint"
                android:background="@drawable/rounded_button_blue"
                android:text="@string/action_sign_in"
                android:textAllCaps="false" />

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/btn_fingerprint"
                android:layout_width="@dimen/space_x6"
                android:layout_height="@dimen/space_x6"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/space_x1_half"
                android:background="@drawable/rounded_button_blue"
                android:contentDescription="@null"
                android:src="@drawable/ic_fingerprint_black_24dp"
                android:textColor="@color/whiteColor"
                android:visibility="gone" />
        </RelativeLayout>
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_voice_assistant"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/iv_voice_assistant"
            android:layout_width="@dimen/size_18dp"
            android:layout_height="@dimen/size_18dp"
            app:lottie_rawRes="@raw/arrow_lottie"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_avatar_sabrina"
            android:layout_width="@dimen/size_28dp"
            android:layout_height="@dimen/size_28dp"
            android:layout_marginEnd="@dimen/space_x1"
            android:src="@drawable/avatar_sabrina"
            app:layout_constraintBottom_toBottomOf="@+id/cl_detail_voice_assistant"
            app:layout_constraintEnd_toStartOf="@+id/cl_detail_voice_assistant"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/cl_detail_voice_assistant" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_detail_voice_assistant"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_x1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_avatar_sabrina"
            app:layout_constraintTop_toBottomOf="@+id/iv_voice_assistant">

            <TextView
                android:id="@+id/tv_voice_assistant_label"
                style="@style/Body2MediumText.Bold.PrimaryBlue80"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/brimo_voice_assistant"
                android:textAlignment="center"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_swipe_up"
                style="@style/Caption1SmallText.Regular.NeutralLight80"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_half"
                android:text="@string/swipe_up"
                android:textAlignment="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_voice_assistant_label" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/divider"
            android:layout_width="@dimen/size_2dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/space_x2"
            android:background="@color/primaryBlue80"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_detail_voice_assistant" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>