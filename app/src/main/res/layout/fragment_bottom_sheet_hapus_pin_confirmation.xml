<?xml version="1.0" encoding="utf-8"?>
<id.co.bri.brimo.ui.widget.BaseBottomSheetLayout xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:app="http://schemas.android.com/apk/res-auto"
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		android:orientation="vertical"
		android:id="@+id/bbsl_root"
		app:setTitle=""
		android:maxHeight="@dimen/size_500dp">

	<ImageView
			android:layout_width="220dp"
			android:layout_height="220dp"
			android:layout_gravity="center"
			android:src="@drawable/ic_sad_new_ns"/>

	<TextView
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:layout_marginTop="@dimen/space_x2"
			android:layout_marginHorizontal="@dimen/space_x2"
			android:text="@string/txt_hapus_favorit"
			android:textSize="16sp"
			android:textStyle="bold"
			android:textColor="@color/text_black_default_ns"
			android:gravity="center"/>

	<TextView
			android:id="@+id/tvDescription"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:layout_marginTop="@dimen/space_x1"
			android:layout_marginHorizontal="@dimen/space_x2"
			android:text="@string/desc_hapus_dialog_ns"
			android:textSize="14sp"
			android:textColor="@color/text_gray_default_ns"
			android:gravity="center"/>

	<LinearLayout
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:layout_gravity="bottom"
			android:layout_marginTop="40dp"
			android:layout_alignParentBottom="true"
			android:orientation="vertical"
			android:padding="16dp"
			android:background="@android:color/white">


		<Button
				android:id="@+id/btnCancel"
				android:layout_width="match_parent"
				android:layout_height="56dp"
				style="@style/CustomButtonStyle"
				android:text="@string/batal"
				android:textSize="16sp"
				android:textAllCaps="false"
				android:textStyle="bold"
				android:background="@drawable/rounded_button_ns"
				android:textColor="@color/selector_text_color_button_primary_ns"
				android:enabled="true" />

		<Button
				android:id="@+id/btnSubmit"
				android:layout_width="match_parent"
				android:layout_height="56dp"
				android:layout_marginTop="12dp"
				style="@style/CustomButtonStyle"
				android:text="@string/delete"
				android:textSize="16sp"
				android:textAllCaps="false"
				android:textStyle="bold"
				android:background="@drawable/rounded_button_border_blue_ns"
				android:textColor="@color/text_brand_primary_ns"
				android:enabled="true" />
	</LinearLayout>



</id.co.bri.brimo.ui.widget.BaseBottomSheetLayout>