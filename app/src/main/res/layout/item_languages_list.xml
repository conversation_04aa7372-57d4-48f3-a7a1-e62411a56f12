<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/space_x2">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/imgLanguage"
        android:layout_width="@dimen/size_32dp"
        android:layout_height="@dimen/size_32dp"
        android:layout_marginVertical="@dimen/size_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintCircleRadius="@dimen/space_x1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.App.CornerSize50Percent"
        tools:src="@tools:sample/avatars" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLanguageName"
        style="@style/BodyText.Large.SemiBold.BlackNsMain"
        android:layout_width="@dimen/item_offset"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/size_12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/rgSelectedLanguage"
        app:layout_constraintStart_toEndOf="@id/imgLanguage"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@tools:sample/cities" />

    <RadioGroup
        android:id="@+id/rgSelectedLanguage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/imgLanguage"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/imgLanguage">

        <RadioButton
            android:id="@+id/rbSelectedLanguage"
            android:layout_width="@dimen/size_24dp"
            android:layout_height="@dimen/size_24dp"
            android:button="@drawable/selector_radio_button_new" />

    </RadioGroup>

</androidx.constraintlayout.widget.ConstraintLayout>