<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_file_type"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:layout_marginHorizontal="@dimen/_13sdp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_18sdp"
        android:layout_marginBottom="@dimen/_18sdp"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_download_pdf_ns"/>

        <TextView
            android:id="@+id/tv_file_type_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            style="@style/Body1LargeText.Medium"
            android:layout_marginStart="@dimen/_13sdp"
            android:textColor="@color/ns_black900"
            android:textSize="18dp"
            android:text="PDF" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/black_ns_200"/>

</LinearLayout>