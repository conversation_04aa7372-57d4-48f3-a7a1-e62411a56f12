<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/rounded_dialog">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="@dimen/space_x1"
        android:paddingBottom="@dimen/space_x2">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_line_bottomsheet"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">
            <TextView
                android:id="@+id/txtTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/document_type"
                android:textSize="18sp"
                style="@style/Body1LargeText.Medium"
                android:textStyle="bold"
                android:textColor="@color/ns_black900"
                android:layout_centerInParent="true" />

            <ImageView
                android:id="@+id/ivClose"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="20dp"
                android:layout_centerInParent="true"
                android:layout_alignParentEnd="true"
                android:src="@drawable/ic_cancel_button_ns" />
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list_file_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:listitem="@layout/item_file_type"
            android:visibility="gone"
            android:paddingHorizontal="@dimen/space_x2"/>

        <include layout="@layout/item_file_type" />

        <include layout="@layout/item_file_type" />

    </LinearLayout>
</FrameLayout>