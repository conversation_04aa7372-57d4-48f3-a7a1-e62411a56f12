<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_new_skin_activity_container"
    tools:context=".ui.activities.InputPasswordNewSkinActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_new_skin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:layout_marginTop="90dp"
        android:background="@drawable/bg_new_skin_activity">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginTop="24dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <RelativeLayout
                android:id="@+id/rl_inisial"
                android:layout_width="@dimen/space_x6"
                android:layout_height="@dimen/space_x6"
                android:background="@drawable/round_history_revamp"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_inisial"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_marginHorizontal="@dimen/size_5dp"
                    android:layout_marginVertical="@dimen/size_10dp"
                    android:contentDescription="@null" />

                <TextView
                    android:id="@+id/tv_inisial"
                    style="@style/BodyText.Medium.SemiBold.BlackNs600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="DC" />
            </RelativeLayout>

            <TextView
                android:id="@+id/tv_name"
                style="@style/TitleText.ExtraLarge.SemiBold.BlackNsMain"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/space_x7"
                android:layout_marginTop="@dimen/space_x2"
                android:textAlignment="center"
                tools:text="Hai, " />

            <TextView
                style="@style/BodyText.Large.Regular.BlackNs600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_x2"
                android:text="@string/masukkan_password_untuk_masuk" />

            <id.co.bri.brimo.ui.customviews.forminput.FormInputDefaultView
                android:id="@+id/input_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/space_x2"
                android:layout_marginTop="@dimen/size_24dp"
                android:hint="Password"
                android:inputType="textPassword"
                app:iconColor="@color/text_black_default_ns"
                app:iconDrawable="@drawable/ic_new_skin_eye_close"
                app:iconMode="custom" />
            <TextView
                android:id="@+id/tv_error_message"
                style="@style/CaptionText.Medium.Red"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/space_x2"
                android:layout_marginTop="@dimen/space_half"
                android:paddingHorizontal="@dimen/space_x2"
                android:text="@string/msg_password_salah"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_forget_pass"
                style="@style/TitleText.Small.SemiBold.NsPrimary600"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_x4"
                android:text="@string/forgot_password"
                android:textAlignment="center" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/ns_graysoft"
            app:layout_constraintBottom_toTopOf="@id/layout_button"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/space_x2"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_login"
                style="@style/ButtonPrimaryNewSkin"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="@dimen/space_x7"
                android:text="@string/action_sign_in"
                app:iconGravity="textStart"
                app:iconPadding="8dp"
                app:backgroundTint="@color/selector_button_primary_bg"
                android:enabled="false" />

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/btn_fingerprint"
                android:layout_width="@dimen/space_x6"
                android:layout_height="@dimen/space_x6"
                android:layout_marginStart="@dimen/space_x1_half"
                android:layout_gravity="center_vertical"
                android:background="@drawable/bg_fingerprint_button"
                android:contentDescription="@null"
                android:src="@drawable/ic_fingerprint_black_24dp"
                android:tint="@color/fingerprint_icon_tint"/>
        </LinearLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>