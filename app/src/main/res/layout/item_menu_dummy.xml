<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_menu"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/space_x1"
    android:gravity="top|center_horizontal"
    android:minHeight="@dimen/space_x12"
    tools:ignore="MissingDefaultResource">

    <FrameLayout
        android:layout_width="@dimen/space_x10"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/kategoriDot"
            android:layout_width="@dimen/space_x2"
            android:layout_height="@dimen/space_x2"
            android:layout_gravity="right"
            android:layout_marginEnd="@dimen/space_x1"
            android:elevation="@dimen/space_half"
            android:src="@drawable/circle_red"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/btnMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_2sdp"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/Gambar"
                android:layout_width="@dimen/space_x6"
                android:layout_height="@dimen/space_x6"
                android:layout_gravity="center"
                android:layout_marginBottom="@dimen/space_x1"
                android:src="@drawable/ic_kategori_asuransi" />

            <TextView
                android:id="@+id/namaMenu"
                style="@style/BodySmallText.DemiBold.Black"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/btnMenu"
                android:layout_gravity="center"
                android:breakStrategy="simple"
                android:gravity="top|center_horizontal"
                android:lineSpacingExtra="@dimen/line_spacing_menu"
                android:minHeight="@dimen/space_x5"
                tools:text="Menu" />
        </LinearLayout>

    </FrameLayout>

</RelativeLayout>
