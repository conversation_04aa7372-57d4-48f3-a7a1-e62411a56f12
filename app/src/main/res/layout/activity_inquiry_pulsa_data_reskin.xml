<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_new_skin_activity_container"
        android:orientation="vertical">
        <include
            android:id="@+id/toolbar"
            layout="@layout/toolbar_ns"/>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="16dp"
            android:background="@drawable/bg_card_rounded_ns">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <androidx.coordinatorlayout.widget.CoordinatorLayout
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:background="@null"
                    app:layout_constraintBottom_toTopOf="@+id/ly_total_detail">

                    <com.google.android.material.appbar.AppBarLayout
                        android:id="@+id/appbarDashboard"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:elevation="0dp"
                        android:elevation="0dp"
                        android:background="@null">

                        <com.google.android.material.appbar.CollapsingToolbarLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@null"
                            android:minHeight="0dp"
                            android:elevation="0dp"
                            app:expandedTitleTextAppearance="@style/TextAppearance.AppCompat.Title"
                            app:layout_scrollFlags="scroll|exitUntilCollapsed"
                            app:scrimAnimationDuration="0"
                            app:titleEnabled="false">

                            <id.co.bri.brimo.ui.widget.input_til.BaseInputView
                                android:id="@+id/biv_no_pelanggan"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="21dp"
                                app:prefixText="+62"
                                app:expanded="true"
                                app:hintText="Nomor HP" />

                        </com.google.android.material.appbar.CollapsingToolbarLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">
                            <com.google.android.material.tabs.TabLayout
                                android:id="@+id/tabLayout"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:tabIndicatorColor="@color/ns_primary500"
                                app:tabIndicatorHeight="3dp"
                                app:tabTextColor="@color/ns_black500"
                                app:tabSelectedTextColor="@color/ns_primary500"
                                app:tabMode="scrollable"
                                app:tabIndicatorFullWidth="false"
                                app:tabGravity="start"
                                app:elevation="0dp"
                                android:elevation="0dp"
                                app:layout_scrollFlags="enterAlways"
                                app:tabTextAppearance="@style/CustomTabLayoutTextStyleRevamp" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/one_point_five_px"
                                android:background="@color/border_gray_soft_ns" />
                        </LinearLayout>

                    </com.google.android.material.appbar.AppBarLayout>

                    <androidx.viewpager2.widget.ViewPager2
                        android:id="@+id/viewPager"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:layout_behavior="@string/appbar_scrolling_view_behavior"
                        android:layout_weight="1"/>
                </androidx.coordinatorlayout.widget.CoordinatorLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/size_1dp"
                    android:background="#E9EEF6"
                    app:layout_constraintBottom_toTopOf="@+id/ly_total_detail" />
                <LinearLayout
                    android:id="@+id/ly_total_detail"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:background="@android:color/white">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingVertical="16dp"
                        android:gravity="center_vertical"
                        android:background="@android:color/white">

                        <!-- Card Image -->
                        <ImageView
                            android:id="@+id/iv_rekening"
                            android:layout_width="48dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_card_britama"
                            android:scaleType="fitCenter"
                            android:layout_marginEnd="12dp" />

                        <!-- Text Container -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <!-- Account Info -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center|left">
                                <TextView
                                    android:id="@+id/tv_number_account"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0290 3445 9681 112"
                                    android:textSize="12sp"
                                    android:textColor="#000000" />

                                <ImageView
                                    android:id="@+id/iv_showhide_currency"
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_marginStart="5dp"
                                    android:src="@drawable/icon_unhide_eye" />
                            </LinearLayout>

                            <!-- Balance -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">
                                <TextView
                                    android:id="@+id/tv_nominal_account"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    tools:text="Rp120"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:textColor="#181C21"
                                    android:layout_marginTop="4dp" />

                                <TextView
                                    android:id="@+id/tv_warning_currency"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="4dp"
                                    android:text="Saldo tidak cukup"
                                    android:textSize="12sp"
                                    android:textColor="#E84040"
                                    android:visibility="gone"
                                    android:layout_marginTop="4dp" />
                            </LinearLayout>
                        </LinearLayout>

                        <!-- Edit (Ubah) Button -->
                        <TextView
                            android:id="@+id/btnUbah"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Ubah"
                            android:textColor="#2563EB"
                            android:textStyle="bold"
                            android:textSize="14sp"
                            android:visibility="gone" />

                        <ImageView
                            android:id="@+id/iv_change"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/ic_chevron_down_reskin" />
                    </LinearLayout>

                    <Button
                        android:id="@+id/btnSubmit"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        style="@style/CustomButtonStyle"
                        android:text="Lanjutkan"
                        android:textSize="16sp"
                        android:textAllCaps="false"
                        android:background="@drawable/rounded_button_ns"
                        android:textColor="@color/selector_text_color_button_primary_ns"
                        android:enabled="false" />
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </FrameLayout>
    </LinearLayout>
</layout>