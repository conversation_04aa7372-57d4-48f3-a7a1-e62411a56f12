<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootCoordinator"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_new_skin_activity_container">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_new_skin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="0dp"
        android:layout_marginTop="90dp"
        android:layout_marginEnd="0dp"
        android:background="@drawable/background_cardview_white_newskin"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:id="@+id/linearLayout5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/space_x2"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvDesc"
                style="@style/BodyText.Large.Regular.BlackNs600"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="24dp"
                android:includeFontPadding="false"
                android:text="Masukkan password kamu saat ini untuk menjaga keamanan akun"
                android:layout_marginHorizontal="@dimen/space_x2" />


            <id.co.bri.brimo.ui.customviews.forminput.FormInputDefaultView
                android:id="@+id/input_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_24dp"
                android:hint="Password Lama"
                android:inputType="textPassword"
                app:iconColor="@color/text_black_default_ns"
                app:iconDrawable="@drawable/ic_new_skin_eye_close"
                app:iconMode="custom" />

            <TextView
                android:id="@+id/tv_lupa_pass"
                android:layout_width="wrap_content"
                android:layout_marginTop="24dp"
                style="@style/BodyText.Large.SemiBold.NsPrimary600"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="center_horizontal"
                android:text="@string/forgot_password" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/layout_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/white"
        android:elevation="0dp"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_1dp"
            android:background="@color/ns_graysoft" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_next"
            style="@style/ButtonPrimaryNewSkin"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_x7"
            android:layout_margin="@dimen/space_x2"
            android:text="@string/lanjutkan"
            app:backgroundTint="@color/selector_button_primary_bg"
            android:enabled="false" />
    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
