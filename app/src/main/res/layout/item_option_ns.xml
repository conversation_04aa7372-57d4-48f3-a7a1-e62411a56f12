<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/space_x2">

    <LinearLayout
        android:id="@+id/ll_option_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/color_layout_onclick"
        android:layout_gravity="center_vertical"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_marginBottom="@dimen/size_24dp"
        android:paddingVertical="@dimen/size_2dp">

        <ImageView
            android:id="@+id/iv_option_icon"
            android:layout_width="@dimen/space_x3"
            android:layout_height="@dimen/space_x3"
            android:layout_gravity="center_vertical"/>

        <TextView
            android:id="@+id/tv_option_name"
            style="@style/BodyMediumText.DemiBold.Black"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_x1_half"
            android:text="@string/empty"/>
    </LinearLayout>

    <View
        android:id="@+id/bottom_border"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/size_1dp"
        android:background="@color/border_gray_soft_ns"
        android:layout_marginBottom="@dimen/space_x3"/>
</LinearLayout>