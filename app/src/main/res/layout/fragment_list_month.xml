<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/rounded_dialog_newskin"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="@dimen/space_x1">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_line_bottomsheet"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp">
            <TextView
                android:id="@+id/txtTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="@string/select_month"
                android:textSize="18sp"
                style="@style/Body1LargeText.Medium"
                android:textStyle="bold"
                android:textColor="@color/ns_black900"
                android:layout_centerInParent="true" />

            <ImageView
                android:id="@+id/ivClose"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="20dp"
                android:layout_centerInParent="true"
                android:layout_alignParentEnd="true"
                android:src="@drawable/ic_cancel_button_ns" />
        </RelativeLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="2"
                android:layout_marginTop="@dimen/space_half">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginHorizontal="16dp"
                    android:orientation="vertical">

                    <NumberPicker
                        android:id="@+id/numberMonth"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:descendantFocusability="blocksDescendants"
                        android:theme="@style/DefaultNumberPickerTheme" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/black_ns_200"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginHorizontal="16dp"
                    android:orientation="vertical">

                    <NumberPicker
                        android:id="@+id/numberYear"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:descendantFocusability="blocksDescendants"
                        android:theme="@style/DefaultNumberPickerTheme" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/black_ns_200"/>

                </LinearLayout>


            </LinearLayout>
        </FrameLayout>

        <LinearLayout
            android:id="@+id/layout_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_x2"
            android:padding="@dimen/space_x2"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_select"
                style="@style/ButtonPrimaryNewSkin"
                android:layout_width="0dp"
                android:layout_height="@dimen/space_x6_half"
                android:layout_weight="1"
                android:text="@string/simpan"
                android:textAllCaps="false" />

        </LinearLayout>

    </LinearLayout>

</FrameLayout>