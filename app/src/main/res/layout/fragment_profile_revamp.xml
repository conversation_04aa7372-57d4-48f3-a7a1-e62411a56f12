<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context="id.co.bri.brimo.ui.fragments.profilerevamp.ProfileRevampFragment">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_new_skin_activity_container"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="153dp" />

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/tb_profile"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_marginTop="@dimen/space_x2"
            android:elevation="@dimen/_2sdp"
            android:textAlignment="center"
            android:gravity="center"
            app:theme="@style/AppThemeBlueBar">

            <RelativeLayout
                android:id="@+id/rl_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginTop="@dimen/space_x2"
                android:layout_marginHorizontal="@dimen/space_x2">

                <TextView
                    android:id="@+id/textTitle"
                    style="@style/Body2MediumText.Bold.NeutralBaseWhite"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/miniapp_settings" />

            </RelativeLayout>


        </androidx.appcompat.widget.Toolbar>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/contentProfile"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/tb_profile"
            android:visibility="visible"
            android:background="@drawable/bg_new_skin_activity">

            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/swipe_refresh"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent">


                <androidx.core.widget.NestedScrollView
                    android:id="@+id/scrollView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">


                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">
                        <!-- android:background="@color/accent3Color"
                        -->

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="153dp"
                            android:layout_marginTop="-55dp" />
                        <!-- android:background="@drawable/background_profile_revamp" -->

                        <androidx.cardview.widget.CardView
                            android:id="@+id/cv_account"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="16dp"
                            android:layout_marginTop="16dp"
                            android:background="@color/cardview_shadow_start_color"
                            android:visibility="gone"
                            android:outlineAmbientShadowColor="@color/colorOtpBox"
                            android:outlineSpotShadowColor="@color/colorOtpBox"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="@dimen/_10sdp">

                            <LinearLayout
                                android:id="@+id/ll_akun"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <RelativeLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:gravity="center_vertical">

                                    <LinearLayout
                                        android:id="@+id/ly_profile"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal">


                                        <com.joooonho.SelectableRoundedImageView
                                            android:id="@+id/iv_profile_pict"
                                            android:layout_width="48dp"
                                            android:layout_height="48dp"
                                            android:layout_centerHorizontal="true"
                                            android:layout_centerVertical="true"
                                            android:layout_gravity="center_vertical"
                                            android:layout_marginStart="14dp"
                                            android:layout_marginTop="14dp"
                                            android:layout_marginBottom="14dp"
                                            android:scaleType="centerCrop"
                                            android:src="@drawable/background_bukarekening"
                                            android:visibility="gone"
                                            app:sriv_left_bottom_corner_radius="50dp"
                                            app:sriv_left_top_corner_radius="50dp"
                                            app:sriv_right_bottom_corner_radius="50dp"
                                            app:sriv_right_top_corner_radius="50dp" />

                                        <RelativeLayout
                                            android:id="@+id/ic_profile"
                                            android:layout_width="56dp"
                                            android:layout_height="56dp"
                                            android:layout_alignParentLeft="true"
                                            android:layout_gravity="center_vertical"
                                            android:layout_margin="16dp"
                                            android:background="@drawable/round_button"
                                            android:backgroundTint="@color/colorButtonGrey"
                                            android:orientation="vertical"
                                            android:visibility="visible">

                                            <ImageView
                                                android:id="@+id/iv_inisial"
                                                android:layout_width="31dp"
                                                android:layout_height="match_parent"
                                                android:layout_gravity="center" />

                                            <TextView
                                                android:id="@+id/tv_inisial"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_centerInParent="true"
                                                android:fontFamily="@font/avenir_next_bold"
                                                android:text="-"
                                                android:textColor="@color/colorTextBlueBri"
                                                android:textSize="16dp" />
                                        </RelativeLayout>
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_centerInParent="true"
                                        android:layout_toLeftOf="@id/ic_chevron"
                                        android:layout_toRightOf="@id/ly_profile"
                                        android:orientation="vertical">

                                        <TextView
                                            android:id="@+id/nama_akun"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="@dimen/_3sdp"
                                            android:fontFamily="@font/avenir_next_bold"
                                            android:text="@string/gagal_memuat_data"
                                            android:textColor="@color/neutral_dark40"
                                            android:textSize="16dp" />

                                        <LinearLayout
                                            android:id="@+id/lyPoint"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:orientation="horizontal"
                                            android:visibility="visible">

                                            <ImageView
                                                android:id="@+id/iv_poin"
                                                android:layout_width="@dimen/_20sdp"
                                                android:layout_height="@dimen/_20sdp"
                                                android:layout_marginTop="6dp"
                                                android:layout_marginEnd="6dp"
                                                android:src="@drawable/ic_bripoin" />

                                            <TextView
                                                android:id="@+id/tv_poin"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_gravity="center"
                                                android:layout_marginTop="2dp"
                                                android:fontFamily="@font/avenir_next_demi"
                                                android:text="-"
                                                android:textColor="@color/colorTextBlueBri"
                                                android:textSize="14dp"
                                                android:visibility="visible" />
                                        </LinearLayout>
                                    </LinearLayout>

                                    <ImageView
                                        android:id="@+id/ic_chevron"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentRight="true"
                                        android:layout_centerInParent="true"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginEnd="10dp"
                                        android:src="@drawable/ic_arrow_right_blue" />

                                </RelativeLayout>
                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/clBripoinCoupon"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/cv_account"
                            android:layout_marginTop="@dimen/space_x1_half"
                            android:padding="@dimen/space_x2"
                            android:background="@color/white"
                            android:visibility="gone"
                            tools:visibility="gone">

                            <androidx.cardview.widget.CardView
                                android:id="@+id/cvCoupon"
                                android:layout_width="@dimen/item_offset"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/space_half"
                                android:visibility="gone"
                                tools:visibility="visible"
                                app:cardBackgroundColor="@color/primary_blue80"
                                app:cardCornerRadius="@dimen/_10sdp"
                                app:cardElevation="@dimen/item_offset"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintEnd_toEndOf="parent">

                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/space_x10">

                                    <androidx.constraintlayout.widget.Group
                                        android:id="@+id/visibilityFailedLoadData"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:visibility="gone"
                                        tools:visibility="visible"
                                        app:constraint_referenced_ids="tvFailedLoadData, imgReloadBripoin"/>

                                    <androidx.appcompat.widget.AppCompatTextView
                                        android:id="@+id/tvFailedLoadData"
                                        style="@style/Body3SmallText.Bold"
                                        android:layout_width="@dimen/item_offset"
                                        android:layout_height="wrap_content"
                                        android:layout_marginHorizontal="@dimen/space_x2"
                                        android:text="@string/txt_bripoin_failed_load_coupon"
                                        android:textColor="@color/white"
                                        app:layout_constraintStart_toStartOf="parent"
                                        app:layout_constraintEnd_toStartOf="@id/imgReloadBripoin"
                                        app:layout_constraintTop_toTopOf="parent"
                                        app:layout_constraintBottom_toBottomOf="parent"/>

                                    <ImageView
                                        android:id="@+id/imgReloadBripoin"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginEnd="10dp"
                                        android:src="@drawable/ic_load_clockwise"
                                        android:contentDescription="@string/txt_bripoin_coupon_count"
                                        app:tint="@color/white"
                                        app:layout_constraintTop_toTopOf="@id/guideline"
                                        app:layout_constraintBottom_toBottomOf="@id/guideline"
                                        app:layout_constraintEnd_toEndOf="parent"/>

                                    <androidx.constraintlayout.widget.Group
                                        android:id="@+id/visibilityCoupon"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:visibility="gone"
                                        tools:visibility="visible"
                                        app:constraint_referenced_ids="imgCoupon, tvCouponCountInfo, tvCouponLastUpdate, icChevronBripoin"/>

                                    <com.google.android.material.imageview.ShapeableImageView
                                        android:id="@+id/imgCoupon"
                                        style="@style/ShapeableImageViewStyle50Percent"
                                        android:layout_width="@dimen/space_x6"
                                        android:layout_height="@dimen/space_x6"
                                        android:layout_margin="@dimen/space_x1_half"
                                        android:background="@drawable/round_button"
                                        android:backgroundTint="@color/white"
                                        tools:src="@drawable/ic_bripoin"
                                        app:layout_constraintTop_toTopOf="parent"
                                        app:layout_constraintBottom_toBottomOf="parent"
                                        app:layout_constraintStart_toStartOf="parent"/>

                                    <androidx.constraintlayout.widget.Guideline
                                        android:id="@+id/guideline"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal"
                                        app:layout_constraintGuide_percent=".5"/>

                                    <androidx.appcompat.widget.AppCompatTextView
                                        android:id="@+id/tvCouponCountInfo"
                                        style="@style/Body3SmallText.Bold"
                                        android:layout_width="@dimen/item_offset"
                                        android:layout_height="wrap_content"
                                        android:layout_marginHorizontal="@dimen/space_x2"
                                        tools:text="@string/txt_bripoin_coupon_count"
                                        android:textColor="@color/white"
                                        android:maxLines="1"
                                        android:ellipsize="end"
                                        app:layout_constraintVertical_bias=".9"
                                        app:layout_constraintTop_toTopOf="@id/imgCoupon"
                                        app:layout_constraintBottom_toBottomOf="@id/guideline"
                                        app:layout_constraintStart_toEndOf="@id/imgCoupon"
                                        app:layout_constraintEnd_toEndOf="parent"/>

                                    <androidx.appcompat.widget.AppCompatTextView
                                        android:id="@+id/tvCouponLastUpdate"
                                        style="@style/Caption2MicroText.Medium"
                                        android:layout_width="@dimen/item_offset"
                                        android:layout_height="wrap_content"
                                        tools:text="@string/txt_bripoin_last_update"
                                        android:textColor="@color/white"
                                        app:layout_constraintVertical_bias=".0099"
                                        app:layout_constraintTop_toTopOf="@id/guideline"
                                        app:layout_constraintBottom_toBottomOf="@id/imgCoupon"
                                        app:layout_constraintStart_toStartOf="@id/tvCouponCountInfo"
                                        app:layout_constraintEnd_toEndOf="@id/tvCouponCountInfo"/>

                                    <ImageView
                                        android:id="@+id/icChevronBripoin"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginEnd="10dp"
                                        android:src="@drawable/ic_arrow_right_blue"
                                        android:contentDescription="@string/txt_bripoin_coupon_count"
                                        app:tint="@color/white"
                                        app:layout_constraintTop_toTopOf="@id/guideline"
                                        app:layout_constraintBottom_toBottomOf="@id/guideline"
                                        app:layout_constraintEnd_toEndOf="parent"/>

                                </androidx.constraintlayout.widget.ConstraintLayout>

                            </androidx.cardview.widget.CardView>

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_list_info"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_below="@+id/clBripoinCoupon"
                            android:layout_marginTop="@dimen/space_x1_half" />

                        <LinearLayout
                            android:id="@+id/ly_logout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:layout_below="@+id/rv_list_info"
                            android:layout_marginBottom="@dimen/space_half"
                            android:orientation="vertical"
                            android:visibility="gone">

                            <androidx.cardview.widget.CardView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:elevation="0dp"
                                android:layout_marginHorizontal="@dimen/space_x1_half"
                                android:layout_marginVertical="@dimen/space_x1"
                                android:outlineAmbientShadowColor="@color/neutral_dark30"
                                android:outlineSpotShadowColor="@color/neutral_dark30"
                                app:cardCornerRadius="@dimen/space_x1_half"
                                app:cardUseCompatPadding="true"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintEnd_toEndOf="parent">

                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:id="@+id/cl_logout"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:padding="@dimen/space_half">

                                    <ImageView
                                        android:id="@+id/iv_info_profile"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:src="@drawable/ic_logout_info"
                                        android:padding="@dimen/space_x1_half"
                                        app:layout_constraintStart_toStartOf="parent"
                                        app:layout_constraintTop_toTopOf="parent"
                                        app:layout_constraintBottom_toBottomOf="parent"/>

                                    <TextView
                                        android:id="@+id/tv_name"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        style="@style/Body3SmallText.Medium.Error"
                                        android:paddingStart="@dimen/space_x1"
                                        android:paddingVertical="@dimen/space_x1_half"
                                        android:text="Logout"
                                        app:layout_constraintStart_toEndOf="@+id/iv_info_profile"
                                        app:layout_constraintTop_toTopOf="parent"/>

                                </androidx.constraintlayout.widget.ConstraintLayout>
                            </androidx.cardview.widget.CardView>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginBottom="@dimen/space_x4"
                            android:layout_below="@+id/ly_logout"
                            android:orientation="vertical"
                            android:background="@color/white">
                            <!--android:background="@color/accent3Color"
                             -->

                            <TextView
                                android:id="@+id/tv_versi"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginVertical="@dimen/space_x2"
                                android:gravity="start"
                                android:layout_marginStart="16dp"
                                android:text="@string/application_version"
                                android:textColor="@color/accent1Color"
                                android:background="@color/white"/>
                            <!-- android:background="@color/accent3Color"
                                -->

                        </LinearLayout>
                    </RelativeLayout>
                </androidx.core.widget.NestedScrollView>
            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>
</RelativeLayout>