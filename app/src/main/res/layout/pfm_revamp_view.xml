<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/tools"
    android:id="@+id/cv_pfm_dashboard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/neutral_light10">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/neutralLight10">

        <LinearLayout
            android:id="@+id/column_top_pfm"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_x9"
            android:orientation="horizontal"
            android:weightSum="2"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:id="@+id/ll_income"
                android:layout_width="0dp"
                android:layout_height="@dimen/space_x9"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:orientation="horizontal">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:gravity="left|center_vertical"
                        android:orientation="vertical"
                        android:padding="@dimen/space_x2">

                        <TextView
                            style="@style/Body2MediumText.Regular.PfmTop"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/income" />

                        <id.co.bri.brimo.ui.customviews.saldo.saldoTextView
                            android:id="@+id/tv_income_value"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:minHeight="@dimen/space_x2_half"
                            app:size="DEFAULT" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="@dimen/space_x9"
                        android:layout_gravity="center_vertical"
                        android:gravity="left|center_vertical"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/space_x9"
                            android:layout_gravity="end"
                            android:src="@drawable/bg_pfm_pemasukan" />
                    </LinearLayout>
                </FrameLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_outcome"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:orientation="horizontal">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:gravity="left|center_vertical"
                        android:orientation="vertical"
                        android:padding="@dimen/space_x2">

                        <TextView
                            style="@style/Body2MediumText.Regular.PfmTop"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/outcome" />

                        <id.co.bri.brimo.ui.customviews.saldo.saldoTextView
                            android:id="@+id/tv_outcome_value"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:minHeight="@dimen/space_x2_half"
                            app:size="DEFAULT" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="@dimen/space_x9"
                        android:layout_gravity="center_vertical"
                        android:gravity="left|center_vertical"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/space_x9"
                            android:layout_gravity="end"
                            android:src="@drawable/bg_pfm_pengeluaran" />
                    </LinearLayout>
                </FrameLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:weightSum="2"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/column_top_pfm">

            <LinearLayout
                android:id="@+id/ll_report"
                android:layout_width="0dp"
                android:layout_height="@dimen/space_x9"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="vertical"
                android:padding="@dimen/space_x2">

                <TextView
                    style="@style/Body2MediumText.Regular.PfmBottom"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="@string/budget_difference" />

                <id.co.bri.brimo.ui.customviews.saldo.saldoTextView
                    android:id="@+id/tv_budget"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:minHeight="@dimen/space_x3"
                    app:size="LARGE" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="right|center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/space_x3">

                <LinearLayout
                    android:id="@+id/btn_lihat_pfm"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_lihat_pfm"
                        style="@style/Title1Text.Bold.Primary80.TextButton"
                        android:layout_width="wrap_content"
                        android:layout_height="16dp"
                        android:layout_gravity="right|center_vertical"
                        android:text="@string/see_details" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/space_x1"
                        android:src="@drawable/ic_arrow_right_long" />
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>