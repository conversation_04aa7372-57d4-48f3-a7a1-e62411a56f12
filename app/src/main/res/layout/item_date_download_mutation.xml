<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/rl_date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/space_x2"
            android:paddingTop="@dimen/space_x2"
            android:visibility="visible"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/Body1LargeText.Medium"
                android:textSize="16sp"
                android:textColor="@color/ns_black500"
                android:text="@string/empty"/>
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_item_download"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:listitem="@layout/item_e_statement"
            android:visibility="visible"
            android:nestedScrollingEnabled="true"/>
    </LinearLayout>
</LinearLayout>