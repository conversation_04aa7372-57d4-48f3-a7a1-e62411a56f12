<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/receipt_share_parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/neutral_light10">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/flipButtonReceipt"
        android:layoutMode="clipBounds">


        <ScrollView
            android:id="@+id/receipt_share"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/tb_receipt"
            android:overScrollMode="never">

            <RelativeLayout
                android:id="@+id/layout_receipt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/neutral_light10"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="383dp"
                    android:background="@drawable/background_receipt_revamp" />

                <RelativeLayout
                    android:id="@+id/rv_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingVertical="@dimen/space_x2">

                    <com.wajahatkarim3.easyflipview.EasyFlipView
                        android:id="@+id/flipLogoReceipt"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_x2"
                        app:flipDuration="400"
                        app:flipEnabled="true"
                        app:flipFrom="front"
                        app:flipOnTouch="false"
                        app:flipType="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/imgCeklist"
                                android:layout_width="@dimen/space_x8_half"
                                android:layout_height="@dimen/space_x8_half"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="@dimen/space_x1"
                                android:contentDescription="@string/information"
                                android:src="@drawable/ic_receipt_00_revamp" />

                        </LinearLayout>

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <pl.droidsonroids.gif.GifImageView
                                android:id="@+id/imgCeklist2"
                                android:layout_width="@dimen/space_x8_half"
                                android:layout_height="@dimen/space_x8_half"
                                android:layout_centerInParent="true"
                                android:layout_marginTop="@dimen/space_x1"
                                android:contentDescription="@string/information"
                                android:src="@drawable/receipt_00_revamp_gif" />

                            <ImageView
                                android:id="@+id/imgCeklist2_img"
                                android:layout_width="@dimen/space_x8_half"
                                android:layout_height="@dimen/space_x8_half"
                                android:layout_centerInParent="true"
                                android:layout_marginTop="@dimen/space_x1"
                                android:contentDescription="@string/information"
                                android:src="@drawable/ic_receipt_00_revamp"
                                android:visibility="gone" />

                            <LinearLayout
                                android:id="@+id/ll_pusat_bantuan"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_gravity="end"
                                android:background="@drawable/background_cardview_noborder_half"
                                android:backgroundTint="@color/blue_BRI80"
                                android:orientation="horizontal"
                                android:padding="@dimen/space_half">

                                <ImageView
                                    android:layout_width="@dimen/space_x3"
                                    android:layout_height="@dimen/space_x3"
                                    android:contentDescription="@string/information"
                                    android:src="@drawable/ic_call_center_outline" />

                                <TextView
                                    style="@style/Caption2MicroText.Bold.NeutralBaseWhite"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/size_2dp"
                                    android:text="@string/pusat_bantuan_2" />
                            </LinearLayout>

                        </RelativeLayout>


                    </com.wajahatkarim3.easyflipview.EasyFlipView>


                    <TextView
                        android:id="@+id/tv_title"
                        style="@style/Title4Text.Bold.NeutralLight10"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/flipLogoReceipt"
                        android:layout_marginTop="@dimen/space_x3"
                        android:gravity="center_horizontal"
                        android:paddingHorizontal="@dimen/space_half"
                        android:text="@string/transaction_success" />

                    <TextView
                        android:id="@+id/tv_date"
                        style="@style/Body3SmallText.Medium.NeutralBaseWhite"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/tv_title"
                        android:gravity="center_horizontal"
                        android:paddingVertical="@dimen/space_half" />

                    <TextView
                        android:id="@+id/wvSubtitle_html"
                        style="@style/Caption1SmallText.Medium.NeutralLight80"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_x2"
                        android:clickable="false"
                        android:longClickable="false"
                        android:nestedScrollingEnabled="false"
                        android:scrollbars="none"
                        android:textColor="@color/neutral_light80"
                        android:layout_below="@+id/tv_date"
                        android:layout_marginHorizontal="@dimen/space_x2"
                        android:textSize="12sp"
                        android:visibility="gone" />
                </RelativeLayout>


                <androidx.cardview.widget.CardView
                    android:id="@+id/cv_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/rv_header"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:layout_marginTop="@dimen/space_x3"
                    android:orientation="vertical"
                    app:cardBackgroundColor="@color/transparent"
                    app:cardElevation="0dp">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="fitXY"
                        android:src="@drawable/bg_ticket_top_revamp" />

                    <LinearLayout
                        android:id="@+id/ll_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/transparent"
                        android:orientation="vertical"
                        android:paddingVertical="@dimen/space_x2">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_total_data_view"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginBottom="@dimen/space_x1"
                            android:overScrollMode="never" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_header_data_view"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginTop="@dimen/space_x1"
                            android:layout_marginBottom="@dimen/space_x1"
                            android:overScrollMode="never" />

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/cv_additional_info"
                            style="@style/Alert.Outline.Default"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginTop="@dimen/space_half"
                            android:layout_marginBottom="@dimen/space_x2"
                            android:padding="@dimen/space_x1_half"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/tv_additional_info"
                                style="@style/Caption1SmallText.Medium.NeutralDark40"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:lineSpacingExtra="@dimen/space_half" />
                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/mcv_redirect_button"
                            style="@style/Alert.Outline.Default"
                            app:strokeColor="@color/neutral_light30"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginBottom="@dimen/space_x2"
                            android:paddingVertical="@dimen/space_x1_half"
                            android:paddingHorizontal="@dimen/space_x2"
                            android:visibility="gone"
                            tools:visibility="visible">
                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical">
                                <ImageView
                                    android:id="@+id/iv_icon_redirect"
                                    android:layout_width="@dimen/space_x2"
                                    android:layout_height="@dimen/space_x2"
                                    android:src="@drawable/ic_financial_bill"
                                    app:layout_constraintTop_toTopOf="parent"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    android:importantForAccessibility="no" />

                                <TextView
                                    android:id="@+id/tv_redirect"
                                    style="@style/Body3SmallText.SemiBold.PrimaryBlue80"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_margin="@dimen/space_half"
                                    android:text="@string/default_button_redirect_receipt"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toStartOf="@id/iv_icon_redirect_right"
                                    app:layout_constraintStart_toEndOf="@+id/iv_icon_redirect"
                                    app:layout_constraintTop_toTopOf="parent" />
                                <ImageView
                                    android:id="@+id/iv_icon_redirect_right"
                                    android:layout_width="@dimen/space_x2"
                                    android:layout_height="@dimen/space_x2"
                                    android:src="@drawable/ic_chevron_right_primary80"
                                    android:layout_gravity="end"
                                    android:layout_weight="1"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:tint="@color/neutral_light70"
                                    android:importantForAccessibility="no" />

                            </androidx.constraintlayout.widget.ConstraintLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <WebView
                            android:id="@+id/wvInfo"
                            style="@style/Caption1SmallText.Medium.NeutralLight80"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clickable="false"
                            android:longClickable="false"
                            android:nestedScrollingEnabled="false"
                            android:scrollbars="none"
                            android:textColor="@color/neutral_light80"
                            android:layout_below="@+id/tv_date"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:textSize="12dp"
                            android:visibility="gone"
                            tools:ignore="WebViewLayout" />
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_eticket_data_view"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginBottom="@dimen/space_x2"
                            android:background="@drawable/background_cardview_stroked_primary50"
                            android:overScrollMode="never"
                            android:padding="@dimen/space_x1_half"
                            android:visibility="gone" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_voucher_data_view"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginTop="@dimen/space_x1"
                            android:layout_marginBottom="@dimen/space_x2"
                            android:background="@drawable/background_cardview_noborder"
                            android:backgroundTint="#eff8ff"
                            android:overScrollMode="never"
                            android:padding="@dimen/space_x2"
                            android:visibility="gone" />

                        <RelativeLayout
                            android:id="@+id/rl_cara_redeem"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginBottom="@dimen/space_x2"
                            android:background="@drawable/bg_nl10_border_nl30"
                            android:paddingHorizontal="@dimen/space_x2"
                            android:paddingVertical="@dimen/space_x1_half"
                            android:visibility="gone">

                            <ImageView
                                android:id="@+id/iv_voucher"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:contentDescription="@null"
                                android:src="@drawable/ic_voucher_redeem" />

                            <TextView
                                android:id="@+id/tv_redeem"
                                style="@style/Body3SmallText.SemiBold.PrimaryBlue80"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_marginHorizontal="@dimen/space_half"
                                android:layout_toStartOf="@id/iv_chevron"
                                android:layout_toEndOf="@id/iv_voucher"
                                android:text="@string/lihat_cara_redeem" />

                            <ImageView
                                android:id="@+id/iv_chevron"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:contentDescription="@null"
                                android:src="@drawable/ic_chevron_right_blue80" />

                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/rl_success"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/background_cardview_success_green"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginBottom="@dimen/space_x2"
                            android:padding="@dimen/space_x1_half"
                            android:visibility="gone">

                            <ImageView
                                android:id="@+id/iv_success"
                                android:layout_width="@dimen/space_x2"
                                android:layout_height="@dimen/space_x2"
                                android:contentDescription="@string/information"
                                android:src="@drawable/ic_ceklis_bg_green" />

                            <TextView
                                android:id="@+id/tv_success_binding"
                                style="@style/Caption1SmallText.Medium.NeutralDark20"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/space_x1"
                                android:layout_toEndOf="@id/iv_success"
                                android:contentDescription="@string/information"
                                android:text="@string/empty" />
                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/rl_warning"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/background_cardview_warning"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginBottom="@dimen/space_x2"
                            android:padding="@dimen/space_x1_half"
                            android:visibility="gone">

                            <ImageView
                                android:id="@+id/iv_warning"
                                android:layout_width="@dimen/space_x2"
                                android:layout_height="@dimen/space_x2"
                                android:contentDescription="@string/information"
                                android:src="@drawable/ic_alert" />

                            <TextView
                                android:id="@+id/tv_warning_binding"
                                style="@style/Caption1SmallText.Medium.NeutralDark20"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/space_x1"
                                android:layout_toEndOf="@id/iv_warning"
                                android:contentDescription="@string/information"
                                android:text="@string/empty" />
                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/rl_lihat_binding_merchant"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginBottom="@dimen/space_x2"
                            android:background="@drawable/bg_nl10_border_nl30"
                            android:paddingHorizontal="@dimen/space_x2"
                            android:paddingVertical="@dimen/space_x1_half"
                            android:visibility="gone">

                            <ImageView
                                android:id="@+id/iv_merchant"
                                android:layout_width="@dimen/_15sdp"
                                android:layout_height="@dimen/_20sdp"
                                android:layout_centerVertical="true"
                                android:contentDescription="@null"
                                android:src="@drawable/ic_mutasi_color" />

                            <TextView
                                android:id="@+id/tv_merchant"
                                style="@style/Body3SmallText.SemiBold.PrimaryBlue80"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_marginHorizontal="@dimen/space_half"
                                android:layout_toStartOf="@id/iv_arrow_merchant"
                                android:layout_toEndOf="@id/iv_merchant"
                                android:text="@string/lihat_merchant_terhubung" />

                            <ImageView
                                android:id="@+id/iv_arrow_merchant"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:contentDescription="@null"
                                android:src="@drawable/ic_chevron_right_blue80" />

                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <RelativeLayout
                                android:id="@+id/rl_dotted_total_receipt"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/space_x3"
                                android:layout_centerVertical="true"
                                android:layout_marginVertical="@dimen/space_x1_half">

                                <ImageView
                                    android:id="@+id/circleLeft"
                                    android:layout_width="@dimen/space_x2_half"
                                    android:layout_height="@dimen/space_x3"
                                    android:layout_marginStart="-2dp"
                                    android:layout_alignParentStart="true"
                                    android:scaleType="fitStart"
                                    android:src="@drawable/half_circle_left_revamp" />

                                <ImageView
                                    android:id="@+id/circleRight"
                                    android:layout_width="@dimen/space_x3"
                                    android:layout_height="@dimen/space_x3"
                                    android:layout_marginEnd="-2dp"
                                    android:scaleType="fitEnd"
                                    android:layout_alignParentEnd="true"
                                    android:src="@drawable/half_circle_right_revamp" />

                            </RelativeLayout>

                            <LinearLayout
                                android:id="@+id/ll_source_data"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/space_x2"
                                android:background="@drawable/background_cardview_stroked_transparent"
                                android:orientation="vertical"
                                android:padding="@dimen/space_x1">
                                <!--recycler view source_account_data_view-->

                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/rv_item_receipt_source_account_data"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/space_x1"
                                    android:overScrollMode="never" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/size_1dp"
                                    android:layout_marginVertical="@dimen/space_x1"
                                    android:background="@color/neutral_light20" />
                                <!--recycler view billing_detail-->

                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/rv_item_receipt_billing_detail"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/space_x1"
                                    android:overScrollMode="never" />
                            </LinearLayout>


                        </RelativeLayout>
                        <!--recycler view transaction_data_view-->
                        <!--rv_item_receipt_source_account_data-->
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_transaction_data_view_small"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginTop="@dimen/space_x2"
                            android:overScrollMode="never"
                            android:visibility="gone" />

                        <!--Success Smart Transfer-->
                        <RelativeLayout
                            android:id="@+id/rl_success_smart_transfer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/background_orange10"
                            android:layout_marginTop="@dimen/space_x1"
                            android:layout_marginBottom="@dimen/space_x1_half"
                            android:padding="@dimen/space_x1_half"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:visibility="gone">

                            <ImageView
                                android:id="@+id/iv_logo_hemat"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@drawable/ic_discount_orange"
                                android:contentDescription="logo"
                                android:layout_alignParentStart="true"
                                android:layout_centerInParent="true" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                style="@style/Caption1SmallText.Medium"
                                android:layout_alignParentEnd="true"
                                android:layout_centerInParent="true"
                                android:layout_marginStart="@dimen/space_x1"
                                android:text="@string/txt_sukses_smart_transfer"
                                android:layout_toEndOf="@+id/iv_logo_hemat"/>
                        </RelativeLayout>

                        <LinearLayout
                            android:id="@+id/ll_lht_lebih"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:orientation="vertical">

                            <LinearLayout
                                android:id="@+id/lihat_lebih"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginTop="@dimen/space_x2"
                                android:layout_marginBottom="@dimen/space_x2"
                                android:orientation="horizontal"
                                android:visibility="visible">

                                <TextView
                                    style="@style/Body2MediumText.Bold.PrimaryBlue80"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:text="@string/receipt_see_more" />

                                <ImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:contentDescription="@string/information"
                                    android:src="@drawable/dropdown" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/ll_show_more"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:visibility="gone">

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/size_1dp"
                                    android:layout_marginTop="@dimen/space_x2"
                                    android:layout_marginBottom="@dimen/space_x1"
                                    android:background="@color/neutral_light20" />

                                <!-- detail rute pesawat -->
                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/rv_detail_route"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginHorizontal="-6dp"
                                    android:overScrollMode="never"
                                    android:visibility="gone"
                                    tools:itemCount="2"
                                    tools:listitem="@layout/item_detail_rute_pesawat" />

                                <LinearLayout
                                    android:id="@+id/ll_detail_pos_aja"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:visibility="gone">

                                    <!-- detail package -->
                                    <androidx.cardview.widget.CardView
                                        android:id="@+id/cv_order_detail"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/space_x1"
                                        android:layout_marginBottom="@dimen/space_x2"
                                        app:cardBackgroundColor="@color/neutral_baseWhite"
                                        app:cardCornerRadius="@dimen/space_x1"
                                        app:cardElevation="1dp">

                                        <RelativeLayout
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content">

                                            <RelativeLayout
                                                android:id="@+id/rl_date"
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"
                                                android:background="@color/primary_blue10"
                                                android:paddingVertical="@dimen/space_x1">

                                                <TextView
                                                    android:id="@+id/tv_date_lifestyle"
                                                    style="@style/Body3SmallText.Medium.NeutralDark40"
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:layout_alignParentStart="true"
                                                    android:layout_centerVertical="true"
                                                    android:layout_marginHorizontal="@dimen/space_x1_half"
                                                    android:layout_toStartOf="@id/tv_duration"
                                                    android:text="@string/schedule" />

                                                <TextView
                                                    android:id="@+id/tv_duration"
                                                    style="@style/Caption2MicroText.SemiBold.NeutralBaseWhite"
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:layout_alignParentEnd="true"
                                                    android:layout_marginEnd="@dimen/space_x1_half"
                                                    android:layout_centerVertical="true"
                                                    android:background="@drawable/bg_radius4dp"
                                                    android:paddingHorizontal="6dp"
                                                    android:paddingVertical="2dp"
                                                    android:text="@string/empty" />

                                            </RelativeLayout>

                                            <LinearLayout
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"
                                                android:layout_below="@+id/rl_date"
                                                android:background="@color/transparent"
                                                android:orientation="vertical">

                                                <!-- start of content ekspedisi -->
                                                <RelativeLayout
                                                    android:id="@+id/rl_card_expedition"
                                                    android:layout_width="match_parent"
                                                    android:layout_height="wrap_content"
                                                    android:layout_marginHorizontal="@dimen/space_x1"
                                                    android:layout_marginTop="@dimen/space_x1_half"
                                                    android:paddingBottom="@dimen/space_half"
                                                    android:visibility="visible">

                                                    <ImageView
                                                        android:id="@+id/iv_sender_expedition"
                                                        android:layout_width="@dimen/size_21dp"
                                                        android:layout_height="@dimen/size_20dp"
                                                        android:layout_alignParentStart="true"
                                                        android:contentDescription="@null"
                                                        android:src="@drawable/ic_send_package" />

                                                    <View
                                                        android:id="@+id/separator_expedition"
                                                        android:layout_width="1dp"
                                                        android:layout_height="wrap_content"
                                                        android:layout_below="@+id/iv_sender_expedition"
                                                        android:layout_gravity="center_horizontal"
                                                        android:layout_marginVertical="6dp"
                                                        android:layout_marginStart="@dimen/size_10dp"
                                                        android:layout_marginBottom="@dimen/space_x1"
                                                        android:background="@color/neutral_light40" />

                                                    <TextView
                                                        android:id="@+id/tv_title_sender"
                                                        style="@style/Caption1SmallText.Medium.NeutralLight80"
                                                        android:layout_width="match_parent"
                                                        android:layout_height="wrap_content"
                                                        android:layout_marginStart="@dimen/space_half"
                                                        android:layout_toEndOf="@id/iv_sender_expedition"
                                                        android:ellipsize="end"
                                                        android:maxLines="1"
                                                        android:text="@string/empty" />

                                                    <LinearLayout
                                                        android:id="@+id/ll_sender"
                                                        android:layout_width="match_parent"
                                                        android:layout_height="wrap_content"
                                                        android:layout_marginStart="@dimen/space_half"
                                                        android:layout_toEndOf="@id/iv_sender_expedition"
                                                        android:layout_below="@id/tv_title_sender"
                                                        android:orientation="vertical">

                                                        <TextView
                                                            android:id="@+id/tv_sender_name"
                                                            style="@style/Body3SmallText.SemiBold.NeutralDark40"
                                                            android:layout_width="match_parent"
                                                            android:layout_height="wrap_content"
                                                            android:ellipsize="end"
                                                            android:maxLines="1"
                                                            android:text="@string/empty" />

                                                        <TextView
                                                            android:id="@+id/tv_sender_phone"
                                                            style="@style/Body3SmallText.SemiBold.NeutralDark10"
                                                            android:layout_width="match_parent"
                                                            android:layout_height="wrap_content"
                                                            android:ellipsize="end"
                                                            android:maxLines="1"
                                                            android:text="@string/empty" />

                                                        <TextView
                                                            android:id="@+id/tv_sender_address"
                                                            style="@style/Body3SmallText.Medium.NeutralLight80"
                                                            android:layout_width="match_parent"
                                                            android:layout_height="wrap_content"
                                                            android:ellipsize="end"
                                                            android:maxLines="2"
                                                            android:text="@string/empty" />

                                                    </LinearLayout>

                                                    <LinearLayout
                                                        android:layout_width="match_parent"
                                                        android:layout_height="wrap_content"
                                                        android:layout_below="@id/separator_expedition"
                                                        android:layout_alignParentStart="true"
                                                        android:orientation="horizontal">

                                                        <ImageView
                                                            android:id="@+id/iv_recipient_expedition"
                                                            android:layout_width="@dimen/size_21dp"
                                                            android:layout_height="@dimen/size_20dp"
                                                            android:contentDescription="@null"
                                                            android:src="@drawable/ic_receive_package" />

                                                        <LinearLayout
                                                            android:layout_width="match_parent"
                                                            android:layout_height="wrap_content"
                                                            android:layout_marginStart="@dimen/space_half"
                                                            android:orientation="vertical">

                                                            <TextView
                                                                android:id="@+id/tv_title_recipient"
                                                                style="@style/Caption1SmallText.Medium.NeutralLight80"
                                                                android:layout_width="match_parent"
                                                                android:layout_height="wrap_content"
                                                                android:ellipsize="end"
                                                                android:maxLines="1"
                                                                android:text="@string/empty" />

                                                            <TextView
                                                                android:id="@+id/tv_recipient_name_pos"
                                                                style="@style/Body3SmallText.SemiBold.NeutralDark40"
                                                                android:layout_width="match_parent"
                                                                android:layout_height="wrap_content"
                                                                android:ellipsize="end"
                                                                android:maxLines="1"
                                                                android:text="@string/empty" />

                                                            <TextView
                                                                android:id="@+id/tv_recipient_phone"
                                                                style="@style/Body3SmallText.SemiBold.NeutralDark10"
                                                                android:layout_width="match_parent"
                                                                android:layout_height="wrap_content"
                                                                android:ellipsize="end"
                                                                android:maxLines="1"
                                                                android:text="@string/empty" />

                                                            <TextView
                                                                android:id="@+id/tv_recipient_address"
                                                                style="@style/Body3SmallText.Medium.NeutralLight80"
                                                                android:layout_width="match_parent"
                                                                android:layout_height="wrap_content"
                                                                android:ellipsize="end"
                                                                android:maxLines="2"
                                                                android:text="@string/empty" />

                                                        </LinearLayout>

                                                    </LinearLayout>

                                                </RelativeLayout>

                                                <View
                                                    android:layout_width="match_parent"
                                                    android:layout_height="1dp"
                                                    android:layout_marginVertical="@dimen/space_x1"
                                                    android:background="@color/neutral_light20" />

                                                <LinearLayout
                                                    android:layout_width="match_parent"
                                                    android:layout_height="wrap_content"
                                                    android:layout_marginHorizontal="@dimen/space_x1_half"
                                                    android:layout_marginBottom="@dimen/space_x1"
                                                    android:orientation="horizontal">

                                                    <TextView
                                                        android:id="@+id/tv_transport_class"
                                                        style="@style/Caption1SmallText.Medium.NeutralDark40"
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content" />

                                                </LinearLayout>

                                            </LinearLayout>

                                        </RelativeLayout>

                                    </androidx.cardview.widget.CardView>
                                    <!-- end of detail package -->

                                    <View
                                        android:layout_width="match_parent"
                                        android:layout_height="@dimen/size_2dp"
                                        android:background="@color/neutral_light20" />

                                </LinearLayout>

                                <!--detail_data_view-->
                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/rv_data_view_transaction"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/space_x2"
                                    android:overScrollMode="never" />

                                <LinearLayout
                                    android:id="@+id/ll_detail_mobelanja"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:visibility="gone">

                                    <View
                                        android:layout_width="match_parent"
                                        android:layout_height="@dimen/size_2dp"
                                        android:background="@color/neutral_light20" />

                                    <TextView
                                        style="@style/Body3SmallText.SemiBold.NeutralDark40"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/space_x2"
                                        android:text="@string/txt_daftar_pembelian" />

                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/rv_order_detail_mobelanja"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/space_x2"
                                        android:layout_marginBottom="@dimen/space_x1"
                                        android:overScrollMode="never" />

                                </LinearLayout>

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/size_2dp"
                                    android:background="@color/neutral_light20" />

                                <LinearLayout
                                    android:id="@+id/ll_detail_amount_data_view"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:visibility="gone">
                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/rv_detail_amount_data_view"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/space_x2"
                                        android:overScrollMode="never" />

                                    <View
                                        android:layout_width="match_parent"
                                        android:layout_height="@dimen/size_2dp"
                                        android:background="@color/neutral_light20" />
                                </LinearLayout>

                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/rv_amount_data_view"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/space_x2"
                                    android:overScrollMode="never" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/size_2dp"
                                    android:background="@color/neutral_light20" />

                                <TextView
                                    android:id="@+id/tv_footer"
                                    style="@style/Caption1SmallText.Medium.NeutralLight80"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/space_x2"
                                    android:layout_marginBottom="@dimen/space_x1"
                                    android:gravity="center" />

                                <TextView
                                    android:id="@+id/wv_footer"
                                    style="@style/Caption1SmallText.Medium.NeutralLight80"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/space_x2"
                                    android:layout_marginBottom="7dp"
                                    android:clickable="false"
                                    android:longClickable="false"
                                    android:nestedScrollingEnabled="false"
                                    android:scrollbars="none"
                                    android:textColor="@color/neutral_light80"
                                    android:textSize="12sp" />

                                <com.wajahatkarim3.easyflipview.EasyFlipView
                                    android:id="@+id/flipLihatSedikit"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginBottom="@dimen/space_x2"
                                    app:flipDuration="400"
                                    app:flipEnabled="true"
                                    app:flipFrom="front"
                                    app:flipOnTouch="false"
                                    app:flipType="vertical">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content" />

                                    <LinearLayout
                                        android:id="@+id/lihat_sedikit"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"

                                        android:orientation="horizontal"
                                        android:visibility="gone">

                                        <TextView
                                            style="@style/Body2MediumText.Bold.PrimaryBlue80"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_gravity="center"
                                            android:text="@string/lihat_lebih_sedikit_revamp" />

                                        <ImageView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_gravity="center_vertical"
                                            android:src="@drawable/dropdownup" />
                                    </LinearLayout>
                                </com.wajahatkarim3.easyflipview.EasyFlipView>

                            </LinearLayout>
                        </LinearLayout>

                    </LinearLayout>


                </androidx.cardview.widget.CardView>

                <ImageView
                    android:id="@+id/iv_bottom_struk"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_x2"
                    android:layout_below="@id/cv_content"
                    android:layout_marginHorizontal="16.5dp"
                    android:layout_marginTop="-4dp"
                    android:src="@drawable/ic_bottom_struk_revamp" />
                <!--disini-->

                <LinearLayout
                    android:id="@+id/ll_bottom_field"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/iv_bottom_struk"
                    android:visibility="gone"
                    android:orientation="vertical">

                    <View
                        android:id="@+id/separator"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/size_1dp"
                        android:layout_marginVertical="@dimen/_12sdp"
                        android:layout_marginHorizontal="16.5dp"
                        android:background="@color/neutral_light20"/>

                    <!--Smart Transfer-->
                    <RelativeLayout
                        android:id="@+id/rl_smart_transfer_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/space_x2"
                        android:layout_marginBottom="@dimen/space_x3"
                        android:visibility="gone">

                        <RelativeLayout
                            android:id="@+id/rl_smart_transfer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/background_cardview_stroked"
                            android:layout_marginTop="@dimen/space_x1"
                            android:layout_marginStart="@dimen/space_half"
                            android:padding="@dimen/space_x2">

                            <ImageView
                                android:id="@+id/iv_alih_transfer"
                                android:layout_width="@dimen/space_x9"
                                android:layout_height="@dimen/space_x9"
                                android:src="@drawable/ic_alih_transfer"
                                android:layout_alignParentStart="true"
                                android:layout_centerInParent="true"
                                android:contentDescription="alih_transfer" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_toEndOf="@id/iv_alih_transfer"
                                android:layout_toStartOf="@id/iv_arrow_right"
                                android:layout_centerInParent="true"
                                android:layout_marginHorizontal="@dimen/space_x1"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/title_smart_transfer"
                                    style="@style/Body3SmallText.Bold" />

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/txt_desc_alih_transfer"
                                    android:layout_marginTop="@dimen/space_half"
                                    style="@style/Body3SmallText.Medium.NeutralDark10"/>
                            </LinearLayout>

                            <ImageView
                                android:id="@+id/iv_arrow_right"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@drawable/ic_arrow_right_blue"
                                android:layout_alignParentEnd="true"
                                android:layout_centerInParent="true"
                                android:contentDescription="arrow_right" />
                        </RelativeLayout>

                        <ImageView
                            android:layout_width="@dimen/size_35dp"
                            android:layout_height="@dimen/size_20dp"
                            android:src="@drawable/ic_new_menu"
                            android:contentDescription="new_menu" />
                    </RelativeLayout>

                    <!--Smart Recom-->
                    <LinearLayout
                        android:id="@+id/ll_smart_recom"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <id.co.bri.brimo.ui.customviews.textview.TextViewMo
                            android:id="@+id/tv_smart_recommendation"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            app:textTitle="@string/txt_produk_pilihan_untuk"
                            app:textStyle="@style/BodyMediumText.Bold.Black"
                            android:gravity="center_vertical" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_smart_recommendation"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layoutAnimation="@anim/layout_animation_fall_down"
                            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                            android:layout_marginTop="@dimen/space_x2"
                            android:layout_marginHorizontal="16.5dp"
                            android:orientation="horizontal"
                            android:overScrollMode="never" />
                    </LinearLayout>
                </LinearLayout>

                <com.wajahatkarim3.easyflipview.EasyFlipView
                    android:id="@+id/flipFooterReceipt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/ll_bottom_field"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:layout_marginTop="@dimen/space_x5"
                    app:flipDuration="400"
                    app:flipEnabled="true"
                    app:flipFrom="front"
                    app:flipOnTouch="false"
                    app:flipType="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Caption1SmallText.SemiBold.NeutralBaseBlack"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/footer_revamp" />

                        <TextView
                            android:id="@+id/tv_empty_footer"
                            style="@style/Caption1SmallText.Medium.NeutralLight80"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="15dp"
                            android:gravity="center"
                            android:text="@string/footer_revamp_2" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </com.wajahatkarim3.easyflipview.EasyFlipView>

            </RelativeLayout>

        </ScrollView>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <com.wajahatkarim3.easyflipview.EasyFlipView
        android:id="@+id/flipButtonReceipt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        app:flipDuration="400"
        app:flipEnabled="true"
        app:flipFrom="front"
        app:flipOnTouch="false"
        app:flipType="vertical">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="0dp" />

        <LinearLayout
            android:id="@+id/layout_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:orientation="horizontal"
            android:padding="@dimen/space_x2">

            <LinearLayout
                android:id="@+id/btn_share"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/space_x1_half"
                android:background="@drawable/rounded_blue_no_background"
                android:gravity="center">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/ic_menu_share_white_24dp"
                    android:backgroundTint="@color/colorText"
                    app:tint="@color/primaryBlue80" />

                <TextView
                    android:id="@+id/tv_share"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    style="@style/Body2MediumText.Bold.PrimaryBlue80"
                    android:paddingVertical="@dimen/space_x2"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/space_x1"
                    android:text="@string/share" />
            </LinearLayout>

            <View
                android:id="@+id/button_separator"
                android:layout_width="@dimen/space_x1_half"
                android:layout_height="match_parent"/>

            <LinearLayout
                android:id="@+id/btn_close"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/space_x1_half"
                android:background="@drawable/rounded_button_blue"
                android:gravity="center">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_checklist_circle"
                    android:contentDescription="@string/finish" />

                <TextView
                    android:id="@+id/tv_close"
                    style="@style/Body2MediumText.Bold.NeutralBaseWhite"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/space_x1"
                    android:paddingVertical="@dimen/space_x2"
                    android:text="@string/finish" />
            </LinearLayout>
        </LinearLayout>
    </com.wajahatkarim3.easyflipview.EasyFlipView>
</RelativeLayout>