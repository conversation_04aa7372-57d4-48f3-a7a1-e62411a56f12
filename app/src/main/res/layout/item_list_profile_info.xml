<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_item"
    android:orientation="vertical"
    android:paddingTop="@dimen/space_half">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/space_half"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title_info"
                style="@style/Body2MediumText.Bold.NeutralDark40"
                android:textColor="#7B90A6"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/space_half"
                android:paddingStart="@dimen/space_x2"
                android:text="@string/empty"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:elevation="0dp"
                android:layout_marginHorizontal="@dimen/space_x1_half"
                android:outlineAmbientShadowColor="@color/neutral_dark30"
                android:outlineSpotShadowColor="@color/neutral_dark30"
                app:cardCornerRadius="@dimen/space_x1_half"
                app:cardUseCompatPadding="true"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_menu_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>
            </androidx.cardview.widget.CardView>

            <View
                android:layout_width="wrap_content"
                android:layout_height="@dimen/size_6dp"
                android:layout_marginTop="@dimen/space_x1_half"
                android:background="@color/accent3Color"
                android:visibility="visible" />

        </LinearLayout>
    </RelativeLayout>
</LinearLayout>