<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.activities.RiplayActivity">

    <RelativeLayout
        android:id="@+id/rlBackground"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/newskin_background"/>

    <RelativeLayout
        android:id="@+id/rlToolbar"
        android:layout_height="80dp"
        android:layout_width="match_parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/llBack"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="24dp"
            android:layout_marginTop="24dp"
            android:background="@drawable/circle_bg_grey_newskin"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="11dp"
                android:layout_height="11dp"
                android:src="@drawable/ic_arrow_left_revamp" />
        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:fontFamily="@font/bri_digital_text_semi_bold"
            android:text="@string/pedoman_riplay"
            android:layout_marginStart="16dp"
            android:textSize="20sp"
            android:layout_centerHorizontal="true"
            android:textColor="@color/white" />

    </RelativeLayout>



    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/ly_bottom"
        android:background="@drawable/background_cardview_white_newskin"
        android:layout_below="@+id/rlToolbar"
        android:scrollbarAlwaysDrawVerticalTrack="true">

        <ScrollView
            android:id="@+id/scrollview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <WebView
                android:id="@+id/wv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </ScrollView>

        <ImageView
            android:id="@+id/img_skip"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:contentDescription="img_skip"
            android:src="@drawable/icon_circle_arrow"
            android:visibility="gone"
            app:layout_anchor="@id/scrollview"
            app:layout_anchorGravity="bottom|center" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:id="@+id/ly_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:background="@color/white"
        android:paddingHorizontal="@dimen/space_x2"
        android:paddingVertical="@dimen/space_x1_half">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_setuju"
            style="@style/ButtonPrimaryNewSkin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_x1_half"
            android:paddingHorizontal="@dimen/space_x1_half"
            android:paddingVertical="@dimen/space_x1_half"
            android:text="@string/setuju"
            tools:ignore="RelativeOverlap" />

        <TextView
            android:id="@+id/tv_batal"
            style="@style/ButtonPrimaryBorderNewSkin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_x1_half"
            android:gravity="center"
            android:paddingHorizontal="@dimen/space_x1_half"
            android:paddingVertical="@dimen/space_x1_half"
            android:text="@string/batal2"
            tools:ignore="RelativeOverlap" />
    </LinearLayout>
</RelativeLayout>