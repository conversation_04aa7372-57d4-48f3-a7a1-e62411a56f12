<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_new_skin_activity_container"
    android:fitsSystemWindows="true"
    tools:context=".ui.activities.newskinonboarding.OnboardingVerifyEKYCActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_new_skin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="0dp"
        android:layout_marginTop="90dp"
        android:layout_marginEnd="0dp"
        android:background="@drawable/background_cardview_white_newskin"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/space_x2"
            android:orientation="vertical">

            <ImageView
                android:layout_width="@dimen/space_x15"
                android:layout_height="@dimen/space_x15"
                android:layout_gravity="center"
                android:contentDescription="image"
                android:src="@drawable/ic_smile" />

            <TextView
                android:id="@+id/tv_desc_panduan"
                style="@style/BodyText.Large.Regular.BlackNs600"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_x2"
                android:lineSpacingExtra="@dimen/_4sdp"
                android:text="@string/desc_panduan_liveness" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/space_x2"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_checklist_green" />

                <TextView
                    style="@style/BodyText.Large.SemiBold.BlackNsMain"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:text="@string/info_panduan_liveness1" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/ns_graysoft" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/space_x2"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_checklist_green" />

                <TextView
                    style="@style/BodyText.Large.SemiBold.BlackNsMain"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:text="@string/info_panduan_liveness2" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/ns_graysoft" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/space_x2"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_checklist_green" />

                <TextView
                    style="@style/BodyText.Large.SemiBold.BlackNsMain"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:text="@string/info_panduan_liveness3" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/ns_graysoft" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/space_x2"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_checklist_green" />

                <TextView
                    style="@style/BodyText.Large.SemiBold.BlackNsMain"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:text="@string/info_panduan_liveness4" />
            </LinearLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/layoutButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/white"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_1dp"
            android:background="@color/black_ns_200"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_lanjutkan"
            style="@style/ButtonPrimaryNewSkin"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_x7"
            android:layout_margin="@dimen/space_x2"
            android:text="@string/lanjutkan"
            app:backgroundTint="@color/selector_button_primary_bg"
            android:enabled="true" />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>