<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.activities.asuransirevamp.DashboardAsuransiActivity">

    <include android:id="@+id/tb_asuransi"
        layout="@layout/toolbar_revamp"/>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/tb_asuransi"
        android:background="@color/neutralLight10">
<!--        <ScrollView-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content">-->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <View
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_marginTop="-60dp"
                    android:background="@drawable/ic_bg_dashboard_asuransi"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
                <RelativeLayout
                    android:id="@+id/rl_tilte"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/space_x2"
                        android:layout_marginHorizontal="@dimen/space_x2">
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/space_half"
                            android:layout_toStartOf="@id/btn_polish"
                            android:orientation="vertical">
                            <TextView
                                style="@style/Body2MediumText.Bold.NeutralBaseWhite"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/title_asuransi"/>
                            <TextView
                                style="@style/Caption1SmallText.Medium"
                                android:textColor="@color/primary_blue10"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_half"
                                android:text="@string/detail_asuransi"/>
                        </LinearLayout>

                        <androidx.appcompat.widget.AppCompatButton
                            android:id="@+id/btn_polish"
                            android:layout_centerVertical="true"
                            style="@style/Caption1SmallText.Bold.NeutralBaseWhite"
                            android:layout_width="109dp"
                            android:layout_height="32dp"
                            android:layout_alignParentEnd="true"
                            android:background="@drawable/bg_blue80_border_white"
                            android:text="@string/txt_btn_lihat"
                            android:textAllCaps="false" />
                    </RelativeLayout>

                </RelativeLayout>

                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/card_view"
                    android:layout_below="@+id/rl_tilte"
                    android:layout_marginTop="@dimen/space_x2"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:layout_marginBottom="@dimen/space_x1"
                    app:cardCornerRadius="8dp">
                    <RelativeLayout
                        android:id="@+id/rl_bayar_asuransi"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/space_x1_half">
                        <ImageView
                            android:id="@+id/iv_menu_asuransi"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@drawable/ic_bayar_asuransi_revamp"/>
                        <TextView
                            style="@style/Body2MediumText.Bold.NeutralDark40"
                            android:layout_toRightOf="@+id/iv_menu_asuransi"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginVertical="@dimen/space_x1_half"
                            android:layout_marginStart="@dimen/space_x1_half"
                            android:text="@string/judulTambahAsuransi"/>
                        <ImageView
                            android:id="@+id/iv_arrow_right"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginTop="14dp"
                            android:layout_alignParentRight="true"
                            android:src="@drawable/ic_arrow_right_lifestyle"/>
                    </RelativeLayout>

                </androidx.cardview.widget.CardView>
                <RelativeLayout
                    android:id="@+id/rl_asuransi"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/card_view"
                    android:layout_marginTop="@dimen/space_x2"
                    android:layout_marginHorizontal="@dimen/space_x2">
                    <TextView
                        android:id="@+id/tv_title_asuransi"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/Body2MediumText.Bold.NeutralDark40"
                        android:text="@string/txt_title_asuransi_untuk"/>
                    <ImageView
                        android:layout_toRightOf="@+id/tv_title_asuransi"
                        android:layout_width="27dp"
                        android:layout_height="10dp"
                        android:layout_marginTop="6dp"
                        android:layout_marginStart="3dp"
                        android:src="@drawable/ic_mo"/>
                </RelativeLayout>
                <com.ogaclejapan.smarttablayout.SmartTabLayout
                    android:id="@+id/tabCatatanKeuangan"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_x4_half"
                    android:layout_marginHorizontal="10dp"
                    android:layout_marginTop="@dimen/space_x1_half"
                    android:layout_below="@id/rl_asuransi"
                    android:clipToPadding="false"
                    app:cardElevation="@dimen/margin_elevation_card"
                    app:stl_defaultTabBackground="@android:color/transparent"
                    app:stl_defaultTabTextAllCaps="false"
                    app:stl_defaultTabTextSize="14dp"
                    app:stl_distributeEvenly="false"
                    app:stl_dividerColor="@color/transparent"
                    app:stl_dividerThickness="0dp"
                    app:stl_indicatorColor="@color/brand_primary_70"
                    app:stl_indicatorCornerRadius="@dimen/space_x3"
                    app:stl_indicatorGravity="center"
                    app:stl_indicatorInterpolation="linear"
                    app:stl_indicatorThickness="@dimen/space_x4_half"
                    app:stl_titleOffset="auto_center"
                    app:stl_underlineThickness="0dp" />
                <androidx.viewpager.widget.ViewPager
                    android:visibility="visible"
                    android:layout_below="@id/tabCatatanKeuangan"
                    android:id="@+id/vp_asuransi"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/space_x1_half"
                    android:layout_marginHorizontal="@dimen/space_x2"/>

            </RelativeLayout>
<!--        </ScrollView>-->

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</RelativeLayout>