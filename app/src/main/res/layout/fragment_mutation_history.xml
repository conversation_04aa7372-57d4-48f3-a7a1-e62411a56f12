<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_mutation"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- Title -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_13sdp"
                android:layout_marginTop="@dimen/_13sdp"
                android:fontFamily="@font/bri_digital_text_semibold"
                android:text="@string/txt_source_account"
                android:textColor="@color/ns_black900"
                android:textSize="16sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Card View (Account Info) -->
            <androidx.cardview.widget.CardView
                android:id="@+id/view_connect_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginHorizontal="@dimen/_13sdp"
                app:cardCornerRadius="@dimen/_13sdp"
                app:cardElevation="0dp"
                app:cardBackgroundColor="@color/black_ns_100"
                app:layout_constraintTop_toBottomOf="@id/tv_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="@dimen/_14sdp"
                    android:paddingVertical="@dimen/_12sdp">

                    <ImageView
                        android:id="@+id/ivCardLogo"
                        android:layout_width="@dimen/_53sdp"
                        android:layout_height="@dimen/_33sdp"
                        android:layout_alignParentStart="true"
                        android:layout_centerVertical="true"
                        android:scaleType="fitXY"
                        android:src="@drawable/dummy_britama" />

                    <ImageView
                        android:id="@+id/ivArrow"
                        android:layout_width="@dimen/_20sdp"
                        android:layout_height="@dimen/_20sdp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/ic_arrow_down_ns"
                        app:tint="@color/black_ns_main" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_toEndOf="@id/ivCardLogo"
                        android:layout_toStartOf="@id/ivArrow"
                        android:layout_marginStart="12dp"
                        android:layout_marginEnd="4dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvAccountName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/empty"
                            android:textColor="@color/ns_black900"
                            android:textSize="@dimen/_10sdp"
                            style="@style/Body3SmallText.Regular" />

                        <TextView
                            android:id="@+id/tvAccountNumber"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/empty"
                            android:textColor="@color/ns_black900"
                            android:textSize="@dimen/_10sdp"
                            style="@style/Body1LargeText.Regular" />
                    </LinearLayout>

                </RelativeLayout>
            </androidx.cardview.widget.CardView>

            <!-- Filter -->
            <RelativeLayout
                android:id="@+id/view_filter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginHorizontal="@dimen/_13sdp"
                app:layout_constraintTop_toBottomOf="@id/view_connect_card"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <TextView
                    android:id="@+id/tv_title_filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/bri_digital_text_semibold"
                    android:text="@string/all_transaction"
                    android:textColor="@color/ns_black900"
                    android:textSize="16sp"
                    android:layout_centerVertical="true" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_month"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:layout_toStartOf="@id/view_filter_time"
                    android:nestedScrollingEnabled="false"
                    android:visibility="gone"
                    tools:layoutManager="LinearLayoutManager"
                    tools:orientation="horizontal"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_list_recomendation_newskin" />

                <LinearLayout
                    android:id="@+id/view_filter_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:orientation="horizontal">

                    <include
                        android:id="@+id/view_reset_filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        android:layout_marginTop="5dp"
                        android:layout_marginEnd="8dp"
                        layout="@layout/item_select_chip" />

                    <RelativeLayout
                        android:id="@+id/view_filter_icon"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_marginVertical="@dimen/space_half"
                        android:background="@drawable/rounded_dialog_grey_newskin"
                        app:cardCornerRadius="@dimen/space_x1">

                        <ImageView
                            android:id="@+id/iv_filter"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_centerInParent="true"
                            android:src="@drawable/ic_filter_icon_ns" />

                        <ImageView
                            android:id="@+id/iv_bullet"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/iv_filter"
                            android:layout_alignEnd="@id/iv_filter"
                            android:layout_marginTop="-2dp"
                            android:layout_marginEnd="-2dp"
                            android:src="@drawable/ic_bullet_filter_active"
                            android:visibility="gone" />
                    </RelativeLayout>
                </LinearLayout>
            </RelativeLayout>

            <!-- RecyclerView Mutasi List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_item_mutasi"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clipToPadding="false"
                android:paddingBottom="120dp"
                app:layout_constraintTop_toBottomOf="@id/view_filter"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                tools:listitem="@layout/list_bulan_mutasi"
                tools:itemCount="10" />

            <!-- Empty State View -->
            <include
                android:id="@+id/view_empty"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_50sdp"
                android:layout_marginHorizontal="@dimen/_13sdp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/view_filter"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                layout="@layout/view_empty_state_ns" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>


</androidx.constraintlayout.widget.ConstraintLayout>