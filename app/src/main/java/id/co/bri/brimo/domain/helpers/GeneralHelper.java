package id.co.bri.brimo.domain.helpers;

import static android.content.Context.ACTIVITY_SERVICE;
import static android.content.Context.KEYGUARD_SERVICE;
import static id.co.bri.brimo.ui.activities.base.BaseActivity.ANIMATE_GONE;
import static id.co.bri.brimo.ui.activities.base.BaseActivity.ANIMATE_INVISIBLE;
import static id.co.bri.brimo.ui.activities.base.BaseActivity.ANIMATE_SHOW;

import android.accessibilityservice.AccessibilityServiceInfo;
import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.AlertDialog;
import android.app.Application;
import android.app.Dialog;
import android.app.KeyguardManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.CountDownTimer;
import android.text.InputFilter;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.text.style.ImageSpan;
import android.text.style.StyleSpan;
import android.text.style.UnderlineSpan;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.accessibility.AccessibilityManager;
import android.view.inputmethod.InputMethodManager;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestListener;
import com.google.android.material.snackbar.BaseTransientBottomBar;
import com.google.android.material.snackbar.Snackbar;
import com.google.android.material.tabs.TabLayout;
import com.google.gson.Gson;
import com.scottyab.rootbeer.RootBeer;

import java.math.BigInteger;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import id.co.bri.brimo.BuildConfig;
import id.co.bri.brimo.R;
import id.co.bri.brimo.data.preference.BRImoPrefRepository;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.config.FontConfig;
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseSequence;
import id.co.bri.brimo.domain.providers.CardType;
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SavedResponse;
import id.co.bri.brimo.ui.activities.AskActivity;
import id.co.bri.brimo.ui.activities.DashboardIBActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogIlustration;
import id.co.bri.brimo.ui.customviews.dialog.DialogTutorial;
import id.co.bri.brimo.ui.customviews.dialog.DialogWithHtml;
import id.co.bri.brimo.ui.fragments.FragmentBottomDialog;
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment;
import id.co.bri.brimo.ui.fragments.rdn.CustomBottomDialogFragment;
import id.co.bri.brimo.util.extension.DebugType;
import id.co.bri.brimo.util.extension.StringExtKt;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;

public class GeneralHelper {

    private static final String TAG = "GeneralHelper";
    protected static Context helperContext = null;
    protected static Dialog progressDialog;
    protected static SavedResponse savedResponse;

    protected static boolean isAnimatedShow = false;
    protected static boolean isAnimatedGone = false;
    protected static boolean isAnimatedInvisible = false;

    public static void setHelperContext(Context helperContext) {
        GeneralHelper.helperContext = helperContext;
    }

    public static boolean isRootedDevice() {

        if (AppConfig.ROOT_CHECKING) {
            RootBeer rootBeer = new RootBeer(helperContext);

            return rootBeer.detectRootManagementApps() || rootBeer.detectPotentiallyDangerousApps() || rootBeer.checkForBinary("su")
                    || rootBeer.checkForDangerousProps() || rootBeer.checkForRWPaths()
                    || rootBeer.detectTestKeys() || rootBeer.checkSuExists() || rootBeer.checkForRootNative() || rootBeer.checkForMagiskBinary();
        } else {
            return true;
        }
    }

    public static boolean hasSuspiciousAccessibilityServices(Context context) {
        AccessibilityManager am = (AccessibilityManager) context.getSystemService(Context.ACCESSIBILITY_SERVICE);
        PackageManager pm = context.getPackageManager();
        List<AccessibilityServiceInfo> services = am.getEnabledAccessibilityServiceList(AccessibilityServiceInfo.FEEDBACK_ALL_MASK);
        List<String> suspiciousApps = new ArrayList<>();
        String[] allowedServices = new String[]{
                "0365d8ad4cc5326e2c9e93ed514e449e013caf8125637bbb79812bfaef9ef772",
                "c6301cbcb13851b8f6bcb9dfd51c51b6ec893fc4f806d69fed417cc674193b55",
                "9c0e3c8e7c4935ed19081940b0e2e4a728f2bbd2eb2b60c34b32e58a54e8a979",
                "48f96ab723a440c715fa22ccebbbde72f8363abde37074beff64c14c221c5fa4",
                "c7e1539f6bf78d3e9541830723b4e72ad29e45e014c7e934b79f50f88b00bce1"
        };
        MessageDigest digest;
        try {
            digest = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException e) {
            return false;
        }
        for (AccessibilityServiceInfo asi : services) {
            String packageName = asi.getResolveInfo().serviceInfo.packageName;
            try {
                ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
                String installer = pm.getInstallerPackageName(packageName);
                boolean isSideloaded = (installer == null || !GeneralHelper.isContains(Constant.ALLOWED_INSTALLER, installer));
                if (isSideloaded) {
                    byte[] hashBytes = digest.digest(packageName.getBytes(StandardCharsets.UTF_8));
                    String hexString = String.format("%0" + (hashBytes.length * 2) + 'x', new BigInteger(1, hashBytes));
                    if (GeneralHelper.isContains(allowedServices, hexString) && installer == null) continue;
                    String appLabel = pm.getApplicationLabel(appInfo).toString();
                    suspiciousApps.add(appLabel + " (" + packageName + ")");
                }
            } catch (Exception ignored) {}
        }
        return !suspiciousApps.isEmpty();
    }

    public static String formatNominal(String number) {
        String result = "";
        try {
            result = formatNominal(Double.parseDouble(number));
        } catch (Exception e) {
            //do nothing since not a number
        }
        return result;
    }

    public static int dpToPx(Context c, int dp) {
        Resources r = c.getResources();
        return Math.round(TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, r.getDisplayMetrics()));
    }

    //Get phone name
    public static String getPhoneName() {

        String reqString = Build.MANUFACTURER
                + " " + Build.MODEL + " " + Build.VERSION.RELEASE
                + " " + Build.VERSION_CODES.class.getFields()[Build.VERSION.SDK_INT].getName();

        return reqString;
    }

    //method double
    public static double round(double value, int places) {
        if (places < 0) throw new IllegalArgumentException();

        long factor = (long) Math.pow(10, places);
        value = value * factor;
        long tmp = Math.round(value);
        return (double) tmp / factor;
    }

    //Method padding
    public static String padding(String str, String pad, int length, boolean isPaddingLeft) {
        if (str == null) return "";
        while (str.length() < length) {
            if (isPaddingLeft) {
                str = pad + str;
            } else {
                str = str + pad;
            }
        }
        return str;
    }

    //right padding
    public static String rightPad(String str, int length, char pad) {
        StringBuilder strBuilder = new StringBuilder(str.trim());
        for (int i = 0; i <= length - strBuilder.length(); i++) {
            strBuilder.append(pad);
        }
        str = strBuilder.toString();
        return str;
    }


    public static int getStatusBarHeight(Activity activity) {
        Rect rectgle = new Rect();
        Window window = activity.getWindow();
        window.getDecorView().getWindowVisibleDisplayFrame(rectgle);
        int StatusBarHeight = rectgle.top;

        return StatusBarHeight;
    }

    /**
     * method untuk membuat spasi tiap 4 digit di kartu
     *
     * @param cardNumber
     * @return
     */
    public static String formatCard(String cardNumber) {
        if (cardNumber == null) return null;
        char delimiter = ' ';
        return cardNumber.replaceAll(".{4}(?!$)", "$0" + delimiter);
    }

    public static int getActionBarHeight(Activity activity) {
        int actionBarHeight = 0;
        TypedValue tv = new TypedValue();
        if (activity.getTheme().resolveAttribute(android.R.attr.actionBarSize, tv, true)) {
            actionBarHeight = TypedValue.complexToDimensionPixelSize(tv.data, activity.getResources().getDisplayMetrics());
        }

        return actionBarHeight;
    }

    @Nullable
    public static String extractOrderNumberFromUri(String uri) {
        Pattern pattern = Pattern.compile("orderNumber=([A-F0-9]+)");
        Matcher matcher = pattern.matcher(uri);
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return null;
        }
    }

    /**
     * this method used to extract any string from uri/url in webview
     * @param uri
     * @param param
     * @return
     */
    @Nullable
    public static String extractStringFromUri(String uri, String param) {
        Pattern pattern = Pattern.compile(param+"=([A-F0-9]+)");
        Matcher matcher = pattern.matcher(uri);
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return null;
        }
    }

    public static String formatNominal(int number) {
        String result = "";
        try {
            result = formatNominal((double) (number));
        } catch (Exception e) {
            //do nothing since not a number
        }
        return result;
    }


    /**
     * Method untuk mendapatakan id resource image dengan memasukkan string nama image.
     *
     * @param context
     * @param image
     * @return
     */

    public static int getImageId(Context context, String image) {
        try {
            return context.getResources().getIdentifier(image, "drawable", context.getPackageName());
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * Method digunakan untuk menampilkan Icon transaksi.
     *
     * @param context      any context from activity.
     * @param imgUrl       urlInformasi image untuk download dari server
     * @param imgLocalName nama local icon asset yang biasanya diisi dari backend Erangel
     * @param view         imageView yang digunakan untuk meletakkan icon
     * @param defaultIcon  default local icon yang digunakan pada saat menampilkan icon dari imgUrl
     **/
    public static void loadIconTransaction(Context context, String imgUrl, String imgLocalName, ImageView view, int defaultIcon) {
        int localIconSource = 0;

        try {
            if (!imgLocalName.equals("") && imgLocalName != null) {
                String[] iconName = imgLocalName.split(".");
                if (iconName.length > 1) {
                    localIconSource = GeneralHelper.getImageId(context, iconName[0]);
                } else {
                    localIconSource = GeneralHelper.getImageId(context, imgLocalName);
                }
            }

            // jika resource di lokal ada
            if (localIconSource != 0) {
                view.setImageResource(localIconSource);
                view.setPadding(0, 0, 0, 0);
            } else if (!imgUrl.equals("") && localIconSource == 0) {
                //menampilkan image dari url
                if (defaultIcon != 0)
                    loadImageUrl(context, imgUrl, view, defaultIcon, 1);
                else loadImageUrl(context, imgUrl, view, R.drawable.bri, 1);
            } else {
                //menampilkan default icon
//                int padding = SizeHelper.dpToPx(context, 10);
//                view.setPadding(padding,padding,padding,padding);
                if (defaultIcon != 0) {
                    view.setImageResource(defaultIcon);
                } else {
                    view.setImageResource(R.drawable.bri);
                }
                view.setPadding(20, 20, 20, 20);
            }
        } catch (Exception e) {
            //e.printStackTrace();
        }
    }

    /**
     * Method digunakan untuk menampilkan Icon transaksi.
     *
     * @param context      any context from activity.
     * @param imgUrl       urlInformasi image untuk download dari server
     * @param imgLocalName nama local icon asset yang biasanya diisi dari backend Erangel
     * @param view         imageView yang digunakan untuk meletakkan icon
     * @param errorImage  default local icon yang digunakan pada saat menampilkan icon dari imgUrl
     **/
    public static void loadIconTransactionWithPlaceholder(Context context, String imgUrl, String imgLocalName, ImageView view, int errorImage, int paddingImage, int placeholderImage) {
        int localIconSource = 0;

        try {
            if (!imgLocalName.equals("") && imgLocalName != null) {
                String[] iconName = imgLocalName.split(".");
                if (iconName.length > 1) {
                    localIconSource = GeneralHelper.getImageId(context, iconName[0]);
                } else {
                    localIconSource = GeneralHelper.getImageId(context, imgLocalName);
                }
            }

            // jika resource di lokal ada
            if (localIconSource != 0) {
                view.setImageResource(localIconSource);
                view.setPadding(paddingImage, paddingImage, paddingImage, paddingImage);
            } else if (!imgUrl.equals("") && localIconSource == 0) {
                //menampilkan image dari url
                if (errorImage != 0)
                    loadImageUrlWithPlaceholder(context, imgUrl, view, errorImage, paddingImage, placeholderImage);
            } else {
                //menampilkan default icon
                if (errorImage != 0) {
                    view.setImageResource(errorImage);
                } else {
                    view.setImageResource(R.drawable.error_image_circle);
                }
            }
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "loadIconTransactionSavedWithPlaceholder: ", e);
            }
        }
    }

    /**
     * Method digunakan untuk menampilkan image dari URL dengan library Glide
     *
     * @param mContext   context dari activity yg digunakan untuk me load image
     * @param imgUrl   URL image
     * @param view  view untuk menampilkan image
     * @param placeholderImage image placeholder yang di tampilkan ketika sedang load/download image dari url
     * @param paddingImage default image/icon apabila gagal meload image
     * @param errorImage default image/icon apabila gagal meload image
     */

    public static void loadIconTransactionSavedWithPlaceholder(Context mContext, String imgUrl, String imgLocalName, ImageView view, RequestListener listener, int placeholderImage, int paddingImage, int errorImage) {
        int localIconSource = 0;

        try {
            if (!imgLocalName.equals("") && imgLocalName != null) {
                String[] iconName = imgLocalName.split(".");
                if (iconName.length > 1) {
                    localIconSource = GeneralHelper.getImageId(mContext, iconName[0]);
                } else {
                    localIconSource = GeneralHelper.getImageId(mContext, imgLocalName);
                }
            }

            // jika resource di lokal ada
            if (localIconSource != 0) {
                view.setImageResource(localIconSource);
            } else if (!imgUrl.equals("") && localIconSource == 0) {
                //menampilkan image dari urlInformasi
                loadImageUrlWithPlaceholder(mContext, imgUrl, view, errorImage, paddingImage, placeholderImage);
            } else {
            }
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "loadIconTransactionSavedWithPlaceholder: ", e);
            }
        }
    }

    /**
     * Method digunakan untuk menampilkan image dari URL dengan library Glide
     *
     * @param mContext   context dari activity yg digunakan untuk me load image
     * @param imageUrl   URL image
     * @param imageView  view untuk menampilkan image
     * @param paddingImage memberikan padding all ke image yang di set ke view
     * @param placeHolder image placeholder yang di tampilkan ketika sedang load/download image dari url
     * @param errorImage default image/icon apabila gagal meload image
     */
    public static void loadImageUrlWithPlaceholder(Context mContext, String imageUrl, ImageView imageView, int errorImage, int paddingImage, int placeHolder) {

        int padding = SizeHelper.dpToPx(mContext, paddingImage);
        imageView.setPadding(padding, padding, padding, padding);

        Glide.with(mContext)
                .asBitmap()
                .load(imageUrl)
                .placeholder(placeHolder)
                .error(errorImage)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .skipMemoryCache(true)
                .into(imageView);
    }

    /**
     * Method digunakan untuk menampilkan image dari URL dengan library Glide
     *
     * @param mContext   context dari activity yg digunakan untuk me load image
     * @param imageUrl   URL image
     * @param imageView  view untuk menampilkan image
     * @param paddingImage memberikan padding all ke image yang di set ke view
     * @param placeHolder image placeholder yang di tampilkan ketika sedang load/download image dari url
     * @param errorImage default image/icon apabila gagal meload image
     */
    public static void loadImageUrlWithPlaceholderSetSize(Context mContext, String imageUrl, ImageView imageView, int errorImage, int paddingImage, int placeHolder, int width, int height) {

        int padding = SizeHelper.dpToPx(mContext, paddingImage);
        imageView.setPadding(padding, padding, padding, padding);

        Glide.with(mContext)
                .asBitmap()
                .load(imageUrl)
                .placeholder(placeHolder)
                .override(width, height)
                .error(errorImage)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .skipMemoryCache(true)
                .into(imageView);
    }

    // method chuck log
    public static String responseChuck(RestResponse response) {
        String chuck = "";
        Gson gson = new Gson();
        chuck = gson.toJson(response);
        Log.d("Chuck", chuck);
        return chuck;
    }

    /**
     * Method digunakan untuk menampilkan image dari URL dengan library Glide
     *
     * @param mContext   context dari activity yg digunakan untuk me load image
     * @param imageUrl   URL image
     * @param imageView  view untuk menampilkan image
     * @param errorImage default image/icon apabila gagal meload image
     */
    public static void loadImageUrl(Context mContext, String imageUrl, ImageView imageView, int errorImage, int paddingImage) {
        if(mContext == null) return;

        int padding = SizeHelper.dpToPx(mContext, paddingImage);
        imageView.setPadding(padding, padding, padding, padding);

        Glide.with(mContext)
                .asBitmap()
                .load(imageUrl)
                .error(errorImage)
                .into(imageView);
    }


    /**
     * Method digunakan untuk menampilkan Icon transaksi.
     *
     * @param context      any context from activity.
     * @param imgUrl       urlInformasi image untuk download dari server
     * @param imgLocalName nama local icon asset yang biasanya diisi dari backend Erangel
     * @param view         imageView yang digunakan untuk meletakkan icon
     **/
    public static boolean loadIconTransactionSaved(Context context, String imgUrl, String imgLocalName, ImageView view, RequestListener listener) {
        int localIconSource = 0;

        try {
            if (!imgLocalName.equals("") && imgLocalName != null) {
                String[] iconName = imgLocalName.split(".");
                if (iconName.length > 1) {
                    localIconSource = GeneralHelper.getImageId(context, iconName[0]);
                } else {
                    localIconSource = GeneralHelper.getImageId(context, imgLocalName);
                }
            }

            // jika resource di lokal ada
            if (localIconSource != 0) {
                view.setImageResource(localIconSource);
                return true;
            } else if (!imgUrl.equals("") && localIconSource == 0) {
                //menampilkan image dari urlInformasi
                loadImageUrlError(context, imgUrl, view, 5, listener);
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            //e.printStackTrace();
            return false;
        }
    }


    /**
     * Method digunakan untuk menampilkan image dari URL dengan library Glide dan listener ketika error
     *
     * @param mContext  context dari activity yg digunakan untuk me load image
     * @param imageUrl  URL image
     * @param imageView view untuk menampilkan image
     * @param listener  onLoad icon listener
     */
    public static void loadImageUrlError(Context mContext, String imageUrl, ImageView imageView, int paddingImage, RequestListener listener) {
        int padding = SizeHelper.dpToPx(mContext, paddingImage);
        imageView.setPadding(padding, padding, padding, padding);

        Glide.with(mContext)
                .load(imageUrl)
                .listener(listener)
                .into(imageView);

    }


    /**
     * Method untuk mendapatakan id resource COLOUR
     *
     * @param context
     * @param color
     * @return
     */
    public static int getColor(Activity context, int color) {
        int getColor = 0;
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                getColor = ContextCompat.getColor(context, color);
            } else {
                getColor = context.getResources().getColor(color);
            }
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "BRImo getColor: ", e);
            }
        }
        return getColor;
    }

    public static int getColor(int color) {
        return helperContext.getResources().getColor(color);
    }

    public static String getString(Context context, int stringId) {
        return context.getResources().getString(stringId);
    }

    public static String getString(int stringId) {
        if (stringId > 0 && helperContext != null)
            return helperContext.getResources().getString(stringId);
        else
            return "";
    }


    // method untuk generate initial nama
    public static String formatInitialName(String name) {
        String initial = "";
        try {
            if (!name.equals("")) {
                String[] splitName = name.trim().split(" ");

                if (splitName.length > 1) {
                    int lastIndex = splitName.length - 1;
                    String firstInitial = splitName[0].substring(0, 1);
                    String lastInitial = splitName[lastIndex].substring(0, 1);

                    if (!firstInitial.isEmpty() && !lastInitial.isEmpty())
                        initial = String.format("%s%s", firstInitial, lastInitial);
                    else
                        initial = String.format("%s", splitName[0].charAt(0));
                } else {
                    initial = String.format("%s", splitName[0].charAt(0));
                }
            }

            initial = initial.toUpperCase();
        } catch (Exception e) {
            if (!GeneralHelper.isProd())
                Log.e(TAG, "formatInitialName: ", e);
        }

        return initial;
    }

    // method untuk generate initial nama alphanumeric
    public static String formatInitialNameAlphaNumeric(String name) {
        String initial = "";
        try {
            if (!name.equals("")) {
                name = name.replace("*", "");
                String[] splitName = name.trim().split(" ");

                if (splitName.length > 1) {
                    int lastIndex = splitName.length - 1;
                    String firstInitial = splitName[0].substring(0, 1);
                    String lastInitial = splitName[lastIndex].substring(0, 1);

                    if (!firstInitial.isEmpty() && !lastInitial.isEmpty())
                        initial = String.format("%s%s", firstInitial, lastInitial);
                    else
                        initial = String.format("%s", splitName[0].charAt(0));
                } else {
                    initial = String.format("%s", splitName[0].charAt(0));
                }
            }

            initial = initial.toUpperCase();
        } catch (Exception e) {
            if (!GeneralHelper.isProd())
                Log.e(TAG, "formatInitialName: ", e);
        }

        return initial;
    }


    // method untuk convert format nominal rupiah
    public static String formatNominal(double number) {
        String result = "";
        try {
            double amount = number;
            DecimalFormat numberFormatter = new DecimalFormat("#,###.##");

            DecimalFormatSymbols symbols = numberFormatter.getDecimalFormatSymbols();
            symbols.setCurrencySymbol("");
            symbols.setGroupingSeparator('.');
            numberFormatter.setDecimalFormatSymbols(symbols);
            result = numberFormatter.format(amount);
        } catch (Exception e) {
            //do nothing since not a number
        }
        return result;
    }

    public static String formatNominalBiasa(double number) {
        String result = "";
        try {
            double amount = number;
            DecimalFormat numberFormatter = new DecimalFormat("#,###");

            DecimalFormatSymbols symbols = numberFormatter.getDecimalFormatSymbols();
            symbols.setCurrencySymbol("");
            symbols.setGroupingSeparator('.');
            numberFormatter.setDecimalFormatSymbols(symbols);
            result = numberFormatter.format(amount);
        } catch (Exception e) {
            //do nothing since not a number
        }
        return result;
    }

    public static String formatNominalIDR(double number) {
        String result = "";
        try {
            double amount = Math.abs(number);
            DecimalFormat numberFormatter = new DecimalFormat("#,###.##");

            DecimalFormatSymbols symbols = numberFormatter.getDecimalFormatSymbols();
            symbols.setCurrencySymbol("");
            symbols.setGroupingSeparator('.');
            symbols.setDecimalSeparator(',');
            numberFormatter.setDecimalFormatSymbols(symbols);
            numberFormatter.setMinimumFractionDigits(2);
            result = numberFormatter.format(amount);
        } catch (Exception e) {
            //do nothing since not a number
        }
        return result;
    }

    /*v2 is temporary function used because formatNominalIDR wrongly formated on some device*/
    public static String formatNominalIDRv2(double number) {
        String result = "";
        try {
            double amount = Math.abs(number);
            DecimalFormat numberFormatter = new DecimalFormat("#,###.##");

            DecimalFormatSymbols symbols = numberFormatter.getDecimalFormatSymbols();
            symbols.setCurrencySymbol("");
            symbols.setGroupingSeparator('.');
            symbols.setDecimalSeparator(',');
            numberFormatter.setDecimalFormatSymbols(symbols);
            numberFormatter.setMinimumFractionDigits(2);
            numberFormatter.setRoundingMode(RoundingMode.DOWN);
            result = numberFormatter.format(amount);
            if (result.length() > 2)
                result = result.substring(0, result.length() - 3) + ',' + result.substring(result.length() - 2);
        } catch (Exception e) {
            //do nothing since not a number
        }
        return result;
    }

    /**
     * Method untuk menampilkan Saldo beserta Currency-nya
     *
     * @param currency
     * @param number
     * @return
     */
    public static String formatNominalIDR(String currency, double number) {
        String result = "";
        try {
            double amount = Math.abs(number);
            DecimalFormat numberFormatter = new DecimalFormat("#,###.##");

            DecimalFormatSymbols symbols = numberFormatter.getDecimalFormatSymbols();
            symbols.setCurrencySymbol("");
            symbols.setGroupingSeparator('.');
            numberFormatter.setDecimalFormatSymbols(symbols);
            numberFormatter.setMinimumFractionDigits(2);
            result = numberFormatter.format(amount);
        } catch (Exception e) {
            //do nothing since not a number
        }

        if (number < 0) {
            result = String.format("%s%s%s", "-", currency, result);
        } else {
            result = String.format("%s%s", currency, result);
        }

        return result;
    }

    public static String formatNominalIDR(String currency, String number) {
        String result = "";
        double numberDouble = 0.0;
        try {
            numberDouble = Double.parseDouble(clearingAmountSigned(number));
            double amount = Math.abs(numberDouble);
            DecimalFormat numberFormatter = new DecimalFormat("#,###.##");
            DecimalFormatSymbols symbols = numberFormatter.getDecimalFormatSymbols();
            symbols.setCurrencySymbol("");
            symbols.setGroupingSeparator('.');
            symbols.setDecimalSeparator(',');
            numberFormatter.setDecimalFormatSymbols(symbols);
            numberFormatter.setMinimumFractionDigits(2);
            result = numberFormatter.format(amount);
        } catch (Exception e) {
            if (!isProd()) {
                Log.e(TAG, "formatNominalIDR: ", e);
            }
            //do nothing since not a number
        }

        if (numberDouble < 0) {
            result = String.format("%s%s%s", "-", currency, result);
        } else {
            result = String.format("%s%s", currency, result);
        }

        return result;
    }

    public static String formatNominal00(String number) {
        String result = "";
        double numberDouble = 0.0;
        try {
            numberDouble = Double.parseDouble(clearingAmountSigned(number));
            double amount = Math.abs(numberDouble);
            DecimalFormat numberFormatter = new DecimalFormat("#,###.##");

            DecimalFormatSymbols symbols = numberFormatter.getDecimalFormatSymbols();
            symbols.setCurrencySymbol("");
            symbols.setGroupingSeparator('.');
            symbols.setDecimalSeparator(',');
            numberFormatter.setDecimalFormatSymbols(symbols);
            numberFormatter.setMinimumFractionDigits(2);
            result = numberFormatter.format(amount);
        } catch (Exception e) {
            if (!isProd()) {
                Log.e(TAG, "formatNominal00: ", e);
            }
            //do nothing since not a number
        }

        if (numberDouble < 0) {
            result = String.format("%s%s", "-", result);
        } else {
            result = String.format("%s", result);
        }

        return result;
    }

    public static String formatNominalRateIDR(double number) {
        String result = "";
        try {
            double amount = number;
            DecimalFormat numberFormatter = new DecimalFormat("#,###.##");

            DecimalFormatSymbols symbols = numberFormatter.getDecimalFormatSymbols();
            symbols.setCurrencySymbol("");
            symbols.setGroupingSeparator('.');
            symbols.setDecimalSeparator(',');
            numberFormatter.setDecimalFormatSymbols(symbols);
            numberFormatter.setMaximumFractionDigits(4);
            result = numberFormatter.format(amount);
        } catch (Exception e) {
            //do nothing since not a number
        }
        return result;
    }

    public static String formatNominalRateValas(double number) {
        String result = "";
        try {
            double amount = number;
            DecimalFormat numberFormatter = new DecimalFormat("#,###.##");

            DecimalFormatSymbols symbols = numberFormatter.getDecimalFormatSymbols();
            symbols.setCurrencySymbol("");
            symbols.setGroupingSeparator('.');
            symbols.setDecimalSeparator(',');
            numberFormatter.setDecimalFormatSymbols(symbols);
            numberFormatter.setMaximumFractionDigits(4);
            result = numberFormatter.format(amount);
        } catch (Exception e) {
            //do nothing since not a number
        }
        return result;
    }


    public static String clearingAmountSigned(String amount) {
        String result = "";
        try {
            result = amount.replace(".", "").replace("Rp", "").replace(",", ".");
        } catch (Exception e) {

        }

        return result;
    }

    public static String clearingAmount(String amount) {
        String result = "";
        try {
            result = amount.replace(".", "").replace("Rp", "").replace("-", "").replace(",", ".");
        } catch (Exception e) {

        }

        return result;
    }


    public static boolean isContains(String[] strings, String string) {
        return Arrays.asList(strings).contains(string);
    }

    public static boolean isContains(int idArray, String string) {
        String[] strings = helperContext.getResources().getStringArray(idArray);
        return Arrays.asList(strings).contains(string);
    }

    public static String getString(int idArray, int pos) {
        String[] strings = helperContext.getResources().getStringArray(idArray);
        return Arrays.asList(strings).get(pos);
    }

    public static String convertObjectArrayToString(Object[] arr, String delimiter) {
        StringBuilder sb = new StringBuilder();
        for (Object obj : arr)
            sb.append(obj.toString()).append(delimiter);
        return sb.substring(0, sb.length() - 1);

    }

    public static boolean isContains(List<String> strings, String string) {
        boolean result = false;
        for (String s : strings) {
            if (s.equalsIgnoreCase(string)) {
                result = true;
                break;
            }
        }
        return result;
    }

    public static boolean isContains(List<Integer> integers, Integer integer) {
        boolean result = false;
        for (Integer i : integers) {
            if (i.equals(integer)) {
                result = true;
                break;
            }
        }
        return result;
    }

    public static boolean isContains(String strings, String string) {
        boolean result = strings.equalsIgnoreCase(string);
        return result;
    }

    public static void setToolbarRevamp(Activity activity, Toolbar toolbar, String text) {
        TextView textView = (toolbar.findViewById(R.id.textTitle));
        textView.setText(text);
        textView.setSelected(true);
        toolbar.setNavigationIcon(R.drawable.ic_arrow_left_toolbar);
        toolbar.setNavigationOnClickListener(v -> activity.onBackPressed());
    }

    public static void setToolbar(Activity activity, Toolbar toolbar, String text) {
        TextView textView = (toolbar.findViewById(R.id.textTitle));
        textView.setText(text);
        textView.setSelected(true);
        toolbar.setNavigationIcon(R.drawable.ic_arrow_back_white_24dp);
        toolbar.setNavigationOnClickListener(v -> activity.onBackPressed());
    }

    public static void setToolbarNoIconBack(Activity activity, Toolbar toolbar, String text) {
        TextView textView = (toolbar.findViewById(R.id.textTitle));
        textView.setText(text);
    }

    public static void setToolbarDashboard(Activity activity, Toolbar toolbar, String text) {
        TextView textView = (toolbar.findViewById(R.id.textTitle));
        textView.setText(text);
        toolbar.setNavigationIcon(R.drawable.ic_arrow_back_white_24dp);
        toolbar.setNavigationOnClickListener(v -> DashboardIBActivity.launchIntent(activity));
    }


    public static void setToolbarAsk(Activity activity, Toolbar toolbar, String text) {
        TextView textView = (toolbar.findViewById(R.id.textTitle));
        textView.setText(text);
        toolbar.setNavigationIcon(R.drawable.ic_arrow_back_white_24dp);
        toolbar.setNavigationOnClickListener(v -> AskActivity.launchIntent(activity, true));
    }

    public static void setToolbarPin(Activity activity, Toolbar toolbar, String text) {
        TextView textView = (toolbar.findViewById(R.id.textTitle));
        textView.setText(text);
        toolbar.setBackgroundColor(activity.getResources().getColor(R.color.colorBackgroundPin));
        toolbar.setNavigationIcon(R.drawable.ic_arrow_back_white_24dp);
        toolbar.setNavigationOnClickListener(v -> activity.onBackPressed());
    }

    public static void setToolbarWithLogo(Activity activity, Toolbar toolbar) {
        toolbar.setNavigationIcon(R.drawable.ic_arrow_back_white_24dp);
        toolbar.setNavigationOnClickListener(v -> activity.onBackPressed());
    }

    public static void setToolbarWithLogoRevamp(Activity activity, Toolbar toolbar, int imageLogo) {
        ImageView imageToolbar = (toolbar.findViewById(R.id.img_toolbar));
        imageToolbar.setImageResource(imageLogo);
        toolbar.setNavigationIcon(R.drawable.ic_arrow_left_revamp);
        toolbar.setNavigationOnClickListener(v -> activity.onBackPressed());
    }

    /**
     * Method ini digunakan untuk set toolbar dengan button back atau button close
     *
     * @param activity
     * @param toolbar
     * @param text
     * @param backButton
     * @param closeButton
     */
    public static void setToolbarBackClose(Activity activity, Toolbar toolbar, String text, boolean backButton, boolean closeButton) {
        TextView textView = (toolbar.findViewById(R.id.text_toolbar));
        textView.setText(text);
        textView.setSelected(true);

        ImageView imageClose = (toolbar.findViewById(R.id.iv_close_toolbar));

        if (Boolean.TRUE.equals(backButton)) {
            toolbar.setNavigationIcon(R.drawable.ic_arrow_left_revamp);
            toolbar.setNavigationOnClickListener(v -> activity.onBackPressed());
        }

        if (Boolean.TRUE.equals(closeButton)) {
            imageClose.setVisibility(View.VISIBLE);
            imageClose.setOnClickListener(v -> activity.onBackPressed());
        } else {
            imageClose.setVisibility(View.GONE);
        }
    }

    /**
     * This method changes the tab layout font into desired typeface programmatically
     */
    public static void changeTabsFont(Activity activity, TabLayout mTabLayout) {
        final Typeface typeface = Typeface.createFromAsset(activity.getAssets(), FontConfig.AVENIR_BOLD);

        final LinearLayout lyTabs = (LinearLayout) mTabLayout.getChildAt(0);
        for (int j = 0; j < lyTabs.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) lyTabs.getChildAt(j);
            tvTabTitle.setTypeface(typeface, Typeface.NORMAL);
        }
    }

    public static void changeSmartTabsFont(Activity activity, LinearLayout ly, int position) {
        final Typeface typefaceBold = Typeface.createFromAsset(activity.getAssets(), FontConfig.AVENIR_MEDIUM);
        final Typeface typefaceNormal = Typeface.createFromAsset(activity.getAssets(), FontConfig.AVENIR_REGULAR);

        for (int j = 0; j < ly.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) ly.getChildAt(j);
            tvTabTitle.setTypeface(typefaceNormal);
            tvTabTitle.setTextColor(activity.getResources().getColor(R.color.white));
            if (j == position) {
                tvTabTitle.setTextColor(activity.getResources().getColor(R.color.toolbar_blue));
                tvTabTitle.setTypeface(typefaceBold);
            }
        }
    }

    public static void changeTabsFontSimple(Activity activity, LinearLayout ly, int position) {
        final Typeface typefaceBold = Typeface.createFromAsset(activity.getAssets(), FontConfig.AVENIR_MEDIUM);
        final Typeface typefaceReguler = Typeface.createFromAsset(activity.getAssets(), FontConfig.AVENIR_REGULAR);

        for (int j = 0; j < ly.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) ly.getChildAt(j);
            tvTabTitle.setTypeface(typefaceReguler);
            tvTabTitle.setTextColor(activity.getResources().getColor(R.color.black3));
            if (j == position) {
                tvTabTitle.setTypeface(typefaceBold);
                tvTabTitle.setTextColor(activity.getResources().getColor(R.color.colorButtonBlue));
            }
        }
    }

    public static void changeTabsFontSimpleNew(Activity activity, LinearLayout ly, int position) {
        final Typeface typefaceBold = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_DIGITAL_TEXT_BOLD);
        final Typeface typefaceReguler = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_DIGITAL_TEXT_MEDIUM);

        for (int j = 0; j < ly.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) ly.getChildAt(j);
            tvTabTitle.setTypeface(typefaceReguler);
            tvTabTitle.setTextColor(activity.getResources().getColor(R.color.primary_blue40));
            if (j == position) {
                tvTabTitle.setTypeface(typefaceBold);
                tvTabTitle.setTextColor(activity.getResources().getColor(R.color.whiteColor));
            }
        }
    }

    public static void changeTabsFontBold(Activity activity, LinearLayout ly, int position) {
        final Typeface typefaceBold = Typeface.createFromAsset(activity.getAssets(), FontConfig.AVENIR_BOLD);
        final Typeface typefaceReguler = Typeface.createFromAsset(activity.getAssets(), FontConfig.AVENIR_MEDIUM);

        for (int j = 0; j < ly.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) ly.getChildAt(j);
            tvTabTitle.setTypeface(typefaceReguler);
            tvTabTitle.setTextColor(activity.getResources().getColor(R.color.accent2Color));
            if (j == position) {
                tvTabTitle.setTypeface(typefaceBold);
                tvTabTitle.setTextColor(activity.getResources().getColor(R.color.primaryColor));
                tvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
            }
        }
    }

    public static void changeTabsFontBoldRevamp(Activity activity, LinearLayout ly, int position) {
        final Typeface typefaceBold = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_BOLD);
        final Typeface typefaceReguler = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_MEDIUM);

        for (int j = 0; j < ly.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) ly.getChildAt(j);
            tvTabTitle.setTypeface(typefaceReguler);
            tvTabTitle.setTextColor(activity.getResources().getColor(R.color.accent2Color));
            if (j == position) {
                tvTabTitle.setTypeface(typefaceBold);
                tvTabTitle.setTextColor(activity.getResources().getColor(R.color.highlightColor));
                tvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
            }
        }
    }

    public static void changeTabsFontBoldRevampDynamicColorAndFontSizeText(Activity activity, LinearLayout ly, int position, int colorTextDefault, int colorTextSelected, int fontSize) {
        final Typeface typefaceBold = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_DIGITAL_TEXT_BOLD);
        final Typeface typefaceReguler = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_DIGITAL_TEXT_MEDIUM);

        for (int j = 0; j < ly.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) ly.getChildAt(j);
            tvTabTitle.setTypeface(typefaceReguler);
            tvTabTitle.setTextColor(activity.getResources().getColor(colorTextDefault));
            if (j == position) {
                tvTabTitle.setTypeface(typefaceBold);
                tvTabTitle.setTextColor(activity.getResources().getColor(colorTextSelected));
                tvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, fontSize);
            }
        }
    }

    public static void changeTabsFontBoldBackgroundRounded(Activity activity, LinearLayout ly, int position, int colorTextDefault, int colorTextSelected, int bgTabDefault, int bgTabSelected, int colorBgTabDef, int colorBgTabSelect) {
        final Typeface typefaceBold = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_BOLD);
        final Typeface typefaceReguler = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_MEDIUM);

        for (int j = 0; j < ly.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) ly.getChildAt(j);
            LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) tvTabTitle.getLayoutParams();

            if (position == 0) {
                params.setMargins(0, 0, 16, 0);
            } else {
                params.setMargins(8, 0, 8, 0);
            }
            tvTabTitle.setLayoutParams(params);

            tvTabTitle.setTypeface(typefaceReguler);
            tvTabTitle.setTextColor(activity.getResources().getColor(colorTextDefault));
            tvTabTitle.setBackgroundResource(bgTabDefault);
            if (colorBgTabDef != 0) {
                tvTabTitle.getBackground().setColorFilter(GeneralHelper.getColor(colorBgTabDef), PorterDuff.Mode.SRC_ATOP);
            }
            //bg default dan bg selected
            //check variabel color bg jika tidak 0 atau null maka set color dynamic
            if (j == position) {
                tvTabTitle.setTypeface(typefaceBold);
                tvTabTitle.setTextColor(activity.getResources().getColor(colorTextSelected));
                tvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
                tvTabTitle.setBackgroundResource(bgTabSelected);
                if (colorBgTabSelect != 0) {
                    tvTabTitle.getBackground().setColorFilter(GeneralHelper.getColor(colorBgTabSelect), PorterDuff.Mode.SRC_ATOP);
                }
            }
        }
    }

    public static void changeTabsFontBoldForMutation(Activity activity, LinearLayout ly, int position) {
        final Typeface typefaceBold = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_BOLD);
        final Typeface typefaceReguler = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_MEDIUM);

        for (int j = 0; j < ly.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) ly.getChildAt(j);
            tvTabTitle.setTypeface(typefaceReguler);
            tvTabTitle.setTextColor(activity.getResources().getColor(R.color.whiteColor));
            if (j == position) {
                tvTabTitle.setTypeface(typefaceBold);
                tvTabTitle.setTextColor(activity.getResources().getColor(R.color.blue100));
                tvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
            }
        }
    }

    public static void changeTabsFontBoldForRencana(Activity activity, LinearLayout ly, int position) {
        final Typeface typefaceBold = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_BOLD);
        final Typeface typefaceReguler = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_MEDIUM);

        for (int j = 0; j < ly.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) ly.getChildAt(j);
            tvTabTitle.setTypeface(typefaceReguler);
            tvTabTitle.setTextColor(activity.getResources().getColor(R.color.neutral_light40));
            if (j == position) {
                tvTabTitle.setTypeface(typefaceBold);
                tvTabTitle.setTextColor(activity.getResources().getColor(R.color.primary_blue80));
                tvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
            }
        }
    }

    public static void changeTabsFontBoldMedium(Activity activity, LinearLayout ly, int position) {
        final Typeface typefaceBold = Typeface.createFromAsset(activity.getAssets(), FontConfig.AVENIR_BOLD);
        final Typeface typefaceMedium = Typeface.createFromAsset(activity.getAssets(), FontConfig.AVENIR_MEDIUM);

        for (int j = 0; j < ly.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) ly.getChildAt(j);
            tvTabTitle.setTypeface(typefaceMedium);
            tvTabTitle.setTextColor(activity.getResources().getColor(R.color.accent1Color));

            if (j == position) {
                tvTabTitle.setTypeface(typefaceBold);
                tvTabTitle.setTextColor(activity.getResources().getColor(R.color.blue100));
                tvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
            }
        }
    }


    public static void changeTabsFontBoldForAsuransi(Activity activity, LinearLayout ly, int position) {
        final Typeface typefaceBold = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_BOLD);
        final Typeface typefacesemibold = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_SEMI_BOLD);

        for (int j = 0; j < ly.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) ly.getChildAt(j);
            tvTabTitle.setTypeface(typefacesemibold);
            tvTabTitle.setTextColor(activity.getResources().getColor(R.color.primary_blue20));
            if (j == position) {
                tvTabTitle.setTypeface(typefaceBold);
                tvTabTitle.setTextColor(activity.getResources().getColor(R.color.white));
                tvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
            }
        }
    }

    public static void changeTabsFontDashboardAsuransiRevamp(Activity activity, LinearLayout ly, int position) {
        final Typeface typefaceBold = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_DIGITAL_TEXT_MEDIUM);
        final Typeface typefaceReguler = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_DIGITAL_TEXT_MEDIUM);

        for (int j = 0; j < ly.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) ly.getChildAt(j);
            tvTabTitle.setTypeface(typefaceReguler);
            tvTabTitle.setTextColor(activity.getResources().getColor(R.color.neutral_dark10));
            if (j == position) {
                tvTabTitle.setTypeface(typefaceBold);
                tvTabTitle.setTextColor(activity.getResources().getColor(R.color.primaryColor));
                tvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
            }
        }
    }

    public static void changeTabsFontDashboardDplkRevamp(Activity activity, LinearLayout ly, int position) {
        final Typeface typefaceBold = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_DIGITAL_TEXT_BOLD);
        final Typeface typefaceReguler = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_DIGITAL_TEXT_MEDIUM);

        for (int j = 0; j < ly.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) ly.getChildAt(j);
            tvTabTitle.setTypeface(typefaceReguler);
            tvTabTitle.setTextColor(activity.getResources().getColor(R.color.primary_blue40));
            tvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
            if (j == position) {
                tvTabTitle.setTypeface(typefaceBold);
                tvTabTitle.setTextColor(activity.getResources().getColor(R.color.white));
                tvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
            }
        }
    }

    public static void changeTabsFontDashboardInvestasi(Activity activity, LinearLayout ly, int position) {
        final Typeface typefaceBold = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_DIGITAL_TEXT_MEDIUM);
        final Typeface typefaceReguler = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_DIGITAL_TEXT_MEDIUM);

        for (int j = 0; j < ly.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) ly.getChildAt(j);
            tvTabTitle.setTypeface(typefaceReguler);
            tvTabTitle.setTextColor(activity.getResources().getColor(R.color.primary_blue40));
            if (j == position) {
                tvTabTitle.setTypeface(typefaceBold);
                tvTabTitle.setTextColor(activity.getResources().getColor(R.color.neutral_baseWhite));
                tvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
            }
        }
    }

    public static void showToast(Context context, String message) {
        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
    }

    /*
     * ========== Method untuk mendapatkan setting environment ==========
     */
    public static boolean isProd() {
        return BuildConfig.FLAVOR.equalsIgnoreCase("production");
    }

    /*
     *  Method untuk generate md5
     *
     * */

    public static String md5(final String s) {
        final String MD5 = "MD5";
        try {
            // Create MD5 Hash
            MessageDigest dijus = MessageDigest
                    .getInstance(MD5);
            dijus.update(s.getBytes());
            byte[] messageDigest = dijus.digest();

            // Create Hex String
            StringBuilder hexString = new StringBuilder();
            for (byte aMessageDigest : messageDigest) {
                String h = Integer.toHexString(0xFF & aMessageDigest);
                while (h.length() < 2)
                    h = "0" + h;
                hexString.append(h);
            }
            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {

        }
        return "";
    }


    // Get Version
    public static String getVersion(Context ctx) {
        PackageInfo pInfo;
        String version;
        try {
            pInfo = ctx.getPackageManager().getPackageInfo(ctx.getPackageName(), 0);
            version = pInfo.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            version = "0";
        }
        return version;
    }

    public static void showSnackBar(View viewParent, String message) {
        if (message == null) {
            return; // Ensure that message is not null
        }
        SpannableStringBuilder builder = new SpannableStringBuilder();
        builder.append(" ");
        builder.setSpan(new ImageSpan(helperContext, R.drawable.ic_cancel_danger_18dp), builder.length() - 1, builder.length(), 0);
        builder.append(" ").append(message);

        Snackbar snackbar = Snackbar.make(viewParent, builder, Snackbar.LENGTH_LONG)
                .setActionTextColor(Color.WHITE)
                // .setTextMaxLines(5)
                .setAnimationMode(BaseTransientBottomBar.ANIMATION_MODE_FADE)
                .setBackgroundTint(GeneralHelper.getColor(R.color.error80));

        View view = snackbar.getView();
        view.setPadding(8, 8, 8, 8);

        CoordinatorLayout.LayoutParams params = (CoordinatorLayout.LayoutParams) view.getLayoutParams();
        params.gravity = Gravity.TOP | Gravity.CENTER_HORIZONTAL;
        params.setMargins(20, 20, 20, 20);
        params.width = CoordinatorLayout.LayoutParams.MATCH_PARENT;
        view.setLayoutParams(params);
        snackbar.show();
    }

    public static void showSnackBarWithTransparentStatusBar(View viewParent, String message, int color, int resourceId) {
        if (message == null) {
            return; // Ensure that message is not null
        }
        SpannableStringBuilder builder = new SpannableStringBuilder();
        builder.append(" ");
        builder.setSpan(new ImageSpan(helperContext, resourceId), builder.length() - 1, builder.length(), 0);
        builder.append(" ").append(message);

        Snackbar snackbar = Snackbar.make(viewParent, builder, Snackbar.LENGTH_LONG)
                .setActionTextColor(Color.WHITE)
                // .setTextMaxLines(5)
                .setAnimationMode(BaseTransientBottomBar.ANIMATION_MODE_FADE)
                .setBackgroundTint(GeneralHelper.getColor(color));

        View view = snackbar.getView();
        view.setPadding(8, 8, 8, 8);

        CoordinatorLayout.LayoutParams params = (CoordinatorLayout.LayoutParams) view.getLayoutParams();
        params.gravity = Gravity.TOP | Gravity.CENTER_HORIZONTAL;
        params.setMargins(20, 220, 20, 20);
        params.width = CoordinatorLayout.LayoutParams.MATCH_PARENT;
        view.setLayoutParams(params);
        snackbar.show();
    }

    public static void showSnackBarGreen(View viewParent, String message) {
        if (message == null) {
            return; // Ensure that message is not null
        }
        SpannableStringBuilder builder = new SpannableStringBuilder();
        builder.append(" ");
        builder.setSpan(new ImageSpan(helperContext, R.drawable.ic_check_circle), builder.length() - 1, builder.length(), 0);
        builder.append(" ").append(message);

        Snackbar snackbar = Snackbar.make(viewParent, builder, Snackbar.LENGTH_LONG)
                .setActionTextColor(Color.WHITE)
                //.setTextMaxLines(5)
                .setAnimationMode(BaseTransientBottomBar.ANIMATION_MODE_FADE)
                .setBackgroundTint(GeneralHelper.getColor(R.color.success80));


        View view = snackbar.getView();
        view.setPadding(8, 8, 8, 8);

        CoordinatorLayout.LayoutParams params = (CoordinatorLayout.LayoutParams) view.getLayoutParams();
        params.gravity = Gravity.TOP | Gravity.CENTER_HORIZONTAL;
        params.setMargins(20, 20, 20, 20);
        params.width = CoordinatorLayout.LayoutParams.MATCH_PARENT;
        view.setLayoutParams(params);
        snackbar.show();
    }

    /*this method uses material 2*/
    public static void showSnackBarRevamp(View viewParent, String message) {
        SpannableStringBuilder builder = new SpannableStringBuilder();
        builder.append(" ");
        builder.setSpan(new ImageSpan(helperContext, R.drawable.ic_cancel_danger_18dp), builder.length() - 1, builder.length(), 0);
        builder.append(" ").append(message);

        Snackbar snackbar = Snackbar.make(viewParent, builder, Snackbar.LENGTH_LONG)
                .setActionTextColor(Color.WHITE)
                //.setTextMaxLines(5)
                .setAnimationMode(BaseTransientBottomBar.ANIMATION_MODE_FADE)
                .setBackgroundTint(GeneralHelper.getColor(R.color.error80));

        View view = snackbar.getView();

        CoordinatorLayout.LayoutParams params = (CoordinatorLayout.LayoutParams) view.getLayoutParams();
        params.gravity = Gravity.TOP | Gravity.CENTER_HORIZONTAL;
        params.setMargins(20, 20, 20, 20);
        params.width = CoordinatorLayout.LayoutParams.MATCH_PARENT;
        view.setLayoutParams(params);
        snackbar.show();
    }

    public static void showSnackBarGreenRevamp(View viewParent, String message) {
        SpannableStringBuilder builder = new SpannableStringBuilder();
        builder.append(" ");
        builder.setSpan(new ImageSpan(helperContext, R.drawable.ic_check_circle), builder.length() - 1, builder.length(), 0);
        builder.append(" ").append(message);

        Snackbar snackbar = Snackbar.make(viewParent, builder, Snackbar.LENGTH_LONG)
                .setActionTextColor(Color.WHITE)
                //.setTextMaxLines(5)
                .setAnimationMode(BaseTransientBottomBar.ANIMATION_MODE_FADE)
                .setBackgroundTint(GeneralHelper.getColor(R.color.success80));

        View view = snackbar.getView();

        CoordinatorLayout.LayoutParams params = (CoordinatorLayout.LayoutParams) view.getLayoutParams();
        params.gravity = Gravity.TOP | Gravity.CENTER_HORIZONTAL;
        params.setMargins(20, 20, 20, 20);
        params.width = CoordinatorLayout.LayoutParams.MATCH_PARENT;
        view.setLayoutParams(params);
        snackbar.show();
    }

    public static void showSnackBarBlueRevamp(View viewParent, String message) {
        SpannableStringBuilder builder = new SpannableStringBuilder();

        builder.append(" ");

        Drawable drawable = ContextCompat.getDrawable(helperContext, R.drawable.ic_info_bg_blue);
        if (drawable != null) {
            drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
            ImageSpan imageSpan = new ImageSpan(drawable);
            builder.setSpan(imageSpan, builder.length() - 1, builder.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }

        builder.append(" ").append(message);
        ForegroundColorSpan blackSpan = new ForegroundColorSpan(GeneralHelper.getColor(R.color.neutral_dark20));
        builder.setSpan(blackSpan, builder.length() - message.length(), builder.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        Snackbar snackbar = Snackbar.make(viewParent, builder, Snackbar.LENGTH_LONG)
                .setActionTextColor(Color.BLACK)
                .setAnimationMode(BaseTransientBottomBar.ANIMATION_MODE_FADE)
                .setBackgroundTint(GeneralHelper.getColor(R.color.primary_blue10));

        View view = snackbar.getView();

        CoordinatorLayout.LayoutParams params = (CoordinatorLayout.LayoutParams) view.getLayoutParams();
        params.gravity = Gravity.TOP | Gravity.CENTER_HORIZONTAL;
        params.setMargins(20, 20, 20, 20);
        params.width = CoordinatorLayout.LayoutParams.MATCH_PARENT;
        view.setLayoutParams(params);
        snackbar.show();
    }

    public static void showSnackBarRevampLines(View viewParent, String message, Integer line) {
        SpannableStringBuilder builder = new SpannableStringBuilder();
        builder.append(" ");
        builder.setSpan(new ImageSpan(helperContext, R.drawable.ic_cancel_danger_18dp), builder.length() - 1, builder.length(), 0);
        builder.append(" ").append(message);

        Snackbar snackbar = Snackbar.make(viewParent, builder, Snackbar.LENGTH_LONG)
                .setActionTextColor(Color.WHITE)
                .setTextMaxLines(line)
                .setAnimationMode(BaseTransientBottomBar.ANIMATION_MODE_FADE)
                .setBackgroundTint(GeneralHelper.getColor(R.color.error80));

        View view = snackbar.getView();

        CoordinatorLayout.LayoutParams params = (CoordinatorLayout.LayoutParams) view.getLayoutParams();
        params.gravity = Gravity.TOP | Gravity.CENTER_HORIZONTAL;
        params.setMargins(20, 20, 20, 20);
        params.width = CoordinatorLayout.LayoutParams.MATCH_PARENT;
        view.setLayoutParams(params);
        snackbar.show();
    }

    /*this method uses material 2*/

    public static Boolean checkBiometricSupport() {

        KeyguardManager keyguardManager =
                (KeyguardManager) helperContext.getSystemService(KEYGUARD_SERVICE);

        PackageManager packageManager = helperContext.getPackageManager();

        if (!keyguardManager.isKeyguardSecure()) {
//            GeneralHelper.showToast(context, "Lock screen security not enabled in Settings");
            return false;
        }

        /*
         * cek jika device support biometric dengan face recognition (camera bukan sensor) force off
         * face recognition (camera) dan turn on fingerprint, jika face recognition di device menggunakan sensor
         * maka face recognition dan fingerprint aktif.
         * jika device hanya support biometric face recognition dengan sensor, maka face recognition turn on.
         */
        if (packageManager.hasSystemFeature(PackageManager.FEATURE_FINGERPRINT) &&
                packageManager.hasSystemFeature(PackageManager.FEATURE_FACE)) {
            if (packageManager.hasSystemFeature(PackageManager.FEATURE_FINGERPRINT)) {
                return true;
            } else {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    if (packageManager.hasSystemFeature(PackageManager.FEATURE_FACE)) {
                        return false;
                    }
                }
            }
            return true;
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            if (packageManager.hasSystemFeature(PackageManager.FEATURE_FACE)) {
                return true;
            }
        }

        if (packageManager.hasSystemFeature(PackageManager.FEATURE_FINGERPRINT) &&
                packageManager.hasSystemFeature(PackageManager.FEATURE_FACE)) {
            if (packageManager.hasSystemFeature(PackageManager.FEATURE_FINGERPRINT)) {
                return true;
            } else {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    if (packageManager.hasSystemFeature(PackageManager.FEATURE_FACE)) {
                        return false;
                    }
                }
            }
            return true;
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            if (packageManager.hasSystemFeature(PackageManager.FEATURE_FACE)) {
                return true;
            }
        }

        return true;
    }


    public static String bytesToHex(byte[] hash) {
        StringBuffer hexString = new StringBuffer();
        for (int i = 0; i < hash.length; i++) {
            String hex = Integer.toHexString(0xff & hash[i]);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }

    public static int[] getTimeFormat(int seconds) {

        int hours = seconds / 3600;
        int minutes = (seconds % 3600) / 60;
        seconds = seconds % 60;

        return new int[]{hours, minutes, seconds};
    }

    public static String getDurationFormat(int seconds) {
        long days = TimeUnit.SECONDS.toDays(seconds);
        long hours = TimeUnit.SECONDS.toHours(seconds) % 24;
        long minutes = TimeUnit.SECONDS.toMinutes(seconds) % 60;
        long secs = seconds % 60;

        if (days > 0) {
            return String.format("%d:%02d:%02d:%02d", days, hours, minutes, secs);
        } else if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes, secs);
        } else {
            return String.format("%02d:%02d", minutes, secs);
        }
    }

    public static void showDialog(Context context) {
        if(context != null && !((Activity) context).isFinishing() && !((Activity) context).isDestroyed()) {
            progressDialog = new Dialog(context);
            progressDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            progressDialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
            progressDialog.setContentView(R.layout.loading_dot);
            progressDialog.setCancelable(false);
            progressDialog.show();
        }
    }

    public static void dismissDialog() {
        if (progressDialog != null)
            progressDialog.dismiss();
    }

    public static int longToInt(long theLongOne) {
        int value = (int) theLongOne;
        return value;
    }

    public static void showAlert(Context ctx, String msg) {
        AlertDialog.Builder alert = new AlertDialog.Builder(ctx);
        alert.setMessage(msg).setCancelable(true).setPositiveButton("OK", (dialog, which) -> dialog.dismiss()).show();
    }

    public static void showDialogTutorial(FragmentActivity activity, String title, String detail, BubbleShowCaseSequence bubbleShowCaseSequence, DialogTutorial.DialogTutorialCallback dialogTutorialCallback) {
        if (!activity.isFinishing()) {
            DialogTutorial dialog = new DialogTutorial(title, detail, dialogTutorialCallback);
            dialog.sendBubbleShowSequences(bubbleShowCaseSequence);
            FragmentTransaction ft = activity.getSupportFragmentManager().beginTransaction();
            ft.add(dialog, null);
            ft.commitAllowingStateLoss();
        }
    }

    public static void showDialogTutorialHTML(FragmentActivity activity, String title, int detail, BubbleShowCaseSequence bubbleShowCaseSequence) {
        if (!activity.isFinishing()) {
            DialogWithHtml dialog = new DialogWithHtml(title, detail);
            dialog.sendBubbleShowSequences(bubbleShowCaseSequence);
            FragmentTransaction ft = activity.getSupportFragmentManager().beginTransaction();
            ft.add(dialog, null);
            ft.commitAllowingStateLoss();
        }
    }

    //===== Method untuk mengambil nama activity yang sedang aktif
    @RequiresApi(api = Build.VERSION_CODES.Q)
    public static String getLastActivity(Context mContext) {
        ActivityManager am = (ActivityManager) mContext.getSystemService(ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> taskInfo = am.getRunningTasks(1);
        ComponentName componentInfo = taskInfo.get(0).topActivity;
        String fullActivityName = componentInfo.getClassName();
        return fullActivityName.substring(fullActivityName.lastIndexOf('.') + 1);
    }

    //===== Method untuk mengambil nama activity yang sedang aktif
    public static String getCurrentActivity(Context mContext) {
        String nameResult = "";
        String name = mContext.getClass().getName();
        String[] splitName = name.split(".");
        if (splitName.length > 0) {
            nameResult = splitName[splitName.length + 1];
        }
        return nameResult;
    }

    public static String getCurrentFlavor(){
        return BuildConfig.FLAVOR;
    }

    public static void showBottomDialog(FragmentActivity activity, String type) {
        String title = "", desc = "", imageName = "";
        switch (type) {
            case Constant.COMING_SOON:
                title = GeneralHelper.getString(R.string.title_coming_soon);
                desc = GeneralHelper.getString(R.string.desc_coming_soon);
                imageName = Constant.IMAGE_COMING_SOON;
                break;
            case Constant.KONEKSI_TERPUTUS:
                title =  GeneralHelper.getString(R.string.title_koneksi_terputus);
                desc =  GeneralHelper.getString(R.string.desc_koneksi_terputus);
                imageName = Constant.IMAGE_KONEKSI_TERPUTUS;
                break;
            case Constant.SERVER_UNDER_MAINTENANCE:
                title = GeneralHelper.getString(R.string.title_server_under_maintenance);
                desc = GeneralHelper.getString(R.string.desc_server_under_maintenance);
                imageName = Constant.IMAGE_SERVER_UNDER_MAINTENANCE;
                break;
            case Constant.TRANSAKSI_GAGAL:
                title = GeneralHelper.getString(R.string.title_transaki_gagal);
                desc =  GeneralHelper.getString(R.string.desc_transaki_gagal);
                imageName = Constant.IMAGE_TRANSAKSI_GAGAL;
                break;
            case Constant.NFC_GAGAL:
                title = GeneralHelper.getString(R.string.title_nfc_gagal);;
                desc = GeneralHelper.getString(R.string.desc_nfc_gagal);;
                imageName = Constant.IMAGE_NFC_GAGAL;
                break;
            case Constant.AKUN_TERBLOKIR:
                title =  GeneralHelper.getString(R.string.title_koneksi_terputus);
                desc =  GeneralHelper.getString(R.string.desc_koneksi_terputus);
                imageName = Constant.IMAGE_KONEKSI_TERPUTUS;
                break;
            default:
                title = GeneralHelper.getString(R.string.title_transaki_gagal);
                desc = GeneralHelper.getString(R.string.desc_transaki_gagal);
                imageName = Constant.IMAGE_TRANSAKSI_GAGAL;
                break;
        }
        try {
            FragmentBottomDialog fragmentBottomDialog = new FragmentBottomDialog(type, title, desc, imageName);
            fragmentBottomDialog.show(activity.getSupportFragmentManager(), "");
        } catch (Exception e) {
            if (!isProd()) {
                Log.e(TAG, "showBottomDialog: ", e);
            }
        }
    }

    public static void showDialogGagalBack(FragmentActivity activity, String type) {
        String title = "", desc = "", imageName = "";
        switch (type) {
            case Constant.COMING_SOON:
                title = GeneralHelper.getString(R.string.title_coming_soon);
                desc = GeneralHelper.getString(R.string.desc_coming_soon);
                imageName = Constant.IMAGE_COMING_SOON;
                break;
            case Constant.KONEKSI_TERPUTUS:
                title =  GeneralHelper.getString(R.string.title_koneksi_terputus);
                desc =  GeneralHelper.getString(R.string.desc_koneksi_terputus);
                imageName = Constant.IMAGE_KONEKSI_TERPUTUS;
                break;
            case Constant.SERVER_UNDER_MAINTENANCE:
                title = GeneralHelper.getString(R.string.title_server_under_maintenance);
                desc = GeneralHelper.getString(R.string.desc_server_under_maintenance);
                imageName = Constant.IMAGE_SERVER_UNDER_MAINTENANCE;
                break;
            case Constant.TRANSAKSI_GAGAL:
                title = GeneralHelper.getString(R.string.title_transaki_gagal);
                desc = GeneralHelper.getString(R.string.desc_transaki_gagal);
                imageName = Constant.IMAGE_TRANSAKSI_GAGAL;
                break;
            case Constant.NFC_GAGAL:
                title = GeneralHelper.getString(R.string.title_nfc_gagal);;
                desc = GeneralHelper.getString(R.string.desc_nfc_gagal);;
                imageName = Constant.IMAGE_NFC_GAGAL;
                break;
            default:
                title = GeneralHelper.getString(R.string.title_transaki_gagal);
                desc = GeneralHelper.getString(R.string.desc_transaki_gagal);
                imageName = Constant.IMAGE_TRANSAKSI_GAGAL;
                break;
        }

        if (activity != null && !activity.isFinishing() && !activity.isDestroyed()) {
            try {
                FragmentBottomDialog fragmentBottomDialog = new FragmentBottomDialog(activity, type, title, desc, imageName, true);
                if (!activity.getSupportFragmentManager().isStateSaved()) {
                    fragmentBottomDialog.show(activity.getSupportFragmentManager(), "FragmentBottomDialogTag");
                }
            } catch (IllegalStateException e) {

            }
        }
    }

    public static void showDialogGagalBackFastMenuRevamp(FragmentActivity activity, String type, boolean fromPinScreen) {
        String title = "", desc = "", imageName = "";
        switch (type) {
            case Constant.COMING_SOON:
                title = GeneralHelper.getString(R.string.title_coming_soon);
                desc = GeneralHelper.getString(R.string.desc_coming_soon);
                imageName = Constant.IMAGE_COMING_SOON;
                break;
            case Constant.KONEKSI_TERPUTUS:
                title =  GeneralHelper.getString(R.string.title_koneksi_terputus);
                desc =  GeneralHelper.getString(R.string.desc_koneksi_terputus);
                imageName = Constant.IMAGE_KONEKSI_TERPUTUS;
                break;
            case Constant.SERVER_UNDER_MAINTENANCE:
                title = GeneralHelper.getString(R.string.title_server_under_maintenance);
                desc = GeneralHelper.getString(R.string.desc_server_under_maintenance);
                imageName = Constant.IMAGE_SERVER_UNDER_MAINTENANCE;
                break;
            case Constant.TRANSAKSI_GAGAL:
                title = GeneralHelper.getString(R.string.title_transaki_gagal);
                desc = GeneralHelper.getString(R.string.desc_transaki_gagal);
                imageName = Constant.IMAGE_TRANSAKSI_GAGAL;
                break;
            case Constant.NFC_GAGAL:
                title = GeneralHelper.getString(R.string.title_nfc_gagal);;
                desc = GeneralHelper.getString(R.string.desc_nfc_gagal);;
                imageName = Constant.IMAGE_NFC_GAGAL;
                break;
            default:
                title = GeneralHelper.getString(R.string.title_transaki_gagal);
                desc = GeneralHelper.getString(R.string.desc_transaki_gagal);
                imageName = Constant.IMAGE_TRANSAKSI_GAGAL;
                break;
        }

        boolean isBack = !fromPinScreen;
        FragmentBottomDialog fragmentBottomDialog = new FragmentBottomDialog(activity, type, title, desc, imageName, isBack);
        fragmentBottomDialog.show(activity.getSupportFragmentManager(), "");
    }

    public static void showDialogGagalBackDescBerubah(FragmentActivity activity, String type, String description) {
        String title = "", desc = "", imageName = "";
        switch (type) {
            case Constant.COMING_SOON:
                title = GeneralHelper.getString(R.string.title_coming_soon);
                desc = GeneralHelper.getString(R.string.desc_coming_soon);
                imageName = Constant.IMAGE_COMING_SOON;
                break;
            case Constant.KONEKSI_TERPUTUS:
                title =  GeneralHelper.getString(R.string.title_koneksi_terputus);
                desc =  GeneralHelper.getString(R.string.desc_koneksi_terputus);
                imageName = Constant.IMAGE_KONEKSI_TERPUTUS;
                break;
            case Constant.SERVER_UNDER_MAINTENANCE:
                title = GeneralHelper.getString(R.string.title_server_under_maintenance);
                desc = GeneralHelper.getString(R.string.desc_server_under_maintenance);
                imageName = Constant.IMAGE_SERVER_UNDER_MAINTENANCE;
                break;
            case Constant.TRANSAKSI_GAGAL:
                title = GeneralHelper.getString(R.string.title_transaki_gagal);
                desc = description;
                imageName = Constant.IMAGE_TRANSAKSI_GAGAL;
                break;
            case Constant.NFC_GAGAL:
                title = GeneralHelper.getString(R.string.title_nfc_gagal);;
                desc = GeneralHelper.getString(R.string.desc_nfc_gagal);;
                imageName = Constant.IMAGE_NFC_GAGAL;
                break;
            case Constant.API_LIMIT:
                title = Constant.TITLE_API_LIMIT;
                desc = description;
                imageName = Constant.IMAGE_API_LIMIT;
        }
        FragmentBottomDialog fragmentBottomDialog = new FragmentBottomDialog(activity, type, title, desc, imageName, true);
//        fragmentBottomDialog.setCancelable(false);
        fragmentBottomDialog.show(activity.getSupportFragmentManager(), "");
    }

    public static void showDialogGagalBackDescBerubahRevamp(FragmentActivity activity, String type, String description, Boolean mBack, Boolean mBackto) {
        String title = "", desc = "", imageName = "";
        switch (type) {
            case Constant.COMING_SOON:
                title = GeneralHelper.getString(R.string.title_coming_soon);
                desc = GeneralHelper.getString(R.string.desc_coming_soon);
                imageName = Constant.IMAGE_COMING_SOON;
                break;
            case Constant.KONEKSI_TERPUTUS:
                title =  GeneralHelper.getString(R.string.title_koneksi_terputus);
                desc =  GeneralHelper.getString(R.string.desc_koneksi_terputus);
                imageName = Constant.IMAGE_KONEKSI_TERPUTUS;
                break;
            case Constant.SERVER_UNDER_MAINTENANCE:
                title = GeneralHelper.getString(R.string.title_server_under_maintenance);
                desc = GeneralHelper.getString(R.string.desc_server_under_maintenance);
                imageName = Constant.IMAGE_SERVER_UNDER_MAINTENANCE;
                break;
            case Constant.TRANSAKSI_GAGAL:
                title = GeneralHelper.getString(R.string.title_transaki_gagal);
                desc = description;
                imageName = Constant.IMAGE_TRANSAKSI_GAGAL;
                break;
            case Constant.NFC_GAGAL:
                title = GeneralHelper.getString(R.string.title_nfc_gagal);;
                desc = GeneralHelper.getString(R.string.desc_nfc_gagal);;
                imageName = Constant.IMAGE_NFC_GAGAL;
                break;
            case Constant.API_LIMIT:
                title = Constant.TITLE_API_LIMIT;
                desc = description;
                imageName = Constant.IMAGE_API_LIMIT;
        }
        FragmentBottomDialog fragmentBottomDialog = new FragmentBottomDialog(activity, type, title, desc, imageName, mBack, mBackto);
        fragmentBottomDialog.show(activity.getSupportFragmentManager(), "");
    }

    public static void showBlockedAccountDialog(FragmentManager fragmentManager, ExceptionResponse response,
                                                Runnable submitBtnAction, Runnable cancelBtnAction) {
        if (response != null) {
            String imageName = response.getImageName();
            String formattedImageName = StringExtKt.getFormattedImageName(imageName);
            String title = Optional.ofNullable(response.getTitle()).orElse(GeneralHelper.getString(R.string.text_blocked_account_title));
            String desc = Optional.ofNullable(response.getDescription()).orElse(GeneralHelper.getString(R.string.text_blocked_account_desc));

            OpenBottomSheetGeneralFragment.INSTANCE.showDialogConfirmation(
                    fragmentManager, "", formattedImageName,
                    title,
                    desc,
                    createKotlinFunction0(submitBtnAction),
                    createKotlinFunction0(cancelBtnAction),
                    false,
                    GeneralHelper.getString(R.string.text_blocked_account_btn_1),
                    GeneralHelper.getString(R.string.text_blocked_account_btn_2),
                    true
            );
        }
    }

    // convert function java to kotlin (Unit)
    public static Function0<Unit> createKotlinFunction0(Runnable action) {
        return () -> {
            action.run();
            return Unit.INSTANCE;
        };
    }

    public static boolean isProhibitedActivity(String lastActivity) {
        return lastActivity.contains("Konfirmasi") || lastActivity.contains("KonfirmasiGeneral");
    }

    public static AppStart checkAppStart() {
        PackageInfo pInfo;
        AppStart appStart = AppStart.NORMAL;
        try {
            BRImoPrefRepository prefRepository = new BRImoPrefRepository(helperContext);
            pInfo = helperContext.getPackageManager().getPackageInfo(helperContext.getPackageName(), 0);
            int lastVersionCode = prefRepository.getVersionCodeApp();
            int currentVersionCode = pInfo.versionCode;
            appStart = checkAppStart(currentVersionCode, lastVersionCode);
            // Update version in preferences

            prefRepository.saveVersionCodeApp(currentVersionCode);
        } catch (PackageManager.NameNotFoundException e) {
            Log.w("Log",
                    "Unable to determine current app version from pacakge manager. Defenisvely assuming normal app start.");
        }
        return appStart;
    }

    private static AppStart checkAppStart(int currentVersionCode, int lastVersionCode) {
        if (lastVersionCode == -1) {
            return AppStart.FIRST_TIME;
        } else if (lastVersionCode < currentVersionCode) {
            return AppStart.FIRST_TIME_VERSION;
        } else if (lastVersionCode > currentVersionCode) {
            Log.w("Log", "Current version code (" + currentVersionCode
                    + ") is less then the one recognized on last startup ("
                    + lastVersionCode
                    + "). Defenisvely assuming normal app start.");
            return AppStart.NORMAL;
        } else {
            return AppStart.NORMAL;
        }
    }

    @SuppressLint({"ClickableViewAccessibility", "SetJavaScriptEnabled"})
    public static void setWebViewWithoutZoom(WebView webView, String baseUrl, String htmlData) {
        WebSettings webSetting = webView.getSettings();
        webSetting.setJavaScriptEnabled(true);
        webView.getSettings().setJavaScriptEnabled(true);
        webView.getSettings().setLoadWithOverviewMode(true);
        webView.getSettings().setUseWideViewPort(true);
        webView.getSettings().setBuiltInZoomControls(false);
        webView.getSettings().setDisplayZoomControls(false);
        webView.getSettings().setPluginState(WebSettings.PluginState.ON);
        webView.loadDataWithBaseURL(baseUrl, htmlData, "text/html", "utf-8", null);
    }

    @SuppressLint({"ClickableViewAccessibility", "SetJavaScriptEnabled"})
    public static void setWebView(WebView webView, String baseUrl, String htmlData) {
        WebSettings webSetting = webView.getSettings();
        webSetting.setJavaScriptEnabled(true);
        webView.getSettings().setJavaScriptEnabled(true);
        webView.getSettings().setLoadWithOverviewMode(true);
        webView.getSettings().setUseWideViewPort(true);
        webView.getSettings().setBuiltInZoomControls(true);
        webView.getSettings().setPluginState(WebSettings.PluginState.ON);
        webView.loadDataWithBaseURL(baseUrl, htmlData, "text/html", "utf-8", null);
    }

    public static void setWebViewStandart(WebView webView, String baseUrl, String htmlData) {
        WebSettings webSetting = webView.getSettings();
        webSetting.setJavaScriptEnabled(true);
        webSetting.setDomStorageEnabled(true);
        webView.setOnTouchListener(new View.OnTouchListener() {

            public boolean onTouch(View v, MotionEvent event) {
                return (event.getAction() == MotionEvent.ACTION_MOVE);
            }
        });
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
        }
        webView.loadDataWithBaseURL(baseUrl, htmlData, "text/html", "utf-8", null);
    }

    public static void setWebViewReceipt(WebView webView, String baseUrl, String htmlData) {
        WebSettings webSetting = webView.getSettings();
        webSetting.setJavaScriptEnabled(true);
        webSetting.setDefaultFontSize((11));
        webView.setOnTouchListener(new View.OnTouchListener() {

            public boolean onTouch(View v, MotionEvent event) {
                return (event.getAction() == MotionEvent.ACTION_MOVE);
            }
        });
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
        }
        webView.loadDataWithBaseURL(baseUrl, htmlData, "text/html", "utf-8", null);
    }

    public static void setWebViewBackground(WebView webView, String baseUrl, String htmlData, Integer color) {
        WebSettings webSetting = webView.getSettings();
        webSetting.setJavaScriptEnabled(true);
        webView.setOnTouchListener(new View.OnTouchListener() {

            public boolean onTouch(View v, MotionEvent event) {
                return (event.getAction() == MotionEvent.ACTION_MOVE);
            }
        });
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
        }
        webView.setBackgroundColor(color);
        webView.loadDataWithBaseURL(baseUrl, htmlData, "text/html", "utf-8", null);
    }

    public static void setWebViewDetailProduct(WebView webView, String baseUrl, String htmlData) {
        WebSettings webSetting = webView.getSettings();
        webSetting.setJavaScriptEnabled(true);
        webView.getSettings().setJavaScriptEnabled(true);
        webView.getSettings().setPluginState(WebSettings.PluginState.ON);
        webView.loadDataWithBaseURL(baseUrl, htmlData, "text/html", "utf-8", null);
    }

    public static String formatRupiah(Double number) {
        Locale localeID = new Locale("in", "ID");
        NumberFormat formatRupiah = NumberFormat.getCurrencyInstance(localeID);
        return formatRupiah.format(number);
    }

    /**
     * Get version code BRImo
     *
     * @return
     */
    public static String getLastAppVersionCode() {
        String version = "";
        try {
            PackageInfo pInfo = helperContext.getPackageManager().getPackageInfo(helperContext.getPackageName(), 0);
            version = String.valueOf(pInfo.versionCode);
        } catch (PackageManager.NameNotFoundException e) {
            if (!isProd()) {
                Log.e(TAG, "getLastAppVersion: ", e);
            }
        }

        return version;
    }

    /**
     * get version name BRImo
     *
     * @return
     */
    public static String getLastAppVersion() {
        String version = "";
        try {
            PackageInfo pInfo = helperContext.getPackageManager().getPackageInfo(helperContext.getPackageName(), 0);
            version = String.valueOf(pInfo.versionName);
        } catch (PackageManager.NameNotFoundException e) {
            if (!isProd()) {
                Log.e(TAG, "getLastAppVersion: ", e);
            }
        }

        return version;
    }

    public static String getOSVersion() {
        String versionOS = "";
        try {
            versionOS = Build.VERSION.RELEASE;
        } catch (Exception e) {
            if (!isProd()) {
                Log.e(TAG, "getLastAppVersion: ", e);
            }
        }

        return versionOS;
    }

    public static String getLastActivityNew(Context context) {
        String name = null;
        ActivityManager result = (ActivityManager) context.getSystemService(ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> services = result
                .getRunningTasks(Integer.MAX_VALUE);
        try {
            name = services.get(0).topActivity.toString();
        } catch (Exception e) {

        }
        return name;
    }

    /**
     * Method utk mengkonversi string seqnum menjadi padded seqnum )
     *
     * @param sequenceNumber
     * @return
     */
    public static String getZeroPaddedSeqnum(String sequenceNumber) {
        String seqNum = GeneralHelper.padding(sequenceNumber, "0", 12, true);
        if (seqNum.length() <= 8)
            return seqNum.substring(0, 8);
        else
            return seqNum.substring(seqNum.length() - 8);
    }

    /**
     * Method generate seqnum dengan menambahkan 0 kiri (padded left)
     * dan menamhkan "F" kanan (padded right)
     *
     * @param sequenceNumber string seqnum non padded
     * @return
     */
    public static String getPaddedSeqnum(String sequenceNumber) {
        String zeroPadded = GeneralHelper.padding(sequenceNumber, "0", 12, true);
        return GeneralHelper.padding(zeroPadded, "F", 16, false);
    }

    /**
     * Method untuk setting progress
     *
     * @param min,max,seekbar
     */
    public static SeekBar setSeekProgress(SeekBar seekBar, double min, double max) {
        double c = max - min;
        double progres = ((c / max) * 100);
        int seekIndex = (int) Math.round(progres);
        seekBar.setMax(100);
        seekBar.setProgress(100 - seekIndex);
        seekBar.refreshDrawableState();
        seekBar.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return true;
            }
        });
        return seekBar;
    }

    /**
     * Method untuk format countdown
     *
     * @Param milliSeconds, h (hours), m (minute), s (second)
     */
    public static String countDownFormat(long milliSeconds, boolean h, boolean m, boolean s) {
        String text = "";
        if (milliSeconds != 0) {
            if (h && m && s) {
                text = String.format("%02d:%02d:%02d",
                        TimeUnit.MILLISECONDS.toHours(milliSeconds),
                        TimeUnit.MILLISECONDS.toMinutes(milliSeconds) - TimeUnit.HOURS.toMinutes(TimeUnit.MILLISECONDS.toHours(milliSeconds)),
                        TimeUnit.MILLISECONDS.toSeconds(milliSeconds) - TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(milliSeconds)));
            } else if (h && !m && s) {
                text = String.format("%02d:%02d",
                        TimeUnit.MILLISECONDS.toHours(milliSeconds),
                        TimeUnit.MILLISECONDS.toSeconds(milliSeconds) - TimeUnit.HOURS.toHours(TimeUnit.MILLISECONDS.toHours(milliSeconds)));
            } else if (h && m && !s) {
                text = String.format("%02d:%02d",
                        TimeUnit.MILLISECONDS.toHours(milliSeconds),
                        TimeUnit.MILLISECONDS.toMinutes(milliSeconds) - TimeUnit.HOURS.toMinutes(TimeUnit.MILLISECONDS.toHours(milliSeconds)));
            } else if (h && !m && !s) {
                text = String.format("%02d",
                        TimeUnit.MILLISECONDS.toHours(milliSeconds));
            } else if (!h && m && s) {
                text = String.format("%02d:%02d",
                        TimeUnit.MILLISECONDS.toMinutes(milliSeconds),
                        TimeUnit.MILLISECONDS.toSeconds(milliSeconds) - TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(milliSeconds)));
            } else if (!h && m && !s) {
                text = String.format("%02d",
                        TimeUnit.MILLISECONDS.toMinutes(milliSeconds));
            } else if (!h && !m && s) {
                text = String.format("%02d",
                        TimeUnit.MILLISECONDS.toSeconds(milliSeconds));
            }
        } else {
            text = "00:00:00";
        }
        return text;
    }

    /**
     * Method untuk setting countdown
     *
     * @Param textView, countdownMilis, intervalMilis, h (hours), m (minute), s (second)
     */
    public static TextView setCountDown(TextView textView, long countdownMilis, long intervalMilis, boolean h, boolean m, boolean s) {
        CountDownTimer countDownTimer = new CountDownTimer(countdownMilis, intervalMilis) {
            @Override
            public void onTick(long millisUntilFinished) {
                textView.setText(countDownFormat(millisUntilFinished, h, m, s));
            }

            @Override
            public void onFinish() {
                textView.setText(countDownFormat(countdownMilis, h, m, s));
            }

        }.start();
        countDownTimer.start();
        return textView;
    }


    /**
     * Method ini digunakan utk menampilkan label eror dan set message di label tersebut
     *
     * @param context
     * @param tvError
     * @param layoutError
     * @param isAllowed
     * @param isShow
     * @param label
     */
    public static void showAmountValidation(Context context, TextView tvError, LinearLayout layoutError, boolean isAllowed, boolean isShow, String label) {
        if (isShow) {
            onAnimator(layoutError, true, ANIMATE_SHOW, Constant.REQUEST_BASE_INQUIRY);
            tvError.setText(String.format("%s", label));
            tvError.setVisibility(View.VISIBLE);
        } else {
            onAnimator(layoutError, true, ANIMATE_GONE, Constant.REQUEST_BASE_INQUIRY);
            tvError.setVisibility(View.GONE);
            return;
        }
        if (isAllowed) {
            tvError.setTextColor(context.getResources().getColor(R.color.colorErrorMinTrx));
        } else {
            tvError.setTextColor(context.getResources().getColor(R.color.red));
        }
    }


    /**
     * Method ini digunakan utk mapping animasi dari view
     *
     * @param view
     * @param isVisible
     * @param typeAnimation
     * @param tagIdAnimate
     */
    public static void onAnimator(View view, boolean isVisible, int typeAnimation, String tagIdAnimate) {
        if (view != null) {

            if (typeAnimation == ANIMATE_SHOW && !isAnimatedShow) {
                onAnimatorShow(view, isVisible, tagIdAnimate);
                isAnimatedShow = true;
                isAnimatedInvisible = false;
                isAnimatedGone = false;
            }

            if (typeAnimation == ANIMATE_GONE && !isAnimatedGone) {
                onAnimatorFade(view, isVisible, tagIdAnimate);
                isAnimatedGone = true;
                isAnimatedShow = false;
            }

            if (typeAnimation == ANIMATE_INVISIBLE && !isAnimatedInvisible) {
                onAnimatorInvisible(view, isVisible);
                isAnimatedInvisible = true;
                isAnimatedShow = false;
            }
        }
    }

    /**
     * Method ini digunakan ketika ingin memunculkan animasi
     *
     * @param view
     * @param isVisible
     * @param tagId
     */
    public static void onAnimatorShow(View view, boolean isVisible, String tagId) {
        if (isVisible)
            view.setVisibility(View.VISIBLE);

        ObjectAnimator anim = ObjectAnimator.ofFloat(view, "alpha", 0, 1f);
        anim.setDuration(500).addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                view.setVisibility(View.VISIBLE);
                onAnimatorShowEnd(tagId);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                onAnimatorShowEnd(tagId);
                view.setVisibility(View.VISIBLE);
            }

        });
        anim.start();
    }

    /**
     * Method ini digunakan ketika show animasi berakhir
     *
     * @param tagId Id activity request
     */
    protected static void onAnimatorShowEnd(String tagId) {

    }

    /**
     * Method ini digunakan untuk animasi invisible
     *
     * @param view
     * @param isVisible
     */
    public static void onAnimatorInvisible(View view, boolean isVisible) {
        if (isVisible)
            view.setVisibility(View.VISIBLE);
        else
            view.setVisibility(View.INVISIBLE);

        ObjectAnimator anim = ObjectAnimator.ofFloat(view, "alpha", 1f, 0);
        anim.setDuration(400).addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                view.setVisibility(View.INVISIBLE);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                view.setVisibility(View.INVISIBLE);
            }

        });
        anim.start();
    }

    /**
     * Method ini digunakan ketika label atau view akan menghilang
     *
     * @param view
     * @param isVisible
     * @param tagIdFade
     */
    public static void onAnimatorFade(View view, boolean isVisible, String tagIdFade) {
        if (isVisible)
            view.setVisibility(View.VISIBLE);
        else
            view.setVisibility(View.GONE);

        ObjectAnimator anim = ObjectAnimator.ofFloat(view, "alpha", 1f, 0);
        anim.setDuration(500).addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                onAnimatorFadeEnd(tagIdFade);
                view.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                view.setVisibility(View.GONE);
                onAnimatorFadeEnd(tagIdFade);
            }

        });
        anim.start();
    }

    /**
     * Override method ini untuk menangkap callback animation Fade end
     *
     * @param tagIdFade Id activity request
     */
    protected static void onAnimatorFadeEnd(String tagIdFade) {

    }

    /**
     * Method untuk menampilkan dialog
     * parameterize title, detail, drawable
     */
    public static void showDialogIlustration(FragmentActivity activity, String drawable, String title, String detail) {
        if (!activity.isFinishing()) {
            DialogIlustration dialog = new DialogIlustration(activity.getApplicationContext(), drawable, title, detail, "OK");
            FragmentTransaction ft = activity.getSupportFragmentManager().beginTransaction();
            ft.add(dialog, null);
            ft.commitAllowingStateLoss();
        }
    }

    public static void showDialogCustom(FragmentActivity activity, ExceptionResponse response) {
        CustomBottomDialogFragment customBottomDialogFragment = new CustomBottomDialogFragment(activity, response.getTitle(), response.getDescription(), getString(R.string.btn_okay_sad), response.getImageName(), response.getImagePath(), false);
        customBottomDialogFragment.show(activity.getSupportFragmentManager(), "");
    }

    public static void showDialogCustomBack(FragmentActivity activity, ExceptionResponse response) {
        CustomBottomDialogFragment customBottomDialogFragment = new CustomBottomDialogFragment(activity, response.getTitle(), response.getDescription(), getString(R.string.btn_okay_sad), response.getImageName(), response.getImagePath(), true);
        customBottomDialogFragment.show(activity.getSupportFragmentManager(), "");
    }

    public static void gotoGmailActivity(Activity lastAcitivity) {
        try {
            lastAcitivity.startActivity(lastAcitivity.getPackageManager().getLaunchIntentForPackage("com.google.android.gm"));
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "gotoGmailActivity: ", e);
            }
        }
    }

    public static String maskValue(String value) {
        char[] ch = value.toCharArray();
        int initialLen = ch.length;
        int finalLen = 15;
        Arrays.fill(ch, 4, initialLen, '*');
        if (initialLen < finalLen) {
            StringBuilder builder = new StringBuilder(new String(ch));
            for (int i = initialLen; i < finalLen; i++) {
                builder.append("*");
            }
            return builder.toString();
        }
        return new String(ch);
    }

    public static String maskingCard(String cardNumber) {
        String digitsOnly = cardNumber.replaceAll("\\s+", "");
        int length = digitsOnly.length();

        if (length <= 8) {
            // Jika panjang digit kurang dari atau sama dengan 8, tidak ada cukup digit untuk masking
            return cardNumber;
        }

        // Ambil 4 digit pertama dan 4 digit terakhir
        String start = digitsOnly.substring(0, 4);
        String end = digitsOnly.substring(length - 4);

        // Buat bagian tengah menjadi '*'
        StringBuilder masked = new StringBuilder();
        for (int i = 4; i < length - 4; i++) {
            masked.append("*");
        }

        // Gabungkan semuanya
        String result = start + " " + masked.substring(0, 4) + " " + masked.substring(4) + " " + end;

        return result;
    }

    /*function to get progress percentage based on viewed item*/
    public static int progressPercentage(RecyclerView recyclerView) {
        int offset = recyclerView.computeHorizontalScrollOffset();
        int extent = recyclerView.computeHorizontalScrollExtent();
        int range = recyclerView.computeHorizontalScrollRange();

        return (int) (100.0 * offset / (float) (range - extent));
    }

    public static String removeDotinDouble(double d) {
        if (d == (long) d)
            return String.format("%d", (long) d);
        else
            return String.format("%s", d);
    }

    public static Spannable spannableText(String inputText, int startIndex, int endIndex, int textColor) {
        Spannable outputColoredText = new SpannableString(inputText);
        outputColoredText.setSpan(new ForegroundColorSpan(textColor), startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        outputColoredText.setSpan(new StyleSpan(Typeface.BOLD), startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        outputColoredText.setSpan(new UnderlineSpan(), startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        return outputColoredText;
    }

    /**
     * Method untuk load image from url dengan clear cache
     */
    public static void loadImageUrlClearCache(Context mContext, String imageUrl, ImageView imageView, int errorImage, int paddingImage) {

        int padding = SizeHelper.dpToPx(mContext, paddingImage);
        imageView.setPadding(padding, padding, padding, padding);

        Glide.with(mContext)
                .asBitmap()
                .load(imageUrl)
                .error(errorImage)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .skipMemoryCache(true)
                .into(imageView);

    }

    public static void debugMessage(Object object, String location, DebugType debugType) {
        if (object == null) return;

        String message = String.format("%s >> %s", location, object);

        if (!GeneralHelper.isProd()) {
            switch (debugType) {
                case DEBUG:
                    Log.d("Lihat", message);
                    break;
                case ERROR:
                    Log.e("Lihat", message);
                    break;
                case INFO:
                    Log.i("Lihat", message);
                    break;
            }
        }
    }

    public static void changeTabsFontBoldForDeposito(Activity activity, LinearLayout ly, int position) {
        final Typeface typefaceBold = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_BOLD);
        final Typeface typefacesemibold = Typeface.createFromAsset(activity.getAssets(), FontConfig.BRI_SEMI_BOLD);

        for (int j = 0; j < ly.getChildCount(); j++) {
            TextView tvTabTitle = (TextView) ly.getChildAt(j);
            tvTabTitle.setTypeface(typefacesemibold);
            tvTabTitle.setTextColor(activity.getResources().getColor(R.color.primary_blue20));
            if (j == position) {
                tvTabTitle.setTypeface(typefaceBold);
                tvTabTitle.setTextColor(activity.getResources().getColor(R.color.white));
                tvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
            }
        }
    }

    /*filter to remove whitespace in edittext*/
    public static InputFilter filterWhiteSpace = new InputFilter() {
        public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
            for (int i = start; i < end; i++) {
                if (Character.isWhitespace(source.charAt(i))) {
                    return "";
                }
            }
            return null;
        }
    };

    /**
     * Function to set margin into ImageView
     */
    public static void setMarginImageView(Context mContext, int leftMargin, int topMargin, int rightMargin, int bottomMargin, ImageView imageView) {
        ViewGroup.MarginLayoutParams marginParams = (ViewGroup.MarginLayoutParams) imageView.getLayoutParams();
        marginParams.setMargins(
                dpToPx(mContext, leftMargin),
                dpToPx(mContext, topMargin),
                dpToPx(mContext, rightMargin),
                dpToPx(mContext, bottomMargin));
        imageView.setLayoutParams(marginParams);
    }


    public static void showKeyboard(Activity activity, View view) {
        view.requestFocus();

        activity.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        InputMethodManager methodManager = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        methodManager.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
    }

    public static String randomTrID() {
        long trId = ThreadLocalRandom.current().nextLong(1000000000L, 9999999999L);

        return String.valueOf(trId);
    }
    public static String addBoldTags(String input) {
        StringBuilder result = new StringBuilder();
        char[] chars = input.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            if (chars[i] == '*') {
                if (i % 2 == 0) {
                    result.append("<b style=\"font-weight: 800\">");
                } else {
                    result.append("</b>");
                }
            } else {
                result.append(chars[i]);
            }
        }
        return result.toString();
    }

    public enum AppStart {
        FIRST_TIME, FIRST_TIME_VERSION, NORMAL
    }

    public static String orNull(@Nullable String string) {
        if (string == null) return "";
        else return string;
    }

    public static String maskAccountNumber(String accountNumber) {
        // Masking the account number ex: 2112 **** **** 123
        String part1 = accountNumber.substring(0, 4);
        String part2 = accountNumber.substring(12);

        return part1 + " **** **** " + part2;
    }

    public static String handleStrCardType(int resource, CardType cardType) {
        String debitKredit;
        if (cardType == CardType.DEBIT || cardType == CardType.DEBIT_ACTIVATION) {
            debitKredit = GeneralHelper.getString(R.string.txt_debit);
        } else {
            debitKredit = GeneralHelper.getString(R.string.txt_kredit);
        }
        return String.format(GeneralHelper.getString(resource), debitKredit);
    }

    public static String convertUnicodeToEmoji(String text) {
        StringBuilder result = new StringBuilder();
        int currentIndex = 0;
        while (currentIndex < text.length()) {
            int uPlusIndex = text.indexOf("U+", currentIndex);
            if (uPlusIndex == -1) {
                // If "U+" is not found, append the remaining text and exit the loop
                result.append(text.substring(currentIndex));
                break;
            } else {
                // Append the text before "U+"
                result.append(text.substring(currentIndex, uPlusIndex));
                // Find the end of the Unicode code point (the first non-hexadecimal character)
                int codePointEndIndex = uPlusIndex + 2;
                while (codePointEndIndex < text.length() && Character.isLetterOrDigit(text.charAt(codePointEndIndex))) {
                    codePointEndIndex++;
                }
                String codePointHex = text.substring(uPlusIndex + 2, codePointEndIndex);
                try {
                    int codePoint = Integer.parseInt(codePointHex, 16);
                    String emoji = new String(Character.toChars(codePoint));
                    result.append(emoji);
                } catch (NumberFormatException e) {
                    // If the conversion fails, append the original "U+codePoint" string
                    result.append("U+").append(codePointHex);
                }
                // Move the current index to the end of the Unicode code point
                currentIndex = codePointEndIndex;
            }
        }
        return result.toString();
    }

    // function ini untuk memvalidasi inputannya harus kelipatan sesuai parameter
    public static boolean isMultipleOfValue(Long kelipatan, Long nominal) {
        return nominal != null && nominal % kelipatan == 0;
    }

    public static int dpToPx(int dp, Context context) {
        return (int) (dp * context.getResources().getDisplayMetrics().density);
    }
}