package id.co.bri.brimo.ui.fragments.pengelolaankartu

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.PinNumberAdapter
import id.co.bri.brimo.contract.IPresenter.pengelolaankartu.IChangeDebitPINPresenter
import id.co.bri.brimo.contract.IView.pengelolaankartu.IChangeDebitPINView
import id.co.bri.brimo.databinding.FragmentInputExistingPinBinding
import id.co.bri.brimo.di.components.fragment.DaggerFragmentComponent
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant.REQ_PIN_CHANGE_MAX_RETRY
import id.co.bri.brimo.domain.config.Constant.REQ_PIN_CHANGE_SUCCESS
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.providers.CardType
import id.co.bri.brimo.security.AES
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.pengelolaankartu.ChangePINDebitActivity
import id.co.bri.brimo.ui.activities.pengelolaankartu.ChangePINDebitActivity.Companion.CARD_TYPE_EXTRA
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers
import id.co.bri.brimo.ui.fragments.BaseFragment
import id.co.bri.brimo.ui.fragments.PinFragment
import id.co.bri.brimo.ui.widget.inputpin.InputPinView
import id.co.bri.brimo.util.viewBindingNullable
import javax.inject.Inject

class InputExistingPinFragment : BaseFragment(), IChangeDebitPINView {

    private var binding by viewBindingNullable<FragmentInputExistingPinBinding>()

    companion object {
        const val NEW_INPUT_PIN_EXTRA = "new_pin_extra"
        const val REFERENCE_NUMBER_EXTRA = "reference_number_extra"
        const val TAG = "fragment-input-existing"
    }

    @Inject
    lateinit var presenter: IChangeDebitPINPresenter<IChangeDebitPINView>

    private var newInputPin: String? = null
    private var referenceNumber: String? = null
    private var cardNumber: String? = null
    private var cardToken: String? = null
    private var cardType: CardType = CardType.DEBIT

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        injectComponent()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentInputExistingPinBinding.inflate(inflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        getBundleData()
        handleCardGuidance(cardType)
        initUrlChangePin()
        handlePinBehavior()
    }

    private fun getBundleData() {
        cardToken = arguments?.getString(ChangePINDebitActivity.CARD_TOKEN)
        cardType = arguments?.getString(CARD_TYPE_EXTRA)?.let { CardType.valueOf(it) } ?: CardType.DEBIT
    }

    private fun initUrlChangePin() {
        presenter.view = this
        presenter.setUrlChangePin(
            GeneralHelper.getString(
                if (cardType == CardType.DEBIT || cardType == CardType.VIRTUAL_DEBIT)
                    R.string.url_card_management_change_pin
                else
                    R.string.url_card_management_change_pin_cc
            )
        )
    }

    private fun handlePinBehavior() {
        binding?.inputPinView?.isFocused = true

        context?.let {
            val pinNumber = PinNumberAdapter(InsertPinNumbers.getPinNumberList(it))
            pinNumber.setPinPadColor(R.color.neutral_dark40)
            binding?.rvInputPin?.adapter = pinNumber
            binding?.rvInputPin?.layoutManager = GridLayoutManager(it, 3)

            pinNumber.onPinNumberListener = object : PinNumberAdapter.OnPinNumberListener {
                override fun onPinClicked(pinNumber: Int) {
                    binding?.inputPinView?.addPin(pinNumber.toString())
                }

                override fun onDeleteClicked() {
                    binding?.inputPinView?.deletePin()
                }
            }
        }

        binding?.inputPinView?.setOnCompleteListener(object : InputPinView.InputPinViewListener {
            override fun onPinInputCompleted(pin: String) {
                binding?.inputPinView?.setSuccess()
                showPinFragment(pin)
            }

            override fun onPinDeletedAll() {
                /* no-op */
            }
        })
    }

    private fun handleCardGuidance(cardType: CardType) {
        val inputPinHeader = GeneralHelper.handleStrCardType(
            R.string.change_pin_debit_credit_card_guidance_header,
            cardType
        )
        val inputPinDesc = GeneralHelper.handleStrCardType(
            R.string.change_pin_debit_credit_card_guidance_desc,
            cardType
        )
        val enterCardPin = GeneralHelper.handleStrCardType(
            R.string.change_pin_debit_credit_card_input_pin_header,
            cardType
        )

        binding?.apply {
            tvInputPinGuidanceHeader.text = inputPinHeader
            tvInputPinGuidanceDesc.text = inputPinDesc
            inputPinView.setInputNewPinLabel(enterCardPin)
        }
    }

    override fun onResume() {
        super.onResume()
        newInputPin = arguments?.getString(NEW_INPUT_PIN_EXTRA)
        cardNumber = arguments?.getString(ChangePINDebitActivity.CARD_NUMBER_EXTRA)
        referenceNumber = arguments?.getString(REFERENCE_NUMBER_EXTRA)
        binding?.inputPinView?.deleteAllPin()
    }

    private fun showPinFragment(oldPin: String) {
        activity?.let {
            val pinFragment = PinFragment(it, object : PinFragment.SendPin {
                override fun onSendPinComplete(pin: String?) {
                    when (cardType) {
                        CardType.DEBIT, CardType.VIRTUAL_DEBIT -> {
                            presenter.changePin(
                                authPin = pin,
                                newCardPin = newInputPin,
                                oldCardPin = AES.AESEncrypt(
                                    oldPin,
                                    referenceNumber,
                                    AppConfig.KEY_CHANGE_PIN
                                ),
                                cardNumber = cardNumber,
                            )
                        }
                        else -> {
                            presenter.changePinCc(
                                authPin = pin,
                                newCardPin = newInputPin,
                                oldCardPin = AES.AESEncrypt(
                                    oldPin,
                                    referenceNumber,
                                    AppConfig.KEY_CHANGE_PIN
                                ),
                                cardToken = cardToken
                            )
                        }
                    }
                }

                override fun onLupaPin() {
                    LupaPinActivity.launchIntent(it)
                }
            })
            pinFragment.setOnDismissListener {
                binding?.inputPinView?.deleteAllPin()
                binding?.inputPinView?.isFocused = true
            }
            pinFragment.show()
        }
    }

    private fun injectComponent() {
        DaggerFragmentComponent.builder()
            .activityComponent(activityComponent)
            .build()
            .inject(this)
    }

    override fun onSuccessInitPin(referenceNumber: String) {
        /* no-op */
    }

    override fun onSuccessChangePin(message: String) {
        sendBackMessage(REQ_PIN_CHANGE_SUCCESS, message)
    }

    private fun sendBackMessage(code: Int, message: String) {
        activity?.let { fragmentActivity ->
            if (fragmentActivity is ChangePINDebitActivity) {
                val intent = Intent().apply {
                    putExtra(ChangePINDebitActivity.CHANGE_PIN_MESSAGE, message)
                }
                fragmentActivity.setResult(code, intent)
                fragmentActivity.finish()
            }
        }
    }

    override fun onMaximumRetries(message: String) {
        sendBackMessage(REQ_PIN_CHANGE_MAX_RETRY, message)
    }

    override fun onErrorPin() {
        //to do
    }

    override fun onException(message: String?) {
        super.onException(message)
        binding?.inputPinView?.showError()
    }
}