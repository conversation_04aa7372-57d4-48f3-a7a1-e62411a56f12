package id.co.bri.brimo.presenters.inbox;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.inbox.IInboxPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.inbox.IInboxView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.InboxRequest;
import id.co.bri.brimo.models.apimodel.request.StatusRequest;
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.FilterAktivityResponse;
import id.co.bri.brimo.models.apimodel.response.InboxResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptAmkkmResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptInternationalInboxResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptKaiInboxTravel;
import id.co.bri.brimo.models.apimodel.response.ReceiptResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampInboxResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.esbn.earlyredeem.CatatanAktivitasEarly;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class InboxPresenter<V extends IMvpView & IInboxView> extends MvpPresenter<V> implements IInboxPresenter<V> {

    private String urlInbox, urlDetailInbox;
    private FilterAktivityResponse filterAktivityResponse;
    private InboxResponse inboxResponse;
    private ReceiptResponse receiptResponse;
    private ReceiptRevampInboxResponse receiptRevampResponse;
    private StatusRequest statusRequest;
    private InboxRequest confirmationRequest;
    private String trxType;

    private boolean isSessionEndCalled = false;
    private boolean isLoading, isLoadingItem, isLoadingFilter = false;

    public InboxPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlInbox(String urlInbox) {
        this.urlInbox = urlInbox;
    }

    @Override
    public void setUrlDetailInbox(String urlDetailInbox) {
        this.urlDetailInbox = urlDetailInbox;
    }

    @Override
    public void setTrxType(String trxType) {
        this.trxType = trxType;
    }

    @Override
    public void getFilterInbox() {
        if (isLoadingFilter) {
            return;
        } else {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            if (isViewAttached()) {
                isLoadingFilter = true;
                getCompositeDisposable().add(
                        getApiSource().getFilterAktifitas(seqNum).subscribeOn(getSchedulerProvider().single())
                                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                                .observeOn(getSchedulerProvider().mainThread())
                                .subscribeWith(new ApiObserver(getView(), seqNum) {
                                    @Override
                                    protected void onFailureHttp(String type) {
                                        isLoadingFilter = false;
                                        getView().hideProgress();
                                        getView().onException(type);
                                    }

                                    @Override
                                    protected void onApiCallSuccess(RestResponse response) {
                                        isLoadingFilter = false;
                                        getView().hideProgress();
                                        filterAktivityResponse = restResponse.getData(FilterAktivityResponse.class);
                                        getView().onSuccessGetFilterData(filterAktivityResponse);
                                    }

                                    @Override
                                    protected void onApiCallError(RestResponse restResponse) {
                                        getView().hideProgress();
                                        isLoadingFilter = false;
                                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                            if (isSessionEndCalled)
                                                return;

                                            isSessionEndCalled = true;
                                            getView().onSessionEnd(restResponse.getDesc());
                                        } else
                                            getView().onException(restResponse.getDesc());
                                    }
                                })
                );
            }
        }
    }


    public void setupData(String periode, String status, String fitur, String subFitur, String lastId) {
        try {
            if (periode.equalsIgnoreCase("") & status.equalsIgnoreCase("") &
                    fitur.equalsIgnoreCase("")) {
                periode = "ALL";
                status = "ALL";
                fitur = "ALL";
            }

            if (subFitur.equalsIgnoreCase("")) {
                subFitur = "ALL";
            }

            if (lastId.equalsIgnoreCase("")) {
                confirmationRequest = new InboxRequest(fitur, subFitur, status, periode, "0");
            } else {
                confirmationRequest = new InboxRequest(fitur, subFitur, status, periode, lastId);
            }
        } catch (Exception e) {
            // do nothing
        }
    }

    @Override
    public void getInbox(String periode, String status, String fitur, String subFitur, String lastId, boolean isRefresh) {
        if (isViewAttached()) {
            isLoading = true;
            setupData(periode, status, fitur, subFitur, lastId);

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlInbox, confirmationRequest, seqNum)//function(param)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().single())
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    isLoading = false;
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    isLoading = false;
                                    getView().hideProgress();
                                    //TO-DO onSuccess
                                    try {
                                        if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                            inboxResponse = response.getData(InboxResponse.class);
                                            getView().onSuccessGetInbox(inboxResponse, isRefresh);
                                        } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                            getView().onInboxEnd(response.getDesc());
                                        }
                                    } catch (Exception e) {
                                        getView().onInboxEnd(e.getMessage());
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    isLoading = false;
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                        getView().onException12();
                                    } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                        getView().onException93();
                                    } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_HIT_EXCEEDED.getValue())) {
                                        getView().onException06(restResponse.getData(ExceptionResponse.class));
                                    } else {
                                        getView().onException(restResponse.getDesc());
                                    }
                                }
                            })
            );
        }
    }

    @Override
    public void getInboxDetail(String refnumber) {
        if (isLoadingItem) {
            return;
        } else {

            if (isViewAttached()) {
                isLoadingItem = true;
                getView().showProgress();

                statusRequest = new StatusRequest(refnumber);
                String seqNum = getBRImoPrefRepository().getSeqNumber();

                getCompositeDisposable().add(
                        getApiSource().getData(urlDetailInbox, statusRequest, seqNum)//function(param)
                                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                                .subscribeOn(getSchedulerProvider().io())
                                .observeOn(getSchedulerProvider().mainThread())
                                .subscribeWith(new ApiObserver(getView(), seqNum) {

                                    @Override
                                    protected void onFailureHttp(String errorMessage) {
                                        isLoadingItem = false;
                                        getView().hideProgress();
                                        getView().onExceptionNoBackAction(errorMessage);
                                    }

                                    @Override
                                    protected void onApiCallSuccess(RestResponse response) {
                                        try {
                                            receiptResponse = response.getData(ReceiptResponse.class);
                                        } catch (Exception e) {
                                        }
                                        try {
                                            receiptRevampResponse = response.getData(ReceiptRevampInboxResponse.class);
                                        } catch (Exception e) {
                                        }
                                        getView().hideProgress();
                                        isLoadingItem = false;
                                        //TO-DO onSuccess
                                        switch (trxType) {
                                            case "PaymentOpenAccountGeneric":
                                                receiptResponse = response.getData(ReceiptResponse.class);
                                                getView().onSuccessGetOpenAccountDetail(receiptResponse.getPendingResponses());
                                                break;
                                            case "PaymentOpenInsuranceS3f":
                                                receiptResponse = response.getData(ReceiptResponse.class);
                                                ReceiptAmkkmResponse.ReceiptAmkkmInboxResponse receiptAmkkmResponse = response.getData(ReceiptAmkkmResponse.ReceiptAmkkmInboxResponse.class);
                                                getView().onSuccessGetInboxAmkkmDetail(receiptAmkkmResponse.getReceiptAmkkmResponse());
                                                break;
                                            case "PaymentRedeemSBN":
                                                CatatanAktivitasEarly responses = response.getData(CatatanAktivitasEarly.class);
                                                getView().onSuccessPencairan(responses.getReceiptResponseNew());
                                                break;
                                            case "PaymentTransferInternationalCounterpartTransfer001":
                                            case "PaymentTransferInternationalCounterpartTransfer002":
                                            case "PaymentTransferInternationalCounterpartTransfer003":
                                            case "PaymentTransferInternationalCounterpartTransfer004":
                                            case "PaymentTransferInternationalCounterpartTransfer005":
                                            case "PaymentTransferInternationalCounterpartTransfer006":
                                            case "PaymentTransferInternationalCounterpartCashPickUp":
                                            case "PaymentTransferInternationalCounterpartWallet":
                                            case "PaymentTransferInternationalCounterpartTransfer":
                                            case "PaymentTransferInternationalSwift":
                                                ReceiptInternationalInboxResponse receiptInternationalInboxResponse = response.getData(ReceiptInternationalInboxResponse.class);
                                                getView().onSuccessInternasional(receiptInternationalInboxResponse.getReceiptInternasionalResponse());
                                                break;
                                            case "PurchaseKaiTravel":
                                                ReceiptKaiInboxTravel receiptKaiInboxTravel = response.getData(ReceiptKaiInboxTravel.class);
                                                getView().onSuccessKai(receiptKaiInboxTravel.getReceiptTravelTrainResponse());
                                                break;
                                            case "PaymentOpenAccountJunio":
                                                receiptResponse = response.getData(ReceiptResponse.class);
                                                getView().onSuccessGetOpenJunio(receiptResponse.getPendingResponses());
                                                break;
                                            case "PaymentBriva-V3":
                                            case "PurchaseListrik-V3":
                                            case "PaymentNonTaglis-V3":
                                            case "PaymentListrikH2H-V3":
                                            case "PaymentPurchasePulsa-V3":
                                            case "PaymentPurchaseData-V3":
                                            case "PurchaseKcicTravel":
                                            case "PaymentTopupRDN":
                                            case "PaymentCloseDepositoRevamp":
                                            case "PaymentSIGNAL":
                                            case "PaymentPurchaseDataCVM-V3":
                                            case "PaymentParking":
                                            case "PaymentHoreka":
                                            case "PaymentTopupRencana":
                                            case "PaymentWithdrawalRencana":
                                            case "PaymentClosingRencana":
                                            case "PaymentKKIGPN":
                                            case "PaymentBindingKKIGPN":
                                            case "PaymentTokenKKIGPN":
                                            case "NotifPaymentGPN":
                                            case "PurchaseFlightTravel":
                                            case "PaymentEducation-V3":
                                            case "PaymentTelkom-V3":
                                            case "PaymentEducation-V3H2H":
                                            case Constant.PAYMENT_MOBELANJA:
                                            case "PaymentDonation-V3":
                                            case "BindingPaymentGPN":
                                            case "PaymentListikH2H-V3AGF":
                                            case "PaymentMoliga":
                                            case "PaymentGoldMolding":
                                            case "PaymentIPLProperty":
                                            case "PaymentMokirim":
                                            case "PaymentGoldTopup":
                                            case "PaymentAftEmas":
                                            case "PaymentBuyEmas":
                                            case "PaymentTopUpDplkRevamp":
                                            case "PaymentUpdateStatusClaimDPLK":
                                            case "PaymentTelsel-V3":
                                                getView().onSuccessGetReceiptRevamp(receiptRevampResponse);
                                                break;
                                            case "PaymentOpenAccountGeneric-V3":
                                            case "PaymentOpenAccountValas-V3":
                                            case "PaymentOpenAccountJunio-V3":
                                            case "PaymentOpenAccountBancass-V3":
                                            case "PaymentOpenDplkRevamp":
                                            case "PaymentSubmitRegistrationDPLK":
                                                receiptRevampResponse = response.getData(ReceiptRevampInboxResponse.class);
                                                getView().onSuccessTabunganRevamp(receiptRevampResponse.getReceiptRevampResponse(), trxType);
                                                break;
                                            case "PaymentOpenAccountS3f-V3":
                                                receiptRevampResponse = response.getData(ReceiptRevampInboxResponse.class);
                                                getView().onSuccesTabunganS3fRevamp(receiptRevampResponse.getReceiptRevampResponse());
                                                break;
                                            case "PaymentTransferRTGS-V3":
                                            case "PaymentTransferDalam-V3":
                                            case "PaymentTransferLuar-V3":
                                            case "PaymentTransferBIFast-V3":
                                            case "PurchaseVoucherGame":
                                            case "PaymentTopupEmas":
                                            case "PaymentSellEmas":
                                            case "PurchaseDana-V3":
                                            case "PurchaseGopay-V3":
                                            case "PurchaseIsaku-V3":
                                            case "PurchaseLinkaja-V3":
                                            case "PurchaseOvo-V3":
                                            case "PurchaseShopeepay-V3":
                                            case "PurchaseStreaming-V3":
                                            case "PaymentNFC":
                                                getView().onSuccessGetInboxDetailRevamp(receiptRevampResponse);
                                                break;
                                            case "PaymentOnboardEmas":
                                                receiptRevampResponse = response.getData(ReceiptRevampInboxResponse.class);
                                                getView().onSuccesOnboardEmas(receiptRevampResponse.getReceiptRevampResponse());
                                                break;
                                            case "PaymentPelniMokapal":
                                            case "PaymentAlfamartMobelanja":
                                            case "PaymentTrxSplitBill":
                                                getView().onSuccessInboxPattern(receiptRevampResponse);
                                                break;
                                            case "PaymentMoevent":
                                                if (receiptRevampResponse.getReceiptRevampResponse().getTitleImage().equalsIgnoreCase(Constant.RECEIPT68_REVAMP) || receiptRevampResponse.getReceiptRevampResponse().getTitleImage().equalsIgnoreCase(Constant.RECEIPT58_REVAMP)) {
                                                    getView().onAbnormalReceipt(receiptRevampResponse);
                                                } else if (receiptRevampResponse.getReceiptRevampResponse().getTitleImage().equalsIgnoreCase(Constant.RECEIPT00_REVAMP)) {
                                                    getView().onSuccessReceiptTicketEvent(receiptRevampResponse);
                                                }
                                                break;
                                            default:
                                                receiptResponse = response.getData(ReceiptResponse.class);
                                                getView().onSuccessGetInboxDetail(receiptResponse);
                                                break;
                                        }
                                    }

                                    @Override
                                    protected void onApiCallError(RestResponse restResponse) {
                                        getView().hideProgress();
                                        isLoadingItem = false;
                                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                            getView().onSessionEnd(restResponse.getDesc());
                                        } else
                                            getView().onExceptionNoBackAction(restResponse.getDesc());
                                    }
                                })
                );
            }
        }
    }
}