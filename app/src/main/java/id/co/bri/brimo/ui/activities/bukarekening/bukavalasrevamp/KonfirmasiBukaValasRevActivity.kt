package id.co.bri.brimo.ui.activities.bukarekening.bukavalasrevamp

import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.DetailTransaksiRevampAdapter
import id.co.bri.brimo.adapters.SumberTransaksiRevAdapter
import id.co.bri.brimo.adapters.TotalTransaksiRevAdapter
import id.co.bri.brimo.contract.IPresenter.britamarencanarevamp.IKonfrimasiTabunganRevPresenter
import id.co.bri.brimo.contract.IView.britamarencanarevamp.IKonfirmasiTabunganRevView
import id.co.bri.brimo.databinding.ActivityKonfirmasiBukaValasRevBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.extension.gone
import id.co.bri.brimo.domain.extension.visible
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.PendingResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.emas.ReceiptEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.ReceiptGagalEmasResponse
import id.co.bri.brimo.ui.activities.GeneralSyaratActivity
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.LupaPinFastActivity
import id.co.bri.brimo.ui.activities.RiplayActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.bukarekening.PendingTabunganActivity
import id.co.bri.brimo.ui.activities.bukarekening.ReceiptTabunganActivity
import id.co.bri.brimo.ui.fragments.PinFragment
import java.lang.Boolean
import javax.inject.Inject
import kotlin.Int
import kotlin.String

class KonfirmasiBukaValasRevActivity : BaseActivity(), View.OnClickListener,
    IKonfirmasiTabunganRevView,
    PinFragment.SendPin {

    private val binding by lazy(LazyThreadSafetyMode.NONE) {
        ActivityKonfirmasiBukaValasRevBinding.inflate(
            layoutInflater
        )
    }

    @Inject
    lateinit var presenter: IKonfrimasiTabunganRevPresenter<IKonfirmasiTabunganRevView>

    var detailTransaksiRevampAdapter: DetailTransaksiRevampAdapter? = null
    var sumberTransaksiRevAdapter: SumberTransaksiRevAdapter? = null
    var totalTransaksiRevAdapter: TotalTransaksiRevAdapter? = null
    var isSyarat = false
    var isRiplay = false
    var isCheckedSave = false


    companion object {
        var mGeneralConfirmationResponse: GeneralConfirmationResponse? = null
        var mAccountModel: AccountModel? = null
        var mSetoranAwal: String = ""
        var mUrlPayment: String = ""
        var mUrlPending: String = ""
        fun launchIntent(
            caller: Activity,
            generalConfirmationResponse: GeneralConfirmationResponse,
            accountModel: AccountModel,
            setoranAwal: String,
            urlPayment: String,
            urlPending: String
        ) {
            val intent = Intent(caller, KonfirmasiBukaValasRevActivity::class.java)
            mGeneralConfirmationResponse = generalConfirmationResponse
            mAccountModel = accountModel
            mSetoranAwal = setoranAwal
            mUrlPayment = urlPayment
            mUrlPending = urlPending
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        binding.apply {
            GeneralHelper.setToolbarRevamp(
                this@KonfirmasiBukaValasRevActivity,
                tbKonfirmasi.toolbar,
                getString(R.string.toolbar_konfirmasi_tabungan)
            )

            llSyaratKetentuan.setOnClickListener(this@KonfirmasiBukaValasRevActivity)
            btnSubmit.setOnClickListener(this@KonfirmasiBukaValasRevActivity)
            cbSyarat.setOnClickListener(this@KonfirmasiBukaValasRevActivity)
            cbSyarat1.setOnClickListener(this@KonfirmasiBukaValasRevActivity)
            llSyaratKetentuan1.setOnClickListener(this@KonfirmasiBukaValasRevActivity)
            llRiplay.setOnClickListener(this@KonfirmasiBukaValasRevActivity)
            cbRiplay.setOnClickListener(this@KonfirmasiBukaValasRevActivity)
            injectDependency()
            setupView()
        }
    }


    fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlPayment(mUrlPayment)
        presenter.start()
    }

    fun setupView() {
        //set blue bar
        if (Build.VERSION.SDK_INT >= 21) {
            val window = window
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            window.statusBarColor = resources.getColor(R.color.highlightColor)
        }

        binding.apply {
            if (mGeneralConfirmationResponse?.isRateAdjusted == true) wvDescInfoBorder.visible() else wvDescInfoBorder.gone()

            mGeneralConfirmationResponse?.rateAdjustedString()?.let {
                wvDescInfoBorder.loadDataWithBaseURL(
                    null,
                    it,
                    "text/html",
                    "utf-8",
                    null
                )
            }

            if (mAccountModel!!.isDefault == 1) {
                ivIconUtama.visibility = View.VISIBLE
            } else {
                ivIconUtama.visibility = View.GONE
            }

            tvNoRek.text = mGeneralConfirmationResponse!!.sourceAccountDataView.subtitle
            tvAliasRek.text = mAccountModel!!.alias

            if (mAccountModel!!.saldoReponse == null) {
                tvSaldoRek.text = GeneralHelper.formatNominalIDR(
                    mAccountModel!!.currency,
                    mSetoranAwal
                )
            } else {
                tvSaldoRek.text = GeneralHelper.formatNominalIDR(
                    mAccountModel!!.currency,
                    mAccountModel!!.saldoReponse.balanceString
                )
            }


            if (mAccountModel!!.alias != null && !mAccountModel!!.alias.equals("")) {
                tvAliasRek.text = mAccountModel!!.alias
            } else {
                tvAliasRek.text = getString(R.string.belum_ada_alias_text)
            }

            GeneralHelper.loadImageUrl(
                this@KonfirmasiBukaValasRevActivity,
                mAccountModel!!.imagePath,
                ivIconRek,
                R.drawable.bri,
                0
            )

            //Detail Nominal
            rvDetail.setHasFixedSize(true)
            rvDetail.layoutManager = LinearLayoutManager(
                applicationContext, RecyclerView.VERTICAL, false
            )
            detailTransaksiRevampAdapter =
                DetailTransaksiRevampAdapter(
                    mGeneralConfirmationResponse!!.transactionDataView,
                    this@KonfirmasiBukaValasRevActivity
                )
            rvDetail.adapter = detailTransaksiRevampAdapter


            rvPayment.setHasFixedSize(true)
            rvPayment.layoutManager = LinearLayoutManager(
                applicationContext, RecyclerView.VERTICAL, false
            )

            sumberTransaksiRevAdapter =
                SumberTransaksiRevAdapter(
                    mGeneralConfirmationResponse!!.amountDataView,
                    this@KonfirmasiBukaValasRevActivity
                )
            rvPayment.adapter = sumberTransaksiRevAdapter

            rvTotalPayment.setHasFixedSize(true)
            rvTotalPayment.layoutManager = LinearLayoutManager(
                applicationContext,
                RecyclerView.VERTICAL,
                false
            )
            totalTransaksiRevAdapter =
                TotalTransaksiRevAdapter(
                    mGeneralConfirmationResponse!!.totalDataView,
                    this@KonfirmasiBukaValasRevActivity
                )
            rvTotalPayment.adapter = totalTransaksiRevAdapter

            btnSubmit.text = mGeneralConfirmationResponse!!.btnText
        }
    }

    override fun onClick(p0: View?) {
        binding.apply {

            when (p0?.id) {
                R.id.cb_syarat -> GeneralSyaratActivity.launchIntent(
                    this@KonfirmasiBukaValasRevActivity,
                    mGeneralConfirmationResponse!!.snk
                )

                R.id.ll_syarat_ketentuan -> GeneralSyaratActivity.launchIntent(
                    this@KonfirmasiBukaValasRevActivity,
                    mGeneralConfirmationResponse!!.snk
                )

                R.id.cb_syarat1 -> {
                    setvalidasiButton()
                }

                R.id.ll_syarat_ketentuan1 -> {
                    isCheckedSave = if (!isCheckedSave) {
                        cbSyarat1.isChecked = true
                        true
                    } else {
                        cbSyarat1.isChecked = false
                        false
                    }
                    setvalidasiButton()
                }

                R.id.cb_riplay -> {
                    mGeneralConfirmationResponse?.riplay?.let {
                        RiplayActivity.launchIntent(this@KonfirmasiBukaValasRevActivity,
                            it)
                    }
                }

                R.id.ll_riplay -> {
                    mGeneralConfirmationResponse?.riplay?.let {
                        RiplayActivity.launchIntent(this@KonfirmasiBukaValasRevActivity,
                            it)
                    }
                }

                R.id.btnSubmit -> openPin()
            }
        }
    }

    private fun openPin() {
        val pinFragment = PinFragment(this, this)
        pinFragment.show()
    }


    fun setvalidasiButton() {
        binding.apply {
            if (cbSyarat.isChecked && cbSyarat1.isChecked && cbRiplay.isChecked) {
                btnSubmit.alpha = 1f
                btnSubmit.isEnabled = true
            } else {
                btnSubmit.alpha = 0.3f
                btnSubmit.isEnabled = false
            }
        }
    }


    override fun onSuccessGetPayment(paymentResponse: ReceiptRevampResponse?) {
        if (paymentResponse!!.immediatelyFlag == true){
            ReceiptTabunganActivity.launchIntent(
                this,
                paymentResponse,
                false, GeneralHelper.getString(R.string.btn_receipt_tabungan)
            )
        } else PendingTabunganActivity.launchIntent(
            this,
            paymentResponse,
            mUrlPending
        )
    }

    override fun onException93(message: String?) {
        val i = Intent()
        i.putExtra(Constant.TAG_ERROR_MESSAGE, message)
        setResult(RESULT_CANCELED, i)
        finish()
    }

    override fun onException01(message: String?) {
        GeneralHelper.showDialogGagalBackDescBerubah(this, Constant.TRANSAKSI_GAGAL, message)
    }

    override fun onSuccessGetPaymentEmas(receiptEmasResponse: ReceiptRevampResponse) {

    }

    override fun onSuccessGetPaymentEmasFailed(receiptGagalEmasResponse: ReceiptGagalEmasResponse) {

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constant.REQ_BUKA_REKENING && data != null) {
            when (resultCode) {
                RESULT_OK -> {
                    this.setResult(RESULT_OK, data)
                    finish()
                }

                RESULT_CANCELED -> {
                    setResult(RESULT_CANCELED, data)
                    finish()
                }

                RESULT_FIRST_USER -> {
                    this.setResult(RESULT_FIRST_USER, data)
                    finish()
                }
            }
        } else if (requestCode == Constant.REQ_FORGET_PIN) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK)
                finish()
            }
        } else if (requestCode == Constant.REQ_PETTUNJUK1 && data != null) {
            if (resultCode == RESULT_OK) {
                isSyarat = Boolean.parseBoolean(data.getStringExtra("checkbox"))
                if (isSyarat) {
                    binding.cbSyarat.isChecked = true
                    isCheckedSave = true
                } else {
                    binding.cbSyarat.isChecked = false
                    isCheckedSave = false
                }
                setvalidasiButton()
            }
        } else if (requestCode == Constant.REQ_RIPLAY && data != null) {
            if (resultCode == RESULT_OK) {
                isRiplay = Boolean.parseBoolean(data.getStringExtra("checkbox_riplay"))
                if (isRiplay) {
                    binding.cbRiplay.isChecked = true
                    isCheckedSave = true
                } else {
                    binding.cbRiplay.isChecked = false
                    isCheckedSave = false
                }
                setvalidasiButton()
            }
        } else {
            setResult(RESULT_CANCELED, data)
            finish()
        }
    }

    override fun onSendPinComplete(pin: String?) {
        presenter.getDataPayment(
            pin,
            "",
            mGeneralConfirmationResponse,
            isFromFastMenu
        )
    }

    override fun onLupaPin() {
        //TO DO routing
        if (isFromFastMenu) LupaPinFastActivity.launchIntent(this) else LupaPinActivity.launchIntent(
            this
        )
    }

}