@file:Suppress("DEPRECATION")

package id.co.bri.brimo.ui.activities.lifestyle

import android.annotation.SuppressLint
import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.webkit.CookieManager
import android.webkit.GeolocationPermissions
import android.webkit.PermissionRequest
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.core.view.isVisible
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.lifestyle.IWebviewLifestylePresenter
import id.co.bri.brimo.contract.IView.lifestyle.IWebviewLifestyleView
import id.co.bri.brimo.databinding.ActivityWebviewLifestyleBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.LifestyleConfig
import id.co.bri.brimo.domain.config.LifestyleConfig.MenuLifestyleCode
import id.co.bri.brimo.domain.extension.showToast
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.ParameterKonfirmasiModel
import id.co.bri.brimo.models.ParameterModel
import id.co.bri.brimo.models.apimodel.request.lifestyle.LifestylePatternRequest
import id.co.bri.brimo.models.apimodel.request.lifestyle.RequestMoliga
import id.co.bri.brimo.models.apimodel.response.GeneralWebviewResponse.PostData
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.lifestyle.ConfirmationLifestyleResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.travel.WebviewKonfirmasiActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom.DialogClickYesNoListener
import id.co.bri.brimo.ui.fragments.FragmentDialogNoImageRevamp
import java.net.URLEncoder
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import javax.inject.Inject

class WebviewLifestyleActivity: BaseActivity(),
    DialogExitCustom.DialogDefaultListener,
    IWebviewLifestyleView,
    DialogClickYesNoListener,
    FragmentDialogNoImageRevamp.DialogDefaultListener {

    lateinit var binding: ActivityWebviewLifestyleBinding

    private val mSecondConst = 1000
    private var mIsFinished = false
    private var mForceBack: Boolean = false
    private var isRevokeSession = false
    private var mIsPayment = false

    private var brivaNumber = ""
    private var mBillingCode = ""

    //title desc pop up exit
    private var mDialogExitTitle = ""
    private var mDialogExitDesc = ""
    private var mTextBtnYes = ""
    private var mTextBtnNo = ""

    protected var fileUploadCallback: ValueCallback<Array<Uri>>? = null

    @Inject
    lateinit var presenter: IWebviewLifestylePresenter<IWebviewLifestyleView>

    private lateinit var request: LifestylePatternRequest

    companion object {
        const val BRIMO_DOMAIN = "brimo.bri.co.id"

        private var mUrl: String = ""
        private var mConfirmationType: String = ""
        private var mReceiptType: String = ""
        private var mTextTitleBar: String = ""
        private var mSessionId: String = ""
        private var mMenuCode: String = ""
        private var mAdditionalData: String = ""
        private var mPosData: PostData? = null
        private var mIsFromBanner = false

        @JvmStatic
        fun launchIntent(caller: Activity, url : String, sessionId: String, textTitleBar: String,
                         confirmationType: String, receiptType: String, menuCode: String, postData: PostData) {
            val intent = Intent(caller, WebviewLifestyleActivity::class.java)
            mUrl = url
            mSessionId = sessionId
            mTextTitleBar = textTitleBar
            mConfirmationType = confirmationType
            mReceiptType = receiptType
            mMenuCode = menuCode
            mPosData = postData
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }

        @JvmStatic
        fun launchIntentBanner(caller: Activity, url : String, sessionId: String, textTitleBar: String,
                         confirmationType: String, receiptType: String, menuCode: String, postData: PostData,
                         isFromBanner: Boolean) {
            val intent = Intent(caller, WebviewLifestyleActivity::class.java)
            mUrl = url
            mSessionId = sessionId
            mTextTitleBar = textTitleBar
            mConfirmationType = confirmationType
            mReceiptType = receiptType
            mMenuCode = menuCode
            mPosData = postData
            mIsFromBanner = isFromBanner
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }

        fun launchIntentStatus(caller: Activity, url: String, textTitleBar: String) {
            val intent = Intent(caller, WebviewLifestyleActivity::class.java)
            mUrl = url
            mTextTitleBar = textTitleBar
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityWebviewLifestyleBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()
        setTextTimer()

        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlRevokeSession(GeneralHelper.getString(R.string.url_brimo_id_revoke_session))
        presenter.setUrlConfirmation(GeneralHelper.getString(R.string.url_dashboard_lifestyle_confirmation))
        presenter.setUrlConfirmationMoliga(GeneralHelper.getString(R.string.url_confirmation_moliga))
        presenter.setUrlPaymentMoliga(GeneralHelper.getString(R.string.url_payment_moliga))
        presenter.start()
    }

    private fun setupView() {
        GeneralHelper.setToolbarBackClose(
            this,
            binding.toolbar.toolbarBackClose,
            mTextTitleBar,
            false,
            true
        )

        initWebView()
        setupFooter()
        setupExitDialog()

    }

    private fun setupFooter() {
        with(binding) {
            if (mPosData?.message != null) {
                if (!mPosData?.message?.title.isNullOrEmpty() &&
                    !mPosData?.message?.iconPath.isNullOrEmpty()) {
                    rlPowered.visibility = View.VISIBLE
                    tvTitlePowered.text = mPosData?.message?.title

                    if (!mPosData?.message?.subtitle.isNullOrEmpty()) {
                        tvDescPowered.visibility = View.VISIBLE
                        tvDescPowered.text = mPosData?.message?.subtitle
                    } else {
                        tvDescPowered.visibility = View.GONE
                    }

                    GeneralHelper.loadImageUrlWithPlaceholder(
                        this@WebviewLifestyleActivity,
                        mPosData?.message?.iconPath,
                        ivPowered,
                        R.drawable.error_rectangle_horizontal,
                        0,
                        R.drawable.placeholder_rectangle_horizontal
                    )
                } else {
                    rlPowered.visibility = View.GONE
                }
            } else {
                rlPowered.visibility = View.GONE
            }
        }
    }

    private fun setupExitDialog() {
        mDialogExitTitle = GeneralHelper.getString(R.string.title_popup_exit_webview)
        mTextBtnYes = GeneralHelper.getString(R.string.keluar)
        when(mMenuCode) {
            LifestyleConfig.MenuLifestyleCode.MENU_MOBELANJA.menuCode -> {
                mDialogExitTitle = GeneralHelper.getString(R.string.dialog_exit_title_wv_paket)
                mDialogExitDesc = GeneralHelper.getString(R.string.dialog_exit_desc_wv_paket)
                mTextBtnNo = GeneralHelper.getString(R.string.txt_tetap_disini)
            }

            LifestyleConfig.MenuLifestyleCode.MENU_INDIHOME.menuCode -> {
                mDialogExitTitle = GeneralHelper.getString(R.string.str_konfirmasi)
                mDialogExitDesc = GeneralHelper.getString(R.string.str_konfirmasi_web_view)
                mTextBtnNo = GeneralHelper.getString(R.string.ya)
                mTextBtnYes = GeneralHelper.getString(R.string.txt_btn_no)
            }

            else -> {
                mDialogExitDesc = GeneralHelper.getString(R.string.desc_popup_exit_webview)
                mTextBtnNo = GeneralHelper.getString(R.string.txt_tetap_disini)
            }
        }
    }

    private fun encodeUrl(url: String) = URLEncoder.encode(url, "UTF-8")

    private fun setTextTimer() {
        val countDown = mSecondConst * 32
        object : CountDownTimer(countDown.toLong(), mSecondConst.toLong()) {
            override fun onTick(millisUntilFinished: Long) {
                if (mIsFinished) onFinish()
            }

            override fun onFinish() {
                if (mIsFinished) {
                    binding.progressBar1.visibility = View.GONE
                    binding.webviewLifestyle.visibility = View.VISIBLE
                } else {
                    val i = Intent()
                    i.putExtra(
                        Constant.TAG_ERROR_MESSAGE,
                        GeneralHelper.getString(R.string.halaman_gagal_coba_lagi)
                    )
                    setResult(RESULT_CANCELED, i)
                    finish()
                }
            }
        }.start()
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initWebView() {
        binding.webviewLifestyle.visibility = View.GONE
        binding.progressBar1.visibility = View.VISIBLE
        binding.webviewLifestyle.clearCache(true)
        binding.webviewLifestyle.loadUrl(mUrl)

        val webSetting: WebSettings = binding.webviewLifestyle.settings
        webSetting.javaScriptEnabled = true
        webSetting.javaScriptCanOpenWindowsAutomatically = true
        webSetting.domStorageEnabled = true
        webSetting.useWideViewPort = true
        webSetting.loadWithOverviewMode = true

        if (!GeneralHelper.isProd()) {
            when (mMenuCode) {
                LifestyleConfig.MenuLifestyleCode.MENU_PESAWAT.menuCode -> {
                    // Enable cookies. This must occur before setContentView() instantiates your WebView.
                    val cookieManager = CookieManager.getInstance()
                    binding.webviewLifestyle.clearCache(true)
                    cookieManager.removeAllCookies(null)
                    cookieManager.acceptCookie()
                    cookieManager.setAcceptCookie(true)
                    cookieManager.setAcceptThirdPartyCookies(binding.webviewLifestyle, true)

                    // Set cookie domain.
                    val domain = mUrl
                    // Set cookie path.
                    val path = "/"
                    // Set cookie name and value.
                    val name = "x-tvlk-brimo-access"
                    val value = "LThnkQ39cJEzxx8BaTYpgXQckzTjM9xS"
                    // Set cookie expiration date.
                    val expirationDateMillis =
                        System.currentTimeMillis() + 31556926L * 1000 // 1 year from now.

                    // Format expiration date in the required format.
                    val sdf =
                        SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.getDefault())
                    sdf.timeZone = TimeZone.getTimeZone("GMT")
                    val expires = sdf.format(Date(expirationDateMillis))

                    // Construct the cookie string without the 'secure' directive.
                    val cookieString = "$name=$value; path=$path; expires=$expires"

                    // Set the cookie.
                    cookieManager.setCookie(domain, cookieString)

                    // Flush cookies to disk.
                    cookieManager.flush()

                    // Set the request's mode to 'no-cors' to disable CORS (Cross-Origin Resource Sharing).
                    webSetting.allowUniversalAccessFromFileURLs = true
                    webSetting.allowFileAccessFromFileURLs = true
                    webSetting.allowFileAccess = true
                    webSetting.allowContentAccess = true
                }
            }
        }

        if (mMenuCode == MenuLifestyleCode.MENU_INDIHOME.menuCode ||
            mMenuCode == MenuLifestyleCode.MENU_MOBELANJA.menuCode) {
            binding.webviewLifestyle.settings.allowUniversalAccessFromFileURLs = true
            binding.webviewLifestyle.settings.allowFileAccessFromFileURLs = true
            binding.webviewLifestyle.settings.allowFileAccess = true
            binding.webviewLifestyle.settings.allowContentAccess = true

            binding.webviewLifestyle.settings.setGeolocationEnabled(true)
            binding.webviewLifestyle.settings.builtInZoomControls = true

            binding.webviewLifestyle.settings.databaseEnabled = true

            binding.webviewLifestyle.isVerticalScrollBarEnabled = true

            binding.webviewLifestyle.webChromeClient = (object : WebChromeClient() {

                override fun onShowFileChooser(
                    webView: WebView?,
                    filePathCallback: ValueCallback<Array<Uri>>?,
                    fileChooserParams: FileChooserParams?
                ): Boolean {
                    fileUploadCallback?.apply {
                        onReceiveValue(null)
                        fileUploadCallback = null
                    }
                    fileUploadCallback = filePathCallback
                    val intent = fileChooserParams?.createIntent()
                    try {
                        intent?.let {
                            startActivityForResult(intent, Constant.REQ_OPEN_GALERY)
                        } ?: throw ActivityNotFoundException()
                    } catch (e: ActivityNotFoundException) {
                        fileUploadCallback = null
                        showSnackbarErrorMessageRevamp(
                            GeneralHelper.getString(R.string.message_cannot_open_file_chooser),
                            ALERT_ERROR,
                            this@WebviewLifestyleActivity,
                            false
                        )
                        return false
                    }
                    return true
                }

                override fun onPermissionRequest(request: PermissionRequest?) {
                    request?.grant(request.resources)
                }

                override fun onGeolocationPermissionsShowPrompt(
                    origin: String?,
                    callback: GeolocationPermissions.Callback?
                ) {
                    callback?.invoke(origin, true, false)
                }
            })
        }

        binding.webviewLifestyle.clearCache(true)
        binding.webviewLifestyle.webViewClient = (object : WebViewClient() {

            override fun onReceivedHttpError(
                view: WebView?,
                request: WebResourceRequest?,
                errorResponse: WebResourceResponse?
            ) {
                super.onReceivedHttpError(view, request, errorResponse)
                if (errorResponse?.statusCode == 500 && errorResponse.statusCode == 403) {
                    GeneralHelper.showDialogGagalBack(
                        this@WebviewLifestyleActivity,
                        Constant.SERVER_UNDER_MAINTENANCE
                    )
                }
            }

            override fun shouldOverrideUrlLoading(
                view: WebView?,
                request: WebResourceRequest
            ): Boolean {
                mIsFinished = false
                if (mMenuCode.contentEquals(LifestyleConfig.MenuLifestyleCode.MENU_PESAWAT.menuCode)) {
                    if (request.url.toString()
                            .contains(GeneralHelper.getString(R.string.flag_timeout))
                    ) {
                        binding.webviewLifestyle.stopLoading()
                        onExceptionTimeout();
                    } else {
                        binding.webviewLifestyle.loadUrl(request.url.toString())
                    }
                } else if (request.url.toString().startsWith(
                        GeneralHelper.getString(R.string.mailto))) {
                    startActivity(Intent(Intent.ACTION_SENDTO, request.url))
                } else if (request.url.toString().startsWith(
                        GeneralHelper.getString(R.string.telp))) {
                    startActivity(Intent(Intent.ACTION_DIAL, request.url))
                } else if (mMenuCode == MenuLifestyleCode.MENU_INDIHOME.menuCode
                    && request.url.toString().contains(BRIMO_DOMAIN)
                ) {
                    presenter.getRevokeSession(mSessionId, true)
                } else if (request.url.toString().contains(GeneralHelper.getString(R.string.flag_session_timeout))) {
                    val fragmentDialogNoImageRevamp = FragmentDialogNoImageRevamp.newInstance(
                        this@WebviewLifestyleActivity,
                        GeneralHelper.getString(R.string.title_timeout_mobelanja),
                        GeneralHelper.getString(R.string.desc_timeout_mobelanja),
                        GeneralHelper.getString(R.string.txt_button_timeout_mobelanja),
                        "",
                        hilang = true,
                        aktif = true,
                        touchOutside = true
                    )
                    fragmentDialogNoImageRevamp.show(supportFragmentManager, "")
                } else {
                    binding.webviewLifestyle.loadUrl(request.url.toString())
                }

                return true
            }

            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?
            ) {
                mIsFinished = true
                binding.progressBar1.visibility = View.GONE

                if (mIsPayment) {
                    binding.webviewLifestyle.stopLoading()
                    binding.webviewLifestyle.isVisible = false
                    view?.clearHistory()
                    view?.clearCache(true)
                    view?.clearView()

                    // this code below is available if web view is still showing "Webpage not available screen".
                    binding.webviewLifestyle.loadUrl("about:blank")
                }
                super.onReceivedError(view, request, error)
            }

            override fun onPageFinished(view: WebView, url: String) {
                if (!(this@WebviewLifestyleActivity as Activity).isFinishing) {
                    super.onPageFinished(view, url)
                    mIsFinished = true

                    binding.progressBar1.visibility = View.GONE

                    val uri = Uri.parse(url)

                    if (url.contains(GeneralHelper.getString(R.string.flag_url_brimo))) {
                        if (mMenuCode == LifestyleConfig.MenuLifestyleCode.MENU_MOLIGA.menuCode) {
                            val brivaQuery = uri.getQueryParameter(
                                GeneralHelper.getString(R.string.flag_briva_number)
                            ).orEmpty()

                            brivaNumber = encodeUrl(brivaQuery)

                            if (brivaNumber.isNotEmpty() && brivaNumber != "null" && !isRevokeSession) {
                                binding.webviewLifestyle.visibility = View.GONE
                                presenter.getRevokeSession(mSessionId, true)
                                isRevokeSession = true
                            }
                        } else if (mMenuCode == MenuLifestyleCode.MENU_INDIHOME.menuCode) {
                            if (url.contains(BRIMO_DOMAIN))
                                presenter.getRevokeSession(mSessionId, true)
                        } else {
                            val additionalData = getQueryString(url)

                            mBillingCode =
                                additionalData[GeneralHelper.getString(R.string.flag_briva_number)]
                                    .toString()

                            if (additionalData.size != 1) {
                                additionalData.remove(GeneralHelper.getString(R.string.flag_briva_number))
                            }

                            mAdditionalData = Gson().toJson(additionalData)

                            if (mBillingCode.isNotEmpty() && mBillingCode != "null" &&
                                mAdditionalData.isNotEmpty() && mAdditionalData != "null"
                                && !isRevokeSession
                            ) {
                                binding.webviewLifestyle.visibility = View.GONE
                                presenter.getRevokeSession(mSessionId, true)
                                isRevokeSession = true
                                mIsPayment = true
                            }
                        }
                    }
                }
            }
        })
    }

    fun getQueryString(url: String): HashMap<String, String> {
        val uri = Uri.parse(url)

        val map = HashMap<String, String>()
        for (paramName in uri.queryParameterNames) {
            if (paramName != null) {
                val paramValue = uri.getQueryParameter(paramName)
                if (paramValue != null) {
                    map[paramName] = paramValue
                }
            }
        }

        return map
    }

    override fun onClickYes() {
        if (mMenuCode == LifestyleConfig.MenuLifestyleCode.MENU_INDIHOME.menuCode) {
            presenter.getRevokeSession(mSessionId, false)
        } else {
            val returnIntent = Intent()
            this.setResult(RESULT_CANCELED, returnIntent)
            finish()
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (event.action == KeyEvent.ACTION_DOWN) {
            when (keyCode) {
                KeyEvent.KEYCODE_BACK -> {
                    if (binding.webviewLifestyle.canGoBack() && !mForceBack) {
                        binding.webviewLifestyle.goBack()
                    } else {
                        showExitDialog()
                    }
                    return true
                }
            }
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun showExitDialog() {
        val dialogExitCustom = if (mMenuCode == LifestyleConfig.MenuLifestyleCode.MENU_INDIHOME.menuCode) {
            DialogExitCustom(
                this,
                mDialogExitTitle,
                mDialogExitDesc,
                true
            )
        } else {
            DialogExitCustom(
                this,
                mDialogExitTitle,
                mDialogExitDesc,
                mTextBtnYes,
                mTextBtnNo,
                true
            )
        }
        val ft = this.supportFragmentManager.beginTransaction()
        ft.add(dialogExitCustom, null)
        ft.commitAllowingStateLoss()
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        showExitDialog()
    }

    override fun onSuccessRevokeSession(isInquiry: Boolean) {
         if (isInquiry) {
            if (mMenuCode.isNotEmpty()) {
                when (mMenuCode) {
                    LifestyleConfig.MenuLifestyleCode.MENU_MOLIGA.menuCode -> {
                        val requestMoliga = RequestMoliga(
                            brivaNumber
                        )

                        presenter.getConfirmationMoliga(requestMoliga)
                    }

                    LifestyleConfig.MenuLifestyleCode.MENU_INDIHOME.menuCode -> {
                        this.finish()
                    }

                    else -> {
                        getConfirmationPattern()
                    }
                }
            } else {
                getConfirmationPattern()
            }
        } else {
             if (mIsFromBanner) {
                 val intentBack = Intent()
                 this.setResult(LifestyleConfig.ActivityResultLifestyle.RESULT_BACK, intentBack)
                 this.finish()
             } else {
                 val returnIntent = Intent()

                 this.setResult(RESULT_CANCELED, returnIntent)
                 finish()
             }
        }

    }

    private fun getConfirmationPattern() {
        request = LifestylePatternRequest(
            mMenuCode,
            mBillingCode,
            mAdditionalData
        )
        presenter.getConfirmation(request)
    }

    override fun onSuccessConfirmation(response: ConfirmationLifestyleResponse) {
        KonfirmasiLifestyleActivity.launchIntent(
            this,
            response,
            response.confirmationPatternCode,
            response.receiptPatternCode,
            mMenuCode,
            ""
        )
    }

    override fun onSuccessConfirmationMoliga(
        response: InquiryBrivaRevampResponse,
        urlPayment: String
    ) {
        WebviewKonfirmasiActivity.launchIntent(
            this,
            response,
            urlPayment,
            false,
            setParameter(),
            LifestyleConfig.MenuLifestyleCode.MENU_MOLIGA.menuCode
        )
    }

    fun setParameterKonfirmasi(): ParameterKonfirmasiModel {
        val parameterKonfirmasiModel = ParameterKonfirmasiModel()

        parameterKonfirmasiModel.stringLabelTujuan = GeneralHelper.getString(R.string.nomor_tujuan)
        parameterKonfirmasiModel.stringButtonSubmit =
            GeneralHelper.getString(R.string.str_konfirmasi)
        parameterKonfirmasiModel.defaultIcon = 0

        return parameterKonfirmasiModel
    }

    fun setParameter(): ParameterModel {
        val parameterModel = ParameterModel()
        parameterModel.stringLabelTujuan = GeneralHelper.getString(R.string.nomor_tujuan)
        parameterModel.stringLabelNominal = GeneralHelper.getString(R.string.nominal_pembayaran)
        parameterModel.stringButtonSubmit = GeneralHelper.getString(R.string.txt_konfirmasi)
        parameterModel.stringLabelMinimum = ""
        return parameterModel
    }

    override fun onExceptionTrxExp(errorMsg: String) {
        val returnIntent = Intent()

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, errorMsg)

        this.setResult(RESULT_CANCELED, returnIntent)
        finish()
    }

    override fun onException12(message: String) {
        val returnIntent = Intent()

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message)

        this.setResult(RESULT_CANCELED, returnIntent)
        finish()
    }

    override fun onException(message: String?) {
        val returnIntent = Intent()

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message)

        setResult(RESULT_CANCELED, returnIntent)
        finish()
    }

    @Deprecated("Deprecated in Java")
    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            Constant.REQ_PAYMENT -> {
                if (resultCode == RESULT_OK) {
                    setResult(RESULT_OK, data)
                    finish()
                } else {
                    this.setResult(RESULT_CANCELED, data)
                    finish()
                }

            }

            Constant.REQ_OPEN_GALERY -> {
                fileUploadCallback?.onReceiveValue(
                    WebChromeClient.FileChooserParams.parseResult(
                        resultCode,
                        data
                    )
                )
                fileUploadCallback = null
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        presenter.stop()
        isRevokeSession = false
        mIsPayment = false
    }

    override fun onClickBtnYes() {
        //do nothing
    }

    override fun onClickBtnNo() {
        presenter.getRevokeSession(mSessionId, false)
    }

    private fun onExceptionTimeout() {
        showSnackbarErrorMessageRevamp(
            GeneralHelper.getString(R.string.txt_timeout_pesawat),
            ALERT_ERROR, this, false
        )
    }

    override fun onPause() {
        super.onPause()
        binding.webviewLifestyle.onPause()
    }

    override fun onResume() {
        super.onResume()
        binding.webviewLifestyle.onResume()
    }

    override fun onClickYes(type: String?) {
        presenter.getRevokeSession(mSessionId, false)
    }

    override fun onClickToSafety() {
        //do nothing
    }

}