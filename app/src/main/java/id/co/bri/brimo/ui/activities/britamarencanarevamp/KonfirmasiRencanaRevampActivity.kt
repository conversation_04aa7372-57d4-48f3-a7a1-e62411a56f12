package id.co.bri.brimo.ui.activities.britamarencanarevamp

import android.app.Activity
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.DataTransaksiRevampAdapter
import id.co.bri.brimo.adapters.TotalKonfirmasiRevampAdapter
import id.co.bri.brimo.contract.IPresenter.general.IGeneralConfirmationPresenter
import id.co.bri.brimo.contract.IView.general.IGeneralConfirmationView
import id.co.bri.brimo.databinding.ActivityKonfirmasiRencanaRevampBinding
import id.co.bri.brimo.domain.ConnectionReceiver
import id.co.bri.brimo.domain.ConnectionReceiver.ConnectivityReceiverListener
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder
import id.co.bri.brimo.models.ParameterKonfirmasiModel
import id.co.bri.brimo.models.apimodel.response.*
import id.co.bri.brimo.ui.activities.*
import id.co.bri.brimo.ui.activities.KonfirmasiGeneralInvestasiActivity.Companion
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.receipt.ReceiptAbnormalRevampActivity
import id.co.bri.brimo.ui.activities.receipt.ReceiptRevampActivity
import id.co.bri.brimo.ui.activities.tartun.ReceiptTarikActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogContinueCustom
import id.co.bri.brimo.ui.customviews.dialog.DialogContinueCustom.DialogContinueDefaultListener
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom
import id.co.bri.brimo.ui.fragments.PinFragment
import javax.inject.Inject

class KonfirmasiRencanaRevampActivity : BaseActivity(), IGeneralConfirmationView,
    PinFragment.SendPin,
    View.OnClickListener,
    ConnectivityReceiverListener,
    DialogContinueDefaultListener {
    private val binding by lazy { ActivityKonfirmasiRencanaRevampBinding.inflate(layoutInflater) }

    var dataTransaksiRevampAdapter: DataTransaksiRevampAdapter? = null
    var totalKonfirmasiAdapter: TotalKonfirmasiRevampAdapter? = null
    var sourceAccountDataViews: DetailListType? = null
    var billingDetails: DetailListType? = null

    protected var mConfirmResponse: GeneralConfirmationResponse? = null
    protected var mUrlPayment: String? = null
    protected var mUrlPending: String? = null
    protected var mTitle: String? = null
    protected var mParameterKonfirmasiModel: ParameterKonfirmasiModel? = null
    protected var mIsFromTopUpOnline = false


    var connectionReceiver: ConnectionReceiver? = null
    var dialogContinueCustom: DialogContinueCustom? = null
    var connectionIndicator: BubbleShowCaseBuilder? = null

    private var timeCon = 0f
    protected var actStatus = 0

    @Inject
    lateinit var presenter: IGeneralConfirmationPresenter<IGeneralConfirmationView>

    private val TAG = "KonfirmasiRencanaRevampActivity"

    companion object {
        protected var mIsFromGeneral = true
        protected var mIsBack = false
        protected var mIsFromBriva = false
        protected var mTrxType = ""
        protected var TAG_KONFIRMASI_DATA = "konfirmasi_data"
        protected var TAG_PARAMS_KONFIRM_DATA = "params_konfirm_data"
        protected var TAG_URL_PAYMENT = "url_payment"
        protected var TAG_URL_PENDING = "url_pending"
        protected var TAG_TITLE = "url_title"

        fun launchIntent(
            caller: Activity,
            generalConfirmationResponse: GeneralConfirmationResponse?,
            urlPayment: String?,
            title: String?,
            parameterKonfirmasiModel: ParameterKonfirmasiModel?,
            isFromFastmenu: Boolean,
            isFromGeneral: Boolean,
            isBack: Boolean
        ) {
            val intent = Intent(caller, KonfirmasiRencanaRevampActivity::class.java)
            isFromFastMenu = isFromFastmenu
            mIsFromGeneral = isFromGeneral
            mIsBack = isBack
            if (generalConfirmationResponse != null) {
                try {
                    intent.putExtra(TAG_KONFIRMASI_DATA, Gson().toJson(generalConfirmationResponse))
                } catch (e: Exception) {
                    if (!GeneralHelper.isProd()) Log.e(TAG_KONFIRMASI_DATA, "launchIntent: ", e)
                }
            }
            if (parameterKonfirmasiModel != null) {
                try {
                    intent.putExtra(
                        TAG_PARAMS_KONFIRM_DATA,
                        Gson().toJson(parameterKonfirmasiModel)
                    )
                } catch (e: Exception) {
                    if (!GeneralHelper.isProd()) Log.e(TAG_PARAMS_KONFIRM_DATA, "launchIntent: ", e)
                }
            }

            //set URL service
            try {
                intent.putExtra(TAG_URL_PAYMENT, urlPayment)
                intent.putExtra(TAG_TITLE, title)
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) Log.e("TAG", "launchIntent: ", e)
            }
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        //parsing data intent
        if (intent.extras != null) {
            parseDataIntentKonfirmasi(intent.extras)
        }

        //inject Presenter dgn Dagger
        injectDependency()

        GeneralHelper.setToolbarRevamp(this, binding.tbKonfirmasi.toolbar, mTitle)

        binding.btnSubmit.setOnClickListener(this)

        //setup view
        setupView()


        val window = window
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        window.statusBarColor = ContextCompat.getColor(applicationContext, R.color.toolbar_blue)


        if (GeneralHelper.isProd()) getWindow().setFlags(
            WindowManager.LayoutParams.FLAG_SECURE,
            WindowManager.LayoutParams.FLAG_SECURE
        )

        connectionIndicator = BubbleShowCaseBuilder(this) //Activity instance
            .title(GeneralHelper.getString(R.string.status_internet)) //Any title for the bubble view
            .description(GeneralHelper.getString(R.string.status_internet_desc))
            .backgroundColor(Color.WHITE)
            .textColor(Color.BLACK)
            .targetView(binding.tbKonfirmasi.imgInternetIndicator)
            .buttonTitle(GeneralHelper.getString(R.string.ok))
            .arrowPosition(BubbleShowCase.ArrowPosition.TOP)


        binding.tbKonfirmasi.imgInternetIndicator.setOnClickListener { view -> connectionIndicator!!.show() }
    }

    /**
     * Method digunakan untuk meng-extract data Intent dari Form Activity
     *
     * @param savedInstanceState Bundle savedInstanceState
     */
    protected fun parseDataIntentKonfirmasi(savedInstanceState: Bundle?) {
        if (savedInstanceState != null) {
            try {

                //get data konfirmasi dari intent
                val tempConfirm = savedInstanceState.getString(TAG_KONFIRMASI_DATA)
                if (tempConfirm != null) {
                    mConfirmResponse =
                        Gson().fromJson(tempConfirm, GeneralConfirmationResponse::class.java)
                }

                //get data parameter dari intent
                val tempParamsKonfirm = savedInstanceState.getString(TAG_PARAMS_KONFIRM_DATA)
                if (tempParamsKonfirm != null) {
                    mParameterKonfirmasiModel =
                        Gson().fromJson(tempParamsKonfirm, ParameterKonfirmasiModel::class.java)
                }
                mUrlPayment = savedInstanceState.getString(TAG_URL_PAYMENT)
                mUrlPending = savedInstanceState.getString(TAG_URL_PENDING)
                mTitle = savedInstanceState.getString(TAG_TITLE)
                if (mTitle!!.isEmpty()) GeneralHelper.getString(R.string.toolbar_confirmation)
            } catch (e: java.lang.Exception) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "parseDataIntentInquiry: ", e)
                }
            }
        }
    }

    protected fun injectDependency() {
        activityComponent.inject(this)
        if (presenter != null) {
            presenter.setView(this)
            presenter.setUrlPayment(mUrlPayment)
            presenter.isGeneral(mIsFromGeneral)
            presenter.isBriva(mIsFromBriva)
            presenter.start()
        }
    }

    protected fun setupView() {
        sourceAccountDataViews = mConfirmResponse!!.sourceAccountDataView

        //Detail Rekening Asal
        val descriptionSource = sourceAccountDataViews?.getDescription()

        if (descriptionSource!!.isEmpty()) {
            binding.tvDescription.visibility = View.GONE
        } else {
            binding.tvDescription.visibility = View.VISIBLE
        }

        //Detail Nominal
        binding.rvDetailPayment.setHasFixedSize(true)
        binding.rvDetailPayment.layoutManager =
            LinearLayoutManager(applicationContext, RecyclerView.VERTICAL, false)
        if (mConfirmResponse!!.detailDataView != null) {
            dataTransaksiRevampAdapter =
                DataTransaksiRevampAdapter(mConfirmResponse!!.detailDataView, this)
            binding.rvDetailPayment.adapter = dataTransaksiRevampAdapter
        } else if (mConfirmResponse!!.amountDataView != null) {
            dataTransaksiRevampAdapter =
                DataTransaksiRevampAdapter(mConfirmResponse!!.amountDataView, this)
            binding.rvDetailPayment.adapter = dataTransaksiRevampAdapter
        }
        binding.rvTotalPayment.setHasFixedSize(true)
        binding.rvTotalPayment.layoutManager =
            LinearLayoutManager(applicationContext, RecyclerView.VERTICAL, false)
        if (mConfirmResponse!!.totalDataView != null) {
            totalKonfirmasiAdapter =
                TotalKonfirmasiRevampAdapter(mConfirmResponse!!.totalDataView, this)
            binding.rvTotalPayment.adapter = totalKonfirmasiAdapter
        }

        //Detail Nama dan Billing
        if (mConfirmResponse!!.billingDetail != null) {
            parseDetailTagihan()
        } else {
            binding.llDetailBilling.visibility = View.GONE
        }
        binding.btnSubmit.text = mParameterKonfirmasiModel!!.stringButtonSubmit
    }

    override fun onSuccessGetPayment(paymentResponse: PendingResponse) {
        if (paymentResponse.immediatelyFlag == true) {
            if (paymentResponse.titleImage.equals(
                    Constant.RECEIPT68,
                    ignoreCase = true
                ) || paymentResponse.titleImage.equals(Constant.RECEIPT58, ignoreCase = true)
            ) {
                ReceiptPendingActivity.launchIntent(this, paymentResponse)
            } else if (paymentResponse.titleImage.equals(Constant.RECEIPT00, ignoreCase = true)) {
                ReceiptActivity.launchIntent(this, paymentResponse)
            }
        } else PendingTransferBankLainActivity.launchIntentPending(
            this,
            paymentResponse,
            isFromFastMenu,
            mConfirmResponse,
            mUrlPending
        )
    }

    override fun onSuccessGetPaymentRevamp(receiptRevampResponse: ReceiptRevampResponse) {
        if (receiptRevampResponse.isOnProcess) {
            ReceiptAbnormalRevampActivity.launchIntent(
                this,
                receiptRevampResponse,
                isFromFastMenu,
                mParameterKonfirmasiModel!!.defaultIcon
            )
        } else {
            ReceiptRevampActivity.launchIntent(
                this,
                receiptRevampResponse,
                mParameterKonfirmasiModel!!.defaultIcon
            )
        }
    }

    override fun onSuccessGetPaymentRevampTracking(receiptRevampResponse: ReceiptRevampResponse?) {
        // do nothing
    }

    override fun onSuccesGetTarikTunai(paymentTarikResponse: PaymentTarikResponse?) {
        ReceiptTarikActivity.launchIntent(this, paymentTarikResponse)
    }

    protected fun parseDetailTagihan() {
        billingDetails = mConfirmResponse!!.billingDetail
        //Set Image Circle
        if (billingDetails?.getListType() == "image") {
            binding.llLogo.visibility = View.VISIBLE
            binding.rlInisial.visibility = View.GONE
            //load icon transaction
            GeneralHelper.loadIconTransaction(
                this,
                billingDetails?.getIconPath(),
                billingDetails?.getIconName(),
                binding.ivIcon,
                mParameterKonfirmasiModel!!.defaultIcon
            )
        } else {
            binding.llLogo.visibility = View.GONE
            binding.rlInisial.visibility = View.VISIBLE
            //Set Initial
            val title = billingDetails?.getTitle()
            binding.tvInisialTujuan.text = GeneralHelper.formatInitialName(title)
        }

        //Set Image Circle
//        if (sourceAccountDataViews!!.listType == "image") {
//            binding.llLogo2.visibility = View.VISIBLE
//            binding.rlSumberdana.visibility = View.GONE
//            //load icon transaction
//            GeneralHelper.loadIconTransaction(
//                    this,
//                    sourceAccountDataViews!!.iconPath,
//                    sourceAccountDataViews!!.iconName,
//                    binding.ivIcon2,
//                    R.drawable.bri)
//        } else {
//            binding.llLogo2.visibility = View.GONE
//            binding.rlSumberdana.visibility = View.VISIBLE
//            //Set Initial
//            val title = sourceAccountDataViews!!.title
//            binding.tvInisial.text = GeneralHelper.formatInitialName(title)
//        }

        //View
        binding.tvTitle.text = billingDetails?.getTitle()
        binding.tvSubtitle.text = billingDetails?.getSubtitle()
        binding.tvDescription.text = billingDetails?.getDescription()
    }

    override fun onExceptionTrxExpired(message: String?) {
        val returnIntent = Intent()
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message)
        setResult(RESULT_CANCELED, returnIntent)
        finish()
    }

    override fun onException(message: String?) {
        if (GeneralHelper.isContains(
                Constant.LIST_TYPE_GAGAL,
                message
            )
        ) GeneralHelper.showDialogGagalBack(this, message) else showSnackbarErrorMessage(
            message,
            ALERT_ERROR,
            this,
            false
        )
    }

    override fun onSendPinComplete(pin: String?) {
        if (presenter != null) {
            if (mTrxType.isEmpty()) {
                presenter.getDataPaymentRevamp(
                    pin,
                    mConfirmResponse,
                    isFromFastMenu,
                    mParameterKonfirmasiModel!!.isUsingC2()
                )
            } else if (mTrxType.equals(Constant.TRX_TYPE_LISTRIK, ignoreCase = true)) {
                presenter.getDataPaymentRevampListrik(pin, mConfirmResponse, isFromFastMenu)
            }
        }
    }

    override fun onLupaPin() {
        //TO DO routing
        if (isFromFastMenu) LupaPinFastActivity.launchIntent(this) else LupaPinActivity.launchIntent(
            this
        )
    }

    override fun onBackPressed() {
        if (mIsFromBriva) {
            super.onBackPressed()
        } else if (mIsBack) {
            val dialogExitCustom = DialogExitCustom(
                { cancelTransaction() },
                GeneralHelper.getString(R.string.title_dialog_exit_konfirmasi),
                GeneralHelper.getString(R.string.content_dialog_exit_konfirmasi)
            )
            val ft = this.supportFragmentManager.beginTransaction()
            ft.add(dialogExitCustom, null)
            ft.commitAllowingStateLoss()
        } else {
            this.setResult(Constant.REQ_PETTUNJUK1, Intent())
            finish()
        }
    }

    override fun onClick(view: View?) {
        if (timeCon < 1000 && timeCon != 0f) {
            openInputPIN()
        } else {
            dialogContinueCustom = DialogContinueCustom(
                this,
                GeneralHelper.getString(R.string.txt_koneksi_internet),
                GeneralHelper.getString(R.string.txt_desc_koneksi_internet)
            )
            val ft = this.supportFragmentManager.beginTransaction()
            ft.add(dialogContinueCustom!!, null)
            ft.commitAllowingStateLoss()
        }
    }

    private fun openInputPIN() {
        val pinFragment = PinFragment(this, this)
        pinFragment.show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else {
                this.setResult(RESULT_CANCELED, data)
                finish()
            }
        } else if (requestCode == Constant.REQ_FORGET_PIN) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK)
                finish()
            }
        }
    }

    override fun onException01(message: String?) {
        GeneralHelper.showDialogGagalBackDescBerubah(this, Constant.TRANSAKSI_GAGAL, message)
        setResult(RESULT_OK)
    }

    override fun onSuccessGetCashback(responses: ListAllCashbackFilterResponse?) {
    }

    override fun onCashbackBlank(message: String?) {
    }

    override fun onExceptionWH(onOnExceptionWH: onExceptionWH?) {

    }

    override fun setDefaultSaldo(
        saldoPref: Double,
        saldoStringPref: String?,
        accountPref: String?,
        saldoHoldPref: Boolean
    ) {
        // DO NOTHING
    }

    override fun onStart() {
        super.onStart()
        actStatus = 0
        connectionStatus()
    }

    override fun onStop() {
        actStatus = 1
        try {
            connectionReceiver!!.setConnectivityReceiverListener(null, 0)
            unregisterReceiver(connectionReceiver)
        } catch (e: IllegalArgumentException) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, e.message!!)
            }
        }
        super.onStop()
    }

    override fun onPause() {
        super.onPause()
        actStatus = 1
        try {
            connectionReceiver!!.setConnectivityReceiverListener(null, 0)
            unregisterReceiver(connectionReceiver)
        } catch (e: IllegalArgumentException) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, e.message!!)
            }
        }
    }

    private fun cancelTransaction() {
        super.onBackPressed()
    }

    override fun onClickContinueYes() {
        openInputPIN()
    }

    override fun onClickContinueNo() {}

    override fun onNetworkConnectionChanged(time: Float) {
        timeCon = time
        runOnUiThread {
            if (time < 1000 && time != 0f) {
                binding.tbKonfirmasi.imgInternetIndicator.setBackgroundResource(R.drawable.internet_indicator_online)
            } else {
                binding.tbKonfirmasi.imgInternetIndicator.setBackgroundResource(R.drawable.internet_indicator_offline)
            }
        }
    }

    private fun connectionStatus() {
        connectionReceiver = ConnectionReceiver()
        connectionReceiver!!.setConnectivityReceiverListener(this, 0)
        val filter = IntentFilter("android.net.conn.CONNECTIVITY_CHANGE")
        registerReceiver(connectionReceiver, filter)
    }
}