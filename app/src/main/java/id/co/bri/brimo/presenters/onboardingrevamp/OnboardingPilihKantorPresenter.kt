package id.co.bri.brimo.presenters.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingPilihKantorPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingPilihKantorView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.LocationNonCifRequest
import id.co.bri.brimo.models.apimodel.request.SelectProductRequest
import id.co.bri.brimo.models.apimodel.response.ListKantorResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardingPilihKantorPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingPilihKantorPresenter<V> where V : IMvpView, V : IOnboardingPilihKantorView {

    private var urlLocation: String = ""
    private var urlSelectProduct: String = ""

    override fun setUrlLocation(url: String) {
        urlLocation = url
    }

    override fun setUrlProduct(url: String) {
        urlSelectProduct = url
    }

    override fun getDeviceId(): String {
        return brImoPrefRepository.deviceId
    }

    override fun sendLocation(locationRequest: LocationNonCifRequest) {
        if (urlLocation.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlLocation, locationRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val listKantorResponse = response.getData(
                            ListKantorResponse::class.java
                        )
                        getView().onLocationDefault(listKantorResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value))
                            getView().onFailedLocation(restResponse.desc)


                        else getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }

    override fun sendSelectProduct(selectProductRequest: SelectProductRequest) {
        if (urlSelectProduct.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber
        view.showProgress()

        compositeDisposable.add(
            apiSource.getData(urlSelectProduct, selectProductRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        getView().onSuccessOffice()
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value))
                            getView().onExceptionStatusNotMatch()
                        else getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }
}