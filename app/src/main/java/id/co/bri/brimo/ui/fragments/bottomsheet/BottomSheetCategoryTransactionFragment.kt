package id.co.bri.brimo.ui.fragments.bottomsheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.TransactionCategoryAdapter
import id.co.bri.brimo.databinding.FragmentCategoryTransactionBinding
import id.co.bri.brimo.models.InboxStatusModel
import id.co.bri.brimo.models.apimodel.response.ActivityGroup
import id.co.bri.brimo.models.apimodel.response.FilterAktivityResponse

class BottomSheetCategoryTransactionFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentCategoryTransactionBinding

    var onSelectedListener: OnClickedCategory? = null
    private var category: InboxStatusModel? = null
    private var sSubFitur: String = ""
    private var sFitur: String = ""
    private var selectedSubFitur: List<String>? = null
    private lateinit var categoryAdapter: TransactionCategoryAdapter

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentCategoryTransactionBinding.inflate(inflater, container, false)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {
            val bottomSheet = dialog?.findViewById<FrameLayout>(R.id.design_bottom_sheet)
            bottomSheet?.let {
                it.background = ContextCompat.getDrawable(requireContext(), R.drawable.rounded_dialog_newskin)
                val behavior = BottomSheetBehavior.from(it)
                // behavior.state = BottomSheetBehavior.STATE_EXPANDED
            }
        }

        setupView()
    }

    private fun setupView() {

        binding.txtTitle.text = ContextCompat.getString(requireActivity(), R.string.txt_select_transaction_category)

        setupRecylerView()

        binding.ivClose.setOnClickListener {
            dismiss()
        }

        binding.btnSave.setOnClickListener {
            sSubFitur = categoryAdapter.getSubFilter().joinToString(",")
            selectedSubFitur = categoryAdapter.getSubFilter()
            category?.let { item -> onSelectedListener?.onClicked(item, sFitur, sSubFitur, categoryAdapter.getSubFilter(), categoryAdapter.getActivityType()) }
            dismiss()
        }

        subCategory?.let { categoryAdapter.setSubFilter(it) }
//        selectedSubFitur?.let { categoryAdapter.setSubFilter(it) }

    }

    private fun setupRecylerView() {

        categoryAdapter = TransactionCategoryAdapter(
            context = requireActivity(),
            category = mCategoryList,
            filterAktivityResponse = filterAktivityResponse,
            onItemClick = { selectedItem ->
                category = selectedItem
                sFitur = selectedItem.code
            },
            onValidationButton = { isValid ->
                binding.btnSave.isEnabled = isValid
            }
        )
        categoryAdapter.clearSelection()
        categoryAdapter.selectDefaultAll()
        if (sCategory?.isNotEmpty() == true) {
            sCategory?.let {
                categoryAdapter.updateSelectedCategoryByCode(it)
            }
        }

        binding.rvCategory.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = categoryAdapter
        }

    }

    interface OnClickedCategory {
        fun onClicked(item: InboxStatusModel, feature: String, subFeature: String, subFeatureList: List<String>, activityTypeList: List<ActivityGroup.ActivityType>)
    }

    companion object {

        private var mCategoryList: ArrayList<InboxStatusModel> = ArrayList()
        private var filterAktivityResponse: FilterAktivityResponse? = null
        private var sCategory: String? = null
        private var subCategory: List<String>? = null

        fun newInstance(model: ArrayList<InboxStatusModel>, activityResponse: FilterAktivityResponse, sFitur: String, subCategory: List<String>): BottomSheetCategoryTransactionFragment {
            mCategoryList = model
            filterAktivityResponse = activityResponse
            sCategory = sFitur
            this.subCategory = subCategory
            return BottomSheetCategoryTransactionFragment()
        }
    }
}