package id.co.bri.brimo.ui.activities

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.View
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.view.animation.DecelerateInterpolator
import android.widget.ImageView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import androidx.core.app.ActivityCompat.OnRequestPermissionsResultCallback
import com.google.android.gms.tasks.Task
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.messaging.FirebaseMessaging
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.ISplashScreenPresenter
import id.co.bri.brimo.contract.IView.ISplashScreenView
import id.co.bri.brimo.databinding.ActivitySplashScreenBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.extension.showToast
import id.co.bri.brimo.domain.helpers.DeviceIDHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.domain.helpers.InAppUpdateUtils
import id.co.bri.brimo.models.NotifikasiModel
import id.co.bri.brimo.models.apimodel.response.MaintenanceAlert
import id.co.bri.brimo.models.apimodel.response.OnboardingBrimoResponse
import id.co.bri.brimo.nfcpayment.NFCPayment
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.newskinonboarding.OnboardingVerifyEKYCActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import javax.inject.Inject

class SplashScreenActivity : NewSkinBaseActivity(), ISplashScreenView,
    OnRequestPermissionsResultCallback {

    private val TAG = "SplashScreenActivity"

    private lateinit var binding: ActivitySplashScreenBinding
    private val PERMISSIONS_NOTIF = 2
    private val SPLASH_DISPLAY_LENGTH = 700

    private var nfcPayment: NFCPayment? = null

    private var handlerCheckInternet: Handler? = null
    private var runnableCheckInternet: Runnable? = null

    private var notifikasiModel: NotifikasiModel? = null
    private var stringNotif: String? = null
    private val inAppUpdateUtils: InAppUpdateUtils by lazy {
        InAppUpdateUtils.Builder(this)
            .setOnUpdateFailed { showToast(getString(R.string.label_failed_install_app)) }
            .build()
    }
    private var hasSplashAnimated = false

    private val initiateSplashScreen: () -> Unit = {
        overridePendingTransition(0, 0)
        if (intent.flags and Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT != 0) {
            if (intent.extras != null) {
                //background notif found
                getIntentNotif()
            } else {
                // Activity was brought to front and not created,
                // Thus finishing this will get us to the last viewed activity
                finish()
            }
        }

        GeneralHelper.setHelperContext(this)
        DeviceIDHelper.setHelperContext(this)

        Handler(Looper.getMainLooper()).postDelayed({
            //get data background notif
            getIntentNotif()

            //cek permission notif
            requestPermissionNotif()
        }, SPLASH_DISPLAY_LENGTH.toLong())
    }

    private var appUpdateActivityResultLauncher: ActivityResultLauncher<IntentSenderRequest>? =
        registerForActivityResult(ActivityResultContracts.StartIntentSenderForResult()) { result ->
            if (result.resultCode != RESULT_OK) {
                Log.i(TAG, getString(R.string.label_canceled_update_app))
                initiateSplashScreen.invoke()
            }
        }

    @Inject
    lateinit var splashScreenPresenter: ISplashScreenPresenter<ISplashScreenView>


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setTheme(R.style.AppThemeNewSkinFullTransparent)
        binding = ActivitySplashScreenBinding.inflate(layoutInflater)
        setContentView(binding.root)
        checkAccessibilityService = false
        with(window) {
            setFlags(
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            )
        }
        binding.tvVersi.text = String.format(
            GeneralHelper.getString(R.string.version_app_splash),
            GeneralHelper.getLastAppVersion()
        )
        getActivityComponent().inject(this)
        checkInAppUpdate()
        updateWidget(this)
        nfcPayment = NFCPayment.getInstance(this@SplashScreenActivity)
    }

    private fun animeSplash(onFinish: () -> Unit = {}) {

        if (hasSplashAnimated) {
            onFinish()
            return
        }
        hasSplashAnimated = true
        binding.frameLayout.post {
            with(binding) {
                cover.pivotX = 0f
                logo2.alpha = 0f

                val originalWidth = logo1.width.toFloat()
                val scaleLogo1 = 0.6f
                val logo1FinalWidth = originalWidth * scaleLogo1
                val logo2Width = logo2.width.toFloat()

                val totalCombinedWidth = logo1FinalWidth + logo2Width

                val shiftLogo1 = - (totalCombinedWidth / 2) + (logo1FinalWidth / 2)
                val shiftLogo2 = logo1FinalWidth / 2

                val rotate = ObjectAnimator.ofFloat(logo1, View.ROTATION, -90f, 0f)
                val shrinkX = ObjectAnimator.ofFloat(logo1, View.SCALE_X, 1f, scaleLogo1)
                val shrinkY = ObjectAnimator.ofFloat(logo1, View.SCALE_Y, 1f, scaleLogo1)

                val logo1Intro = AnimatorSet().apply {
                    playTogether(rotate, shrinkX, shrinkY)
                    duration = 600
                    startDelay = 400
                    interpolator = DecelerateInterpolator()
                }

                val moveLogo1 = ObjectAnimator.ofFloat(logo1, View.TRANSLATION_X, 0f, shiftLogo1)
                val moveLogo2 = ObjectAnimator.ofFloat(logo2, View.TRANSLATION_X, 0f, shiftLogo2)
                val fadeInLogo2 = ObjectAnimator.ofFloat(logo2, View.ALPHA, 0f, 1f)

                val openCover = ObjectAnimator.ofFloat(cover, View.SCALE_X, 1f, 0f).apply {
                    startDelay = 100
                    duration = 600
                    interpolator = DecelerateInterpolator()
                }

                val logosMove = AnimatorSet().apply {
                    playTogether(moveLogo1, moveLogo2, fadeInLogo2, openCover)
                    duration = 700
                    interpolator = DecelerateInterpolator()
                }

                AnimatorSet().apply {
                    playSequentially(logo1Intro, logosMove)
                    addListener(object : AnimatorListenerAdapter() {
                        override fun onAnimationEnd(animation: Animator) {
                            onFinish()
                        }
                    })
                    start()
                }
            }
        }
    }

    private fun startSplashAnimation() {
        binding.root.viewTreeObserver.addOnPreDrawListener(object : ViewTreeObserver.OnPreDrawListener {
            override fun onPreDraw(): Boolean {
                binding.root.viewTreeObserver.removeOnPreDrawListener(this)
//                animeSplash {
                initiateSplashScreen()
//                }
                return true
            }
        })
    }


    fun checkInAppUpdate() {
        if (GeneralHelper.isProd()) {
            /*
            inAppUpdateUtils.checkForAppUpdate(
                activityResultLauncher = appUpdateActivityResultLauncher,
                doAfterCheckUpdate = initiateSplashScreen
            )
            */
            startSplashAnimation()
        } else {
            startSplashAnimation()
        }
    }

    override fun onResume() {
        super.onResume()
        inAppUpdateUtils.onActivityResume(appUpdateActivityResultLauncher)
    }

    override fun onStop() {
        super.onStop()
        inAppUpdateUtils.unregister()
    }

    override fun onDestroy() {
        super.onDestroy()
        appUpdateActivityResultLauncher = null
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        //start app
        initDependency()
    }

    /**
     * request permission akan memanggil onRequestPermissionsResult
     */
    private fun requestPermissionNotif() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!hasPermissions(this, *notif_permissions_33)) {
                ActivityCompat.requestPermissions(
                    this,
                    notif_permissions_33,
                    PERMISSIONS_NOTIF
                )
            } else {
                //start app
                initDependency()
            }
        } else {
            initDependency()
        }
    }

    private fun initDependency() {
        stopInternetWatcher()
        splashScreenPresenter.setView(this)
        splashScreenPresenter.setUrlOnboard(GeneralHelper.getString(R.string.url_onboarding_brimo))
        if (!GeneralHelperNewSkin.isNetworkAvailable(this)) {
            onInternetLost()
            return
        }
        initFirebase()
        splashScreenPresenter.checkAvailabilityNfc(nfcPayment?.isNfcAvailable() ?: false)
        splashScreenPresenter.start()
    }

    /**
     * Method untuk menginisiasi Firebase Token
     */
    private fun initFirebase() {
        FirebaseMessaging.getInstance().getToken().addOnCompleteListener { task: Task<String?> ->
            if (!task.isSuccessful) {
                return@addOnCompleteListener
            }
            // Get new FCM registration token
            val token = task.result
            if (token != null) {
                //subscribe token ke WS
                splashScreenPresenter.subscribeTopicAll(token)
            }
        }
    }

    /**
     * Cek NOTIF Intent
     */
    private fun getIntentNotif() {
        intent.extras?.let { intentExtra ->
            notifikasiModel = NotifikasiModel().apply {
                if (intentExtra.getString(Constant.TAG_TITLE) != null)
                    this.title = intentExtra.getString(Constant.TAG_TITLE)
                if (intentExtra.getString(Constant.TAG_MESSAGE) != null)
                    this.message = intentExtra.getString(Constant.TAG_MESSAGE)
                if (intentExtra.getString(Constant.TAG_IMAGE_POPUP) != null)
                    this.imagePopup = intentExtra.getString(Constant.TAG_IMAGE_POPUP)
                if (intentExtra.getString(Constant.TAG_TYPE) != null)
                    this.type = intentExtra.getString(Constant.TAG_TYPE)
                if (intentExtra.getString(Constant.TAG_BLAST_ID) != null)
                    this.blastId = intentExtra.getString(Constant.TAG_BLAST_ID)
                if (intentExtra.getString(Constant.TAG_PROMO_ID) != null)
                    this.promoId = intentExtra.getString(Constant.TAG_PROMO_ID)
                if (intentExtra.getString(Constant.TAG_REQ_REFNUM) != null)
                    this.requestRefnum = intentExtra.getString(Constant.TAG_REQ_REFNUM)
                if (intentExtra.getString(Constant.TAG_REFNUM) != null)
                    this.refNum = intentExtra.getString(Constant.TAG_REFNUM)
                if (intentExtra.getString(Constant.TAG_SOUND_NOTIF) != null)
                    this.sound = intentExtra.getString(Constant.TAG_SOUND_NOTIF)
                if (intentExtra.getString(Constant.TAG_TITLE_INQUIRRY) != null)
                    this.titileInquiry = intentExtra.getString(Constant.TAG_TITLE_INQUIRRY)
                if (intentExtra.getString(Constant.TAG_TYPE_INQUIRY) != null)
                    this.typeInquiry = intentExtra.getString(Constant.TAG_TYPE_INQUIRY)
                if (intentExtra.getString(Constant.TAG_URL_INQUIRY) != null)
                    this.urlInquiry = intentExtra.getString(Constant.TAG_URL_INQUIRY)
                if (intentExtra.getString(Constant.TAG_URL_CONFIRMATION) != null)
                    this.urlConfirmation = intentExtra.getString(Constant.TAG_URL_CONFIRMATION)
                if (intentExtra.getString(Constant.TAG_URL_PAYMENT) != null)
                    this.urlPayment = intentExtra.getString(Constant.TAG_URL_PAYMENT)
                if (intentExtra.getString(Constant.TAG_REQUEST_CONTENT) != null)
                    this.requestContent = intentExtra.getString(Constant.TAG_REQUEST_CONTENT)
                if (intentExtra.getString(Constant.TAG_PRODUCT_FEATURE) != null)
                    this.productFeature = intentExtra.getString(Constant.TAG_PRODUCT_FEATURE)
                if (intentExtra.getString(Constant.TAG_TITLE_BUTTON) != null)
                    this.titleButton = intentExtra.getString(Constant.TAG_TITLE_BUTTON)
                if (intentExtra.getString(Constant.TAG_TITLE_FEATURE) != null)
                    this.titleFeature = intentExtra.getString(Constant.TAG_TITLE_FEATURE)
                if (intentExtra.getString(Constant.TAG_PRODUCT_URL) != null)
                    this.url = intentExtra.getString(Constant.TAG_PRODUCT_URL)
                if (intentExtra.getString(Constant.TAG_PRODUCT_TYPE) != null)
                    this.productType = intentExtra.getString(Constant.TAG_PRODUCT_TYPE)
                if (intentExtra.getString(Constant.TAG_ALERT_FEATURE) != null)
                    this.alertFeature = intentExtra.getString(Constant.TAG_ALERT_FEATURE)
                if (intentExtra.getString(Constant.TAG_ADDITIONAL) != null)
                    this.additional = intentExtra.getString(Constant.TAG_ADDITIONAL)
                if (intentExtra.getString(Constant.TAG_URL_INQUIRY_REVAMP) != null)
                    this.urlInquiryRevamp = intentExtra.getString(Constant.TAG_URL_INQUIRY_REVAMP)
                if (intentExtra.getString(Constant.TAG_TYPE_INQUIRY_REVAMP) != null)
                    this.typeInquiryRevamp = intentExtra.getString(Constant.TAG_TYPE_INQUIRY_REVAMP)
                if (intentExtra.getString(Constant.TAG_URL_CONFIRMATION_REVAMP) != null)
                    this.urlConfirmationRevamp =
                        intentExtra.getString(Constant.TAG_URL_CONFIRMATION_REVAMP)
                if (intentExtra.getString(Constant.TAG_URL_PAYMENT_REVAMP) != null)
                    this.urlPaymentRevamp = intentExtra.getString(Constant.TAG_URL_PAYMENT_REVAMP)
            }
        }
    }

    /**
     * Menambahkan data notifikasi pada intent extras
     *
     * @param notifikasi data notifikasi
     * @param intent     intent yang akan diberi extras data notifikasi
     * @return
     */
    private fun addIntentNotifikasiExtras(
        notifikasi: NotifikasiModel,
        intent: Intent
    ): Intent {
        try {
            stringNotif = Gson().toJson(notifikasi, NotifikasiModel::class.java)
            intent.putExtra(Constant.TAG_NOTIF, stringNotif)
            intent.putExtra(Constant.TAG_NOTIF_BACKGROUND, true)
            if (!GeneralHelper.isProd())
                Log.d(TAG, "onLoadIbType: stringNotif $stringNotif")
        } catch (e: Exception) {
            if (!GeneralHelper.isProd()) Log.e(TAG, "onLoadIbType: ", e)
        }
        return intent
    }

    override fun onLoadIbType() {
        if (mIsNotValid && mIsRooted)
            return

        val mIntent = notifikasiModel?.let { notif ->
            if (notifikasiModel!!.type != null) {
                val i = Intent(this@SplashScreenActivity, NotificationRouterActivity::class.java)
                addIntentNotifikasiExtras(notif, i)
            } else {
                Intent(this@SplashScreenActivity, FastMenuNewSkinActivity::class.java)
            }
        } ?: Intent(this@SplashScreenActivity, FastMenuNewSkinActivity::class.java)

        mIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(mIntent)
        finish()
    }

    override fun onLoadAsk() {
        if (!mIsNotValid && !mIsRooted) {
            notifikasiModel?.let { notif ->
                if (notif.type != null) {
                    var mIntent = Intent(this@SplashScreenActivity, NotificationRouterActivity::class.java)
                    mIntent = addIntentNotifikasiExtras(notif, mIntent)
                    mIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
                    startActivity(mIntent)
                    finish()
                } else {
                        splashScreenPresenter.getOnboardingBrimo()
                }
                return
            }
                splashScreenPresenter.getOnboardingBrimo()
        }

    }


    override fun onLoadDeviceId(deviceID: String) {
        // Obtain the FirebaseAnalytics instance.
        mFirebaseAnalytics = FirebaseAnalytics.getInstance(this)
        FirebaseCrashlytics.getInstance().setUserId(deviceID)
    }

    override fun onSubscribeTopicSuccess() {
        //Ignored
    }

    override fun onResultSuccess() {
        //Ignored
    }

    override fun onDisconnet() {
        AskActivity.launchIntent(this, true)
    }

    override fun onSuccessOnboardingBrimo(response: OnboardingBrimoResponse?) {
        AskActivity.launchIntent(this, Gson().toJson(response))
    }

    @SuppressLint("HardwareIds")
    override fun generateIdPersistent() {
        splashScreenPresenter.saveIdPersistent(
            Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)
        )
    }

    override fun onMaintenanceAlert(response: MaintenanceAlert) {
        goToMaintenanceAlertView(response)
    }

    override fun onInternetLost() {
        startInternetWatcher()
        runOnUiThread {
            OpenBottomSheetGeneralNewSkinFragment.showDialogConnectivity(
                fragmentManager = supportFragmentManager,
                imgPath = "",
                imgName = "ic_sad_illustration",
                titleTxt = "Internet Tidak Terhubung",
                subTitleTxt = "Terjadi kendala pada koneksi internet. Silakan cek koneksi internet kamu dan coba lagi, ya.",
                btnFirstFunction = {
                    reload()
                },
                isClickableOutside = false,
                firstBtnTxt = "Coba Lagi",
                showCloseButton = false,
                isDismissOnFirstClick = false,
            )
        }
    }

    private fun startInternetWatcher() {
        handlerCheckInternet = Handler(Looper.getMainLooper())
        runnableCheckInternet = Runnable {
            if (GeneralHelperNewSkin.isNetworkAvailable(this@SplashScreenActivity)) {
                initDependency()
            } else {
                handlerCheckInternet?.postDelayed(runnableCheckInternet!!, 1000)
            }
        }
        handlerCheckInternet?.post(runnableCheckInternet!!)
    }

    private fun stopInternetWatcher() {
        handlerCheckInternet?.removeCallbacks(runnableCheckInternet!!)
    }

    private fun reload(){
        val safeIntent = Intent(this@SplashScreenActivity, SplashScreenActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        startActivity(safeIntent)
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
        finish()
    }
}