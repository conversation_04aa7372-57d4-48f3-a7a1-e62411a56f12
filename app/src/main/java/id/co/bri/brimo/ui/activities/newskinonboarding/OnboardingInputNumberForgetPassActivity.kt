package id.co.bri.brimo.ui.activities.newskinonboarding

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.widget.doAfterTextChanged
import androidx.core.widget.doOnTextChanged
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.newskinonboarding.IVerifyNoHpPresenter
import id.co.bri.brimo.contract.IView.newskinonboarding.IVerifyNoHpView
import id.co.bri.brimo.databinding.ActivityInputNumberForgetPassBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.models.apimodel.response.Country
import id.co.bri.brimo.models.apimodel.response.OtpPhoneResponse
import id.co.bri.brimo.models.apimodel.response.SendPhoneResponse
import id.co.bri.brimo.models.apimodel.response.newskinonboarding.OnboardingErrorResponse
import id.co.bri.brimo.ui.activities.FastMenuNewSkinActivity
import id.co.bri.brimo.ui.activities.base.NewSkinOnboardingBaseActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.BottomSheetSearchCountryCode
import id.co.bri.brimo.ui.fragments.bottomsheet.NewSkinOTPChannel
import id.co.bri.brimo.util.extension.setOnSingleClickListener
import id.co.bri.brimo.util.extension.view.addMinLengthCharValidation
import id.co.bri.brimo.util.extension.view.isFragmentShown
import id.co.bri.brimo.util.getParcelableCompat
import id.co.bri.brimo.util.getSavedLanguage
import javax.inject.Inject

class OnboardingInputNumberForgetPassActivity : NewSkinOnboardingBaseActivity(), IVerifyNoHpView {
    @Inject
    lateinit var presenter: IVerifyNoHpPresenter<IVerifyNoHpView>
    private lateinit var binding: ActivityInputNumberForgetPassBinding

    private var currentCountry = Country(
        countryName = "Indonesia",
        countryCode = "+62",
        countryIcon = ""
    )
    private val codeCountryID: String = "+62"
    private val savedCodeCountry = "code_country"
    private val savedImageCountry = "image_country"
    private val data: SendPhoneResponse? by lazy {
        intent.getParcelableCompat<SendPhoneResponse>(RESP_DATA_EXTRA)
    }

    private val launcher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            setResult(RESULT_OK)
            finish()
        }
    }

    private var requestCode: Int = Constant.REQ_UBAH_PIN

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInputNumberForgetPassBinding.inflate(layoutInflater)
        setContentView(binding.root)

        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)

        injectDependencies()
        checkPermission()
        setupButton()
        setupView()
        requestCode = intent.getIntExtra("request_code", Constant.REQ_UBAH_PIN)

        checkSaveInstanceState(savedInstanceState)
    }


    private fun checkSaveInstanceState(savedInstanceState: Bundle?) {
        if (savedInstanceState != null) {
            currentCountry = currentCountry.copy(
                countryCode = savedInstanceState.getString(savedCodeCountry, "+62"),
                countryIcon = savedInstanceState.getString(savedImageCountry, "")
            )
            if (currentCountry.countryIcon.isNotEmpty())
                GeneralHelper.loadImageUrl(
                    this,
                    currentCountry.countryIcon,
                    binding.etNoHp.imageOption,
                    R.drawable.vallas_idr,
                    0
                )
            binding.etNoHp.setPrefix(currentCountry.countryCode)
        }
    }

    private fun injectDependencies() {
        activityComponent.inject(this)
        presenter.apply {
            view = this@OnboardingInputNumberForgetPassActivity
            start()
//            setUrlVerifyNoHp(GeneralHelper.getString(R.string.url_onboarding_brimo_send_otp_phone_v4))
        }
    }

    private fun setupView() {
        binding.apply {
            GeneralHelperNewSkin.setToolbarWithoutNav(
                toolbar.toolbar,
                GeneralHelper.getString(R.string.masukkan_nomor_hp)
            )
            checkPhoneNumber()
            updateCountry(getDefaultCountry())
        }
    }

    private fun getDefaultCountry(): Country {
        currentCountry = data?.countries?.find { it.countryCode == codeCountryID } ?: currentCountry
        return currentCountry
    }

    private fun updateCountry(country: Country) = with(binding) {
        currentCountry = country
        etNoHp.setPrefix(country.countryCode)
        if (country.countryIcon.isNotEmpty()) {
            etNoHp.setImageOption(country.countryIcon)
        } else {
            etNoHp.setImageOption(R.drawable.vallas_idr) // Default Icon
        }
    }

    private fun setupButton() = with(binding) {
        btnVerify.setOnSingleClickListener {
            showOtpChannel()
        }
        etNoHp.setOnOptionClickListener {
            showDropdownCountryCode(data)
        }
    }

    private fun showDropdownCountryCode(data: SendPhoneResponse?) {
        if (supportFragmentManager.isFragmentShown(BottomSheetSearchCountryCode.TAG)) return

        val countryList = data?.countries.orEmpty()
        val bottomSheet = BottomSheetSearchCountryCode.newInstance(countryList, currentCountry)
        bottomSheet.setOnItemSelectedListener { country ->
            updateCountry(country)
        }
        bottomSheet.show(supportFragmentManager, BottomSheetSearchCountryCode.TAG)
    }

    private fun showOtpChannel() {
        NewSkinOTPChannel(this, supportFragmentManager, getSavedLanguage(this)) {
//            val intent = OnboardingOtpNewSkinActivity.launchIntent(
//                this@OnboardingInputNumberForgetPassActivity,
//                method = "",
//                requestCode = requestCode
//            )
//            launcher.launch(intent)
            OnboardingOtpNewSkinActivity.launchIntent(
                this@OnboardingInputNumberForgetPassActivity,
                method = "",
                requestCode = requestCode
            )
        }
    }


    private fun checkPermission() {
        requestOnboardingPermissions(object : PermissionCallback {
            override fun onPermissionGranted() {
                // do nothing
            }

            override fun onPermissionDenied() {
                showAlertPermission(getString(R.string.notes_need_permission))
            }
        })
    }

    private fun checkPhoneNumber() {
        binding.apply {
            etNoHp.editText.doOnTextChanged { text, _, _, _ ->
                updateButtonState((text?.length ?: 0) >= 7, btnVerify)
            }
            etNoHp.editText.doAfterTextChanged { text ->
                val cleanedText = cleanPhoneNumber(text.toString())
                if (cleanedText != text.toString()) {
                    etNoHp.editText.setText(cleanedText)
                    etNoHp.editText.setSelection(cleanedText.length)
                }
            }
            etNoHp.addMinLengthCharValidation(
                minLength = 7,
                errorText = "Minimal harus 7 digit angka"
            )
        }
    }

    private fun cleanPhoneNumber(input: String): String {
        var cleanedText = input.replace("\\D".toRegex(), "")

        if (currentCountry.countryCode == codeCountryID) {
            cleanedText = cleanedText.removePrefix("0").removePrefix("62")
        }

        return if (cleanedText.length > 13) cleanedText.substring(0, 13) else cleanedText
    }

    private var startActivityResult: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            setResult(RESULT_OK)
            finish()
        }
    }

    private val onBackPressedCallback: OnBackPressedCallback =
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
            }
        }

    override fun onSuccessVerifyNoHp(otpPhoneResponse: OtpPhoneResponse) {
//        OnboardingOtpNewSkinActivity.launchIntent(this, "")
    }

    override fun onReadyToLogin(readyResponse: OnboardingErrorResponse) {
        val intent = Intent(this@OnboardingInputNumberForgetPassActivity, FastMenuNewSkinActivity::class.java)
        startActivityResult.launch(intent)
    }

    companion object {
        const val RESP_DATA_EXTRA = "resp_data_extra"

        fun launchIntent(context: Context, requestCode: Int): Intent {
            return Intent(context, OnboardingInputNumberForgetPassActivity::class.java).apply {
                putExtra("request_code", requestCode)
            }
        }
    }

}