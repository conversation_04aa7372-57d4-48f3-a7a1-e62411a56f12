package id.co.bri.brimo.contract.IView.base;

import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.apimodel.response.cc.DetailCcSofResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.SaldoReponse;

public interface IBaseInquiryView extends IMvpView {
    void onSuccessGetConfirmation(GeneralConfirmationResponse brivaConfirmationResponse);

    void onException93(String message);

    int getAmount();

    void onSubmit();

    void setDefaultSaldo(double saldo, String saldoString, String account, boolean saldoHold);

    void onSucessSofCcSaldo(DetailCcSofResponse detailCcSofResponse, SaldoReponse saldoReponse);

    void onShowDialogCcAsSof();

    void onSuccessChangeSof(String desc);

    ParameterKonfirmasiModel setParameterKonfirmasi ();
}
