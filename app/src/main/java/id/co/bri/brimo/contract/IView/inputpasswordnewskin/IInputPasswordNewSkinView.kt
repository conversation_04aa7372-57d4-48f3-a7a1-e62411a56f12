package id.co.bri.brimo.contract.IView.inputpasswordnewskin

import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse
import id.co.bri.brimo.models.apimodel.response.LoginResponse
import id.co.bri.brimo.models.apimodel.response.PromoResponse

interface IInputPasswordNewSkinView : IMvpView {
    val password: String?

    fun onSuccessLogin()

    fun showError(isVisible: Boolean, desc: String)

    fun onExceptionLoginExceed(response: ExceptionResponse?)

    fun onChangeMNV(desc: String?, loginResponse: LoginResponse?)

    fun onDeviceChanged(desc: String?, loginResponse: LoginResponse?)

    fun onChangeDevice(otpExpiredSeconds: Int?)

    fun onSuccessGetDetailItem(promoResponse: PromoResponse?)

}