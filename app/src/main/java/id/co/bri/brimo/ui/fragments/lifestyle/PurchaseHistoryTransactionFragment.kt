package id.co.bri.brimo.ui.fragments.lifestyle

import android.app.SearchManager
import android.content.Context
import android.content.Context.INPUT_METHOD_SERVICE
import android.content.res.ColorStateList
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.ImageView
import android.widget.RadioButton
import androidx.appcompat.widget.AppCompatRadioButton
import androidx.appcompat.widget.SearchView.OnQueryTextListener
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.widget.NestedScrollView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout.OnRefreshListener
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.lifestyle.transaction.filterbase.FilterBaseParentAdapter
import id.co.bri.brimo.adapters.lifestyle.transaction.riwayatpembelian.ListPurchaseHistoryAdapter
import id.co.bri.brimo.contract.IPresenter.transaksisaya.riwayatpembelian.IPurchaseHistoryPresenter
import id.co.bri.brimo.contract.IView.transaksisaya.riwayatpembelian.IPurchaseHistoryView
import id.co.bri.brimo.databinding.FragmentBottomFilterJenisPembelianBinding
import id.co.bri.brimo.databinding.FragmentBottomRentangWaktuBinding
import id.co.bri.brimo.databinding.FragmentPurchaseHistoryBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.LifestyleConfig
import id.co.bri.brimo.domain.extension.*
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimo.models.DurasiModel
import id.co.bri.brimo.models.apimodel.request.PartnerIdRequest
import id.co.bri.brimo.models.apimodel.request.transaksisaya.riwayatpembelian.PurchaseHistoryRequest
import id.co.bri.brimo.models.apimodel.request.transaksisaya.riwayatpembelian.SearchFilterRequest
import id.co.bri.brimo.models.apimodel.response.*
import id.co.bri.brimo.models.apimodel.response.lifestyle.transaction.emptyresult.LifestyleTransactionEmptyResultResponseData
import id.co.bri.brimo.models.apimodel.response.lifestyle.transaction.filterbase.FilterBaseFilterDataResponse
import id.co.bri.brimo.models.apimodel.response.lifestyle.transaction.filterbase.FilterBaseResponseData
import id.co.bri.brimo.models.apimodel.response.lifestyle.transaction.history.PurchaseHistoryResponse
import id.co.bri.brimo.models.apimodel.response.lifestyle.transaction.history.PurchaseHistoryResponseData
import id.co.bri.brimo.ui.activities.ReceiptInboxActivity
import id.co.bri.brimo.ui.activities.ReceiptPendingActivity
import id.co.bri.brimo.ui.activities.lifestyle.ReceiptLifestyleActivity
import id.co.bri.brimo.ui.activities.lifestyle.ReceiptLifestyleActivity.Companion.launchIntentIsFromConfirmation
import id.co.bri.brimo.ui.activities.lifestyle.shopping.WebviewShoppingActivity
import id.co.bri.brimo.ui.activities.receipt.ReceiptAbnormalRevampActivity
import id.co.bri.brimo.ui.activities.receipt.ReceiptRevampActivity
import id.co.bri.brimo.ui.activities.travel.FormBusActivity
import id.co.bri.brimo.ui.activities.travel.ReceiptTravelTrainActivity
import id.co.bri.brimo.ui.activities.voucher.VoucherActivity
import id.co.bri.brimo.ui.fragments.BaseFragment
import id.co.bri.brimo.ui.fragments.CalendarMutationFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.FragmentBottomJenisPembelianFilter
import id.co.bri.brimo.ui.fragments.bottomsheet.FragmentBottomRentangWaktuFilter
import org.threeten.bp.LocalDate
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import kotlin.properties.Delegates


class PurchaseHistoryTransactionFragment : BaseFragment(), IPurchaseHistoryView,
    CalendarMutationFragment.OnSelectDate, OnRefreshListener, View.OnClickListener {

    private var _binding: FragmentPurchaseHistoryBinding? = null
    private val binding get() = _binding!!

    private var purchaseHistoryListAdapter: ListPurchaseHistoryAdapter? = null
    private val filterBaseParentAdapter by lazy { FilterBaseParentAdapter() }

    private var skeletonPurchase: SkeletonScreen? = null
    private var selectedRadioButton: AppCompatRadioButton? = null
    private var currentDuration: DurasiModel? = null
    private var bindingRentangWaktu: FragmentBottomRentangWaktuBinding? = null
    private var bindingJenisPembelian: FragmentBottomFilterJenisPembelianBinding? = null
    private var filterBaseDataResponse: List<FilterBaseFilterDataResponse>? = null
    private var selectedFilter: String? = null
    private var tempSelectedFilter: String? = null

    private var filterBaseResponseData: FilterBaseResponseData? = null
    private var purchaseHistoryList: PurchaseHistoryResponseData? = null

    private var isLoading = false
    private var isSearch = false

    private var saveStartDateStr = ""
    private var saveEndDateStr = ""
    private var lastIdRequest = ""
    private var activityPeriodRequest = Constant.TRANSACTION_TYPE_ALL
    private var activityTypeListRequest: List<String> = emptyList()
    private var dateFromRequest = ""
    private var dateToRequest = ""
    private var searchRequest = ""
    private var activityList: MutableList<PurchaseHistoryResponse?> = ArrayList()
    private var handler: Handler = Handler(Looper.getMainLooper())
    private var emptyResultResponse: LifestyleTransactionEmptyResultResponseData? = null

    private var onCheckedRadioButton by Delegates.observable(false) { _, _, newValue ->
        bindingRentangWaktu?.apply {
            if (context == null) return@observable
            selectedRadioButton?.let { radioButton ->
                with(radioButton) {
                    isChecked = newValue
                    jumpDrawablesToCurrentState()
                    this.isChecked = newValue
                    this.buttonTintList = if (newValue)
                        requireContext().fetchColorStateList(R.color.primaryBlue80)
                    else
                        requireContext().fetchColorStateList(R.color.neutralLight40)
                }
            }
        }
    }

    private var onButtonResetRentangWaktuEnable by Delegates.observable(false) { _, _, newValue ->
        bindingRentangWaktu?.apply {
            if (context == null) return@observable
            btnResetFilterRentangWaktu.apply {
                isEnabled = newValue
                background = if (this.isEnabled)
                    requireContext().fetchDrawable(R.drawable.bg_white_border_blue)
                else
                    requireContext().fetchDrawable(R.drawable.bg_rounded_grey_line)

                requireContext().fetchColor(if (this.isEnabled) R.color.primaryBlue80 else R.color.neutralLight40)
            }

            with(btnApplyRentangWaktu) {
                this.isEnabled = newValue
                this.background = if (this.isEnabled) {
                    requireContext().fetchDrawable(R.drawable.button_primary_bg)
                } else {
                    requireContext().fetchDrawable(R.drawable.bg_button_disable)
                }

                this.setTextColor(
                    if (this.isEnabled) {
                        requireContext().fetchColor(R.color.white)
                    } else {
                        requireContext().fetchColor(R.color.neutral_baseWhite)
                    }
                )
            }
        }
    }

    private var onCheckedCheckBox by Delegates.observable(false) { _, _, newValue ->
        bindingJenisPembelian?.apply {
            if (context == null) return@observable
            changeButtonsStateJenisPembelian(newValue)
            onButtonResetJenisPembelianEnable = newValue
        }
    }

    private var onButtonResetJenisPembelianEnable by Delegates.observable(false) { _, _, newValue ->
        bindingJenisPembelian?.apply {
            if (context == null) return@observable
            btnResetFilterJenisPembelian.apply {
                isEnabled = newValue
                background = if (this.isEnabled)
                    requireContext().fetchDrawable(R.drawable.bg_white_border_blue)
                else
                    requireContext().fetchDrawable(R.drawable.bg_rounded_grey_line)

                requireContext().fetchColor(if (this.isEnabled) R.color.primaryBlue80 else R.color.neutralLight40)
            }

            with(btnApplyFilterJenisPembelian) {
                this.isEnabled = newValue
                this.background = if (this.isEnabled) {
                    requireContext().fetchDrawable(R.drawable.button_primary_bg)
                } else {
                    requireContext().fetchDrawable(R.drawable.bg_button_disable)
                }

                this.setTextColor(
                    if (this.isEnabled) {
                        requireContext().fetchColor(R.color.white)
                    } else {
                        requireContext().fetchColor(R.color.neutral_baseWhite)
                    }
                )
            }
        }
    }

    private var onButtonDateFromChangedListener by Delegates.observable(false) { _, _, newValue ->
        bindingRentangWaktu?.apply {
            if (context == null) return@observable
            with(clDateFrom) {
                isEnabled = newValue
                background = if (this.isEnabled)
                    requireContext().fetchDrawable(R.drawable.bg_rounded_grey_neutral_light_30)
                else
                    requireContext().fetchDrawable(R.drawable.bg_full_grey_border_grey)

                imgDateIconFrom.background = if (clDateFrom.isEnabled)
                    requireContext().fetchDrawable(R.drawable.ic_calendar_outline_white)
                else
                    requireContext().fetchDrawable(R.drawable.ic_date_grey)

                setOnClickListener {
                    selectStartDate()
                }
            }
        }
    }

    private var onButtonDateToChangedListener by Delegates.observable(false) { _, _, newValue ->
        bindingRentangWaktu?.apply {
            if (context == null) return@observable
            with(clDateTo) {
                isEnabled = newValue
                background = if (this.isEnabled)
                    requireContext().fetchDrawable(R.drawable.bg_rounded_grey_neutral_light_30)
                else
                    requireContext().fetchDrawable(R.drawable.bg_full_grey_border_grey)

                imgDateIconTo.background = if (clDateTo.isEnabled)
                    requireContext().fetchDrawable(R.drawable.ic_calendar_outline_white)
                else
                    requireContext().fetchDrawable(R.drawable.ic_date_grey)

                setOnClickListener {
                    selectStartDate()
                }
            }
        }
    }

    @Inject
    lateinit var presenter: IPurchaseHistoryPresenter<IPurchaseHistoryView>

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPurchaseHistoryBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        injectDependency()
        handleSwipeRefresh()
        initHistoryTransactionListAdapter()
        binding.btnResetAllFilters.setOnClickListener(this)
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.apply {
            setUrlPurchaseHistory(getString(R.string.url_history_transaction_list))
            isSearch = getPurchaseHistoryResponse(PurchaseHistoryRequest("0"), true)
            setUrlSearchFilter(getString(R.string.url_search_filter_transaction_list))
            setUrlInboxDetail(GeneralHelper.getString(R.string.url_activity_detail))
            setUrlFilterBase(getString(R.string.url_history_filter_base))
            getFilterBase()
            setUrlWebViewTugu(GeneralHelper.getString(R.string.url_webview_new))
            setUrlFormBus(GeneralHelper.getString(R.string.url_form_bus))
            start()
        }
    }

    private fun performSearchFilter() {
        val searchManager = requireActivity().getSystemService(Context.SEARCH_SERVICE) as SearchManager
        binding.searchTransaction.searchPurchaseHistory.apply {
            setSearchableInfo(searchManager.getSearchableInfo(requireActivity().componentName))
            maxWidth = Int.MAX_VALUE

            setOnQueryTextFocusChangeListener { _, isActive ->
                if (isActive) {
                    setBackgroundResource(
                        GeneralHelper.getImageId(
                            requireActivity(), "bg_blue_border"
                        )
                    )
                } else {
                    setBackgroundResource(
                        GeneralHelper.getImageId(
                            requireActivity(), "bg_grey_border"
                        )
                    )
                }
            }

            setOnQueryTextListener(object : OnQueryTextListener {
                override fun onQueryTextSubmit(query: String?): Boolean {
                    searchRequest = query.toString()
                    showSkeleton(true)

                    val request = SearchFilterRequest(
                        activityPeriod = activityPeriodRequest.orEmpty(),
                        activityType = activityTypeListRequest,
                        dateFrom = dateFromRequest,
                        dateTo = dateToRequest,
                        lastId = "0",
                        search = searchRequest
                    )
                    isSearch = presenter.getPurchaseHistorySearchFilter(request, true)
                    showEmptyResult(false)
                    return true
                }

                override fun onQueryTextChange(newText: String?): Boolean {
                    val isEmpty = newText?.isEmpty()

                    if (isEmpty == true) {
                        view?.clearFocus()
                        hideKeyboard()
                        if (isSearch) {
                            requireActivity().runOnUiThread {
                                showSkeleton(true)
                                binding.apply {
                                    layoutError.layoutErrorMyTrx.visibility = View.GONE
                                    rvPurchaseHistoryList.visibility = View.VISIBLE
                                }
                                isSearch = presenter.getPurchaseHistoryResponse(PurchaseHistoryRequest("0"), false)
                                isSearch = !isSearch
                            }
                        }
                    }

                    return true
                }
            })

            val closeButtonId = this.findViewById<ImageView>(R.id.search_close_btn).id
            val closeButton = this.findViewById<ImageView>(closeButtonId)
            closeButton.setOnClickListener {
                closeButton.setBackgroundColor(
                    ContextCompat.getColor(
                        requireContext(), R.color.transparent
                    )
                )

                this.setQuery("", false)
                this.clearFocus()
                hideKeyboard()
                showEmptyResult(false)
                showSkeleton(true)
                if (searchRequest.isNotEmpty()) {
                    searchRequest = ""
                    activityList.clear()
                }
                if (isSearch) {
                    val countCbx = filterBaseParentAdapter.checkBoxCount()
                    if (countCbx > 0 || selectedFilter != null) {
                        binding.viewDateRange.margin(left = 8F)
                        handleSearchFilterRequest()
                    }
                } else {
                    requireActivity().runOnUiThread {
                        run {
                            binding.rvPurchaseHistoryList.visibility = View.VISIBLE
                            isSearch = presenter.getPurchaseHistoryResponse(PurchaseHistoryRequest("0"), false)
                        }
                    }
                }
            }
        }
    }

    private fun hideKeyboard() {
        binding.apply {
            searchTransaction.searchPurchaseHistory.clearFocus()
            val imm = requireActivity().getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(searchTransaction.searchPurchaseHistory.windowToken, 0)
        }
    }

    private fun initFilterMargin() {
        binding.apply {
            if (btnResetAllFilters.visibility == View.GONE) {
                viewDateRange.margin(left = 0F)
            }
        }
    }

    private fun handleResetAllFilters() {
        binding.apply {

            // reset checked radio button, checked checkbox, and search
            // set visibility of this button to gone
            // and call api before filtered

            if (searchRequest.isEmpty()) {
                showButtonResetAllFilters(true)
                isSearch = presenter.getPurchaseHistoryResponse(PurchaseHistoryRequest("0"), false)
                rvPurchaseHistoryList.smoothScrollToPosition(0)
                activityList.clear()
            } else {
                showButtonResetAllFilters(false)
                handleSearchFilterRequest()
            }

            filterBaseDataResponse?.forEach { filterData ->
                filterData.isSelected = false
                filterData.filterBaseFeatureDatumResponses.forEach { filterFeature ->
                    filterFeature.isSelected = false
                }
            }

            showEmptyResult(false)
            viewDateRange.margin(left = 0F)
            isCheckedRadioButton(false)
            selectedFilter = null
            tempSelectedFilter = null
            onCheckedRadioButton = false
            showButtonFilterBaseUi(false)
            onCheckedCheckBox = false
            activityTypeListRequest = emptyList()
            activityPeriodRequest = Constant.TRANSACTION_TYPE_ALL
            changeButtonsStateJenisPembelian(isCounting = false)
            hideKeyboard()
            showSkeleton(true)
            showButtonResetAllFilters(false)
        }
    }

    /* Button Reset All Filters */
    private fun showButtonResetAllFilters(isShow: Boolean) {
        binding.btnResetAllFilters.visibility = if (isShow) View.VISIBLE else View.GONE
    }
    /* End of Button Reset All Filters */

    /* Swipe/Pull to Refresh */
    private fun handleSwipeRefresh() {
        binding.swRefreshPurchaseHistory.setOnRefreshListener(this)
    }
    /* End of Swipe/Pull to Refresh */

    /* Init Adapter with Pagination */
    private fun initHistoryTransactionListAdapter() {
        purchaseHistoryListAdapter = ListPurchaseHistoryAdapter(requireContext(),
            onItemClickSeeDetails = {
                presenter.apply {
                    setTrxType(it.trxType)
                    getInboxDetail(it.referenceNumber)
                }
            },
            onItemClickBuyAgain = {
                if (it.featureCode.isNotEmpty()) {
                    val codeMenu = it.featureCode
                    when (LifestyleConfig.MenuLifestyleCode.values().find { menuLifeStyleCode -> menuLifeStyleCode.menuCode == codeMenu }) {
                        LifestyleConfig.MenuLifestyleCode.MENU_VOUCHER_GAME -> {
                            VoucherActivity.launchIntent(requireActivity(), Constant.Voucher.GAME.name)
                        }

                        LifestyleConfig.MenuLifestyleCode.MENU_VOUCHER_STREAMING -> {
                            VoucherActivity.launchIntent(requireActivity(), Constant.Voucher.STREAMING.name)
                        }

                        LifestyleConfig.MenuLifestyleCode.MENU_BUS_SHUTTLE -> {
                            presenter.getFormBus()
                        }

                        else -> presenter.getWebViewTugu(PartnerIdRequest(it.partnerId))
                    }
                }
            }
        )
        binding.rvPurchaseHistoryList.apply {
            layoutManager = LinearLayoutManager(requireContext())
            setHasFixedSize(true)
            isNestedScrollingEnabled = false
            adapter = purchaseHistoryListAdapter
            handleLoadMore()
            showSkeleton(true)
        }
    }

    private fun handleLoadMore() {
        binding.apply {
            nestedScrollView.setOnScrollChangeListener(NestedScrollView.OnScrollChangeListener { _, _, scrollY, _, oldScrollY ->
                val lastChild = nestedScrollView.getChildAt(nestedScrollView.childCount - 1)
                if (lastChild != null) {
                    if ((scrollY >= (lastChild.measuredHeight - nestedScrollView.measuredHeight)) && scrollY > oldScrollY && !isLoading) {
                        loadingMorePurchaseHistoryTransaction()
                    }
                }
            })
        }
    }

    private fun loadingMorePurchaseHistoryTransaction() {
        if (activityList.last() == null) return
        activityList.add(null)
        val scrollPosition = activityList.size - 1
        purchaseHistoryListAdapter?.submitList(activityList)
        purchaseHistoryListAdapter?.notifyItemInserted(scrollPosition)

        if (scrollPosition < activityList.size) {
            handler.postDelayed({

                val request = SearchFilterRequest(
                    activityPeriod = activityPeriodRequest.orEmpty(),
                    activityType = activityTypeListRequest,
                    dateFrom = dateFromRequest,
                    dateTo = dateToRequest,
                    lastId = lastIdRequest,
                    search = searchRequest
                )
                presenter.getPurchaseHistorySearchFilter(request, false)
            }, 1000)
        }
    }
    /* End of Init Adapter with Pagination */

    /* Filter Date Range */
    private fun showFilterDateRangeBottomSheet() {
        var currentSelectedFilter: String? = null
        val bind = FragmentBottomRentangWaktuBinding.inflate(LayoutInflater.from(requireContext()))
        val bottomSheet = FragmentBottomRentangWaktuFilter(
            bind.root,
            cancelable = true,
            onTouchOutsideCancelable = true,
            onDraggable = true
        ) {
            handleBackSystemFilterDateRange(bind, currentSelectedFilter)
        }

        bindingRentangWaktu = bind

        val selectedId = bind.rgFilter.checkedRadioButtonId

        bind.apply {
            // radio button condition
            rgFilter.setOnCheckedChangeListener { group, checkedId ->
                selectedRadioButton = group.findViewById(checkedId)
                val rbPosition = group.indexOfChild(selectedRadioButton) / 2

                if (selectedId == -1) {
                    if (selectedRadioButton?.isChecked == true) {
                        when (rbPosition) {
                            FIRST_POS -> {
                                onCheckedRadioButton = true
                                rbToday.checkedRadioButtonColor()
                                onButtonResetRentangWaktuEnable = true
                                onButtonDateFromChangedListener = false
                                onButtonDateToChangedListener = false
                                currentDuration = CalendarHelper.getDurasiHariIni()
                                currentSelectedFilter = Constant.ZERO_DAY_FILTER
                            }

                            SECOND_POS -> {
                                onCheckedRadioButton = true
                                rbThisWeek.checkedRadioButtonColor()
                                onButtonResetRentangWaktuEnable = true
                                onButtonDateFromChangedListener = false
                                onButtonDateToChangedListener = false
                                currentDuration = CalendarHelper.getDurasiMingguIni()
                                currentSelectedFilter = Constant.ONE_WEEK_FILTER
                            }

                            THIRD_POS -> {
                                onCheckedRadioButton = true
                                rbThisMonth.checkedRadioButtonColor()
                                onButtonResetRentangWaktuEnable = true
                                onButtonDateFromChangedListener = false
                                onButtonDateToChangedListener = false
                                currentDuration = CalendarHelper.getDurasiBulanIni()
                                currentSelectedFilter = Constant.ONE_MONTH_FILTER
                            }

                            FOURTH_POS -> {
                                onCheckedRadioButton = true
                                rbChooseDate.checkedRadioButtonColor()
                                onButtonResetRentangWaktuEnable = true
                                onButtonDateFromChangedListener = true
                                onButtonDateToChangedListener = true
                                currentDuration = null
                                currentSelectedFilter = Constant.TRANSACTION_TYPE_ALL
                            }
                        }
                        activityPeriodRequest = currentSelectedFilter.orEmpty()
                    }
                }
            }

            btnResetFilterRentangWaktu.setOnClickListener {
                resetRadioButtonFilterDateRange(this)
            }

            // implement applied filter and call api
            btnApplyRentangWaktu.setOnClickListener {
                val countCbx = filterBaseParentAdapter.checkBoxCount()
                if (selectedRadioButton?.isChecked == true) {
                    if (tempSelectedFilter != null) {
                        tempSelectedFilter = null
                        currentSelectedFilter = null
                    }

                    selectedFilter = if (currentSelectedFilter.isNullOrEmpty() && selectedFilter != null) {
                        activityPeriodRequest
                    } else {
                        currentSelectedFilter
                    }

                    if (!onCheckedRadioButton) {
                        isCheckedRadioButton(false)
                        when {
                            countCbx > 0 || selectedFilter != null -> {
                                if (countCbx == 0 || selectedFilter == null) {
                                    showButtonResetAllFilters(false)
                                    binding.viewDateRange.margin(left = 0F)
                                } else {
                                    showButtonResetAllFilters(true)
                                    binding.viewDateRange.margin(left = 8F)
                                }
                                handleSearchFilterRequest()
                            }

                            else -> {
                                if (countCbx != 0) {
                                    showButtonResetAllFilters(true)
                                    showEmptyResult(false)
                                    binding.viewDateRange.margin(left = 8F)
                                    showSkeleton(true)
                                    handleSearchFilterRequest()
                                } else {
                                    showButtonResetAllFilters(false)
                                    showEmptyResult(false)
                                    binding.viewDateRange.margin(left = 0F)
                                    showSkeleton(true)
                                    isSearch = presenter.getPurchaseHistoryResponse(PurchaseHistoryRequest("0"), false)
                                }
                            }
                        }
                    } else {
                        isCheckedRadioButton(true)
                        showButtonResetAllFilters(countCbx > 0 || selectedFilter != null)
                        showSkeleton(true)
                        // call api search filter here
                        if (selectedFilter != null) {
                            if (selectedFilter.equals(Constant.TRANSACTION_TYPE_ALL)) {
                                val dateNow = CalendarHelper.getFullDateNow()
                                val initDateNow = dateNow.convertDate(
                                    Constant.DATE_FORMAT_DD_MMM_YYYY,
                                    Constant.DATE_FORMAT_YYYY_MM_DD
                                )
                                val dateFrom = if (saveStartDateStr.isEmpty()) initDateNow else saveStartDateStr.convertDate(
                                    Constant.DATE_FORMAT_DD_MMM_YYYY,
                                    Constant.DATE_FORMAT_YYYY_MM_DD
                                )
                                dateFromRequest = dateFrom
                                val dateTo = if (saveEndDateStr.isEmpty()) initDateNow else saveEndDateStr.convertDate(
                                    Constant.DATE_FORMAT_DD_MMM_YYYY,
                                    Constant.DATE_FORMAT_YYYY_MM_DD
                                )
                                dateToRequest = dateTo
                            }

                            handleSearchFilterRequest()
                        }
                    }
                } else {
                    when {
                        countCbx != 0 -> {
                            showEmptyResult(false)
                            showButtonResetAllFilters(true)
                            isCheckedRadioButton(false)
                            binding.viewDateRange.margin(left = 8F)
                            resetRadioButtonFilterDateRange(this)
                            showSkeleton(true)
                            handleSearchFilterRequest()
                        }

                        searchRequest.isNotEmpty() -> {
                            showEmptyResult(false)
                            showButtonResetAllFilters(false)
                            isCheckedRadioButton(false)
                            binding.viewDateRange.margin(left = 8F)
                            resetRadioButtonFilterDateRange(this)
                            showSkeleton(true)
                            handleSearchFilterRequest()
                        }

                        else -> {
                            showEmptyResult(false)
                            showButtonResetAllFilters(false)
                            isCheckedRadioButton(false)
                            resetRadioButtonFilterDateRange(this)
                            binding.viewDateRange.margin(left = 0F)
                            showSkeleton(true)
                            isSearch = presenter.getPurchaseHistoryResponse(PurchaseHistoryRequest(lastId = "0"), true)
                        }
                    }
                }
                // dismiss
                bottomSheet.dismissNow()
            }
        }

        initCheckedRadioButtonFilterDateRange()

        if (!childFragmentManager.isStateSaved) {
            bottomSheet.show(childFragmentManager, "")
        }
    }

    private fun handleBackSystemFilterDateRange(
        bind: FragmentBottomRentangWaktuBinding,
        currentSelectedFilter: String?
    ) {
        bind.apply {
            if (!btnApplyRentangWaktu.isPressed) {
                if ((currentSelectedFilter == selectedFilter && currentSelectedFilter == null) ||
                    (currentSelectedFilter != selectedFilter && currentSelectedFilter != null)
                ) return@apply
            }
        }
    }

    private fun initCheckedRadioButtonFilterDateRange() {
        bindingRentangWaktu?.apply {
            val dateNow = CalendarHelper.getFullDateNow()
            if (tvDateFrom.text.isEmpty() && tvDateTo.text.isEmpty()) {
                tvDateFrom.text = dateNow
                tvDateTo.text = dateNow
                imgDateIconFrom.background = requireContext().fetchDrawable(R.drawable.ic_date_grey)
                imgDateIconTo.background = requireContext().fetchDrawable(R.drawable.ic_date_grey)
            }

            if (selectedFilter != null) {
                handleCheckedRadioButtonDateRangeUi(dateNow, selectedFilter)
                activityPeriodRequest = selectedFilter
            } else if (tempSelectedFilter != null) {
                handleCheckedRadioButtonDateRangeUi(dateNow, tempSelectedFilter)
                activityPeriodRequest = tempSelectedFilter
                selectedFilter = tempSelectedFilter
                tempSelectedFilter = null
            }
        }
    }

    private fun resetRadioButtonFilterDateRange(bind: FragmentBottomRentangWaktuBinding) {
        bind.apply {
            val dateNow = CalendarHelper.getFullDateNow()
            rgFilter.clearCheck()
            tvDateFrom.text = dateNow
            tvDateTo.text = dateNow
            onButtonResetRentangWaktuEnable = false
            onButtonDateFromChangedListener = false
            onButtonDateToChangedListener = false
            tempSelectedFilter = selectedFilter
            selectedFilter = null
            activityPeriodRequest = ""
            onCheckedRadioButton = false
            binding.viewDateRange.margin(left = 8F)
        }
    }

    private fun isCheckedRadioButton(isChecked: Boolean) {
        val countCbx = filterBaseParentAdapter.checkBoxCount()
        binding.apply {
            if (isChecked) {
                with(viewDateRange) {
                    margin(left = 8F)
                    background = requireContext().fetchDrawable(R.drawable.button_primary_fragment_bg)
                    tvDateRange.setTextColor(requireContext().fetchColor(R.color.white))
                }
                imgArrowDownDateRange.imageTintList = requireContext().fetchColorStateList(R.color.white)
                tvDateRange.text = selectedRadioButton?.text.toString()
            } else {
                if (countCbx == 0 || selectedFilter == null) {
                    viewDateRange.margin(left = 0F)
                } else {
                    viewDateRange.margin(left = 8F)
                }
                with(viewDateRange) {
                    background = requireContext().fetchDrawable(R.drawable.bg_full_grey_border_grey)
                    tvDateRange.setTextColor(requireContext().fetchColor(R.color.neutralDark40))
                }
                imgArrowDownDateRange.imageTintList = requireContext().fetchColorStateList(R.color.neutralLight80)
                tvDateRange.text = GeneralHelper.getString(R.string.date_range)
            }
        }
    }

    private fun handleCheckedRadioButtonDateRangeUi(dateNow: String, filterStr: String?) {
        bindingRentangWaktu?.apply {
            when {
                filterStr.equals(Constant.ZERO_DAY_FILTER) -> {
                    rbToday.isChecked = true
                    rbToday.checkedRadioButtonColor()
                    isCheckedRadioButton(true)
                    onButtonResetRentangWaktuEnable = true
                    onButtonDateFromChangedListener = false
                    onButtonDateToChangedListener = false
                }

                filterStr.equals(Constant.ONE_WEEK_FILTER) -> {
                    rbThisWeek.isChecked = true
                    rbThisWeek.checkedRadioButtonColor()
                    isCheckedRadioButton(true)
                    onButtonResetRentangWaktuEnable = true
                    onButtonDateFromChangedListener = false
                    onButtonDateToChangedListener = false
                }

                filterStr.equals(Constant.ONE_MONTH_FILTER) -> {
                    rbThisMonth.isChecked = true
                    rbThisMonth.checkedRadioButtonColor()
                    isCheckedRadioButton(true)
                    onButtonResetRentangWaktuEnable = true
                    onButtonDateFromChangedListener = false
                    onButtonDateToChangedListener = false
                }

                filterStr.equals(Constant.TRANSACTION_TYPE_ALL) -> {
                    rbChooseDate.isChecked = true
                    rbChooseDate.checkedRadioButtonColor()
                    isCheckedRadioButton(true)
                    onButtonResetRentangWaktuEnable = true
                    onButtonDateFromChangedListener = true
                    onButtonDateToChangedListener = true
                    tvDateFrom.text = saveStartDateStr.ifEmpty { dateNow }
                    tvDateTo.text = saveEndDateStr.ifEmpty { dateNow }
                }
            }
        }
    }
    /* End of Filter Date Range */

    /* Filter Base/Jenis Pembelian */
    override fun onSuccessFilterBase(response: FilterBaseResponseData) {
        if (emptyResultResponse != null) return

        filterBaseDataResponse = response.filterBaseFilterDatumResponses

        val filterBaseDataCopy = filterBaseDataResponse?.map { filterDataResponse ->
            filterDataResponse.copy(filterBaseFeatureDatumResponses = filterDataResponse.filterBaseFeatureDatumResponses.map { featureDataResponse ->
                featureDataResponse.copy()
            })
        }
        filterBaseResponseData = response
        binding.apply {
            searchTransaction.searchPurchaseHistory.isVisible = true
            viewDateRange.isVisible = true
            viewDateRange.margin(left = 0F)
            viewPurchaseKind.isVisible = true
        }
        filterBaseParentAdapter.submitData(filterBaseDataCopy ?: emptyList(), true)
    }

    private fun showFilterPurchaseTypeBottomSheet() {
        val bind = FragmentBottomFilterJenisPembelianBinding.inflate(LayoutInflater.from(requireContext()))
        val bottomSheet = FragmentBottomJenisPembelianFilter(
            bind.root,
            cancelable = true,
            onTouchOutsideCancelable = true,
            onDraggable = true
        ) {}

        bindingJenisPembelian = bind

        bind.apply {
            with(searchTransaction.searchPurchaseHistory) {
                queryHint = getString(R.string.txt_search_jenis_pembelian)

                val searchManager = requireActivity().getSystemService(Context.SEARCH_SERVICE) as SearchManager
                setSearchableInfo(searchManager.getSearchableInfo(requireActivity().componentName))
                maxWidth = Int.MAX_VALUE

                setOnQueryTextFocusChangeListener { _, isActive ->
                    if (isActive) {
                        setBackgroundResource(
                            GeneralHelper.getImageId(
                                requireActivity(), "bg_blue_border"
                            )
                        )
                    } else {
                        setBackgroundResource(
                            GeneralHelper.getImageId(
                                requireActivity(), "bg_grey_border"
                            )
                        )
                    }
                }

                setOnQueryTextListener(object : OnQueryTextListener {
                    override fun onQueryTextSubmit(query: String?): Boolean = false

                    override fun onQueryTextChange(newText: String?): Boolean {
                        val unFilteredList = filterBaseParentAdapter.getData()
                        val unFilteredListCopy = unFilteredList.map { filterDataResponse ->
                            filterDataResponse.copy(filterBaseFeatureDatumResponses = filterDataResponse.filterBaseFeatureDatumResponses.map { it.copy() })
                        }
                        val txtStr = newText.toString()
                        if (txtStr.isEmpty()) {
                            view.clearFocus()
                            hideKeyboard()
                            filterBaseParentAdapter.submitData(unFilteredListCopy, false)
                        } else {
                            val filteredList = unFilteredListCopy.map { filterData ->
                                val filteredFeature = filterData.filterBaseFeatureDatumResponses.filter { featureData ->
                                    featureData.featureName.lowercase().contains(txtStr.lowercase())
                                }
                                filterData.copy(filterBaseFeatureDatumResponses = filteredFeature)
                            }.filter {
                                it.filterBaseFeatureDatumResponses.isNotEmpty()
                            }
                            filterBaseParentAdapter.submitFilteredData(filteredList)
                        }
                        return true
                    }
                })
            }

            with(rvFilterBase) {
                layoutManager = LinearLayoutManager(requireContext())
                setHasFixedSize(true)
                itemAnimator = null
                adapter = filterBaseParentAdapter
            }

            filterBaseParentAdapter.onCheckboxChangeParentAdapter = { _ ->
                changeButtonsStateJenisPembelian(filterBaseParentAdapter.checkBoxCount() > 0)
            }

            btnResetFilterJenisPembelian.setOnClickListener {
                // Remove all checkboxes and disable reset button
                resetCheckBoxFilterBase(this)
            }

            btnApplyFilterJenisPembelian.setOnClickListener {
                val checkboxCount = filterBaseParentAdapter.checkBoxCount()
                val filterBaseDataLocal = filterBaseParentAdapter.getData()
                filterBaseDataResponse = filterBaseDataLocal

                showSkeleton(true)
                showEmptyResult(false)
                activityList.clear()
                onButtonResetJenisPembelianEnable = when {
                    checkboxCount > 0 -> {
                        showButtonResetAllFilters(true)
                        showButtonFilterBaseUi(true)
                        binding.viewDateRange.margin(left = 8F)
                        handleSearchFilterRequest()
                        true
                    }

                    selectedFilter != null -> {
                        showButtonResetAllFilters(true)
                        showButtonFilterBaseUi(true)
                        binding.viewDateRange.margin(left = 8F)
                        handleSearchFilterRequest()
                        true
                    }

                    searchRequest.isNotEmpty() -> {
                        showButtonResetAllFilters(false)
                        showButtonFilterBaseUi(true)
                        binding.viewDateRange.margin(left = 0F)
                        handleSearchFilterRequest()
                        true
                    }

                    else -> {
                        showButtonResetAllFilters(false)
                        showButtonFilterBaseUi(true)
                        binding.viewDateRange.margin(left = 0F)
                        isSearch = presenter.getPurchaseHistoryResponse(PurchaseHistoryRequest("0"), false)
                        false
                    }
                }
                bottomSheet.onDismissBottomSheet(false)
            }
        }

        initCheckedCheckBoxFilterBase()

        if (!childFragmentManager.isStateSaved) {
            bottomSheet.show(childFragmentManager, "")
        }
    }

    private fun initCheckedCheckBoxFilterBase() {
        val filterBaseDataCopy = filterBaseDataResponse?.map { filterDataResponse ->
            filterDataResponse.copy(filterBaseFeatureDatumResponses = filterDataResponse.filterBaseFeatureDatumResponses.map { featureDataResponse ->
                featureDataResponse.copy()
            })
        }
        filterBaseParentAdapter.submitData(filterBaseDataCopy ?: emptyList(), true)
        changeButtonsStateJenisPembelian(filterBaseParentAdapter.checkBoxCount() > 0)
    }

    private fun resetCheckBoxFilterBase(bind: FragmentBottomFilterJenisPembelianBinding) {
        bind.apply {
            // reset selected item and button state
            filterBaseParentAdapter.resetSelectedCheckbox()
            onCheckedCheckBox = false
            onButtonResetJenisPembelianEnable = false
        }
    }

    private fun changeButtonsStateJenisPembelian(isCounting: Boolean) {
        bindingJenisPembelian?.apply {
            if (isCounting) {
                with(btnResetFilterJenisPembelian) {
                    isEnabled = true
                    background = requireContext().fetchDrawable(R.drawable.bg_white_border_blue)
                    setTextColor(requireContext().fetchColor(R.color.primaryBlue80))
                }
                with(btnApplyFilterJenisPembelian) {
                    this.isEnabled = true
                    this.background = requireContext().fetchDrawable(R.drawable.button_primary_fragment_bg)
                    this.setTextColor(requireContext().fetchColor(R.color.white))
                }
            } else {
                with(btnResetFilterJenisPembelian) {
                    isEnabled = false
                    background = requireContext().fetchDrawable(R.drawable.bg_rounded_grey_line)
                    setTextColor(requireContext().fetchColor(R.color.neutralLight40))
                }
                with(btnApplyFilterJenisPembelian) {
                    this.isEnabled = false
                    this.background = requireContext().fetchDrawable(R.drawable.bg_button_disable)
                    this.setTextColor(requireContext().fetchColor(R.color.neutral_baseWhite))
                }
            }
        }
    }

    private fun showButtonFilterBaseUi(isChecked: Boolean) {
        binding.apply {
            activityTypeListRequest = filterBaseParentAdapter.getTrxType()
            val countCbx = filterBaseParentAdapter.checkBoxCount()
            if (isChecked) {
                with(viewPurchaseKind) {
                    viewDateRange.margin(left = 8F)
                    background = requireContext().fetchDrawable(R.drawable.button_primary_fragment_bg)
                    tvPurchaseKind.setTextColor(requireContext().fetchColor(R.color.white))
                }
                imgArrowDown.imageTintList = requireContext().fetchColorStateList(R.color.white)

                with(tvBadgeCount) {
                    visibility = View.VISIBLE
                    text = filterBaseParentAdapter.checkBoxCount().toString()
                }
            } else {
                if (selectedFilter == null || countCbx == 0) viewDateRange.margin(left = 0F) else viewDateRange.margin(left = 8F)
                with(viewPurchaseKind) {
                    background = requireContext().fetchDrawable(R.drawable.bg_full_grey_border_grey)
                    tvPurchaseKind.setTextColor(requireContext().fetchColor(R.color.neutralDark40))
                }
                tvBadgeCount.visibility = View.GONE
                imgArrowDown.imageTintList = requireContext().fetchColorStateList(R.color.neutralLight80)
            }
        }
    }
    /* End of Filter Base/Jenis Pembelian */

    /* Handle CTA to Receipt */
    override fun onSuccessGetInboxDetail(receiptResponse: ReceiptResponse?) {
        when {
            receiptResponse?.pendingResponses?.titleImage.equals(
                Constant.RECEIPT68,
                ignoreCase = true
            ) || receiptResponse?.pendingResponses?.titleImage.equals(
                Constant.RECEIPT58,
                ignoreCase = true
            ) -> {
                ReceiptPendingActivity.launchIntentInbox(activity, receiptResponse?.pendingResponses)
            }

            receiptResponse?.pendingResponses?.titleImage.equals(
                Constant.RECEIPT00,
                ignoreCase = true
            ) -> {
                ReceiptInboxActivity.launchIntent(activity, receiptResponse?.pendingResponses)
            }
        }
    }

    override fun onSuccessGetDetailInboxRevamp(response: ReceiptRevampInboxResponse) {
        if (response.receiptRevampResponse != null) {
            when {
                response.receiptRevampResponse.isOnProcess || response.receiptRevampResponse.titleImage.equals(
                    Constant.RECEIPT68_REVAMP, true
                ) || response.receiptRevampResponse.titleImage.equals(
                    Constant.RECEIPT58_REVAMP, true
                ) -> {
                    ReceiptAbnormalRevampActivity.launchIntentReceipt(
                        activity, response.receiptRevampResponse, false, 0
                    )
                }

                else -> {
                    ReceiptRevampActivity.launchIntentReceipt(
                        activity, response.receiptRevampResponse, 0
                    )
                }
            }
        }
    }

    override fun onSuccessGetReceiptPattern(response: ReceiptRevampInboxResponse) {
        if (response.receiptRevampResponse != null) {
            when {
                response.receiptRevampResponse.isOnProcess -> {
                    ReceiptAbnormalRevampActivity.launchIntentReceipt(
                        activity,
                        response.receiptRevampResponse,
                        false,
                        0
                    )
                }

                else -> {
                    ReceiptLifestyleActivity.launchIntent(
                        requireActivity(),
                        response.receiptRevampResponse,
                        response.receiptRevampResponse.receiptPatternCode,
                        response.receiptRevampResponse.featureCode
                    )
                }
            }
        }
    }

    override fun onSuccessGetReceiptPatternBanner(response: ReceiptRevampInboxResponse) {
        if (response.receiptRevampResponse != null) {
            when {
                response.receiptRevampResponse.isOnProcess -> {
                    ReceiptAbnormalRevampActivity.launchIntentReceipt(
                        activity,
                        response.receiptRevampResponse,
                        false,
                        0
                    )
                }

                else -> {
                    ReceiptLifestyleActivity.launchIntentIsFromConfirmation(
                        requireActivity(),
                        response.receiptRevampResponse,
                        response.receiptRevampResponse.receiptPatternCode,
                        response.receiptRevampResponse.featureCode,
                        false
                    )
                }
            }
        }
    }

    override fun onSuccessKai(receiptTravelTrainResponse: ReceiptTravelTrainResponse?) {
        ReceiptTravelTrainActivity.launchIntentReceipt(activity, receiptTravelTrainResponse)
    }

    override fun onSuccessGetReceiptRevamp(receiptResponse: ReceiptRevampInboxResponse) {
        val receiptPattern = receiptResponse.receiptRevampResponse.receiptPatternCode.orEmpty()

        if (!receiptPattern.isNullOrEmpty()) {
            ReceiptLifestyleActivity.launchIntent(
                requireActivity(),
                receiptResponse.receiptRevampResponse,
                receiptResponse.receiptRevampResponse.receiptPatternCode,
                ""
            )
        } else {
            when {
                receiptResponse.receiptRevampResponse.isOnProcess -> {
                    ReceiptAbnormalRevampActivity.launchIntentReceipt(
                        activity,
                        receiptResponse.receiptRevampResponse,
                        false,
                        0
                    )
                }

                else -> {
                    ReceiptRevampActivity.launchIntentReceipt(
                        activity,
                        receiptResponse.receiptRevampResponse,
                        0
                    )
                }
            }
        }
    }
    /* End of Handle CTA to Receipt */

    /* Handle Success Purchase History/Show Purchase History List */
    override fun onSuccessPurchaseHistory(response: PurchaseHistoryResponseData, isRefresh: Boolean) {
        purchaseHistoryList = response
        binding.apply {
            if (isRefresh) {
                activityList.clear()
                swRefreshPurchaseHistory.isRefreshing = false
            } else if (activityList.isNotEmpty() && activityList.last() == null) {
                val lastPosition = activityList.size - 1
                activityList.removeAt(lastPosition)
                purchaseHistoryListAdapter?.notifyItemRemoved(lastPosition)
            }

            searchTransaction.searchPurchaseHistory.isVisible = true
            scrollFilter.isVisible = true
            rvPurchaseHistoryList.isVisible = true
            viewDateRange.isVisible = true
            lastIdRequest = response.lastId

            if (response.lastId == "0") {
                return@apply
            } else {
                initFilterMargin()
                showSkeleton(false)
                activityList.addAll(response.purchaseHistoryResponseList)
                swRefreshPurchaseHistory.margin(top = 20F)
                showPurchaseHistoryList(activityList)

                // Enable search functionality if response is successful
                performSearchFilter()
            }
            isLoading = false
        }
    }

    private fun showPurchaseHistoryList(purchaseHistoryResponseList: List<PurchaseHistoryResponse?>) {
        showEmptyResult(false)
        purchaseHistoryListAdapter?.submitList(purchaseHistoryResponseList)
    }
    /* End of Handle Success Purchase History/Show Purchase History List */

    /* Handle Success WebView Tugu */
    override fun onSuccessGetWebViewTugu(generalWebviewResponse: GeneralWebviewResponse) {
        WebviewShoppingActivity.launchIntent(
            requireActivity(),
            generalWebviewResponse.webviewData.url,
            GeneralHelper.getString(R.string.txt_belanja_harian),
            WebviewShoppingActivity.ORDER_NO,
            LifestyleConfig.Lifestyle.MOBELANJA,
            GeneralHelper.getString(R.string.dialog_exit_title_wv_paket),
            GeneralHelper.getString(R.string.dialog_exit_desc_wv_paket),
            generalWebviewResponse.sessionId
        )
    }

    override fun onSuccessGetForm(cityFormResponse: CityFormResponse) {
        FormBusActivity.launchIntent(requireActivity(), cityFormResponse)
    }
    /* End of Handle Success WebView Tugu*/

    /* Search Filter Request */
    private fun handleSearchFilterRequest() {
        showEmptyResult(false)

        if (activityPeriodRequest.isEmpty()) {
            activityPeriodRequest = Constant.TRANSACTION_TYPE_ALL
        }

        val request = SearchFilterRequest(
            activityPeriod = activityPeriodRequest.orEmpty(),
            activityType = activityTypeListRequest,
            dateFrom = dateFromRequest,
            dateTo = dateToRequest,
            lastId = "0",
            search = searchRequest
        )
        presenter.getPurchaseHistorySearchFilter(request, true)
    }
    /* End of Search Filter Request */

    /* Handle All of Exceptions */
    override fun onExceptionWithDialogFiltered(response: LifestyleTransactionEmptyResultResponseData) {
        showSkeleton(false)
        emptyResultResponse = null
        emptyResultResponse = LifestyleTransactionEmptyResultResponseData(response.referenceNumber, response.subtitle, response.title, response.urlImage)

        // This code below to handle the exception image appears while user reset search
        // from back pressed keyboard or by pressing the X search form
        val countCbx = filterBaseParentAdapter.checkBoxCount()
        if (!isSearch && searchRequest.isEmpty() && (selectedFilter != null || countCbx > 0)) showLayoutError(response)
//        if (!isSearch && searchRequest.isEmpty()) return
        if (!isSearch && searchRequest.isNotEmpty()) {
            searchRequest = ""
            showSkeleton(true)
            showEmptyResult(false)
            isSearch = presenter.getPurchaseHistoryResponse(PurchaseHistoryRequest(lastId = "0"), true)
        } else {
            // handle exception image appears
            showLayoutError(response)
        }
    }

    private fun showLayoutError(response: LifestyleTransactionEmptyResultResponseData) {
        purchaseHistoryListAdapter?.submitList(emptyList())
        binding.apply {
            swRefreshPurchaseHistory.isRefreshing = false
            swRefreshPurchaseHistory.margin(top = 0F)
            showEmptyResult(true)
            val errorImage = if (isSearch) {
                R.drawable.ic_search_not_found
            } else {
                R.drawable.ic_no_transaction
            }
            with(layoutError) {
                GeneralHelper.loadImageUrl(
                    requireContext(),
                    response.urlImage,
                    imgNoLifestyleTransaction,
                    errorImage,
                    0
                )
                tvTitle.text = response.title
                tvDescription.text = response.subtitle
                rvPurchaseHistoryList.isVisible = false
            }
        }
    }

    override fun onException(message: String) {
        binding.apply {
            if (skeletonPurchase != null) {
                when {
                    purchaseHistoryList == null && filterBaseResponseData == null -> {
                        GeneralHelper.showDialogGagalBack(requireActivity(), message)
                    }

                    purchaseHistoryList != null && filterBaseResponseData != null -> {
                        GeneralHelper.showBottomDialog(requireActivity(), message)
                    }

                    purchaseHistoryList != null && filterBaseResponseData == null -> {}
                    else -> GeneralHelper.showBottomDialog(requireActivity(), message)
                }

                swRefreshPurchaseHistory.isRefreshing = false
                searchTransaction.searchPurchaseHistory.enableAllIf(false)
                viewDateRange.setOnClickListener(null)
                viewPurchaseKind.setOnClickListener(null)
            }
        }
    }
    /* End of Handle All of Exceptions */

    /* Handle Skeleton Visibility */
    private fun showSkeleton(isShow: Boolean) {
        binding.apply {
            if (isShow) {
                skeletonPurchase = Skeleton.bind(binding.rvPurchaseHistoryList)
                    .adapter(purchaseHistoryListAdapter)
                    .shimmer(true)
                    .angle(20)
                    .frozen(false)
                    .duration(1200)
                    .color(R.color.shimmer_color)
                    .count(5)
                    .load(R.layout.skeleton_purchase_transaction)
                    .show()

                searchTransaction.searchPurchaseHistory.enableAllIf(false)
                if (selectedFilter != null) {
                    binding.apply {
                        viewDateRange.margin(left = 8F)
                        viewDateRange.background = requireContext().fetchDrawable(R.drawable.button_primary_fragment_bg)
                        tvDateRange.setTextColor(requireContext().fetchColor(R.color.white))
                    }
                    viewDateRange.setOnClickListener(null)
                }

                if (activityTypeListRequest.isNotEmpty()) {
                    binding.apply {
                        viewPurchaseKind.margin(left = 8F)
                        viewPurchaseKind.background = requireContext().fetchDrawable(R.drawable.button_primary_fragment_bg)
                        tvPurchaseKind.setTextColor(requireContext().fetchColor(R.color.white))
                    }
                    viewPurchaseKind.setOnClickListener(null)
                }
            } else {
                skeletonPurchase?.hide()
                searchTransaction.searchPurchaseHistory.enableAllIf(true)
                viewDateRange.setOnClickListener(this@PurchaseHistoryTransactionFragment)
                viewPurchaseKind.setOnClickListener(this@PurchaseHistoryTransactionFragment)
            }
        }
    }
    /* End of Skeleton Visibility */

    private fun showEmptyResult(isShow: Boolean) {
        with(binding.layoutError) {
            layoutErrorMyTrx.isVisible = isShow
            imgNoLifestyleTransaction.setImageResource(
                GeneralHelper.getImageId(
                    requireActivity(),
                    GeneralHelper.getString(R.string.ilustrasi_search_not_found)
                )
            )
        }
    }

    /* Handle Selected Date */
    private fun selectStartDate() {
        val calendarFragment = CalendarMutationFragment(this)
        val args = Bundle()
        if (currentDuration != null) {
            if (currentDuration?.startDateString != null && !currentDuration?.startDateString.isNullOrEmpty() && (currentDuration?.startMonth ?: 0) > 0) {
                args.putString(
                    Constant.TAG_START_DATE,
                    currentDuration?.startDateString
                )
            }
            if (currentDuration?.endDateString != null && !currentDuration?.endDateString.isNullOrEmpty() && ((currentDuration?.endMonth ?: 0) > 0)) {
                args.putString(
                    Constant.TAG_END_DATE,
                    currentDuration?.endDateString
                )
            }
        }
        args.putBoolean(Constant.TAG_PICK_START_DATE, true)
        args.putBoolean(Constant.TAG_PICK_DATE, true)
        args.putBoolean(Constant.TAG_MAX_TODAY, true)
        calendarFragment.arguments = args
        calendarFragment.isCancelable = true
        calendarFragment.show(
            childFragmentManager,
            Constant.REQ_CALENDAR.toString()
        )
    }

    override fun onSelectStart(dateSelect: LocalDate) {
        currentDuration = DurasiModel(
            dateSelect.dayOfMonth,
            dateSelect.monthValue,
            dateSelect.year
        )
        val dateStr = currentDuration?.startDateMutasiStringddMMMyyyy?.run {
            substring(0, 3) + substring(3, 6) + substring(6, 11)
        }.orEmpty()
        bindingRentangWaktu?.tvDateFrom?.text = dateStr
        saveStartDateStr = dateStr
    }

    override fun onSelectEnd(dateSelect: LocalDate) {
        currentDuration = DurasiModel(
            dateSelect.dayOfMonth,
            dateSelect.monthValue,
            dateSelect.year
        )
        val dateStr = currentDuration?.endDateMutasiStringddMMMyyyy?.run {
            substring(0, 3) + substring(3, 6) + substring(6, 11)
        }.orEmpty()
        bindingRentangWaktu?.tvDateTo?.text = dateStr
        saveEndDateStr = dateStr
    }

    override fun onSelectRange(startDateSelect: LocalDate, endDateSelect: LocalDate) {
        currentDuration = DurasiModel(
            startDateSelect.dayOfMonth,
            startDateSelect.monthValue,
            startDateSelect.year,
            endDateSelect.dayOfMonth,
            endDateSelect.monthValue,
            endDateSelect.year
        )

        val startDateString = currentDuration?.startDateMutasiStringddMMMyyyy?.substring(0, 3) +
                    currentDuration?.startDateMutasiStringddMMMyyyy?.substring(3, 6) +
                    currentDuration?.startDateMutasiStringddMMMyyyy?.substring(6, 11)

        val endDateString = (currentDuration?.endDateMutasiStringddMMMyyyy?.substring(0, 3) +
                    currentDuration?.endDateMutasiStringddMMMyyyy?.substring(3, 6) +
                    currentDuration?.endDateMutasiStringddMMMyyyy?.substring(6, 11))

        saveStartDateStr = startDateString
        saveEndDateStr = endDateString

        bindingRentangWaktu?.apply {
            tvDateFrom.text = startDateString
            tvDateTo.text = endDateString
        }
    }

    private fun String.convertDate(inputFormat: String, outputFormat: String): String {
        val formatter = SimpleDateFormat(inputFormat, Locale.getDefault())
        var formatParser = Date()
        if (this.isNotEmpty()) formatParser = formatter.parse(this) ?: Date()
        val newOutputFormat = SimpleDateFormat(outputFormat, Locale.getDefault())
        return newOutputFormat.format(formatParser)
    }

    private fun RadioButton.checkedRadioButtonColor() {
        val colorStateList = ColorStateList(
            arrayOf(
                intArrayOf(-android.R.attr.state_checked),
                intArrayOf(android.R.attr.state_checked)
            ), intArrayOf(
                Color.DKGRAY,
                Color.rgb(16, 120, 202)
            )
        )
        this.buttonTintList = colorStateList
    }

    /* Handle Lifecycle */
    override fun onResume() {
        super.onResume()
        presenter.start()
    }

    override fun onDestroyView() {
        presenter.stop()
        _binding = null
        super.onDestroyView()
    }

    override fun onSessionEnd(message: String?) {
        super.onSessionEnd(message)
        binding.apply {
            swRefreshPurchaseHistory.isRefreshing = false
            presenter.stop()
        }
    }
    /* End of Handle Lifecycle */

    override fun onExceptionSnackbar(message: String) {
        showSnackbarErrorMessageRevamp(message, ALERT_ERROR, requireActivity(), true)
    }

    /* Handle Swipe Refresh/Pull to Refresh */
    override fun onRefresh() {
        binding.apply {
            val countCbx = filterBaseParentAdapter.checkBoxCount()
            if (skeletonPurchase != null) {
                skeletonPurchase?.show()
            }
            if (selectedFilter != null || countCbx > 0 || searchRequest.isNotEmpty()) {
                handleSearchFilterRequest()
            } else {
                showEmptyResult(false)
                isSearch = presenter.getPurchaseHistoryResponse(PurchaseHistoryRequest(lastId = "0"), true)
            }
        }
    }
    /* End of Handle Swipe Refresh/Pull to Refresh */

    override fun onClick(v: View) {
        when (v.id) {
            R.id.btnResetAllFilters -> handleResetAllFilters()
            R.id.viewDateRange -> showFilterDateRangeBottomSheet()
            R.id.viewPurchaseKind -> showFilterPurchaseTypeBottomSheet()
        }
    }

    companion object {
        private const val FIRST_POS = 0
        private const val SECOND_POS = 1
        private const val THIRD_POS = 2
        private const val FOURTH_POS = 3
    }
}