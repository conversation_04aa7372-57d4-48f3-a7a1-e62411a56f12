package id.co.bri.brimo.adapters;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.GestureDetector;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;

import id.co.bri.brimo.databinding.ItemHomeMenuRevampBinding;
import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.models.daomodel.FastMenu;

import java.util.List;
import id.co.bri.brimo.models.daomodel.FastMenuDefault;
import id.co.bri.brimo.util.LocaleUtilKt;
import id.co.bri.brimo.util.extension.StringExtKt;

public class ListHomeAdapter extends RecyclerView.Adapter<ListHomeAdapter.MyViewHolderHome> {

    private final List<FastMenuDefault> listHomeMenuDefaults;
    private final Context context;
    private final OnItemviewClickListener onItemviewClickListener;

    public interface OnItemviewClickListener {
        void onMenuClick(int id);
    }

    public ListHomeAdapter(Context context, List<FastMenuDefault> data, OnItemviewClickListener listener) {
        this.context = context;
        this.listHomeMenuDefaults = data;
        this.onItemviewClickListener = listener;
    }

    @NonNull
    @Override
    public MyViewHolderHome onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new MyViewHolderHome(ItemHomeMenuRevampBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull MyViewHolderHome holder, int position) {
        FastMenuDefault homeModel = listHomeMenuDefaults.get(position);
        int id = context.getResources().getIdentifier(
                homeModel.getGambarMenu(), "drawable", context.getPackageName());

        holder.binding.namaHome.setText(
                homeModel.getMenuName().getCurrentLanguage(LocaleUtilKt.getSavedLanguage(context))
        );
        holder.binding.gambarHome.setImageResource(id);

        holder.bindGesture(homeModel.getId(), onItemviewClickListener);
    }

    @Override
    public int getItemCount() {
        return listHomeMenuDefaults == null ? 0 : Constant.FAST_MENU_SIZE;
    }

    public static class MyViewHolderHome extends RecyclerView.ViewHolder {
        ItemHomeMenuRevampBinding binding;
        GestureDetector gestureDetector;

        public MyViewHolderHome(ItemHomeMenuRevampBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }

        @SuppressLint("ClickableViewAccessibility")
        public void bindGesture(int menuId, OnItemviewClickListener listener) {
            gestureDetector = new GestureDetector(binding.getRoot().getContext(), new GestureDetector.SimpleOnGestureListener() {
                @Override
                public boolean onSingleTapUp(MotionEvent e) {
                    listener.onMenuClick(menuId);
                    return true;
                }

                @Override
                public void onLongPress(MotionEvent e) {
                    if (binding.viewOverlay != null) {
                        binding.viewOverlay.setVisibility(View.VISIBLE);
                    }
                }
            });

            binding.llHome.setOnTouchListener((v, event) -> {
                gestureDetector.onTouchEvent(event);

                if (event.getAction() == MotionEvent.ACTION_UP ||
                        event.getAction() == MotionEvent.ACTION_CANCEL) {
                    if (binding.viewOverlay != null) {
                        binding.viewOverlay.setVisibility(View.GONE);
                    }
                }

                return true;
            });
        }

    }
}
