package id.co.bri.brimo.di.components;

import android.content.Context;

import com.google.gson.Gson;

import id.co.bri.brimo.data.api.ApiInterface;
import id.co.bri.brimo.data.api.ApiService;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.MinioSource;
import id.co.bri.brimo.data.dao.MenuDataBase;
import id.co.bri.brimo.data.dao.PfmDatabase;
import id.co.bri.brimo.data.dao.RateDatabase;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.anggaran.local.AnggaranLocalSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.fastmenu.FastMenuSource;
import id.co.bri.brimo.data.repository.fastmenudefault.FastMenuDefaultSource;
import id.co.bri.brimo.data.repository.fileparameter.FileParameterSource;
import id.co.bri.brimo.data.repository.lifestyle.MenuLifestyleSource;
import id.co.bri.brimo.data.repository.menudashboard.MenuDashboardSource;
import id.co.bri.brimo.data.repository.menukategori.MenuKategoriSource;
import id.co.bri.brimo.data.repository.pengelolaankartu.PengelolaanKartuSource;
import id.co.bri.brimo.data.repository.rate.RateSource;
import id.co.bri.brimo.data.repository.menudashall.MenuDashAllSource;
import id.co.bri.brimo.data.repository.menudashall.local.MenuDashAllLocalSource;
import id.co.bri.brimo.data.repository.menudashfav.MenuDashFavSource;
import id.co.bri.brimo.data.repository.menudashfav.local.MenuDashFavLocalSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.di.modules.ApiModule;
import id.co.bri.brimo.di.modules.ApplicationModule;
import id.co.bri.brimo.di.modules.MinioModule;
import id.co.bri.brimo.di.modules.fragment.RoomPengelolaanKartuModule;
import id.co.bri.brimo.di.modules.singalarity.DynamicKeyModule;
import id.co.bri.brimo.di.modules.RoomLifestyleModule;
import id.co.bri.brimo.di.modules.RoomMenuModule;
import id.co.bri.brimo.di.modules.RoomModule;
import id.co.bri.brimo.di.modules.RoomRateModule;
import id.co.bri.brimo.security.IMyEncrypt;

import javax.inject.Singleton;

import dagger.Component;
import id.co.bri.brimo.util.singalarity.helper.IC2KeyHelper;

@Singleton
@Component(modules = {
        ApplicationModule.class,
        ApiModule.class, RoomModule.class,  MinioModule.class,
        RoomMenuModule.class,
        RoomRateModule.class,
        RoomLifestyleModule.class,
        DynamicKeyModule.class,
        RoomPengelolaanKartuModule.class
})
public interface ApplicationComponent {


    void inject(Object object);

    Context context();

    BRImoPrefSource brimoPrefSource();

    ApiInterface apiClient();

    ApiService apiService();

    PfmDatabase pfmDatabase();

    MenuDataBase menuDatabase();

    RateDatabase rateDatabase();

    RateSource rateSource();

    FastMenuSource fastMenuSource();

    FastMenuDefaultSource fastMenuDefaultSource();

    MenuDashFavSource MenuDashFavSource();

    MenuDashAllSource MenuDashAllSource();

    TransaksiPfmSource transaksiPfmSource();

    AnggaranPfmSource anggaranPfmSource();

    CategoryPfmSource categoryPfmSource();

    FileParameterSource fileParameterSource();

    AnggaranLocalSource anggaranLocalSource();

    MenuDashAllLocalSource menuDashAllLocalSource();

    MenuDashFavLocalSource menuDashFavLocalSource();

    MenuKategoriSource menuKategoriSource();

    MenuDashboardSource menuDashboardSource();

    ApiSource apiSource();

    MinioSource minioSource();

    Gson gson();

    IC2KeyHelper ic2KeyHelper();

    IMyEncrypt myEncrypt();

    MenuLifestyleSource menuLifestyleSource();

    PengelolaanKartuSource pengelolaanKartuSource();
}