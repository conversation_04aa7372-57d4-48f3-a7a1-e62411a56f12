package id.co.bri.brimo.ui.fragments.bottomsheet

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin

class BottomSheetCustomViewGeneralFragment( var customViewId:View,
                                           var cancelable:Boolean?=false,
                                           var canceledOnTouchOutside :Boolean ?=false,
                                           var onCollapse: () -> Unit ) : BottomSheetDialogFragment() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogThemeInput)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return customViewId.rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        GeneralHelperNewSkin.showBlur(requireActivity())

        val bottomSheet = dialog?.findViewById(R.id.design_bottom_sheet) as FrameLayout
        val behavior = BottomSheetBehavior.from(bottomSheet)
        dialog?.setOnShowListener {
            behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }
        dialog?.setCancelable(cancelable!!)
        dialog?.setCanceledOnTouchOutside(canceledOnTouchOutside!!)
    }

    override fun onDismiss(dialog: DialogInterface) {
        GeneralHelperNewSkin.hideBlur(requireActivity())
        onCollapse()
        super.onDismiss(dialog)
    }

}