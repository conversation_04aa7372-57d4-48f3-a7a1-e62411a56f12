package id.co.bri.brimo.adapters

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemCategoryTransactionBinding
import id.co.bri.brimo.domain.helpers.calendar.makeGone
import id.co.bri.brimo.domain.helpers.calendar.makeVisible
import id.co.bri.brimo.models.InboxStatusModel
import id.co.bri.brimo.models.apimodel.response.ActivityGroup.ActivityType
import id.co.bri.brimo.models.apimodel.response.FilterAktivityResponse
import kotlin.math.cbrt

class TransactionCategoryAdapter(
    private val context: Context,
    private val category: ArrayList<InboxStatusModel>,
    private var filterAktivityResponse: FilterAktivityResponse?,
    private val onItemClick: (InboxStatusModel) -> Unit,
    private val onValidationButton: (Boolean) -> Unit
) : RecyclerView.Adapter<TransactionCategoryAdapter.TransactionViewHolder>(){

    private var selectedPosition = -1
    private var activityTypeList: ArrayList<ActivityType> = ArrayList()
    private var sFitur: String = ""
    private var sSubFiturs: ArrayList<String> = ArrayList()
    private var subCategory: List<String>? = null

    inner class TransactionViewHolder(
        private val binding: ItemCategoryTransactionBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        @SuppressLint("NotifyDataSetChanged")
        fun bind(position: Int, data: InboxStatusModel) {

            // Hide all checkboxes
            category.forEach { it.isShowCheckbox = false }

            binding.rbToday.text = data.name

            binding.rbToday.isChecked = data.isSelected
            binding.cbSubfilterAll.root.visibility =
                if (data.isShowCheckbox) View.VISIBLE else View.GONE

            binding.rbToday.setOnClickListener {

                val previousPosition = selectedPosition

                if (selectedPosition == position && data.isSelected) {
                    return@setOnClickListener
                }

                // Deselect previous item if needed
                if (previousPosition != -1 && previousPosition != position) {
                    category[previousPosition].isSelected = false
                    category[previousPosition].isShowCheckbox = false
                    notifyItemChanged(previousPosition)
                }

                // Select current
                data.isSelected = true
                selectedPosition = position

                // Hide all checkboxes
                category.forEach { it.isShowCheckbox = false }
                data.isShowCheckbox = true
                notifyDataSetChanged()

                onItemClick(data)
            }

            // This is crucial to avoid "appearing at the wrong item"
            if (data.isSelected) {
                showChildCategory(position, context, data)
            } else {
                binding.rvSubtitleFilter.makeGone()
            }

        }

        @SuppressLint("NotifyDataSetChanged")
        fun showChildCategory(position: Int, context: Context, category: InboxStatusModel) {
            if (selectedPosition == position && category.isSelected) {
                sFitur = category.code
                sSubFiturs.clear()
                subFilter()

                val adapter = ListInboxSubFilterAdapter(activityTypeList, context) { item ->
                    if (item.isSelected) {
                        if (!sSubFiturs.contains(item.codeType)) {
                            sSubFiturs.add(item.codeType)
                        }
                        if (activityTypeList.size == sSubFiturs.size) {
                            binding.cbSubfilterAll.cbSubfilter.isChecked = true
                        }
                    } else {
                        sSubFiturs.remove(item.codeType)

                        if (activityTypeList.size != sSubFiturs.size) {
                            binding.cbSubfilterAll.cbSubfilter.isChecked = false
                        }
                    }

                    // validate button
                    onValidationButton(sFitur.isNotEmpty() && sSubFiturs.isNotEmpty())
                }
                adapter.setSubFilter(subCategory)

                binding.rvSubtitleFilter.layoutManager =
                    LinearLayoutManager(context, RecyclerView.VERTICAL, false)
                binding.rvSubtitleFilter.adapter = adapter
                binding.rvSubtitleFilter.makeVisible()

                if (activityTypeList.isEmpty()) {
                    onValidationButton(true)
                } else {
                    sSubFiturs.clear()
                    onValidationButton(false)
                }

                binding.cbSubfilterAll.cbSubfilter.text = ContextCompat.getString(context, R.string.all)

                if (activityTypeList.isEmpty()) {
                    binding.cbSubfilterAll.root.makeGone()
                } else {
                    binding.cbSubfilterAll.cbSubfilter.isChecked = false
                    binding.cbSubfilterAll.root.makeVisible()
                }

                binding.cbSubfilterAll.cbSubfilter.setOnClickListener {
                    adapter.isCbSubAll = binding.cbSubfilterAll.cbSubfilter.isChecked
                    sSubFiturs.clear()
                    adapter.notifyDataSetChanged()
                }

            } else {
                binding.rvSubtitleFilter.makeGone()
            }
        }

        private fun subFilter() {
            activityTypeList = ArrayList()

            filterAktivityResponse?.activityGroupList?.forEach { group ->
                if (group.codeGroup.equals(sFitur, ignoreCase = true)) {
                    val types = group.activityTypeList
                    for (j in 0 until types.size - 1) {
                        activityTypeList.add(
                            j,
                            ActivityType(
                                types[j + 1].codeType,
                                types[j + 1].name
                            )
                        )
                    }
                }
            }
        }


    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TransactionViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val binding = ItemCategoryTransactionBinding.inflate(inflater, parent, false)
        return TransactionViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TransactionViewHolder, position: Int) {
        holder.bind(position, category[position])
    }

    override fun getItemCount(): Int = category.size

    fun getActivityType(): List<ActivityType> {
        return activityTypeList
    }

    fun getSubFilter(): List<String> {
        return sSubFiturs
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setSubFilter(newList: List<String>) {
        subCategory = newList
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setData(newList: List<InboxStatusModel>) {
        category.clear()
        category.addAll(newList)
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun clearSelection() {
        category.forEach { it.isSelected = false }
        selectedPosition = -1
        notifyDataSetChanged()
    }

    fun selectDefaultAll() {
        val defaultSelectedPos = category.indexOfFirst { it.code.equals("ALL", ignoreCase = true) }
        if (defaultSelectedPos != -1) {
            category[defaultSelectedPos].isSelected = true
            category[defaultSelectedPos].isShowCheckbox = true
            selectedPosition = defaultSelectedPos
            notifyItemChanged(defaultSelectedPos)
        }
    }

    fun updateSelectedCategoryByCode(code: String) {
        val newPos = category.indexOfFirst { it.code == code }
        if (newPos != -1) {
            if (selectedPosition != -1 && selectedPosition != newPos) {
                category[selectedPosition].isSelected = false
                category[selectedPosition].isShowCheckbox = false
                notifyItemChanged(selectedPosition)
            }

            category[newPos].isSelected = true
            category[newPos].isShowCheckbox = true
            notifyItemChanged(newPos)

            selectedPosition = newPos
        }
    }


}