package id.co.bri.brimo.contract.IPresenter.pulsarevamp.reskin

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.SavedListNs
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.KonfirmasiPulsaRequest
import id.co.bri.brimo.models.apimodel.request.PaymentRevampOpenRequest
import id.co.bri.brimo.models.apimodel.request.revampbriva.PayBrivaRevampRequest
import id.co.bri.brimo.presenters.pulsarevamp.reskin.PaymentNS

interface IPulsaDataReskinPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun getDataForm()

    fun getDataConfirmationPulsa(param: KonfirmasiPulsaRequest)

    fun payment(param: PaymentNS)

    fun addSavedList(param: SavedListNs)

    fun updateSavedList(param: SavedListNs)

    fun removeSavedList(param: SavedListNs)

    fun favoriteSavedList(param: SavedListNs)

    fun unfavoriteSavedList(param: SavedListNs)

    fun getAccountList()
}