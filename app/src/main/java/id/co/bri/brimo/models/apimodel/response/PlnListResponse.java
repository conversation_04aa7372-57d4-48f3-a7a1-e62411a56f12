package id.co.bri.brimo.models.apimodel.response;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class PlnListResponse implements Parcelable {

    @SerializedName("code")
    @Expose
    private String code;
    @SerializedName("name")
    @Expose
    private String name;
    @SerializedName("icon_name")
    @Expose
    private String iconName;
    @SerializedName("icon_path")
    @Expose
    private String iconPath;
    @SerializedName("type")
    @Expose
    private String type;

    public PlnListResponse() {
    }

    protected PlnListResponse(Parcel in) {
        code = in.readString();
        name = in.readString();
        iconName = in.readString();
        iconPath = in.readString();
        type = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(code);
        dest.writeString(name);
        dest.writeString(iconName);
        dest.writeString(iconPath);
        dest.writeString(type);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<PlnListResponse> CREATOR = new Creator<PlnListResponse>() {
        @Override
        public PlnListResponse createFromParcel(Parcel in) {
            return new PlnListResponse(in);
        }

        @Override
        public PlnListResponse[] newArray(int size) {
            return new PlnListResponse[size];
        }
    };

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIconName() {
        return iconName;
    }

    public void setIconName(String iconName) {
        this.iconName = iconName;
    }

    public String getIconPath() {
        return iconPath;
    }

    public void setIconPath(String iconPath) {
        this.iconPath = iconPath;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

}
