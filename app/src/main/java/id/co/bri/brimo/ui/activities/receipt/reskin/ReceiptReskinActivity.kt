package id.co.bri.brimo.ui.activities.receipt.reskin

import android.animation.ValueAnimator
import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.DecelerateInterpolator
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.core.content.FileProvider
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ActivityReceiptReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin.navigateToReact
import id.co.bri.brimo.domain.helpers.image.ImageHelper
import id.co.bri.brimo.domain.helpers.reskin.ImageHelperReskin
import id.co.bri.brimo.domain.helpers.reskin.ReceiptType
import id.co.bri.brimo.domain.helpers.reskin.mappingReceipt
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.ui.activities.DashboardIBActivity
import id.co.bri.brimo.ui.activities.FastMenuNewSkinActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.util.copyToClipboard
import java.io.File

class ReceiptReskinActivity: NewSkinBaseActivity() {
    private var _binding: ActivityReceiptReskinBinding? = null
    protected val binding get() = _binding!!

    private var isExpanded = false
    private var isEmptyMoreContent = false

    private lateinit var imageHelper: ImageHelper

    companion object {
        var dataReceipt: ReceiptRevampResponse?= null
        private const val ANIMATE_DUR: Long = 300
        private var TYPE = ReceiptType.OTHER

        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean, data: ReceiptRevampResponse, receiptType: ReceiptType = ReceiptType.OTHER) {
            TYPE = receiptType
            isFromFastMenu = fromFastMenu
            dataReceipt = data
            caller.apply {
                startActivityForResult(Intent(
                    this,
                    ReceiptReskinActivity::class.java
                ), Constant.REQ_PAYMENT)
            }
        }

        // Expand animation
        private fun expand(view: View) {
            view.measure(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            val targetHeight = view.measuredHeight

            view.layoutParams.height = 0
            view.visibility = View.VISIBLE

            val animator = ValueAnimator.ofInt(0, targetHeight).apply {
                duration = ANIMATE_DUR
                interpolator = DecelerateInterpolator()
                addUpdateListener { animation ->
                    view.layoutParams.height = animation.animatedValue as Int
                    view.requestLayout()
                }
            }
            animator.start()
        }

        // Collapse animation
        private fun collapse(view: View) {
            val initialHeight = view.measuredHeight

            val animator = ValueAnimator.ofInt(initialHeight, 0).apply {
                duration = ANIMATE_DUR
                interpolator = DecelerateInterpolator()
                addUpdateListener { animation ->
                    val value = animation.animatedValue as Int
                    view.layoutParams.height = value
                    view.requestLayout()
                    if (value == 0) {
                        view.visibility = View.GONE
                    }
                }
            }
            animator.start()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityReceiptReskinBinding.inflate(layoutInflater)

        imageHelper = ImageHelper(this)

        setContentView(binding.root)
        onBindIntentData()

        injectDependency()

        onBindView()

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // Tidak ngapa-ngapain
            }
        })
    }

    private fun onBindView() {
        binding.ivCopyClipboard.setOnClickListener {
            copyToClipboard(text = binding.tvNoToken.text.toString())
        }
        binding.btnDownload.setOnClickListener {
            startDownload {

            }
        }

        binding.btnShare.setOnClickListener {
            startDownload { file ->
                shareBitmap(file)
            }
        }

        dataReceipt?.mappingReceipt(TYPE)?.apply {
            binding.tvNoToken.text = token

            binding.tvNameAccount.text = detailTransaction[0].name
            binding.tvContentAccount.text = detailTransaction[0].content

            binding.tvNameTransaction.text = detailTransaction[1].name
            binding.tvTransactionContent.text = detailTransaction[1].content

            binding.tvNoPref.text = prefNumber
            binding.tvTransactionType.text = transactionType

            binding.tvAdminFee.text = adminFee
            binding.tvNominalCard.text = nominalCount

            binding.tvDateTime.text = dateTime
            binding.tvNominal.text = nominalPayment

            isEmptyMoreContent = contentList.size > 0

            if(contentList.size>0) {
                contentList.forEach { content ->
                    val inflater = LayoutInflater.from(this@ReceiptReskinActivity)
                    val itemView = inflater.inflate(R.layout.item_bill_detail, binding.llMoreContent, false)

                    val tvNameContent = itemView.findViewById<TextView>(R.id.tv_name_content)
                    val tvContent = itemView.findViewById<TextView>(R.id.tv_content)

                    tvNameContent.apply {
                        text = content.title
                        setTextColor(Color.parseColor("#7B90A6"))
                    }
                    tvContent.apply {
                        text = content.content
                        setTextColor(Color.parseColor("#181C21"))
                    }

                    binding.llMoreContent.addView(itemView)
                }
            }

            binding.llToken.visibility = if(token.isNullOrEmpty()) View.GONE else View.VISIBLE
        }

        binding.btnLihatLebih.setOnClickListener {
            isExpanded = !isExpanded
            if (isExpanded) {
                if(isEmptyMoreContent) expand(binding.llMoreContent)
                expand(binding.llFooterContent)
                binding.btnLihatLebih.text = GeneralHelper.getString(R.string.sembunyikan)
                if(isEmptyMoreContent) binding.vLineBillDetail.visibility = View.VISIBLE
                binding.llFooterContent.visibility = View.VISIBLE
            } else {
                if(isEmptyMoreContent) collapse(binding.llMoreContent)
                collapse(binding.llFooterContent)
                binding.btnLihatLebih.text = GeneralHelper.getString(R.string.see_more)
                if(isEmptyMoreContent) binding.vLineBillDetail.visibility = View.GONE
                binding.llFooterContent.visibility = View.GONE
            }
        }

        binding.btnSubmit.setOnClickListener {
            startActivity(Intent(this, if(!isFromFastMenu) DashboardIBActivity::class.java
            else FastMenuNewSkinActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
            })
        }
    }

    private fun injectDependency() {
    }

    private fun onBindIntentData() {
    }

    private fun startDownload(isShare: Boolean = false, res: (File) -> Unit) {
        binding.myContent.post {
            ImageHelperReskin.apply {
                val bitmap = captureFullView(binding.myContent)
                try {
                    saveBitmap(bitmap) { file ->
                        res.invoke(file)
                    }

                    if(!isShare)
                        showSnackbar("Berhasil Download", ALERT_CONFIRM)
                } catch (e: Exception) {
                    if(!isShare)
                        showSnackbar("Gagal Download", ALERT_ERROR)
                }
            }
        }
    }

    override fun onBackPressed() {

    }

    fun shareBitmap(file: File) {
        val uri = FileProvider.getUriForFile(
            this,
            "${packageName}.fileprovider",
            file
        )
        val intent = Intent(Intent.ACTION_SEND).apply {
            type = "image/png"
            putExtra(Intent.EXTRA_STREAM, uri)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        startActivity(Intent.createChooser(intent, "Share your image"))
    }
}