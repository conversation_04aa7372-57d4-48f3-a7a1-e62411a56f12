package id.co.bri.brimo.ui.fragments

import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.net.Uri
import android.util.Log
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.PinNumberAdapterNewSkin
import id.co.bri.brimo.adapters.baseadapter.base.BasePinAdapter.PinAdapterListener
import id.co.bri.brimo.adapters.pinadapter.OtpInputAdapterNewSkin
import id.co.bri.brimo.contract.IPresenter.IPinPresenter
import id.co.bri.brimo.contract.IView.IPinView
import id.co.bri.brimo.databinding.FragmentPinNewskinBinding
import id.co.bri.brimo.di.modules.fragment.PinAllModule
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers
import javax.inject.Inject
import javax.inject.Named

class PinFragmentNewSkin : BasePopUpFragment, PinNumberAdapterNewSkin.OnPinNumberListener, PinAdapterListener,
    IPinView, View.OnClickListener {

    lateinit var binding: FragmentPinNewskinBinding
    private lateinit var pinNumberAdapter: PinNumberAdapterNewSkin
    private lateinit var pinHiddenAdapter: OtpInputAdapterNewSkin

    @Named("Pindot")
    @Inject
    lateinit var pinDotLayoutManager: GridLayoutManager

    @Named("Pinpad")
    @Inject
    lateinit var pinPadLayoutManager: GridLayoutManager

    @Inject
    lateinit var itemDecoration: ItemDecoration

    @Inject
    lateinit var presenter: IPinPresenter<IPinView>

    private val mListener: OnFragmentInteractionListener? = null

    var pinAdapterListener: PinAdapterListener? = null
    var mSendPin: SendPin
    private var onDismissListener: OnDismissListenerSuccessOrNo? = null

    private var mIsFromEditFastmenu = false


    interface SendPin {
        fun onSendPinComplete(pin: String?)

        fun onLupaPin()
    }

    override fun isScreenshotDisabled(): Boolean {
        return true
    }

    constructor(context: Context, sendPin: SendPin) : super(context) {
        this.mSendPin = sendPin

        injectDependency()
        setupView()
    }

    constructor(context: Context, sendPin: SendPin, isEditFastMenu: Boolean) : super(context) {
        this.mSendPin = sendPin
        mIsFromEditFastmenu = isEditFastMenu

        injectDependency()
        setupView()
    }

    override fun createContentView(parent: ViewGroup): View {
        binding =
            FragmentPinNewskinBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        binding.root.layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            Resources.getSystem().displayMetrics.heightPixels
        )
        binding.root.minimumHeight = Resources.getSystem().displayMetrics.heightPixels

        pinNumberAdapter = PinNumberAdapterNewSkin(InsertPinNumbers.getPinNumberList(context))
        pinHiddenAdapter = OtpInputAdapterNewSkin(context)

        binding.tvLupaPin.setOnClickListener(this)
        binding.llBack.setOnClickListener(this)

//        Log.d("PopupDebug", "Measured height: ${getContentView().measuredHeight}")


        return binding.root
    }


    override fun onClick(view: View) {
        val id = view.id
        when (id) {
            R.id.tv_lupa_pin -> {
                dismiss()
                mSendPin.onLupaPin()
            }

            R.id.llBack -> {
                dismiss()
                if (onDismissListener != null) onDismissListener!!.onDismissListenerSuccessOrNo(
                    false
                )
            }

            else -> return
        }
    }

    private fun injectDependency() {
        activityComponent
            .plusPinComponent(PinAllModule())
            .inject(this)
    }

    private fun setupView() {
        pinNumberAdapter.onPinNumberListener = this
        pinHiddenAdapter.setListener(this)

        binding.rvBox.layoutManager = pinDotLayoutManager
        binding.rvBox.adapter = pinHiddenAdapter

        binding.rvInput.layoutManager = pinPadLayoutManager
        binding.rvInput.adapter = pinNumberAdapter

        if (mIsFromEditFastmenu) {
            binding.tvLupaPin.visibility = GONE
        } else {
            binding.tvLupaPin.visibility = VISIBLE
        }
    }

    fun onButtonPressed(uri: Uri?) {}

    override fun onPinClicked(pinNumber: Int) {
        binding.tvError.visibility = View.GONE
        pinHiddenAdapter.addPin(pinNumber.toString())
        pinHiddenAdapter.resetError()
    }

    override fun onDeleteClicked() {
        pinHiddenAdapter.deletePin()
        binding.tvError.visibility = View.GONE
        pinHiddenAdapter.resetError()
    }

    override fun notifyChanges() {
        pinHiddenAdapter.notifyDataSetChanged()
    }

    override fun onComplete(string: String) {
        if (pinAdapterListener != null) {
            pinAdapterListener!!.onComplete(string)
        }
        if (string != null) {
            mSendPin.onSendPinComplete(string)
        }
    }

    override fun onPinSucces() {
        onCompleteInput()
    }

    override fun onWrongPin() {
    }

    interface OnFragmentInteractionListener {
        fun onFinishPin()
    }

    fun onCompleteInput() {
        mListener?.onFinishPin()
    }

    fun initProperties() {
        this.tintColor = resources.getColor(R.color.blurry_background)
        this.isDismissOnClickBack = true
        this.isDismissOnTouchBackground = false
        this.blurRadius = 20f
    }

    fun setOnDismissListenerSuccessOrNo(onDismissListener: OnDismissListenerSuccessOrNo?) {
        this.onDismissListener = onDismissListener
    }

    interface OnDismissListenerSuccessOrNo {
        fun onDismissListenerSuccessOrNo(isSuccess: Boolean?)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            dismiss()
            return true
        } else {
            return super.onKeyDown(keyCode, event)
        }
    }

    fun onWrongPin(message: String?) {
        pinHiddenAdapter.setErrorState(true)
        binding.tvError.visibility = View.VISIBLE
        binding.tvError.text = message ?: context.getString(R.string.default_error_message)
        binding.rvBox.post {
            pinHiddenAdapter.deleteAllPin()
        }
    }
}