package id.co.bri.brimo.ui.activities.dplkrevamp

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.LinearLayout
import androidx.viewpager.widget.ViewPager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dplkrevamp.MyPagerListDplkAdapter
import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IListDplkOptionRevampPresenter
import id.co.bri.brimo.contract.IView.dplkrevamp.IListPilihBrifineRevampView
import id.co.bri.brimo.databinding.ActivityListPilihBrifineRevampBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.extension.visible
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.ListPilihBrifineResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.dplkrevamp.ListDplkOptionRevampFragment
import id.co.bri.brimo.util.extension.fromJsonToObject
import id.co.bri.brimo.util.extension.fromObjectToJson
import javax.inject.Inject

class ListDplkOptionRevampActivity : BaseActivity(),
    ViewPager.OnPageChangeListener,
    View.OnClickListener,
    ListDplkOptionRevampFragment.DialogDefaultListener {
    lateinit var binding: ActivityListPilihBrifineRevampBinding
    private var titleList: ArrayList<String> = ArrayList()
    var lyTabs: LinearLayout? = null

    @Inject
    lateinit var presenter: IListDplkOptionRevampPresenter<IListPilihBrifineRevampView>

    companion object {
        private const val TAG_INTENT_RESPONSE = "response"
        private const val TAG_INTENT_IS_SIMULATION = "mIsSimulation"
        private const val TAG_INTENT_IS_REGISTERED = "mIsRegistered"

        private var mListPilihBrifineResponse: ListPilihBrifineResponse = ListPilihBrifineResponse()
        private var mIsSimulation: Boolean = false
        private var mIsRegistered = false

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            response: ListPilihBrifineResponse,
            isSimulation: Boolean,
            isRegistered: Boolean,
        ) {
            val intent = Intent(caller, ListDplkOptionRevampActivity::class.java)
            intent.putExtra(TAG_INTENT_RESPONSE, response.fromObjectToJson())
            intent.putExtra(TAG_INTENT_IS_SIMULATION, isSimulation)
            intent.putExtra(TAG_INTENT_IS_REGISTERED, isRegistered)
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityListPilihBrifineRevampBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
    }

    private fun setupView() {
        setupIntentData()
        initView()
        setupViewPager()
    }

    private fun setupIntentData() {
        intent.getStringExtra(TAG_INTENT_RESPONSE)?.fromJsonToObject(ListPilihBrifineResponse())
            ?.let { mListPilihBrifineResponse = it }
        mIsSimulation = intent.getBooleanExtra(TAG_INTENT_IS_SIMULATION, false)
        mIsRegistered = intent.getBooleanExtra(TAG_INTENT_IS_REGISTERED, false)
    }

    private fun initView() {
        GeneralHelper.setToolbarRevamp(
            this@ListDplkOptionRevampActivity,
            binding.toolbar.toolbar,
            GeneralHelper.getString(R.string.txt_pilih_jenis_brifine)
        )

        if (!mIsRegistered) {
            binding.llStepRegistrationDplk.visible()
        }
    }

    private fun setupViewPager() {
        for (i in mListPilihBrifineResponse.jenisBrifine.indices) {
            titleList.add(mListPilihBrifineResponse.jenisBrifine[i].title)
        }

        val catatanKeuanganFragmentAdapter = MyPagerListDplkAdapter(
            supportFragmentManager,
            mListPilihBrifineResponse,
            titleList,
            mIsSimulation,
            mIsRegistered
        )
        binding.viewpager.adapter = catatanKeuanganFragmentAdapter
        binding.viewpagertab.setViewPager(binding.viewpager)

        //Add bold effect on selected tab
        binding.viewpagertab.setOnPageChangeListener(this@ListDplkOptionRevampActivity)
        lyTabs = binding.viewpagertab.getChildAt(0) as LinearLayout

        //change default style toolbar font
        GeneralHelper.changeTabsFontDashboardDplkRevamp(
            this@ListDplkOptionRevampActivity,
            lyTabs,
            0
        )
    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {

    }

    override fun onPageSelected(position: Int) {
        GeneralHelper.changeTabsFontDashboardDplkRevamp(this, lyTabs, position)
    }

    override fun onPageScrollStateChanged(state: Int) {

    }

    override fun onClick(p0: View?) {
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_BUKA_REKENING) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else if (resultCode == RESULT_CANCELED && data != null) {
                this.setResult(RESULT_CANCELED, data)
                finish()
            } else if (resultCode == RESULT_FIRST_USER && data != null) {
                this.setResult(RESULT_FIRST_USER, data)
                finish()
            }
        }
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else if (resultCode == RESULT_CANCELED && data != null) {
                this.setResult(RESULT_CANCELED, data)
                finish()
            } else if (resultCode == RESULT_FIRST_USER && data != null) {
                this.setResult(RESULT_FIRST_USER, data)
                finish()
            }
        }

    }
}