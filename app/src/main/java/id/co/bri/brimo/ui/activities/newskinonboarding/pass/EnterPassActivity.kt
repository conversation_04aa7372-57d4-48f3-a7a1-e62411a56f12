package id.co.bri.brimo.ui.activities.newskinonboarding.pass

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ActivityChangePassNewskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.changepassnewskin.ChangePassActivity
import id.co.bri.brimo.ui.activities.newskinonboarding.OnboardingOtpNewSkinActivity
import id.co.bri.brimo.ui.activities.newskinonboarding.pin.EnterCurrentPinPassActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.NewSkinOTPChannel
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.util.extension.view.addMinLengthCharValidation
import id.co.bri.brimo.util.extension.view.disableCopyPaste
import id.co.bri.brimo.util.extension.view.onTextChanged
import id.co.bri.brimo.util.extension.view.preventSpaceInput
import id.co.bri.brimo.util.extension.view.togglePasswordVisibility

class EnterPassActivity : NewSkinBaseActivity() {
    private lateinit var binding: ActivityChangePassNewskinBinding
    private var isPasswordVisible = false
    private val correctPassword = "Jakarta123"
    private var passErrorCount = 0

    private var reqCode: Int = -1

    private val launcher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            setResult(RESULT_OK)
            finish()
        }
    }

    private var btnForgetPass: Runnable = Runnable {
        val intent = EnterCurrentPinPassActivity.launchIntent(this, "from_login", Constant.REQ_FORGOT_PASS)
        launcher.launch(intent)
    }

    var secBtnFunction: Runnable = Runnable {

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setTheme(R.style.AppThemeNewSkinFullTransparent)
        binding = ActivityChangePassNewskinBinding.inflate(layoutInflater)
        setContentView(binding.root)
        reqCode = intent.getIntExtra("request_code", -1)

        injectDependency()
        setupView()
    }


    private fun injectDependency() {

    }

    private fun setupView() = with(binding) {
        adjustViewOnKeyboardVisibility(layoutButton, root)
        inputPassword.preventSpaceInput()
        inputPassword.disableCopyPaste()
        setupTogglePasswordVisibility()
        observePassword()
        GeneralHelperNewSkin.setToolbar(
            this@EnterPassActivity,
            toolbar.toolbar,
            if (reqCode == Constant.REQ_UBAH_PIN || reqCode == Constant.REQ_FORGOT_PIN)
                "Password"
            else
                GeneralHelper.getString(R.string.ubah_password)
        )

        if(reqCode == Constant.REQ_UBAH_PIN || reqCode == Constant.REQ_FORGOT_PIN){
            inputPassword.hint = "Password"
        }

        tvLupaPass.setOnClickListener {
            OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
                supportFragmentManager,
                R.drawable.lock_password_new,
                "ic_account_saved",
                resources.getString(R.string.forget_pass_ns),
                resources.getString(R.string.forget_pass_desc),
                createKotlinFunction0(btnForgetPass),
                createKotlinFunction0(secBtnFunction),
                true,
                resources.getString(R.string.btn_reset_pass),
                resources.getString(R.string.btn_cancel),
                false,
                showCloseButton = true,
            )
        }
    }

    private fun observePassword() = with(binding) {
        inputPassword.onTextChanged { s ->
            val input = s?.toString() ?: ""
            val isValidChar = input.length >= 8
            val noSpace = input.isNotEmpty() && !input.contains(" ")

            val allValid = isValidChar && noSpace
            btnNext.isEnabled = allValid
            if (allValid) {
                inputPassword.hideError()
            }
            if (allValid) {

                btnNext.setOnClickListener {
                    val passwordInput = inputPassword.editText.text?.toString() ?: ""

                    if (passwordInput == correctPassword) {
                        passErrorCount = 0
                        navigation()
                    } else {
                        passErrorCount++
                        btnNext.isEnabled = false
                        binding.inputPassword.showError("Password salah, silakan coba lagi.")

                        when (passErrorCount) {
                            2 -> showBottomSheet()
                            3 -> showBottomSheetBlock()
                        }
                    }
                }

            }
        }

        inputPassword.addMinLengthCharValidation(
            minLength = 8,
            errorText = "Minimal harus 8 digit angka"
        )
    }

    private fun setupTogglePasswordVisibility() = with(binding) {
        inputPassword.inputLayout.setEndIconOnClickListener {
            isPasswordVisible = !isPasswordVisible
            inputPassword.togglePasswordVisibility(isPasswordVisible)
        }
    }

    companion object {
        fun launchIntent(caller: Context, reqCode: Int): Intent {
            return Intent(caller, EnterPassActivity::class.java).apply {
                putExtra("request_code", reqCode)
            }
        }
    }

    private fun navigation() {
        if (reqCode == Constant.REQ_FORGOT_PIN) {
            showOtpChannel(reqCode)
        } else {
            val intent =  ChangePassActivity.launchIntent(this, reqCode)
            launcher.launch(intent)
        }

    }

    private fun showOtpChannel(reqCode: Int) {
        val maskNumber = "+6281345602416"
        val message = getString(
            R.string.otp_channel_phone_verif_method_message,
            maskNumber
        )
        NewSkinOTPChannel(this, supportFragmentManager, message) {
            val intent = OnboardingOtpNewSkinActivity.launchIntentReq(
                this@EnterPassActivity,
                method = "",
                requestCode = reqCode
            )
            launcher.launch(intent)
        }
    }

    private fun showBottomSheet() {
        OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
            supportFragmentManager,
            R.drawable.ic_warning_illustration,
            "ic_warning_illustration",
            "Percobaan Login Gagal, Tersisa 1 Kali Kesempatan",
            "Pastikan password yang dimasukkan sudah benar. Jika lupa, silakan atur ulang password.",
            {
                resetInputPass()
            },
            {
                ChangePassActivity.launchIntent(this,"")
            },
            true,
            "Coba Lagi",
            "Atur Ulang Password Sekarang",
            false,
            showCloseButton = true,
        )
    }

    private fun resetInputPass(){
        binding.inputPassword.hideError()
        binding.inputPassword.setText("")
    }

    private fun showBottomSheetBlock(){
        resetInputPass()
        OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
            fragmentManager = supportFragmentManager,
            imgPath = "",
            imgName = "ic_blocked_illustrator",
            titleTxt = "Akun Kamu Terkunci Sementara",
            subTitleTxt = "Kamu salah masukkan password melebihi batas yang ditentukan. Yuk, atur ulang password kamu untuk melanjutkan aktivitas di Qitta.",
            btnFirstFunction = { ChangePassActivity.launchIntent(this,"") },
            isClickableOutside = true,
            showCloseButton = false,
            firstBtnTxt = GeneralHelper.getString(R.string.mengerti),
            showPill = true
        )
    }

    override fun onResume() {
        super.onResume()
        binding.inputPassword.hideError()
        binding.inputPassword.setText("")
    }

    override fun isScreenshotDisabled(): Boolean = true

}