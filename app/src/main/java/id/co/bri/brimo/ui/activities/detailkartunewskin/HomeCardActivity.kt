package id.co.bri.brimo.ui.activities.detailkartunewskin

import android.annotation.SuppressLint
import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Intent
import android.content.res.Resources
import android.os.Bundle
import android.os.Handler
import android.text.SpannableStringBuilder
import android.util.TypedValue
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.core.widget.NestedScrollView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.bukarekeningnewskin.FilterMonthNewSkinAdapter
import id.co.bri.brimo.adapters.bukarekeningnewskin.ListActivityAdapter
import id.co.bri.brimo.adapters.bukarekeningnewskin.MenuDetailKartuAdapter
import id.co.bri.brimo.contract.IPresenter.detailcardnewskin.IDetailCardNewSkinPresenter
import id.co.bri.brimo.contract.IPresenter.inbox.IInboxPresenter
import id.co.bri.brimo.contract.IPresenter.pengelolaankartu.IPengelolaanKartuNewPresenter
import id.co.bri.brimo.contract.IPresenter.saldo.IRekeningPresenter
import id.co.bri.brimo.contract.IView.IDetailCardView
import id.co.bri.brimo.contract.IView.inbox.IInboxView
import id.co.bri.brimo.contract.IView.pengelolaankartu.IPengelolaanKartuNewView
import id.co.bri.brimo.contract.IView.saldo.IRekeningView
import id.co.bri.brimo.databinding.ActivityCardHomeBinding
import id.co.bri.brimo.domain.SnackBarType
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin.formatDecimalText
import id.co.bri.brimo.domain.helpers.calendar.makeGone
import id.co.bri.brimo.domain.helpers.calendar.makeVisible
import id.co.bri.brimo.domain.providers.CardType
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.DetailKelolaKartuReq
import id.co.bri.brimo.models.apimodel.response.DetailListType
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse
import id.co.bri.brimo.models.apimodel.response.FilterAktivityResponse
import id.co.bri.brimo.models.apimodel.response.InboxResponse
import id.co.bri.brimo.models.apimodel.response.InboxResponse.ActivityList
import id.co.bri.brimo.models.apimodel.response.ListPengelolaanKartuRes
import id.co.bri.brimo.models.apimodel.response.ListRekeningResponse
import id.co.bri.brimo.models.apimodel.response.PendingResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptAmkkmResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptInternasionalResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampInboxResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptTravelTrainResponse
import id.co.bri.brimo.models.apimodel.response.esbn.ReceiptResponseNew
import id.co.bri.brimo.models.apimodel.response.nfcpayment.DetailRekeningResponse
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.DetailKelolaKartuRes
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.activationdebit.ProductBriefResponse
import id.co.bri.brimo.payment.app.PaymentActivity
import id.co.bri.brimo.ui.activities.InboxFilterActivity
import id.co.bri.brimo.ui.activities.ReceiptActivity
import id.co.bri.brimo.ui.activities.ReceiptInboxActivity
import id.co.bri.brimo.ui.activities.ReceiptPendingActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.britamajunio.ReceiptOpenJunioActivity
import id.co.bri.brimo.ui.activities.bukarekening.PendingTabunganActivity
import id.co.bri.brimo.ui.activities.bukarekening.ReceiptTabunganActivity
import id.co.bri.brimo.ui.activities.carddetailnewskin.PyhsicCardDetailActivity
import id.co.bri.brimo.ui.activities.carddetailnewskin.VirtualCardDetailActivity
import id.co.bri.brimo.ui.activities.emas.ReceiptGagalAFTActivity
import id.co.bri.brimo.ui.activities.estatement.EStatementActivity
import id.co.bri.brimo.ui.activities.inforekeningnewskin.InformasiRekeningActivity
import id.co.bri.brimo.ui.activities.lifestyle.ReceiptLifestyleActivity
import id.co.bri.brimo.ui.activities.lifestyle.ReceiptLifestyleActivity.Companion.launchIntentIsFromConfirmation
import id.co.bri.brimo.ui.activities.pengelolaankartu.PengelolaanKartuNewActivity
import id.co.bri.brimo.ui.activities.receipt.ReceiptAbnormalRevampActivity
import id.co.bri.brimo.ui.activities.sbn.earlyredemption.ReceiptEarlyRedeemActivity.Companion.launchIntent
import id.co.bri.brimo.ui.activities.simpedes.ReceiptAmkkmActivity
import id.co.bri.brimo.ui.activities.transferinternasional.ReceiptTransferInternationalActivity
import id.co.bri.brimo.ui.activities.travel.ReceiptTravelTrainActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.util.extension.toArrayList
import javax.inject.Inject

class HomeCardActivity : NewSkinBaseActivity(), SwipeRefreshLayout.OnRefreshListener, IDetailCardView, IInboxView, IRekeningView, IPengelolaanKartuNewView, MenuDetailKartuAdapter.OnClickListener,
    ListActivityAdapter.OnClickItem,
    ListActivityAdapter.OnSelected, FilterMonthNewSkinAdapter.OnItemClickListener{

    private lateinit var binding: ActivityCardHomeBinding

    @Inject
    lateinit var inboxPresenter: IInboxPresenter<IInboxView>

    @Inject
    lateinit var detailCardPresenter: IDetailCardNewSkinPresenter<IDetailCardView>

    @Inject
    lateinit var rekeningPresenter: IRekeningPresenter<IRekeningView>

    @Inject
    lateinit var kelolaKartuPresenter: IPengelolaanKartuNewPresenter<IPengelolaanKartuNewView>

    private var accountRek: String? = null
    private var menuAdapter: MenuDetailKartuAdapter? = null
    private lateinit var myClipboard: ClipboardManager
    private lateinit var myClip: ClipData
    private var isSaldoShow = false
    var listItems = ArrayList<ListRekeningResponse.Account>()
    var listItemsCard = ArrayList<ListPengelolaanKartuRes.Account>()
    var matchedAccount: ListRekeningResponse.Account? = null
    var matchedCard: ListPengelolaanKartuRes.Account? = null
    var skeletonScreen: SkeletonScreen? = null
    var skeletonBottom: SkeletonScreen? = null
    private var handler: Handler = Handler()

    var listActivityAdapter: ListActivityAdapter? = null
    var monthAdapter: FilterMonthNewSkinAdapter? = null
    var activityLists: MutableList<ActivityList?> = ArrayList()
    var layoutManager: LinearLayoutManager? = null
    var tempCurrency: String? = null
    var balance: String? = null

    var periode: String = ""
    var status: String = ""
    var fitur: String = ""
    var subFitur: String = ""
    var lastId: String = ""

    private var detailKelolaKartuRes: DetailKelolaKartuRes? = null
    private var isLoading = false
    private var hasShownErrorDialog = false
    private var position: Int? = null
    private var isErrorDetail = false
    private var isLoadPagination = false
    private var isSaldoNormalSuccess = false

    companion object {
        fun launchIntent(caller: Activity, account: String) {
            val intent = Intent(caller, HomeCardActivity::class.java)
            intent.putExtra(Constant.ACCOUNT_HOME, account)
            caller.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityCardHomeBinding.inflate(layoutInflater)
        setContentView(binding.root)
        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)

        val extras = intent.extras
        if (extras != null) {
            accountRek = extras.getString(Constant.ACCOUNT_HOME)
        }

        injectDependency()
        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        detailCardPresenter.setView(this)
        detailCardPresenter.start()
        inboxPresenter.view = this
        inboxPresenter.start()
        inboxPresenter.setUrlInbox(GeneralHelper.getString(R.string.url_activity_list))
        inboxPresenter.setUrlDetailInbox(GeneralHelper.getString(R.string.url_activity_detail))

        rekeningPresenter.view = this
        rekeningPresenter.start()


        kelolaKartuPresenter.view = this
        kelolaKartuPresenter.setUrlCardList(GeneralHelper.getString(R.string.url_card_management_list_v3))
        kelolaKartuPresenter.setUrlCardDetail(GeneralHelper.getString(R.string.url_card_management_card_detail_v2))
        kelolaKartuPresenter.start()


        rekeningPresenter.getAccountWithSaldo()
        accountRek?.let { detailCardPresenter.getSaldoNormal(it) }

        callService(false)
    }

    private fun callService(isRefresh: Boolean) {

        periode = ""
        status = ""
        fitur = ""
        subFitur = ""
        lastId = "0"
        inboxPresenter.getInbox(periode, status, fitur, subFitur, lastId, isRefresh)

    }

    private fun setupView() {

        binding.swipe.setOnRefreshListener(this)

        initiateAdapter()

        binding.rlToolbar.bringToFront()
        binding.llBack.setOnClickListener {
            onBackPressed()
        }
        val menuList = listOf(
            MenuItemCard(Constant.INFORMASI_REKENING, R.drawable.ic_card_menu_1))

        menuAdapter = MenuDetailKartuAdapter(menuList, this)
        binding.rvMenu.setHasFixedSize(true)
        binding.rvMenu.setLayoutManager(
            LinearLayoutManager(
                this,
                RecyclerView.HORIZONTAL,
                false
            )
        )
        binding.rvMenu.adapter = menuAdapter

        binding.rlTitle.viewFilterIcon.setOnClickListener {
            inboxPresenter.getFilterInbox()
        }

        binding.rlTitle.viewResetFilter.tvChip.text = getString(R.string.reset)

        binding.rlTitle.viewResetFilter.viewConnect.setOnClickListener {

            activityLists.clear()
            skeletonScreen?.show()
            skeletonBottom?.show()

            binding.rlTitle.ivBullet.makeGone()
            binding.rlTitle.viewResetFilter.root.makeGone()
            callService(false)

        }

        binding.ivCopy.setOnClickListener {
            myClipboard = getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
            myClip = ClipData.newPlainText("text", matchedAccount?.account)
            myClipboard.setPrimaryClip(myClip)
            GeneralHelperNewSkin.showCustomSnackBar(binding.rlToolbar, getString(R.string.info_saldo_rekening_berhasil_disalin), SnackBarType.SUCCESS)
        }
        hideShowSaldo()
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_UBAH_KATA_KUNCI && resultCode == Activity.RESULT_OK) {
            setResult(RESULT_OK, data)
            finish()
        }else if (requestCode == Constant.REQ_UBAH_KATA_KUNCI && resultCode == 3 && data != null) {
            data.getStringExtra("message")?.let { onException(it) }
        } else if (requestCode == Constant.REQ_FILTER_INBOX) {
            data?.let {
                periode = it.getStringExtra("PERIODE") ?: ""
                status = it.getStringExtra("STATUS") ?: ""
                fitur = it.getStringExtra("FITUR") ?: ""
                subFitur = it.getStringExtra("SUBFITUR") ?: ""
                lastId = it.getStringExtra("LASTID") ?: ""

                binding.rlTitle.ivBullet.makeVisible()
                binding.rlTitle.viewResetFilter.root.makeVisible()
                binding.rlTitle.viewResetFilter.viewConnect.backgroundTintList = ContextCompat.getColorStateList(this, R.color.primary_ns_100)
                binding.rlTitle.viewResetFilter.tvChip.setTextColor(ContextCompat.getColor(this, R.color.primary_ns_600))

                activityLists.clear()
                skeletonScreen?.show()
                skeletonBottom?.show()

                inboxPresenter.getInbox(periode, status, fitur, subFitur, lastId, true)
            }
        } else if (requestCode == Constant.REQ_BACK_INFORMASI) {
            binding.swipe.isRefreshing = false
//            skeletonScreen?.show()
//            skeletonBottom?.show()
            rekeningPresenter.getAccountWithSaldo()
            accountRek?.let { detailCardPresenter.getSaldoNormal(it) }
            callService(true)
        }
    }

    private fun showEmptyState() {

        skeletonScreen?.hide()
        skeletonBottom?.hide()
        binding.rvDetailLis.makeGone()
        binding.viewEmpty.root.makeVisible()
        binding.viewEmpty.tvTitleEmpty.text = getString(R.string.txt_no_history)
        binding.viewEmpty.tvDescEmpty.text = getString(R.string.txt_no_history_desc)

    }

    private val onBackPressedCallback: OnBackPressedCallback =
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val intent = Intent()
                setResult(RESULT_CANCELED, intent)
                finish()
            }
        }

    fun validateButton(isEnabled: Boolean) {
        binding.rlTitle.viewFilterIcon.isEnabled = isEnabled
    }

    private fun showError() {

        if (hasShownErrorDialog) return

        hasShownErrorDialog = true

        val firstBtnFunction = Runnable {

            hasShownErrorDialog = false
            isLoadPagination = false

            if (isErrorDetail) {
                position?.let { pos ->
                    activityLists[pos]?.let { item ->
                        inboxPresenter.setTrxType(item.trxType)
                        inboxPresenter.getInboxDetail(item.referenceNumber)
                    }
                }
            } else {
                hasShownErrorDialog = false
                rekeningPresenter.getAccountWithSaldo()
                accountRek?.let { detailCardPresenter.getSaldoNormal(it) }
                callService(true)
            }

        }
        OpenBottomSheetGeneralNewSkinFragment.showDialogInformation1Button(
            supportFragmentManager,
            "",
            Constant.IC_SAD_NEW_NS,
            getString(R.string.failed_load_page_title),
            getString(R.string.failed_load_page_subtitle),
            createKotlinFunction0(firstBtnFunction),
            true,
            getString( R.string.refresh),
            true
        )

    }

    override fun onResume() {
        super.onResume()
        hasShownErrorDialog = false
    }

    override fun onExceptionRevamp(message: String?) {
        binding.swipe.isRefreshing = false
        isErrorDetail = false
        isSaldoNormalSuccess = false
        showError()
    }

    override fun onExceptionNoBackAction(message: String?) { // error get detail receipt
        binding.swipe.isRefreshing = false
        isErrorDetail = true
        showError()
    }

    override fun onException(message: String) {
        binding.swipe.isRefreshing = false
        isErrorDetail = false
//        showEmptyState()
        showError()
    }

    override fun onDestroy() {
        detailCardPresenter.stop()
        rekeningPresenter.stop()
        kelolaKartuPresenter.stop()
        activityLists.clear()
        super.onDestroy()
    }

    @SuppressLint("SetTextI18n")
    override fun onSuccessGetSaldoNormal(response: DetailRekeningResponse) {
        isSaldoNormalSuccess = true

        binding.ivEyeClose.isEnabled = true
        balance = response.balanceString
        tempCurrency = response.currency
        val spannableBalance = formatDecimalText(balance)
        val combined = SpannableStringBuilder()
            .append("$tempCurrency ")
            .append(spannableBalance)
//        binding.tvSaldo.text = combined

    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessGetInbox(inboxResponse: InboxResponse?, isRefresh: Boolean) {

        inboxResponse?.let { response ->
            if (isRefresh) {
                activityLists.clear()
            }

            binding.swipe.isRefreshing = false
            skeletonScreen?.hide()
            skeletonBottom?.hide()

            try {
                binding.apply {
                    rvDetailLis.makeVisible()
                    binding.viewEmpty.root.makeGone()
                    activityLists.size.let { rvDetailLis.smoothScrollToPosition(it) }
                }

                activityLists.addAll(response.activityLists)
                listActivityAdapter?.setItems(activityLists)
                listActivityAdapter?.notifyDataSetChanged()
            } catch (_: Exception) {
                // Optional: log or handle error silently
            }

            isLoading = false
            isLoadPagination = false
        }

    }


    override fun onInboxEnd(message: String?) {

        if (activityLists.isEmpty()) {
            binding.swipe.isRefreshing = false
            validateButton(true)
            showEmptyState()
        }
    }

    override fun onSuccessGetFilterData(filterAktivityResponse: FilterAktivityResponse?) {
        InboxFilterActivity.launchIntent(this, filterAktivityResponse)
    }

    override fun onSuccessGetInboxDetail(receiptResponse: ReceiptResponse?) {
        if (receiptResponse!!.pendingResponses.titleImage.equals(
                Constant.RECEIPT68,
                ignoreCase = true
            ) || receiptResponse!!.pendingResponses.titleImage.equals(
                Constant.RECEIPT58,
                ignoreCase = true
            )
        ) {
            toReceipt(receiptResponse.pendingResponses.toMappedResponse())
        } else if (receiptResponse.pendingResponses.titleImage.equals(
                Constant.RECEIPT00,
                ignoreCase = true
            )
        ) {
            toReceipt(receiptResponse.pendingResponses.toMappedResponse())
        }
    }

    override fun onSuccessGetInboxDetailRevamp(receiptRevampResponse: ReceiptRevampInboxResponse?) {
        if (receiptRevampResponse!!.receiptRevampResponse != null) {
            if (java.lang.Boolean.TRUE == receiptRevampResponse.receiptRevampResponse.isOnProcess) {
                ReceiptAbnormalRevampActivity.launchIntentReceipt(
                    this,
                    receiptRevampResponse.receiptRevampResponse,
                    false,
                    defaultIcon()
                )
            } else {
                toReceipt(receiptRevampResponse.receiptRevampResponse)
            }
        }
    }


    override fun onSuccessGetInboxAmkkmDetail(amkkmResponse: ReceiptAmkkmResponse?) {
        var flag = false
        if (amkkmResponse!!.dataAlamatResiko[0].name != null) {
            flag = true
        }
        ReceiptAmkkmActivity.launchIntentFromActivity(this, amkkmResponse, flag)
    }

    override fun onSuccessGetOpenAccountDetail(pendingResponse: PendingResponse?) {
        val pendingResponses = pendingResponse?.toMappedResponse()
        toReceipt(pendingResponses)
    }

    override fun onSuccessGetOpenJunio(pendingResponse: PendingResponse?) {
        val pendingResponses = pendingResponse?.toMappedResponse()
        toReceipt(pendingResponses)
    }

    override fun onSuccessInternasional(receiptInternasionalResponse: ReceiptInternasionalResponse?) {
        ReceiptTransferInternationalActivity.launchIntent(this, receiptInternasionalResponse)
    }

    override fun onSuccessKai(receiptTravelTrainResponse: ReceiptTravelTrainResponse?) {
        ReceiptTravelTrainActivity.launchIntentReceipt(this, receiptTravelTrainResponse)
    }

    override fun onSuccessPencairan(responseNew: ReceiptResponseNew?) {
        launchIntent(this, responseNew!!)
    }

    override fun onException12() {

        binding.swipe.isRefreshing = false
        isErrorDetail = false
        validateButton(false)
        if (!isLoadPagination) {
            showEmptyState()
        }
        showError()

    }

    override fun onException06(response: ExceptionResponse?) {
        binding.swipe.isRefreshing = false
        isErrorDetail = false
        validateButton(false)
        binding.rvDetailLis.makeGone()
        showEmptyState()
        showError()
    }

    override fun onException93() {

        binding.swipe.isRefreshing = false
        isErrorDetail = false
        validateButton(false)
        showEmptyState()
        showError()

    }

    override fun onSuccessGetReceiptRevamp(receiptRevampResponse: ReceiptRevampInboxResponse?) {
        if (receiptRevampResponse!!.receiptRevampResponse.isOnProcess) {
            ReceiptAbnormalRevampActivity.launchIntentReceipt(
                this,
                receiptRevampResponse.receiptRevampResponse,
                false,
                defaultIcon()
            )
        } else {
            toReceipt(receiptRevampResponse.receiptRevampResponse)
        }
    }

    override fun onSuccessTabunganRevamp(
        pendingResponse: ReceiptRevampResponse?,
        trxType: String?
    ) {
        if (pendingResponse!!.immediatelyFlag) {
            toReceipt(pendingResponse)
        } else PendingTabunganActivity.launchIntent(
            this,
            pendingResponse, GeneralHelper.getString(R.string.url_buka_tabungan_pending_revamp)
        )
    }

    override fun onSuccesTabunganS3fRevamp(pendingResponse: ReceiptRevampResponse?) {
        toReceipt(pendingResponse)
    }

    override fun onSuccesOnboardEmas(pendingResponse: ReceiptRevampResponse?) {
        if (pendingResponse?.immediatelyFlag == true) {
            ReceiptGagalAFTActivity.launchIntent(this, pendingResponse!!, true)
        } else {
            if (pendingResponse != null) {
               toReceipt(pendingResponse)
            }
        }

    }

    override fun onSuccessInboxPattern(receiptRevampResponse: ReceiptRevampInboxResponse?) {
        if (receiptRevampResponse!!.receiptRevampResponse != null) {
            if (java.lang.Boolean.TRUE == receiptRevampResponse!!.receiptRevampResponse.isOnProcess) {
                ReceiptAbnormalRevampActivity.launchIntentReceipt(
                    this,
                    receiptRevampResponse.receiptRevampResponse,
                    false,
                    defaultIcon()
                )
            } else {
               toReceipt(receiptRevampResponse.receiptRevampResponse)
            }
        }

    }

    override fun onSuccessReceiptTicketEvent(receiptRevampResponse: ReceiptRevampInboxResponse?) {
        toReceipt(receiptRevampResponse?.receiptRevampResponse)
    }

    override fun onAbnormalReceipt(receiptRevampResponse: ReceiptRevampInboxResponse?) {
        toReceipt(receiptRevampResponse?.receiptRevampResponse)
    }

    override fun showSkeleton() {
        skeletonScreen = Skeleton.bind(binding.llTopContent)
            .shimmer(true)
            .angle(20)
            .duration(1200).color(R.color.white)
            .load(R.layout.skeleton_top_home_card)
            .show()

        skeletonBottom = Skeleton.bind(binding.contentBottom)
            .shimmer(true)
            .angle(20)
            .duration(1200).color(R.color.white)
            .load(R.layout.skeleton_home_card)
            .show()
    }

    override fun hideSkeleton() {
        skeletonScreen?.hide()
        skeletonBottom?.hide()
    }

    data class MenuItemCard(val title: String, val drawableResId: Int)

    override fun onClick(title: String) {
        when (title) {
            Constant.INFORMASI_REKENING -> {
                matchedAccount?.let { InformasiRekeningActivity.launchIntent(this, it) }
            }
            Constant.DETAIL_KARTU -> {
                kelolaKartuPresenter.getAccountWithCardNumber()
            }
            Constant.ESTATEMENT -> {
                EStatementActivity.launchIntent(this)
            }
        }
    }

    override fun onClickDetail(position: Int) {
        this.position = position
        hasShownErrorDialog = false
        val type = activityLists[position]?.trxType

        val blockedTypes = listOf(
            "PaymentOpenInsuranceS3f",
            "PurchaseKaiTravel",
            "PaymentTransferInternationalSwift",
            "PaymentPrudential",
            "PaymentAllianz"
        )

        inboxPresenter.setTrxType(type)
        if (type !in blockedTypes) {
            inboxPresenter.getInboxDetail(activityLists[position]?.referenceNumber)
        }
    }

    override fun onSelectedItemId(position: Int) {
        if (position == activityLists.size - 1) {
            activityLists[position]?.id?.let {
                lastId = it
            }
        }
    }

    private fun initiateAdapter() {
        listActivityAdapter = ListActivityAdapter(activityLists, this, this, this)
        binding.rvDetailLis.layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        binding.rvDetailLis.adapter = listActivityAdapter

        binding.scrollView.setOnScrollChangeListener { v: NestedScrollView, _, scrollY, _, oldScrollY ->
            val view = v.getChildAt(v.childCount - 1)
            val diff = view.bottom - (v.height + scrollY)

            if (diff <= 0 && !isLoading) {
                val totalItemCount = activityLists.size
                if (totalItemCount >= 10) {
                    loadMoreInbox()
                    isLoading = true
                    isLoadPagination = true
                }
            }
        }


//        binding.rvDetailLis.addOnScrollListener(object : RecyclerView.OnScrollListener() {
//            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
//                super.onScrolled(recyclerView, dx, dy)
//
//                val layoutManager = recyclerView.layoutManager as? LinearLayoutManager ?: return
//
//                val lastVisibleItem = layoutManager.findLastCompletelyVisibleItemPosition()
//                val totalItemCount = layoutManager.itemCount
//
//                if (!isLoading && lastVisibleItem == activityLists.size - 1 && totalItemCount >= 10) {
//                    loadMoreInbox()
//                    isLoading = true
//                }
//            }
//        })
    }

    private fun loadMoreInbox() {
        activityLists.add(null)
        listActivityAdapter?.notifyItemInserted(activityLists.size - 1)

        handler.postDelayed({
            activityLists.removeAt(activityLists.size - 1)
            val scrollPosition = activityLists.size
            listActivityAdapter?.notifyItemRemoved(scrollPosition)

            inboxPresenter.getInbox(periode, status, fitur, subFitur, lastId, false)
        }, 1000)
    }

    override fun onItemClick(month: String) {
        GeneralHelper.showToast(this, month)
    }

    @SuppressLint("SetTextI18n")
    private fun hideShowSaldo() {
        binding.ivEye.setOnClickListener {
            binding.ivEye.visibility = View.GONE
            binding.tvSaldo.visibility = View.VISIBLE
            binding.ivEyeClose.visibility = View.VISIBLE
            binding.llDotLoadingSaldo.visibility = View.VISIBLE
            binding.tvSaldo.text = getString(R.string.txt_default_saldo)
        }

        binding.ivEyeClose.setOnClickListener {
            binding.ivEye.visibility = View.VISIBLE
            binding.tvSaldo.visibility = View.VISIBLE
            binding.ivEyeClose.visibility = View.GONE
            binding.llDotLoadingSaldo.visibility = View.GONE
            if (isSaldoNormalSuccess) {
                val spannableBalance = formatDecimalText(balance)
                val combined = SpannableStringBuilder()
                    .append("$tempCurrency ")
                    .append(spannableBalance)
                binding.tvSaldo.text = combined
            } else {
                binding.tvSaldo.text = "Rp-"
            }
        }
    }

    override fun onGetSaldo(
        accountList: List<ListRekeningResponse.Account?>?,
        isRefreshed: Boolean
    ) {

    }

    override fun onListRekening(listRekeningResponse: ListRekeningResponse) {
        listItems = listRekeningResponse.account
        matchedAccount = accountRek?.let { findAndMapAccount(it, listItems) }
        binding.tvNoRek.text = matchedAccount?.accountString
        binding.tvTitleToolbar.text = matchedAccount?.productType
    }

    private fun findAndMapAccount(
        accountNumber: String,
        listItems: List<ListRekeningResponse.Account>
    ): ListRekeningResponse.Account? {
        val account = listItems.find { it.account == accountNumber }
        return account
    }

    private fun findAndMapCard(
        accountNumber: String,
        listItems: List<ListPengelolaanKartuRes.Account>
    ): ListPengelolaanKartuRes.Account? {
        val account = listItems.find { it.account == accountNumber }
        return account
    }
    override fun onGetSaldoComplete() {

    }

    override fun onException12(message: String?) {
        binding.swipe.isRefreshing = false
        isErrorDetail = false
        showError()
    }

    override fun onExceptionTotalSaldo() {

    }

    override fun enableButton(enable: Boolean) {

    }

    private fun showDialogCardNotFound() {

        val firstBtnFunction = Runnable {

        }
        OpenBottomSheetGeneralNewSkinFragment.showDialogInformation1Button(
            supportFragmentManager,
            "",
            Constant.IC_CARD_NEWSKIN_NS,
            getString(R.string.txt_title_card_empty),
            getString(R.string.txt_subtitile_card_empty),
            createKotlinFunction0(firstBtnFunction),
            true,
            getString( R.string.onboarding_form_bs_understand_button),
            true
        )

    }

    override fun onSuccessCardList(pengelolaanKartuRes: ListPengelolaanKartuRes) {
        listItemsCard = pengelolaanKartuRes.accounts.toArrayList()
        matchedCard = accountRek?.let { findAndMapCard(it, listItemsCard) }
        if (matchedCard?.cardNumber.isNullOrEmpty()) {
            showDialogCardNotFound()
            // for next development
//            showDialogConfirmation(
//                supportFragmentManager,
//                R.drawable.ic_warning_debit_virtual,
//                "ic_warning_debit_virtual",
//                "kamu tidak memiliki kartu terhubung",
//                "Saat ini kamu tidak memiliki kartu aktif yang terhubung",
//                btnFirstFunction = {
//                    val intent = Intent(
//                        this,
//                        VirtualDebitCardInfoActivity::class.java
//                    )
//                    startActivity(intent)
//                },
//                btnSecondFunction = {},
//                false,
//                "Buat Kartu Debit Virtual",
//                "Batalkan",
//                false,
//                showCloseButton = true
//            )
        } else {
            val request = DetailKelolaKartuReq(matchedCard?.cardNumber, matchedCard?.account)
            kelolaKartuPresenter.getCardDetail(request)
        }
    }

    override fun onSuccessCardDetail(detailKelolaKartuRes: DetailKelolaKartuRes) {
        this.detailKelolaKartuRes = detailKelolaKartuRes
        if (detailKelolaKartuRes.cardType.equals(Constant.VIRTUAL, ignoreCase = true)) {
            goToDetailVdc(detailKelolaKartuRes.cardNumber, detailKelolaKartuRes.isEnabledChangePin)
        } else {
            PyhsicCardDetailActivity.launchIntent(this, detailKelolaKartuRes, matchedCard?.account, 0)
        }
    }

    override fun onSuccessNoCard() {
       showDialogCardNotFound()
    }

    override fun onException12(desc: String, diff: String) {
        //to do
    }

    override fun savePengelolaanKartu(pengelolaanKartuRes: ListPengelolaanKartuRes) {
        kelolaKartuPresenter.savePengelolaanKartu(pengelolaanKartuRes)
    }

    override fun onSuccessProductBrief(productBriefResponse: ProductBriefResponse) {
        //to do
    }

    override fun onMaxRetriesReached(message: String) {
        //to do
    }

    private fun goToDetailVdc(cardNumber: String, enableChangePin: Boolean) {
        VirtualCardDetailActivity.launchIntent(
            this,
            cardNumber,
            enableChangePin,
            CardType.VIRTUAL_DEBIT
        )
    }

    private fun defaultIcon(): Int {
        return R.drawable.bri
    }

    private fun toReceipt(pendingResponse: ReceiptRevampResponse?) {
        val intent = Intent(this, PaymentActivity::class.java)
        intent.putExtra("destination", "receipt");
        intent.putExtra("source", "daily_banking");
        intent.putExtra("pending_data", Gson().toJson(pendingResponse));
        startActivity(intent)
    }

    override fun onRefresh() {
        binding.swipe.isRefreshing = false
        rekeningPresenter.getAccountWithSaldo()
        accountRek?.let { detailCardPresenter.getSaldoNormal(it) }
        callService(true)
    }

    private fun PendingResponse.toMappedResponse(): ReceiptRevampResponse {
        val response = ReceiptRevampResponse()

        response.immediatelyFlag = this.immediatelyFlag
        response.headerDataView = this.headerDataView?.let { ArrayList(it) }
        response.voucherDataView = arrayListOf(this.voucherDataView)
        response.transactionDataView = this.transactionDataView?.let { ArrayList(it) }
        response.minimDataTransaction = this.minimDataTransaction?.let { ArrayList(it) } ?: ArrayList()
        response.detailOpenJunio = this.detailOpenJunio?.let { ArrayList(it) }
        response.amountDataView = this.amountDataView?.let { ArrayList(it) }
        response.kursDataView = this.kursDataView?.let { ArrayList(it) }
        response.totalDataView = this.totalDataView?.let { ArrayList(it) }
        response.streamingId = this.streamingId
        response.title = this.title
        response.subTitle = this.subTitle
        response.description = this.description
        response.titleImage = this.titleImage
        response.referenceNumber = this.referenceNumber
        response.footer = this.footer
        response.footerHtml = this.footerHtml
        response.rowDataShow = this.rowDataShow
        response.transactionDate = this.transactionDate
        response.transactionImage = this.transactionImage
        response.referenceDataView = this.referenceDataView?.let { ArrayList(it) }
        response.mainDataView = this.mainDataView?.let { ArrayList(it) }
        response.subtitle = this.subTitle
        response.transactionSuccess = this.transactionSuccess
        response.trackingDataView = this.trackingDataView?.let { ArrayList(it) }
        response.productDetail = this.productDetail
        response.detailDataView = this.detailDataView?.let { ArrayList(it) }
        response.trxId = this.trxId
        response.paymentDrawer = this.paymentDrawer

        response.billingDetail = DetailListType().apply {
            title = <EMAIL>?.getOrNull(1)?.value.orEmpty()
            subtitle = <EMAIL>?.getOrNull(2)?.value.orEmpty()
        }

        val parts = this.dataViewTransaction?.getOrNull(0)?.value
            ?.split("\n")
            ?.map { it.trim() }
            ?.filter { it.isNotEmpty() }

        val first = parts?.getOrNull(0)
        val second = parts?.getOrNull(1)

        response.sourceAccountDataView = DetailListType().apply {
            title = first.orEmpty()
            subtitle = second.orEmpty()
        }

        return response
    }
}
