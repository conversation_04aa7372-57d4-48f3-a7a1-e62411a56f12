package id.co.bri.brimo.domain.config;

public class Constant {

    public static final Long DELAY_TIMER = 3000L;

    public static final String CURRENCY = "Rp";
    public static final String AES = "aes";
    public static final String EMPTY = "";
    public final static String TOPIC_ALL = "all,android";

    public static final String BLOCK = "BLOCK";
    public static final String AKUN_TERBLOKIR = "akun_terblokir";
    public static final String IMAGE_AKUN_TERBLOKIR = "ic_account_blokir";
    public static final String TITLE_AKUN_TERBLOKIR = "Akun Terblokir";
    public static final String TAG_PICK_CATEGORY = "pick_category";
    public static final String TAG_PICK_DATE = "pick_date";
    public static final String TAG_PICK_ACCOUNT = "pick_account";
    public static final String TAG_PICK_START_DATE = "pick_start_date";
    public static final String TAG_PICK_END_DATE = "pick_end_date";
    public static final String TAG_MAX_TODAY = "max_today";
    public static final String TAG_MIN_DATE = "min_date";
    public static final String TAG_MAX_DATE = "max_date";
    public static final String TAG_START_DATE = "start_date";
    public static final String TAG_END_DATE = "end_date";
    public static final String TAG_DEBET_DATE = "debet_date";
    public static final String TAG_ALL_DATE = "all_date";
    public static final String TAG_DAY_OF_DATE = "day_of_date";

    public static final String CALL_CONTACT_BRI = "1500017";
    public static final String CALL_CONTACT_DPLK = "************";
    public static final String CALL_CONTACT_BRIDS_1 = "1500688";
    public static final String CALL_CONTACT_BRIDS_2 = "************";
    public static final String CALL_CODE_LAYANAN_PERBANKAN = "4452";

    public static final String TAG_DAY_OF_THREE = "day_of_three";
    public static final String TAG_SIX_MONTH = "six_month";
    public static final String TAG_GO_DATE = "go_date";
    public static final String TAG_BACK_DATE = "back_date";
    public static final String TAG_TITLE = "title";
    public static final String TAG_ALIAS = "alias";
    public static final String TAG_VALUE = "value";
    public static final String TAG_TYPE = "type";
    public static final String TAG_ALIAS_BIFAST = "alias_bifast";
    public static final String TAG_ALIAS_AKUN = "alias_akun";
    public static final String TAG_POSITION = "position";
    public static final String TAG_POSITION_DEFAULT = "position_default";
    public static final String TAG_FORCE_LOGOUT = "logout";
    public static final String TAG_ERROR_MESSAGE = "error_message";
    public static final String TAG_ERROR_MESSAGE_BLUE = "error_message_blue";
    public static final String TAG_ERROR_PIN_BRIZZI = "error_pin_brizzi";
    public static final String TAG_ERROR_MESSAGE_VALIDATE = "error_message_validate";
    public static final String[] LIST_RC_NON_VALIDATE_GAGAL = {"OV", "54", "62", "93", "99", "EV", "NF", "NV", "QT", "ER", "AE", "NA", "SC", "IR", "97", "ZB"};
    public static final String TAG_ERROR_MESSAGE_AKTIVASI_BRIZZI = "error_message_aktivasi_brizzi";
    public static final String TAG_ACCOUNT = "account";
    public static final String TAG_ACCOUNT_STRING = "account_string";
    public static final String TAG_PROVIDER = "provider";
    public static final String TAG_NOTIF = "notification";
    public static final String TAG_MESSAGE = "message";
    public static final String TAG_REFFNUM = "reffnum";
    public static final String TAG_IMAGE_POPUP = "image_popup";
    public static final String TAG_BLAST_ID = "blast_id";
    public static final String TAG_PROMO_ID = "promo_id";
    public static final String TAG_SOUND_NOTIF = "sound_notif";
    public static final String TAG_REQ_REFNUM = "req_refnum";
    public static final String TAG_REFNUM = "refnum";
    public static final String TAG_TITLE_INQUIRRY = "title_inquiry";
    //    public static final String TAG_TRX_TYPE = "trx_type";
    public static final String TAG_TYPE_INQUIRY = "type_inquiry";
    public static final String TAG_URL_INQUIRY = "url_inquiry";
    public static final String TAG_URL_CONFIRMATION = "url_confirmation";
    public static final String TAG_URL_PAYMENT = "url_payment";
    public static final String TAG_TYPE_INQUIRY_REVAMP = "type_inquiry_revamp";
    public static final String TAG_URL_INQUIRY_REVAMP = "url_inquiry_revamp";
    public static final String TAG_URL_CONFIRMATION_REVAMP = "url_confirmation_revamp";
    public static final String TAG_URL_PAYMENT_REVAMP = "url_payment_revamp";
    public static final String TAG_REQUEST_CONTENT = "request_content";
    public static final String TAG_PRODUCT_FEATURE = "product_feature";
    public static final String TAG_TITLE_BUTTON = "title_button";
    public static final String TAG_TITLE_FEATURE = "title_feature";
    public static final String TAG_PRODUCT_URL = "product_url";
    public static final String TAG_PRODUCT_TYPE = "product_type";
    public static final String TAG_ALERT_FEATURE = "alert_feature";
    public static final String TAG_ALERT_SALDO = "alert_saldo";
    public static final String TAG_ADDITIONAL = "additional";
    public static final String TAG_TIMESTAMP = "timestamp";
    public static final String TAG_ACTIVITY = "activity";
    public static final String TAG_NOTIF_BACKGROUND = "notification_background";
    public static final String TAG_CONFIRM_DIALOG = "confirm_dialog";
    public static final String TAG_DEPART = "depart_date";
    public static final String TAG_RETURN = "return_date";
    public static final String TAG_REGISID = "registrasi_id";
    public static final String TAG_CASHBACK = "cashback";
    public static final String TAG_IS_REFRESH = "is_refresh";

    public static final String TAG_EDIT_RENCANA = "edit_rencana";
    public static final String LANGUAGE_INDONESIA = "id";
    public static final String TAG_PIN_SALAH = "pin_salah";

    public static final String TAG_CODE = "code";
    public static final String TAG_TRANSACTION_ID = "transaction_id";

    public static final String IB_TYPE = "ib";
    public static final String PFM_TYPE = "pfm";
    public static final String PERSISTENCE_ID = "persistence_id";

    public static final String DATE_PICKER_START = "date_picker_start";
    public static final String DATE_PICKER_END = "date_picker_end";
    public static final String TAG_CURRENT_DAY = "tag_current_day";

    public static final String TAG_CURRENT_DATE = "tag_current_date";

    public static final String TAG_MAX_MONTHYEAR_AFT = "tag_max_monthyear_aft";

    public static final String TAG_MIN_MONTHYEAR_AFT = "tag_min_monthyear_aft";

    public static final String TAG_CURRENT_MONTHYEAR_AFT = "tag_current_monthyear_aft";

    public static final String TAG_MONTHDATE_MONTHYEAR_AFT = "tag_monthdate_monthyear_aft";
    public static final String TAG_SELECTED_DATE_PICKED_MONTH = "tag_selected_date_picked_month";

    public static final String TAG_START_BLOCKED_DAY = "tag_start_blocked_day";
    public static final String TAG_END_BLOCKED_DAY = "tag_end_blocked_day";
    public static final String TAG_SELECTED_DATE = "tag_selected_date";

    public static final String TAG_IS_ENABLE_UNLIMITED = "tag_is_enable_unlimited";
    public static final String TAG_UNLIMITED_STRING = "unlimited";
    public static final String CETAK_EMAS_FIRST_OPENED = "cetak_emas_first_opened";
    public static final String TRANSFER_FIRST_OPENED = "transfer_first_opened";
    public static final String TAG_CUSTOM_CALENDAR = "CUSTOM_CALENDAR";
    public static final String TAG_CALENDAR_FOR_RDN = "calendar_for_rdn";

    public static final String LANGUAGE = "language";

    public static final String LANGUAGE_ID = "language_id";

    public static final int DELAY_SELECTED_LANGUAGE = 0;


    public static final String LANGUAGE_ENGLISH = "en";

    public static final String IS_CHANGE_LANGUAGE = "isChangeLanguage";
    public static final String BILL_CREATED = "bill_created";

    public static final int MIN_PHONE_LENGTH_PREFIX_VALIDATION = 4;
    public static final int MIN_PHONE_POSTPAID_LENGTH_VALIDATION = 9;
    public static final int MAX_PHONE_POSTPAID_LENGTH_VALIDATION = 15;
    public static final int MAX_INSTALLMENT_LENGTH_VALIDATION = 20;
    public static final int MAX_ALL_LENGTH_VALIDATION = 30;

    //new blastype const
    public static class BlastType {
        public static final String PROMO = "promo_blast";
        public static final String TARTUN_NDS = "tartun_nds";
        public static final String INFO = "info";
        public static final String REMINDER = "reminder";
        public static final String RECOMENDATION = "recomendation";
        public static final String CARDLESS = "cardless";
        public static final String SAFETY_MODE = "safety_mode_nds";
        public static final String TRX_PURCHASE = "purchase";
        public static final String TRX_PAYMENT = "payment";
        public static final String TRX_TRANSFER = "transfer";
        public static final String INCOME_TRANSACTION_PFM = "income_transaction";
        public static final String DIRECT_FEATURE = "direct_feature";
        public static final String CASHBACK_BLAST_TYPE = "cashback_blast";
        public static final String BIRTHDAY_GREETINGS = "birthday_greetings";
        public static final String CIA_NOTIF_BLAST_TYPE = "ticketcia";
        public static final String AFT_NOTIF_SCHEDULER_BLAST_TYPE = "aft_notif_scheduler";
        public static final String PROMO_TYPE = "promo";
        public static final String DIRECT_SPECIFIC_FEATURE = "direct_specific_feature";
    }

    public static final String PROMO_BLAST_TYPE = "promo_blast";
    public static final String TARTUN_NDS_TYPE = "tartun_nds";
    public static final String INFO_BLAST_TYPE = "info";
    public static final String REMINDER_BLAST_TYPE = "reminder";
    public static final String RECOMENDATION_BLAST_TYPE = "recomendation";
    public static final String CARDLESS_BLAST_TYPE = "cardless";

    public static final String SAFETY_MODE_BLAST_TYPE = "safety_mode_nds";

    public static final String NOTIF_KK_GPN_BLAST_TYPE ="kk_gpn";

    public static final String NOTIF_BINDING_GPN_BLAST_TYPE ="BindingConfirmationGPN";
    public static final String NOTIF_BINDING_TRX_GPN_BLAST_TYPE ="BindingWithPaymentConfirmationGPN";

    public static final String REGISTRATION_BRIMO = "RegistrationBrimo";
    public static final String TRANSFER_BLAST_TYPE = "transfer";
    public static final String ONBOARDING_BRIMO = "OnboardingBrimo";
    public static final String AFT_NOTIF_SCHEDULER_BLAST_TYPE = "aft_notif_scheduler";
    public static final String INCOME_TRANSACTION_PFM = "income_transaction";
    public static final String EXPENSE_TRANSACTION_PFM = "expense_transaction";
    public static final String TAG_FROM_FASTMENU = "fromfastmenu";
    public static final String TAG_FROM_BINDING_HOME = "frombindinghome";
    public static final String STATUS_NOT_MATCH = "status_not_match";

    public static final String ANDROID_DEVICE_TYPE = "1";
    public static final String MERCHANT_NAME = "Bank BRI";
    public static final String TRANSACTION_ID = "002";
    public static final String FIRST_KCIC = "first_kcic";
    public static final String TRX_TYPE_ASURANSI = "asuransi";
    public static final String TRX_TYPE_KLAIM_DPLK_DETAIL = "klaim_dplk_detail";
    public static final String TRX_TYPE_KLAIM_DPLK_RECEIPT = "klaim_dplk_receipt";
    public static final String TRX_TYPE_WITHDRAW_RDN_RECEIPT = "withdraw_rdn_receipt";
    public static final String TRX_TYPE_BRIVA = "briva";
    public static final String AKUN_DEFAULT = "akun_default";
    public static final String LOGIN_FLAG = "login_flag";
    public static final String USER = "user";
    public static final String TRX_TYPE_IPL = "ipl";
    public static final String TRX_TYPE_TELKOM = "telkom";
    public static final String DIPROSES = "Diproses";

    public static final String TAG_IS_CHECKED_UNLIMITED = "tag_is_checked_unlimited";
    public static final String UNLIMITED_DATE_FLAG = "unlimited";
    public static final String TAG_TERJADWAL_IS_EDIT = "tag_terjadwal_edit";

    public static final String TAG_IS_PENDING_REGIST = "IS_PENDING_REGIST";

    public static final int REQ_KATEGORI = 1;
    public static final int REQ_PEMBAYARAN = 2;
    public static final int REQ_CALENDAR = 3;
    public static final int REQ_INCOME = 4;
    public static final int REQ_OTP = 5;
    public static final int REQ_READ_CONTACT = 6;
    public static final int REQ_DURATION = 7;
    public static final int REQ_NOTIF_DEFAULT = 8;
    public static final int REQ_OPEN_GALERY = 9;
    public static final int REQ_DOWNLOAD_MUTATION = 10;
    //request ID utk membedaakan ID Dialog Pop Up
    public static final int DIALOG_BASE_GE = 40;
    public static final int DIALOG_LOGIN = 42;
    public static final int DIALOG_EDIT_PFM = 41;
    public static final int DIALOG_SET_DEFAULT = 43;
    //startActivity for Result TAG
    public static final int REQ_PAYMENT = 100;
    public static final int REQ_CREATE_PIN = 101;
    public static final int REQ_EDIT_SAVED = 102;
    public static final int REQ_SCAN = 103;
    public static final int REQ_IMAGE = 104;
    public static final int REQ_FILTER_INBOX = 105;
    public static final int REQ_REGIS = 106;
    public static final int REQ_FORGET_PIN = 107;
    public static final int REQ_NON_PAYMENT = 108;
    public static final int REQ_PETTUNJUK1 = 109;
    public static final int REQ_PETTUNJUK2 = 110;
    public static final int REQ_BLOK = 111;
    public static final int REQ_SET_DEFAULT = 112;
    public static final int REQ_SET_Notif = 113;
    public static final int CHROME_CUSTOM_TAB_REQUEST_CODE = 114;
    public static final int REQ_ALIAS = 115;
    public static final int REQ_AKTIF_ALIAS = 116;
    public static final int REQ_DEL_ALIAS = 117;
    public static final int REQ_PICK_SEAT = 118;
    public static final int REQ_PICK_SEAT_RETURN = 119;
    public static final int REQ_CC_SOF = 120;
    public static final int REQ_FORGET = 121;
    public static final int REQ_ABOUT = 122;
    public static final int REQ_UPDATE = 123;
    public static final int REQ_FINANSIAL = 124;
    public static final int REQ_CASHBACK = 125;
    public static final int REQ_MNV = 126;
    public static final int REQ_CARD = 127;
    public static final int REQ_REISSUE = 128;
    public static final int REQ_UBAH_KATA_KUNCI = 129;
    public static final int REQ_UBAH_PIN = 130;
    public static final int REQ_FORGOT_PIN = 1301;
    public static final int REQ_FORGOT_PASS = 1300;
    public static final int REQ_INVESTASI_DASHBOARD = 131;
    public static final int REQ_SAVE_SAVED = 132;
    public static final int REQ_AUTO_PAYMENT = 133;
    public static final int REQ_NEED_REVOKE = 134;
    public static final int REQ_SIMULASI = 135;
    public static final int REQ_RIPLAY = 136;
    public static final int REQ_REGIS_SMART_TRANSFER = 136;
    public static final int REQ_TAGGING_SMART_TRANSFER = 137;
    public static final int REQ_SEARCH_SAVED_HISTORY = 138;
    public static final int REQ_EDIT_BIND_WALLET = 139;
    public static final int REQ_WALLET_BINDING_SUCCESS = 140;

    public static final int REQ_REGIS_CP_1 = 201;
    public static final int REQ_REGIS_CP_2 = 202;
    public static final int REQ_REGIS_CP_3 = 203;
    public static final int REQ_REGIS_CP_4 = 204;
    public static final int REQ_REGIS_CP_5 = 205;
    public static final int REQ_REGIS_CP_6 = 206;
    public static final int REQ_REGIS_CP_7 = 207;
    public static final int REQ_REGIS_CP_8 = 208;
    public static final int REQ_REGIS_CP_9 = 209;
    public static final int REQ_REGIS_CP_10 = 210;
    public static final int REQ_REGIS_CP_11 = 211;
    public static final int REQ_REGIS_CP_12 = 212;
    public static final int REQ_REGIS_CP_13 = 213;
    public static final int REQ_REGIS_CP_14 = 214;
    public static final int REQ_REG_DS_1 = 301;
    public static final int REQ_BUKA_REKENING = 302;
    public static final int REQ_AMBIL_FISIK = 303;
    public static final int REQ_BACK_INFORMASI = 303;
    public static final int MAX_LENGHT_URL = 36;


    // Ubah PIN Debit/Kredit
    public static final int REQ_PIN_CHANGE_SUCCESS = 200;
    public static final int REQ_PIN_CHANGE_MAX_RETRY = 201;
    public static final Long INPUT_PIN_DELAY = 1500L;

    //result code untuk list product
    public static final int REQ_RESCODE_PRODUCT = 401;
    public static final int RESULT_RESCODE_PRODUCT_PLN = 401;
    public static final int REQ_CODE_MICROSITE = 502;
    public static final int OPEN_ACCOUNT_FACE_VERIFICATION = 7;
    public static final int OPEN_ACCOUNT_START_REGISTRATION = 9;
    public static final int REQ_BINDING_MERCHANT = 129;

    public static final String IMAGE_BANNER = "bannerImage";
    public static final String TITLE_BANNER = "bannerTitle";
    public static final String START_DAY = "start_day";
    public static final String START_MONTH = "start_month";
    public static final String START_YEAR = "start_year";
    public static final String END_DAY = "end_day";
    public static final String END_MONTH = "end_month";
    public static final String END_YEAR = "end_year";
    public static final String DB_DATE_FORMAT = "yyyy-MM-dd";
    public static final String DB_TIMER_FORMAT = "HH:mm:ss";
    public static final String PFM_DATE_FORMAT = "dd MMM yyyy";
    public static final String DATE_FORMAT = "dd MM yyyy";
    public static final String DATE_FORMAT_SLASH = "dd/MM/yyyy";
    public static final String DATE_FORMAT_YEAR = "dd MMM yy";
    public static final String DATE_FORMAT_DASH = "dd-MM-yyyy";
    public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
    public static final String DATE_FORMAT_DD_MMM_YYYY = "dd MMM yyyy";
    public static final String DATE_FORMAT_DD_MMMM_YYYY = "dd MMMM yyyy";
    public static final String DATE_FORMATYYYYMMDD = "yyyyMMdd";
    public static final String DATE_FORMATDDMMYY = "ddMMyy";
    public static final String DATE_FORMAT_DATE_TIME = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT_TIME = "HH:mm";
    public static final String KATEGORI_NAME = "req_kategori";
    public static final String KATEGORI_ID = "req_id";
    public static final String KATEGORI_TYPE = "req_type";
    public static final String KATEGORI_ICON = "req_icon";
    public static final String TOKEN_KEY = "token_key";
    public static final String REF_NUM = "reference_number";
    public static final String KEY = "key";
    public static final String USERNAME = "username";
    public static final String USER_ALIAS = "user_alias";
    public static final String FAV_NAME = "favorite_name";
    public static final String AMOUNT = "amount";
    public static final String ACCOUNT = "account";
    public static final String ACCOUNT_NUM = "account_number";
    public static final String USER_EXIST = "userExist";
    public static final String PIN = "pin";
    public static final String NOTE = "note";
    public static final String BRIZZI_VALIDATE = "validate";
    public static final String PFM_FIRST_OPENED = "PFMFirstOpened";
    public static final String TRANSFER_INTERNASIONAL_FIRST_OPENED = "TransferInternasionalFirstOpened";
    public static final String SIKLUS_FIRST_OPENED = "SiklusFirstOpened";
    public static final String VALLAS_FIRST_OPENED = "VallasFirstOpened";
    public static final String FAST_MENU_FIRST_OPENED = "FastMenuFirstOpened";
    public static final String ASURANSI_FIRST_OPENED = "AsuransiFirstOpened";

    public static final String RENCANA_FIRST_OPENED = "RencanaFirstOpened";

    public static final String DETAIL_RENCANA_FIRST_OPENED = "DetailRencanaFirstUsed";

    public static final String INFO_RENCANA_FIRST_OPENED = "InfoRencanaFirstOpened";
    public static final String ALLOW_NOTIF = "allow_popup_notif";
    public static final String DESCRIPTION = "description";
    public static final String CARD_TOKEN = "card_token";
    public static final String CHAT_BANKING_FIRST_OPENED = "ChatBankingFirstOpened";

    public static final String BLOCK_CARD_FIRST_OPENED = "BlockCardFirstOpened";

    public static final String VOICE_ASSISTANT_STATUS = "VoiceAssistant";

    public static final String PROFILE_REVAMP_FIRST_OPENED = "ProfileRevampFirstOpened";
    public static final String EMAS_FIRST_OPENED = "EmasFirstOpened";
    public static final String PENGKINIAN_FIRST_OPENED = "pengkinianFirstOpened";
    public static final String PENGKINIAN_RTRW_DEFAULT = "000";

    public static final String DEPOSITO_REVAMP_FIRST_OPEN = "depositoFirstOpened";
    public static final String LIST_CC_ALREADY_SELECT_ON_QRIS = "listCcAlreadySelectOnQris";
    public static final String COUNT_CC_QRIS_NOT_SOF = "countCcQrisNotSof";
    public static final String RDN_FIRST = "rdn_first";
    public static final String KSEI_FIRST_OPENED = "KseiFirstOpened";
    public static final String INVESTASI_FRIST = "investasi_first";
    public static final String INVESTASI_FRIST_NO_CURRENCY = "investasi_first_currency";
    public static final String INVESTASI_FRIST_NO_ASET = "investasi_first_aset";
    public static final String DASHBOARD_DPLK_REVAMP_FIRST = "dashboard_dplk_revamp_first";
    public static final String BUBBLE_DASHBOARD_RDN_REVAMP = "bubble_dashboard_rdn_revamp";
    public static final String BRIMO_FSTVL_OPEN_UNTIL = "brimo_fstvl_open_until";

    public static final String CHECKPOINT_RDN = "checkpoint_rdn";
    public static final String BACK_GENERAL = "back_general";
    public static final String BLOKIR_KARTU = "blokir_kartu";
    public static final String UBAH_PIN = "ubah_pin";
    public static final String CARD_NUMBER = "card_number";
    public static final String VIRTUAL = "VIRTUAL";
    public static final String FROM_CARD_MANAGEMENT = "fromPengelolaanKartu";
    public static final String TRANSFER = "transfer";
    public static final String USER_TYPE = "usertype";
    public static final String DEVICE_ID = "deviceid";
    public static final String SALDO_REK_UTAMA = "saldo_rek_utama";
    public static final String INSTALL_ID = "install_id";
    public static final String CURRENCY_REK_UTAMA = "currency_rek_utama";
    public static final String SALDO_REK_UTAMA_STRING = "saldo_rek_utama_string";
    public static final String NAME_REK_UTAMA = "name_rek_utama";
    public static final String NICKNAME = "nickname";
    public static final String FULL_NAME = "full_name";
    public static final String VALLAS_LIST = "vallas_list";
    public static final String TGL_STARTDAY_SIKLUS_PFM = "siklus_pfm";
    public static final String TGL_ENDDAY_SIKLUS_PFM = "siklus_pfm_end_day";
    public static final String END_MONTH_SIKLUS_PFM = "end_month_siklus_pfm";
    public static final String START_MONTH_SIKLUS_PFM = "start_month_siklus_pfm";
    public static final String TGL_VIEW_SIKLUS_PFM = "tgl_siklus_pfm";
    public static final String TGL_END_VIEW_SIKLUS_PFM = "tgl_end_siklus_pfm";
    public static final String SAVE_STARDAY_PFM = "save_starday_pfm";
    public static final String SAVE_ENDDAY_PFM = "save_endday_pfm";
    public static final String SAVE_STARMONTH_PFM = "save_starmonth_pfm";
    public static final String SAVE_ENDMONTH_PFM = "save_endmonth_pfm";
    public static final String VALLAS_DOLLAR = "usd";
    public static final String VALLAS_DOLLAR_SINGAPURE = "sgd";
    public static final String VALLAS_DOLLAR_HONGKONG = "hkd";
    public static final String VALLAS_DOLLAR_AUSTRALIA = "aud";
    public static final String VALLAS_EURO = "eur";
    public static final String VALLAS_RIYAL = "sar";
    public static final String VALLAS_POUND = "gbp";
    public static final String VALLAS_YUAN = "cny";
    public static final String VALLAS_YEN = "jpy";
    public static final String VALLAS_DIRHAM = "aed";
    public static final String VALLAS_RUPIAH = "rp";
    public static final String VALLAS_NZD = "nzd";
    public static final String VALLAS_MYR = "myr";
    public static final String VALLAS_THB = "thb";

    public static final String COMING_SOON = "comingsoon";
    public static final String TITLE_COMING_SOON = "Mohon Maaf";
    public static final String DESC_COMING_SOON = "Fitur ini akan segera hadir di BRImo. \n Terimakasih atas pengertiannya.";
    public static final String IMAGE_COMING_SOON = "image_coming_soon";
    public static final String IMAGE_ALIAS_UNAVAIL = "image_alias_unavail";
    public static final String KONEKSI_TERPUTUS = "koneksiterputus";
    public static final String TITLE_KONEKSI_TERPUTUS = "Koneksi internetmu kurang baik";
    public static final String DESC_KONEKSI_TERPUTUS = "Periksa kembali koneksi internet dengan cek ulang \nPaket Data, Wifi atau Jaringanmu.";
    public static final String IMAGE_KONEKSI_TERPUTUS = "ic_no_koneksi";
    public static final String SERVER_UNDER_MAINTENANCE = "serverundermaintenance";
    public static final String TITLE_SERVER_UNDER_MAINTENANCE = "Mohon maaf, kami belum bisa memproses transaksimu";
    public static final String DESC_SERVER_UNDER_MAINTENANCE = "Jangan khawatir! Mohon tunggu beberapa saat \nuntuk dapat menikmati kembali transaksi di aplikasi BRImo.";
    public static final String IMAGE_SERVER_UNDER_MAINTENANCE = "ic_server_maintenance";
    public static final String IMAGE_ACCESSIBILITY_SERVICE_DETECTED = "ic_disable_accessibility";
    public static final String TRANSAKSI_GAGAL = "transaksigagal";
    public static final String TITLE_TRANSAKSI_GAGAL = "Transaksi Belum Berhasil";
    public static final String DESC_TRANSAKSI_GAGAL = "Maaf, transaksi yang Anda lakukan belum berhasil. Silakan coba lagi.";
    public static final String IMAGE_TRANSAKSI_GAGAL = "ic_transaksi_gagal";
    public static final String NFC_GAGAL = "nfcgagal";
    public static final String TITLE_NFC_GAGAL = "NFC Anda tidak tersedia";
    public static final String DESC_NFC_GAGAL = "Setelah melakukan top up, silakan update saldo Anda di ATM BRI atau EDC BRI yang tersedia Agen BRILink atau Kantor Cabang BRI.";
    public static final String IMAGE_NFC_GAGAL = "ic_error_nfc";
    public static final String API_LIMIT = "apilimit";
    public static final String TITLE_API_LIMIT = "Kamu Kerap Melakukan Transaksi Ini";
    public static final String IMAGE_API_LIMIT = "ic_api_limit";
    public static final int REQUEST_READ_PHONE_STATE = 50;
    public static final int REQUEST_WRITE_STATE = 51;
    public static final int REQUEST_RECORD_AUDIO = 52;
    public static final int REQUEST_PHONE_CALL = 53;
    public static final int REQUEST_LOCATION_MAP = 54;
    public static final int REQUEST_CAMERA = 54;
    public static final String REQUEST_PULSA = "pulsa";
    public static final String REQUEST_MUTASI = "mutasi";
    public static final String REQUEST_TARIK = "tariktunai";
    public static final String REQUEST_BASE_INQUIRY = "base_inquiry";
    public static final String REQUEST_CLOSE_INQUIRY = "close_inquiry";
    public static final String REQUEST_INQUIRY = "inquiry";
    public static final String REQUEST_PLN = "listrik";
    public static final String REQUEST_BRIZZI = "brizzi";
    public static final String REQUEST_RECEIPT = "receipt";
    public static final String REQUEST_SALDO = "saldo";
    public static final String REQUEST_RECEIPT_SHARE = "receipt_share";
    public static final String REQUEST_RENCANA = "britama_rencana";
    //ApplyVcc
    public static final String APPLY_VCC_DATA_FORM = "apply_vcc_data_form";
    public static final String APPLY_VCC_REQUEST = "apply_vcc_request";
    public static final String RIPLAY_URL = "url_riplay";
    //Merchant
    public static final String MID_MERCHANT = "mid_merchant";
    public static final String MID_DEFAULT = "mid_default";
    public static final String ACC_NUMB = "acc_numb";
    //Registrasi
    public static final String CHECK_POINT = "checkpoint";
    public static final String VERIF_ID = "verifikasiid";
    public static final String FEATURE_REGIS = "registrasi_brimo";
    public static final String IMAGEKTP = "image_ktp";
    public static final String IMAGESELFIE = "image_selfie";
    public static final String NIK = "nik";
    public static final String BIRTHDATE = "birtdate";
    public static final String VIDEOREGIS = "video_regis";
    public static final String SIGNREGIS = "imagesign";
    public static final String TIMEOTP = "timeotp";
    public static final String EXPTIME = "expiredtime";
    public static final String NAME = "nama";
    public static final String MOTHERNAME = "namaibu";
    public static final String NOHP = "no_hp";
    public static final String TAG_CONTENT = "content";
    public static final String MENU = "menu";
    public static final int CHECK_POINT_DEFAULT = 0;

    //Digital Saving
    public static final String CHECK_POINT_DS = "check_point_ds";
    public static final String VERIF_ID_DS = "verifikasi_id_ds";
    public static final String FEATURE_DS = "digital_saving";
    public static final String CHECK_DOCUMENT_DS = "check_dokumen_ds";
    public static final String CHECK_POINT_CLASS_DS = "check_point_class_ds";
    public static final String TIPEPRODUK = "kode_produk_ds";
    public static final String KODEUKERDS = "kode_uker_ds";
    public static final String IMAGEKTPDS = "image_ktp_ds";
    public static final String NIKDS = "nik_ds";
    public static final String NAMADS = "nama_ds";
    public static final String TGLLAHIRDS = "tanggal_lahir_ds";
    public static final String NOHPDS = "no_hp_ds";
    public static final String EMAILDS = "email_ds";
    public static final String IBUKANDUNGDS = "nama_ibu_ds";
    public static final String KODEPOSKTP = "kodepos_ktp_ds";
    public static final String KODEPOSIDKTP = "kodeposid_ktp_ds";
    public static final String DESKRIPOTPEMAILDS = "deskrip_otp_email_ds";
    public static final String VIDEODS = "video_regis_ds";
    public static final String IMAGESELFIEDS = "image_selfie_ds";
    public static final String IMAGETTDSS = "image_ttd_ds";
    public static final String TMPTLAHIRDS = "tmptLahir_ds";
    public static final String AGAMADS = "agama_ds";
    public static final String KODEAGAMADS = "kode_agama_ds";
    public static final String JENKELDS = "jenkel_ds";
    public static final String NPWPDS = "npwp_ds";
    public static final String OBJECTAR = "object_ar";
    public static final String KEWARGANEGARAANDS = "kewarganegaraan_ds";
    public static final String STATUSPERNIKAHANDS = "status_pernikahan_ds";
    public static final String STATUSPERNIKAHANKODEDS = "status_pernikahan_kode_ds";
    public static final String PENDIDIKANTERAKHIRDS = "pendidikan_terakhir_ds";
    public static final String PENDIDIKANTERAKHIRKODEDS = "pendidikan_terakhir_kode_ds";
    public static final String NAMAKONTAKDARURATDS = "nama_kontak_darurat_ds";
    public static final String HUBUNGANDRTDS = "hubungandrt_ds";
    public static final String NOHPDARURATDS = "no_hp_darurat_Ds";
    public static final String NAMAPERUSAHAANDS = "nama_perusahaan_ds";
    public static final String JENISPEKERJAANDS = "jenis_pekerjaan_ds";
    public static final String JENISPEKERJAANKODEDS = "jenis_pekerjaan_kode_ds";
    public static final String BIDANGPEKERJAANDS = "bidang_pekerjaan_ds";
    public static final String BIDANGPEKERJAANKODEDS = "bidang_pekerjaan_kode_ds";
    public static final String JABATANPEKERJAANDS = "jabatan_pekerjaan_ds";
    public static final String JABATANPEKERJAANKODEDS = "jabatan_pekerjaan_kode_ds";
    public static final String ALAMATKANTORDS = "alamat_kantor_ds";
    public static final String KODEPOSPEKERJAANDS = "kode_pos_ds";
    public static final String SUMBERPENDAPATANDS = "sumber_pendapatan_ds";
    public static final String SUMBERPENDAPATANKODEDS = "sumber_pendapatan_kode_ds";
    public static final String PENDAPATANPERBULANDS = "pendapatan_perbulan_ds";
    public static final String PENDAPATANPERBULANKODEDS = "pendapatan_perbulan_kode_ds";
    public static final String TRXNORMALHARIANDS = "trx_normal_harian_ds";
    public static final String TRXNORMALHARIANKODEDS = "trx_normal_harian_kode_ds";
    public static final String TUJUANPEMBUKAANDS = "tujuan_pembukaan_ds";
    public static final String TUJUANPEMBUKAANKODEDS = "tujuan_pembukaan_kode_ds";
    public static final String PEMILIKDANADS = "pemilik_dana_ds";
    public static final String HUBUNGANKEUANGAN = "hubungan";
    public static final String RTRWKTP = "rtrw_ktp_ds";
    public static final String ALAMATDOMISILI = "alamat_domisili_ds";
    public static final String RTRWDOMISILI = "rtrw_domisili_ds";
    public static final String KODEPOSDOMISILI = "kodepos_domisili_ds";
    public static final String KELURAHANDOMISILI = "kelurahaan_domisili_ds";
    public static final String KECAMATANDOMISILI = "kecamatan_domisili_ds";
    public static final String KOTADOMISILI = "kota_domisili_ds";
    public static final String PROVINSIDOMISILI = "provinsi_domisili_ds";
    public static final String NAMEVERIF = "name_verif_ds";
    public static final String ACCOUNTNUMBERDS = "account_number_ds";
    public static final String USERNAMEDS = "username_ds";
    public static final String OTPTELEPONDS = "opttelepon_ds";
    public static final String DESKRIPDS = "deskrip";
    public static final String TARTUNBRILINK = "TartunBrilink";
    public static final String TARTUNATMCODE = "atm";
    public static final String TARTUNINDOMARETCODE = "indomaret";
    public static final String TARTUNBRILINKCODE = "agen_brilink";
    public static final String TARTUNALFAGROUPCODE = "AlfaGroup";
    //open account
    public static final String OPEN_ACCOUNT_GENERAL = "general";
    public static final String OPEN_ACCOUNT_S3f = "s3f";
    public static final String OPEN_ACCOUNT_RENCANA = "rencana";
    public static final String OPEN_ACCOUNT_VALAS = "valas";
    public static final String OPEN_ACCOUNT_JUNIO = "junio";
    public static final String TARTUNMERCHANT = "TartunMerchant";
    public static final String TARTUNATM = "TartunATM";
    //receipt
    public static final String RECEIPT58 = "receipt_58";
    public static final String RECEIPT68 = "receipt_68";
    public static final String RECEIPT58_REVAMP = "receipt_58_revamp";
    public static final String RECEIPT68_REVAMP = "receipt_68_revamp";
    public static final String DEFTYPEIDENTIFYER = "drawable";
    public static final String PENDING = "pending";
    public static final String VALIDATE = "validate";
    public static final String RECEIPT00 = "receipt_00";
    public static final String RECEIPT00_REVAMP = "receipt_00_revamp";
    public static final String URI_DOWNLOAD = "/Download";
    public static final String FEATURE_QR_MPM = "qr_mpm";
    public static final String GENRES = "generate_response";
    public static final String BINDING_CODE_GOTO = "goto";
    public static final String BINDING_CODE_AUTH_CODE = "authCode";
    public static final String BINDING_CODE_STATE = "state";
    public static final String FLAG_BANK_BRI_CODE = "002";
    public static final String FLAG_METHOD_BIFAST = "bifast";
    public static final String FLAG_METHOD_RTGS = "rtgs";
    public static final String FLAG_METHOD_ONLINE = "online";
    public static final String FLAG_BIFAST_REKENING = "account";

    public static final String SKIP = "skip";

    public static final String FLAG_TYPE_QR_PARKING = "parking_spi";
    public static final String FLAG_TYPE_QR_MPM = "qris_mpm";
    public static final String FLAG_TYPE_QR_CB = "qris_cb";

    public static String TAG_START_NAME = "brimo";
    public static String TAG_END_NAME = ".png";

    public static String KTP_FILE_NAME = "ktp.png";

    //apply vcc
    public static final String PHOTO_KTP_FILE_NAME = "ktp.jpg";
    public static final String PHOTO_SELFIE_FILE_NAME = "selfie.jpg";

    //listrik revamp
    public static String PLN_TYPE_CODE_PREPAID = "prepaid";
    public static String PLN_TYPE_CODE_POSTPAID = "postpaid";
    public static String PLN_TYPE_CODE_NONTAGLIS = "nontaglis";
    public static String TRX_TYPE_LISTRIK = "listrik";

    // emas type
    public static final String EMAS_SELL_TYPE = "emas_jual";
    public static final String EMAS_BUY_TYPE = "emas_beli";
    public static final String EMAS_MOLDING_TYPE = "emas_molding";

    public static final String TRX_TYPE_SIGNAL = "trx_type_signal";

    //Biometric
    public static final String STATUS_BIOMETRIC = "status_biometric";
    public static final String KEY_BIOMERIC = "BrimoBio";
    public static final String VALUE_KEY_BIOMETRIC = "value_biometric";
    public static final String BIOMETRIC_TYPE = "biometric_type";
    public static final String FINGERPRINT_AVAILABLE = "fingerprint_available";
    public static final String FACE_ID_AVAILABLE = "face_available";
    public static final String BIOMETRIC_CHANGED = "is_bio_changed";
    public static final String STATUS_UPDATE_BIOMETRIC = "is_update_bio";
    public static final String FACE_ID = "Face ID";
    public static final String FINGERPRINT_FACE_ID = "Fingerprint";

    public static final String BOTTOM_BIOMETRIC = "is_bottom_biometric";
    public static final int ERROR_NEGATIVE_BUTTON = 13;
    public static final int ERROR_LOCKOUT = 7;
    public static final int ERROR_LOCKOUT_PERMANENT = 9;
    public static final int ERROR_USER_CANCELED = 10;
    public static final int ERROR_CANCELED = 5;
    public static final int ERROR_NO_BIOMETRICS = 11;

    public static String URL_GOOGLE = "www.google.com";
    public static final String ACCOUNT_TYPE = "account_type";
    public static final String REK_TYPE = "T";

    public static String LINK_CODE = "link_code";

    public static String TYPE_FLAG = "type_flag";
    public static String BRIGUNA_NAME = "briguna";
    public static String FILTER = "filter";
    public static String MONTH = "month";
    public static String TODAY_FILTER = "TODAY";
    public static String WEEK_FILTER = "WEEK";
    public static String SEVEN_DAY_FILTER = "7DAY";
    public static String MONTH_FILTER = "MONTH";
    public static String RANGE_FILTER = "RANGE";
    public static String TRANSACTION_TYPE_ID = "transaction_type_id";
    public static String TRANSACTION_TYPE_NAME = "transaction_type_name";
    public static String TRANSACTION_TYPE_ALL = "ALL";
    public static String MONTH_TEXT = "month_text";
    public static String TODAY_TEXT = "Hari Ini";
    public static String SEVEN_DAY_TEXT = "7 Hari Terakhir";

    public static String ZERO_DAY_FILTER = "0D";

    public static String ONE_WEEK_FILTER = "1W";

    public static String ONE_MONTH_FILTER = "1M";

    public static String CARD_STATUS = "CARDSTATUS";
    public static String ECOMM = "ECOMM";
    public static String INT = "INT";
    public static String DOM = "DOM";

    public static String PFM_INCOME = "pemasukan";
    public static String PFM_OUTCOME = "pengeluaran";

    public static String FILTER_ALL = "all";

    public static final String LIST_PFM_FIRST_OPENED = "list_pfm_first_opened";
    public static final String REPORT_PFM_FIRST_OPENED = "report_pfm_first_opened";
    public static final String DONATION_BRIKASIH_CODE = "44";
    public static final String FRAGMENT_DETAIL_KSEI = "detail_ksei_fragment";

    //MNV
    public static final String STATE_MNV = "state";
    public static final String CODE_MNV = "code";
    public static final String ERROR_MNV = "error";
    public static final String TELKOMSEL_MNV = "mnv.telkomsel.com";

    public static final String LOCATION_MNV = "Location";

    //call bri email
    public static final String[] EMAIL_BRI = new String[]{"<EMAIL>"};

    //Dashboard Lifestyle
    public static final String DATE_MENU_UPDATE = "date_menu_update";

    // Belanja Harian
    public static final String PAUSE_TIME = "pause_time";

    // Fast Menu
    public static final String FAST_MENU = "fast_menu";
    public static final String FAST_MENU_DEFAULT = "fast_menu_default";
    public static final String FAST_MENU_DEFAULT_TOGGLE = "fast_menu_default_toggle";

    // SMS Retriever
    public static final String WHATSAPP = "WA";

    //Style
    public static final String SEMIBOLD = "semi-bold";
    public static final String BOLD = "bold";

    //aes padding
    public static final int BEGIN_IV = 16;
    public static final int END_IV = 32;
    //All Menu ID Constan
    public static final int MENU_PFM = 1;
    public static final int MENU_MUTASI = 2;
    public static final int MENU_KONVERSI_VALAS = 3;
    public static final int MENU_QR_MERCHANT = 4;
    public static final int MENU_TARIK_TUNAI = 5;
    public static final int MENU_TRANSFER = 6;
    public static final int MENU_TRANSFER_INTERNASIONAL = 7;
    public static final int MENU_BRIZZI = 8;
    public static final int MENU_DOMPET_DIGITAL = 9;
    public static final int MENU_PULSA = 10;
    public static final int MENU_ASURANSI = 11;
    public static final int MENU_BRIVA = 12;
    public static final int MENU_CICILAN = 13;
    public static final int MENU_KARTU_KREDIT = 14;
    public static final int MENU_LISTRIK = 15;
    public static final int MENU_LTMPT = 16;
    public static final int MENU_PASCA_BAYAR = 17;
    public static final int MENU_PDAM = 18;
    public static final int MENU_TELKOM = 19;
    public static final int MENU_TV = 20;
    public static final int MENU_DEPOSITO = 21;
    public static final int MENU_DPLK = 22;
    public static final int MENU_RDN = 23;
    public static final int MENU_BPJS = 24;
    public static final int MENU_DONASI = 25;
    public static final int MENU_KAI = 26;
    public static final int MENU_TRAVEL = 27;
    public static final int MENU_MPN = 28;
    public static final int MENU_CERIA = 29;
    public static final int MENU_PINJAMAN_BRI = 30;
    public static final int MENU_BANK_RAYA = 31;
    public static final int MENU_QR = 32;
    public static final int MENU_DAFTAR_CC = 33;
    public static final int MENU_QR_TRANSFER = 34;
    public static final int MENU_QR_MCM = 35;
    public static final int MENU_DAFTAR_BRIMO = 37;
    public static final int MENU_BRISPOT = 36;
    public static final int MENU_BUKA_REKENING = 38;
    public static final int MENU_SETOR_TUNAI = 39;
    public static final int MENU_STREAMING = 40;
    public static final int MENU_KARTU_KREDIT_BRI = 41;
    public static final int MENU_ESBN = 42;
    public static final int MENU_PENDIDIKAN = 43;
    public static final int MENU_IBBIZ = 44;
    public static final int MENU_BRI_FINANCE = 45;
    public static final int MENU_PBB = 46;
    public static final int MENU_BRIGHTS = 47;
    public static final int MENU_SNPMB = 48;
    public static final int MENU_JADI_MERCHANT = 49;
    public static final int MENU_GAME = 53;
    public static final int MENU_RENCANA = 62;
    public static final int MENU_DEBIT_VIRTUAL = 57;
    public static final int MENU_SIGNAL = 55;
    public static final int MENU_DASHBOARD_INVESTASI = 54;
    public static final int MENU_EMAS = 50;
    public static final int MENU_KSEI = 52;
    public static final int MENU_MINIAPP = 97;
    public static final int MENU_NFC = 63;
    public static final int MENU_LAINNYA = 100;
    public static final int MENU_PROMO = 110;

    // Appsflyer Event
    public static final String APPSFLYER_TAG = "event_appsflyer";
    public static final String CUSTOMER_ID = "customer_id";
    //Blast Notifikasi
    public static final String BLAST_ID = "blast_id";

    //Response
    public static final String RC_SUCCESS = "00";
    //Response Error
    public static final String RE_SUCCESS = "00";
    public static final String RE01 = "01";
    public static final String RE_ANSWER_VOICE = "03";
    public static final String RE05 = "05";
    public static final String RE06 = "06";
    public static final String RE_REFRESH = "21";
    public static final String RE_CHECKING_DEVICE = "08";
    public static final String RE_CHANGE_DEVICE = "09";
    public static final String RE_CREATE_PIN = "10";
    public static final String RE_CHANGE_DEVICE_MNV = "70";
    public static final String RE93 = "93";
    public static final String RE02 = "02";
    public static final String RE58 = "58";
    public static final String RE_BRIVA_POPUP_BPJS = "59";
    public static final String RE_BRIVA_POPUP_KCIC = "60";
    public static final String RE_ANSWER_PIN_INVALID = "07";
    public static final String RE_ANSWER_ACCOUNT_BLOCK = "08";
    public static final String RE_ANSWER_ERROR = "09";

    public static final String RE_HAS_INFO_STATUS = "78";
    public static final String RE_REDIRECT_CHECK_STATUS = "79";

    public static final String RE94 = "94";
    public static final String RE_SESSION_END = "05";//sessiion_end
    public static final String RE_HIT_EXCEED = "06";//api hit limit  exceed
    public static final String RE12 = "12";//general error
    public static final String RE24 = "24";//general error
    public static final String RE35 = "35";//error ga bisa ngajuin, karena ada pengajuan yang di proses
    public static final String RE_TRX_EXPIRED = "93";
    public static final String RE99 = "99";
    public static final String RE_LIMIT_EXCEED = "61";
    public static final String RE62 = "62";
    public static final String RE_FITUR_OFF = "FO";
    public static final String RETO = "TO";
    public static final String RE_EXPIRED_BILL = "81";
    public static final String RE_ALREADY_PAID = "88";
    public static final String RE_DISABLE_SINGALARITY = "428";
    public static final String RE_ENABLE_SINGALARITY = "426";
    public static final String RE_ACCOUNT_NOT_FOUND = "14";
    public static final String RE_ALERT_MAINTENANCE = "503";
    public static final String RE_LOGIN_EXCEED = "Q2";


    // Ubah Pin CC
    public static final String RE_MAX_RETRIES_DEBIT = "HGPS";
    public static final String RE_MAX_RETRIES_CC = "ERPS";
    public static final String RE_INVALID_KTP = "IK";

    //Trx Type
    public static final String PURCHASE_KAI_TRAVEL = "PurchaseKaiTravel";
    public static final String PURCHASE_KCIC_TRAVEL = "PurchaseKcicTravel";
    public static final String PURCHASE_VOUCHER_GAME = "PurchaseVoucherGame";
    public static final String PAYMENT_MOBELANJA = "PaymentMobelanja";
    public static final String PAYMENT_FLIGHT_TRAVEL = "PurchaseFlightTravel";
    public static final String PAYMENT_VOUCHER_STREAMING = "PurchaseStreaming-V3";
    public static final String PAYMENT_MOLIGA = "PaymentMoliga";
    public static final String PAYMENT_SUBMIT_REGIST_DPLK = "PaymentSubmitRegistrationDPLK";


    public static final String AFT_SCHEDULE_ONCE = "once";
    public static final String AFT_SCHEDULE_WEEKLY = "weekly";
    public static final String AFT_SCHEDULE_MONTHLY = "monthly";

    public static final String PAYMENT_MOKIRIM = "PaymentMokirim";
    public static final String PAYMENT_MOEVENT = "PaymentMoevent";

    public static final String SHARE_IMAGE_AUTH_PATH = ".fileprovider";
    public static final String SHARE_IMAGE_INTENT_TYPE = "image/*";

    public static final String[] LIST_TYPE_GAGAL = {KONEKSI_TERPUTUS, SERVER_UNDER_MAINTENANCE, TRANSAKSI_GAGAL, AKUN_TERBLOKIR};
    //safety mode
    public static final String ID_PUSAT_BANTUAN_SAFETY_MODE = "106";

    public static String YEAR = "year";

    public static String REGULAR_CARD = "REGR";

    // flagging callback microsite
    public static String CALLBACK_MICROSITE_TELKOMSEL = "payment_code";
    // intent tag
    public static String INTENT_PROVIDER_ID = "intent_provider_id";
    public static String INTENT_PARAMETER_KONFIRMASI = "intent_parameter_konfirmasi";
    public static String INTENT_PAKET_CUSTOM_SAVE_AS = "intent_paket_custom_save_as";
    public static String INTENT_PAKET_CUSTOM_PHONE_NO = "intent_paket_custom_phone_no";

    //Cc QRIS MPM as SOF

    public static final String SavingsRequst = "savings";
    public static final String CcRequest = "cc";


    public static final String IDLE_TIME = "idletime";

    // Package name brimo
    public static final String APP_PACKAGE_NAME = "https://play.google.com/store/search?q=brimo&c=apps";

    public static final String WHATSAPP_URL = "https://wa.me/************";
    public static final String PLAY_STORE_URL = "https://play.google.com/store/apps/details?id=com.whatsapp&hl=in";
    public static final String WEBSITE_URL = "https://bri.co.id";

    public static class Reprint {
        public static final int REPRINT = 1;
        public static final int NOT_REPRINT = 0;
    }

    public static class Device {
        public static final String type = "1"; // Android
    }

    public static class EditOption {
        public static final int HAPUS = 0;
        public static final int EDIT = 1;
        public static final int FAV = 2;
        public static final int NON_FAV = 3;
        public static final int SAVE = 4;
        public static final int ADD = 5;
    }

    public static class EditOptionNs {
        public static final int FAV = 0;
        public static final int EDIT = 1;
        public static final int HAPUS = 2;
        public static final int NON_FAV = 3;
        public static final int SAVE = 4;
        public static final int ADD = 5;
    }

    public static class EditOptionRevamp {
        public static final String EDIT = "edit";
        public static final String FAV = "fav";
        public static final String NON_FAV = "non_fav";
        public static final String HAPUS = "delete";
    }

    public static class KeyParser {
        public static final int key0 = 0;
        public static final int key1 = 1;
        public static final int key3 = 3;
        public static final int key4 = 4;
        public static final int key5 = 5;
        public static final int key7 = 7;
        public static final int key10 = 10;
        public static final int key12 = 12;
        public static final int key19 = 19;
        public static final Byte rawByte = 0x00;
        public static final String keyEmpty = "";
    }

    public static class BottomOption {
        public static final int saldoMin = 0;
        public static final int nominalTopUp = 1;
        public static final int frekuensi = 2;
    }

    //Mapping travel
    public static class TravelMenu {
        public static final String TRAVEL_HOTEL = "hotel";
        public static final String TRAVEL_BUS = "bus";
        public static final String TRAVEL_PESAWAT = "flight";
        public static final String TRAVEL_KAI = "kai";
        public static final String TRAVEL_KCIC = "kcic";

        public static final String FROM_KONFIRMASI_TRAVEL = "konfirmasi";
        public static final String FROM_RECEIPT_TRAVEL = "receipt";
    }

    public static class ComplaintMenu {
        public static final String COMPLAINT_ST = "ST";
        public static final String COMPLAINT_DSOTHER = "DSOTHER";
        public static final String COMPLAINT_BZTRX = "BZTRX";

        public static final String COMPLAINT_LAINNYA = "NT";
        public static final String COMPLAINT_NTCONCEN = "NTCONCEN";
        public static final String COMPLAINT_NTUKER = "NTUKER";
        public static final String COMPLAINT_NTFAKE = "NTFAKE";
        public static final String COMPLAINT_NTATM = "NTATM";
        public static final String COMPLAINT_NTATMOFF = "NTATMOFF";
        public static final String COMPLAINT_GUNWDRAW = "GUNWDRAW";
        public static final String COMPLAINT_CCBILL = "CCBILL";
        public static final String COMPLAINT_BZTOP = "bztop";
        public static final String COMPLAINT_EDC = "edc";
        public static final String COMPLAINT_GUNINSTL = "guninstl";
        public static final String COMPLAINT_TAX = "tax";
        public static final String COMPLAINT_RTGS = "rtgs";
        public static final String COMPLAINT_TFINTER = "tfinter";
        public static final String COMPLAINT_CCPAY = "ccpay";

        public static final String DELIVERY = "isDelivery";

        public static final String COUNTRY = "isCountry";

        public static final String BANK = "isBank";

        public static final String PAYROLL = "isPayroll";

        public static final String BRIGUNA = "isBriguna";

    }

    public static class PositionHardCode {
        public static final int posisiBiru = 0;
        public static final int posisiHijau = 1;
        public static final int posisiKuning = 2;
        public static final int posisiOrange = 3;
        public static final int posisiMerah = 4;

    }

    public static class InvestasiConfig {
        public static final String AssetType_DEPOSITO = "Deposito";
        public static final String AssetType_Emas = "Emas";
        public static final String AssetType_SBN = "SBN";
        public static final String AssetType_DPLK = "DPLK";
        public static final String AssetType_Rencana = "Rencana";
        public static final String EMAIL_ASSET_DPLK = "mailto:<EMAIL>";
    }

    public static class PaymentReminderTypeInquiry {
        public static final String inquiryOpen = "1";
        public static final String listrikPrepaid = "2";
        public static final String revampListrikPostPaid = "3";
        public static final String revampListrikPrepaid = "4";
        public static final String revampEwallet = "5";
        public static final String kirimBarang = "6";
        public static final String kkGPN = "7";
        public static final String bindingCcGPN = "8";
    }

    public enum LivenessType {
        PRIVY("01"),
        VIDA("02"),
        ZOLOZ("03");


        private final String type;

        LivenessType(String type) {
            this.type = type;
        }

        public String getType() {
            return type;
        }
    }

    public enum Voucher {

        GAME("Voucher Game"),
        STREAMING("Voucher Streaming");

        private final String voucherTitle;

        Voucher(String voucherTitle) {
            this.voucherTitle = voucherTitle;
        }

        public String getVoucherTitle() {
            return voucherTitle;
        }
    }

    public enum INQUIRYTYPE {
        KONTAK("kontak"),
        INQUIRY("inquiry");

        private final String type;

        INQUIRYTYPE(String type) {
            this.type = type;
        }

        public String getInquiryType() {
            return type;
        }
    }

    // Revamp Fast Menu
    public static final int RESULT_BACK = 101;
    public static final int RESULT_KENDALA_SISTEM = 111;
    public static final int RESULT_CODE_09_CHANGE_DEVICE = 121;
    public static final String SUCCESS_EDIT_FAST_MENU = "success_edit_fm";
    public static final String SUCCESS_GET_UNAVAILABLE_FAST_MENU = "success_get_unavailable_fm";
    public static final String KENDALA_SISTEM = "kendala_sistem";
    public static final String EDIT_FAST_MENU_AFTER_CHANGE_DEVICE = "edit_fast_menu_after_change_device";

    public static class JourneyType {
        public static final String JOURNEY_TYPE_REVAMP_PENDIDIKAN_OPEN = "jtr_pendidikan_open";
        public static final String JOURNEY_TYPE_REVAMP_PENDIDIKAN_CLOSE = "jtr_pendidikan_close";
        public static final String JOURNEY_TYPE_REVAMP_LIST_PRODUCT_LISTRIK = "jtr_list_product_listrik";
        public static final String JOURNEY_TYPE_REVAMP_ADD_SAVED_LIST = "jtr_add_saved_list";
        public static final String JOURNEY_TYPE_REVAMP_LIST_PRODUCT_PENDIDIKAN = "journey_type_list_product_pendidikan";
        public static final String JOURNEY_TYPE_REVAMP_LIST_PRODUCT_PROPERTY = "journey_type_list_product_property";
    }

    public static class JourneyTypeCC {
        public static final String JOURNEY_APPLY_VCC = "jtr_vcc";
        public static final String JOURNEY_LOA = "jtr_loa";
    }

    public static class IntentTag {
        public static String INTENT_TAG_PRODUCT_LIST_PENDIDIKAN = "intent_tag_product_list_pendidikan";
        public static String INTENT_TAG_PRODUCT_LIST_PENDIDIKAN_PARAM = "intent_tag_product_list_pendidikan_param";
        public static String INTENT_TAG_PRODUCT_LIST_LISTRIK = "intent_tag_product_list_listrik";
        public static String INTENT_TAG_PRODUCT_LIST_PROPERTY = "intent_tag_product_list_property";
        public static String INTENT_TAG_PRODUCT_LIST_PROPERTY_PARAM = "intent_tag_product_list_property_param";
    }

    public static class StatusProductList {
        public static final int nonActive = 0;
        public static final int active = 1;
    }


    public static class journeyTypeSbn {
        public static final String oriAsset = "ORI";
        public static final String stAsset = "ST";
        public static final String srAsset = "SR";
        public static final String sbrAsset = "SBR";
    }

    public static class ProfileInfoRevamp {
        public static final int FASTMENU = 0;
        public static final int UPDATE_REKENING = 1;
        public static final int PENGELOLAAN_KARTU = 2;
        public static final int UBAH_PIN = 3;
        public static final int UBAH_KATA_KUNCI = 4;
        public static final int BIOMETRIC = 5;
        public static final int PUSAT_BANTUAN = 6;
        public static final int CHAT_BANKING = 7;
        public static final int LAYANAN_BEBAS_PULSA = 8;
        public static final int KONTAK_KAMI = 9;
        public static final int JENIS_DAN_LIMIT = 10;
        public static final int INFO_KURS = 11;
        public static final int INFO_SAHAM = 12;
        public static final int LOKASI_ATM = 13;
        public static final int LOKASI_KANTOR = 14;
        public static final int SNK = 15;
        public static final int QRIS = 16;
        public static final int VOICE_ASSISTANT = 17;
        public static final int TENTANG_BRIMO = 18;
        public static final int PRIVACY = 19;
        public static final int SMART_TRANSFER = 20;
        public static final int LANGUAGE = 116;
        public static final int LOGOUT = 120;
    }

    public static final String PACKAGE_OPLUS_SETTING_FEATURE_HOMEPAGE = "com.oplus.settings.feature.homepage";

    public static class CIAType {
        public static final String TYPE_BRIZZI_TOP_UP_ONLINE = "brizzi_top_up_online";
        public static final String TYPE_BRIZZI_TOP_UP_OTHER = "brizzi_top_up_other";
        public static final String TYPE_COMPLAINT_IN_APPS_BRIZZI = "complaint_in_apps_brizzi";
        public static final String TYPE_COMPLAINT_IN_APPS_GENERAL = "complaint_in_apps_general";
    }

    public static class AFT_SCHEDULE_TYPE {
        public static final String ONCE = "once";
        public static final String WEEKLY = "weekly";
        public static final String MONTHLY = "monthly";
    }

    /*parameter mengikuti variable trx_type setiap fitur*/
    public static class IsFirstTimeShowAgf {

        public static final String PLN = TRX_TYPE_LISTRIK;
    }

    public static class FlavorType {
        public static final String PRODUCTION = "production";
        public static final String PENTEST = "pentest";
        public static final String SANDBOX = "sandbox";
        public static final String DEVELOPMENT = "development";
    }

    public static class AgfType {
        public static final String REMINDER = "reminder";
        public static final String AUTOMATIC = "automatic";
    }

    public static class IconAgfType {
        public static final String REMINDER = "ic_reminder_agf";
        public static final String AUTOMATIC = "ic_money_wallet";
    }

    public static final String IsFirstTimeShowAft = "IsFirstTimeShowAft";

    public static String ActivityJourneyTypeAftString = "activityJourneyTypeAft";

    public static class ActivityJourneyTypeAft {
        public static final String delete = "delete";
        public static final String success = "success";
        public static final String update = "update";
    }

    public static class AFTNotifSchedulerBlastTypeInquiry {
        public static final String aftFailed = "aft_failed";
    }

    public enum ResponseMNV {
        RC302(302),
        RC303(303),
        RC400(400);

        private final int code;

        ResponseMNV(int code) {
            this.code = code;
        }

        public int getCode() {
            return code;
        }

        public static ResponseMNV fromCode(int code) {
            for (ResponseMNV response : ResponseMNV.values()) {
                if (response.getCode() == code) {
                    return response;
                }
            }
            return null;
        }
    }

    public static class ListComplainStatus {
        public static final String NEW = "New";
        public static final String PROGRESS = "In Progress";
        public static final String CLOSE = "Closed";
    }

    public static final String[] ALLOWED_INSTALLER = {
            "com.android.vending",
            "com.sec.android.app.samsungapps",
            "com.miui.supermarket",
            "com.oppo.market",
            "com.vivo.appstore",
            "com.huawei.appmarket",
            "com.miui.packageinstaller",
            "com.xiaomi.market",
            "com.xiaomi.mipicks",
            "com.google.android.feedback",
            "com.bbk.appstore",
            "com.heytap.market"
    };

    public static class StyleUserProfile {
        public static final String PASS = "pass";
        public static final String FAIL = "fail";

    }

    public static class TrendStatusType {
        public static final String POSITIVE = "positive";
        public static final String NEGATIVE = "negative";

    }

    public static class RemoteConfigParameter {
        public static final String DYNAMIC_APP_ICON = "dynamic_app_icon";
    }

    public static class AppIconKey {
        public static final String DEFAULT_APP_ICON = "default_app_icon";
        public static final String IDUL_FITRI_APP_ICON = "idul_fitri_app_icon";
        public static final String RAMADHAN_APP_ICON = "ramadhan_app_icon";
        public static final String LUNAR_APP_ICON = "lunar_app_icon";
        public static final String INDEPENDENCE_DAY_APP_ICON = "independence_day_app_icon";
        public static final String BRI_BIRTHDAY_APP_ICON = "bri_birthday_app_icon";
        public static final String CHRISTMAS_APP_ICON = "christmas_app_icon";
        public static final String NEW_YEAR_APP_ICON = "new_year_app_icon";
    }

    public static class SpecificFeatureId {
        public static final String FEATURE_PANGELOLAAN_KARTU = "pengelolaan_kartu";
    }

    public static class KontakKami{
        public static final String facebook = "https://www.facebook.com/BRIofficialpage/";
        public static final String whatsApp = "https://wa.me/************";
        public static final String playstoreWa = "https://play.google.com/store/apps/details?id=com.whatsapp&hl=in";
        public static final String appFacebook = "fb://page/***************";
        public static String phoneNumber = "1500017";
        public static final String twitter = "https://www.x.com/kontakBRI/";
        public static final String instagram = "https://www.instagram.com/bankbri_id";
        public static String[] email = new String[]{"<EMAIL>"};
        public static final String web = "https://bri.co.id";
        public static final String contactBriTag = "Contact BRI";
        public static final String emailTag = "Email";
        public static final String websiteTag = "Website";
        public static final String sabrinaTag = "Sabrina";
        public static final String twitterTag = "X / Twitter";
        public static final String facebookTag = "Facebook";
        public static final String instagramTag = "Instagram";
        public static final String websiteBri = "www.bri.co.id";
        public static final String twitterBri = "@kontakBRI";
        public static final String sabrinaBri = "0812 1214 017";
        public static final String facebookBri = "BANK BRI";
        public static final String instagramBri = "bankbri_id";
    }

    public enum SplitBillHistoryPageType {
        ON_PROGRESS("on_progress"),
        FINISHED("finished");

        public final String label;

        SplitBillHistoryPageType(String label) {
            this.label = label;
        }

        public static SplitBillHistoryPageType findHistoryPageType(String label) {
            for(SplitBillHistoryPageType v : values()){
                if( v.label.equals(label)){
                    return v;
                }
            }
            return null;
        }
    }

    //-----NEW SKIN-----//

    public static final String[] LIST_TYPE_GAGAL_GANGGUAN_SISTEM = {KONEKSI_TERPUTUS, SERVER_UNDER_MAINTENANCE};
    public static final String LOCATION = "location";
    public static final int FAST_MENU_SIZE = 5;
    public static final int VIBRATE_ERROR = 2;
    public static final int FM_STATUS_ACTIVE = 1;
    public static final int FM_STATUS_NONACTIVE = 0;
    public static final int REQ_INFO_PAGE = 501;

    public static final String AKUN_TERBLOKIR_SALAH_PIN = "Salah PIN 3 kali. User Anda terblokir.";


    public static final String REQ_INFO_PAGE_SOURCE = "req_info_page_source";
    public enum LivenessMinioDataType {
        IMAGE_1, IMAGE_2, VIDEO
    }
    public static String IMAGE_TYPE = "image/png";
    public static String VIDEO_TYPE = "movie/mp4";
    public static String VIDEO_NAME = "video.mp4";
    public static String IMAGE_NAME_1 = "image1.png";
    public static String IMAGE_NAME_2 = "image2.png";


    public static String KEY_BACK = "back";

    public static final String SMS = "sms";

    public static final String LATITUDE = "latitude";
    public static final String LONGITUDE = "longitude";
    public static final int ALERT_CONFIRM = -1;
    public static final int ALERT_ERROR = -2;

    // TODO: BILL & EWALLET
    public static int REQ_REINQUIRY = 700;
    public static class Electic {
        public static final String WRONG_IDPEL = "IDPEL YANG ANDA MASUKKAN SALAH";
        public static final String NOT_AVAIL = "BELUM TERSEDIA";
        public static final String DONE_PAYMENT = "SUDAH TERBAYAR";
        public static final String WRONG_ID_PEL_NONTAGLIS = "NOMOR REGISTRASI YANG ANDA MASUKKAN SALAH";
    }

    public static final String STATUS = "status";

    public static final String DATE = "date";
    public static final String OCR_METHOD_ZOLOZ = "ZOLOZ";
    public static final String PRESIGN_URL_KTP = "presign_url_ktp";
    public static final String CONTENT_TYPE_IMAGE = "image/png";

    public static String IMAGE_LIST_TYPE = "image";
    public static String BRI_IMAGE = "bri";
    public static String PDAM_IMAGE = "pdam";
    public static String IC_ACCOUNT_SAVED = "ic_account_saved";
    public static String SUCCESS = "success";

    public static String INFORMASI_REKENING = "Informasi Rekening";
    public static String DETAIL_KARTU = "Detail Kartu";
    public static String ESTATEMENT = "E-Statement";
    public static String ACCOUNT_HOME = "account_home";

    public static final String CREATE_ACC = "create_acc";
    public static final String COPY = "copy";
    public static final String ARROW = "arrow";
    public static final String BLOKIR = "blokir";
    public static final String BLOKIR_EMAIL = "blokir_email";
    public static final String OPEN_BLOKIR = "open_blokir";
    public static final String OPEN_BLOKIR_EMAIL = "open_blokir_email";
    public static final String DELETE_PHONE = "delete_phone";
    public static final String DELETE_EMAIL = "delete_email";
    public static final String AMBIL_ALIH = "ambil_alih";
    public static final String FINANCIAL_STATE = "financial_state";
    public static final String MAIN_ACCOUNT = "main_account";
    public static final String BOTTOMSHEET_RANGE_DATE = "bottomsheet_range_date";
    public static final String BOTTOMSHEET_STATUS_TRANSACTION = "bottomsheet_status_transaction";
    public static final String BOTTOMSHEET_SOURCE_OF_ACCOUNT = "bottomsheet_source_of_account";
    public static final String STATUS_MIN1 = "-1";
    public static final String STATUS_MIN2 = "-2";
    public static final String STATUS_1 = "1";
    public static final String STATUS_2 = "2";
    public static final String STATUS_3 = "3";
    public static final String STATUS_0 = "0";
    public static final String IC_WARNING_NS = "ic_warning_newskin";
    public static final String PACKAGE_NAME = "id.co.bri.brimo";
    public static final String MARKET_DETAIL = "market://details?id=";
    public static final String PLAY_STORE = "https://play.google.com/store/apps/details?id";
    public static final String REQUEST_INACTIVE = "INACTIVE";
    public static final String REQUEST_ACTIVATE = "ACTIVATE";
    public static final String REQUEST_DELETE = "DELETE";
    public static final String REQUEST_UPDATE = "UPDATE";
    public static final String IS_FROM_EDIT = "isFromEdit";

    public static final String REQUEST_REKENING_UTAMA = "REKENING_UTAMA";
    public static final String REQUEST_STATUS_FINANSIAL = "STATUS_FINANSIAL";
    public static final String REQUEST_NOTIFIKASI_TRANSAKSI = "NOTIFIKASI_TRANSAKSI";
    public static final String IC_SAD_NEW_NS = "ic_sad_new_ns";
    public static final String IC_CARD_NEWSKIN_NS = "ic_card_newskin_ns";
    public static final String PROXY_PHONE = "01";
    public static final String PROXY_EMAIL = "02";

    // TODO: BILL & EWALLET
    public static long SKELETON_DURATION = 600;
}
