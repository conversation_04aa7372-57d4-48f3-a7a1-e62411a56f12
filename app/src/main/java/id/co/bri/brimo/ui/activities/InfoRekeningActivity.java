package id.co.bri.brimo.ui.activities;

import android.Manifest;
import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.FragmentTransaction;

import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;
import com.google.gson.Gson;

import java.util.Objects;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.detailrekening.IInfoRekeningPresenter;
import id.co.bri.brimo.contract.IView.detailrekening.IInfoRekeningView;
import id.co.bri.brimo.databinding.ActivityInfoRekeningBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.providers.CardType;
import id.co.bri.brimo.models.apimodel.request.PinFinansialRequest;
import id.co.bri.brimo.models.apimodel.request.notificationsetting.GetListNotificationSettingRequest;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.DetailKelolaKartuReq;
import id.co.bri.brimo.models.apimodel.response.BiFastAccountResponse;
import id.co.bri.brimo.models.apimodel.response.DetailBiFast;
import id.co.bri.brimo.models.apimodel.response.EnableKartuResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralOtpResponse;
import id.co.bri.brimo.models.apimodel.response.ListRekeningResponse;
import id.co.bri.brimo.models.apimodel.response.QuestionResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.notificationsetting.NotificationSettingResponse;
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.ForceUpdateResponse;
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.DetailKelolaKartuRes;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

import id.co.bri.brimo.ui.activities.carddetailnewskin.VirtualCardDetailActivity;
import id.co.bri.brimo.ui.activities.notification.NotificationSettingActivity;
import id.co.bri.brimo.ui.activities.pengelolaankartu.DetailKelolaKartuNewActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import id.co.bri.brimo.ui.customviews.dialog.DialogSetDefault;
import id.co.bri.brimo.ui.fragments.FragmentBottomDialog;
import id.co.bri.brimo.ui.fragments.PinFragment;
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment;

public class InfoRekeningActivity extends BaseActivity implements
        View.OnClickListener,
        IInfoRekeningView,
        DialogExitCustom.DialogDefaultListener,
        PinFragment.SendPin {

    private ActivityInfoRekeningBinding binding;

    @Inject
    IInfoRekeningPresenter<IInfoRekeningView> presenter;

    private static ListRekeningResponse.Account rekeningData = null;

    private String nama;
    private String accountString;
    private String acountbiasa;
    private String alias;
    private String statusKartu;
    private String noKartuMask;
    private String newAllias;
    private String position;
    private String message = "";
    private String aliasPhone = "";
    private String aliasEmail = "";

    private String cardType = "";
    private String cardNumber = "";

    private static String errorMesssage;

    private int accountStatus;
    private int finansialStatus;

    private static boolean fromEnable;
    private static BiFastAccountResponse mBiFastAccountResponse;
    private SkeletonScreen skeletonCard;
    private SkeletonScreen skeletonInfo;

    private DetailBiFast detailBiFastPhone;
    private DetailBiFast detailBiFastEmail;
    private ClipboardManager myClipboard;
    private ClipData myClip;
    Intent resultIntent = new Intent();

    protected static final String[] PERMISSIONSREGIS = {Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO};

    protected static final String[] PERMISSIONSREGIS33 = {
            Manifest.permission.READ_MEDIA_IMAGES,
            Manifest.permission.READ_MEDIA_AUDIO,
            Manifest.permission.READ_MEDIA_VIDEO,
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO};

    public static String[] permissions() {
        String[] permission;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permission = PERMISSIONSREGIS33;
        } else {
            permission = PERMISSIONSREGIS;
        }
        return permission;
    }

    protected String[] PERMISSIONS = permissions();

    protected static final int PERMISSIONS_ALL = 1;

    public static void launchIntent(Activity caller, ListRekeningResponse.Account rekening, BiFastAccountResponse biFastAccountResponse, String errorString) {
        Intent intent = new Intent(caller, InfoRekeningActivity.class);
        rekeningData = rekening;
        mBiFastAccountResponse = biFastAccountResponse;
        errorMesssage = errorString;
        caller.startActivityForResult(intent, Constant.REQ_EDIT_SAVED);
    }

    public static void launchIntent(Activity caller, ListRekeningResponse.Account rekening) {
        Intent intent = new Intent(caller, InfoRekeningActivity.class);
        rekeningData = rekening;
        caller.startActivityForResult(intent, Constant.REQ_EDIT_SAVED);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityInfoRekeningBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.info_rekening));

        //set blue bar
        if (Build.VERSION.SDK_INT >= 21) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.setStatusBarColor(getResources().getColor(R.color.toolbar_blue));
        }

        injectDependency();
        setupView();
    }

    public void setupView() {
        if (rekeningData != null) {
            accountString = rekeningData.getAccountString();
            acountbiasa = rekeningData.getAccount();
            accountStatus = rekeningData.getDefault();
            nama = rekeningData.getName();
            alias = rekeningData.getAlias();
            noKartuMask = rekeningData.getCardNumberString();
            finansialStatus = rekeningData.getFinansialStatus();

            // saldo tertahan
            if (rekeningData.getSaldoReponse() != null)
                if (rekeningData.getSaldoReponse().isOnHold())
                    binding.lyErrorTertahan.setVisibility(View.VISIBLE);
                else binding.lyErrorTertahan.setVisibility(View.GONE);
            else binding.lyErrorTertahan.setVisibility(View.GONE);

            disableButton();
            binding.tvNomorRekening.setText(accountString);
            binding.tvNamaRekening.setText(nama);

            //set card
            if (rekeningData.getDetailType().equalsIgnoreCase("s3f")) {
                GeneralHelper.loadIconTransaction(this, "", "ic_card_s3f_full", binding.ivCard, GeneralHelper.getImageId(this, "ic_card_s3f_full"));
                binding.ivCard.setAlpha(1.0f);
            }

            if (alias != null) {
                if (alias.equalsIgnoreCase("")) {
                    binding.tvNamaAlias.setText(GeneralHelper.getString(R.string.hint_no_alias));
                } else {
                    binding.tvNamaAlias.setText(alias);
                }
            } else binding.tvNamaAlias.setText(GeneralHelper.getString(R.string.hint_no_alias));

            if (rekeningData != null) {
                if (rekeningData.getCurrency() != null) {
                    if (rekeningData.getCurrency().equalsIgnoreCase("Rp")) {
                        enableButton();
                    } else
                        disableButton();
                }

                if (rekeningData.getSaldoReponse().isOnHold()) {
                    binding.llStatusFinansial.setEnabled(false);
                    binding.llStatusFinansial.setAlpha(0.3f);
                }
            }
        }

        if (finansialStatus == 0) {
            binding.tvStatusFinansial.setText(GeneralHelper.getString(R.string.nonaktif));
        } else {
            binding.tvStatusFinansial.setText(GeneralHelper.getString(R.string.aktif));
        }

        binding.llNamaAlias.setOnClickListener(this);
        binding.btnSubmit.setOnClickListener(this);
        binding.llStatusKartu.setOnClickListener(this);
        //copy rekening
        binding.btnCopy.setOnClickListener(this);
        //BI-Fast
        binding.llAliasBiFast.setOnClickListener(this);
        //statusFinansial
        binding.llStatusFinansial.setOnClickListener(this);

        // info saldo tertahan
        binding.tvDetailTertahan.setOnClickListener(this);
        binding.rlNotificationSetting.setOnClickListener(this);

        skeletonArmy();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.setUrlStatus(GeneralHelper.getString(R.string.url_check_status_kartu));
            presenter.setUrlDefault(GeneralHelper.getString(R.string.url_set_default_rekening));
            presenter.setUrlBiFast(GeneralHelper.getString(R.string.url_akun_bifast));
            presenter.setUrlInfoSaldoHold(GeneralHelper.getString(R.string.url_qna_detail));
            presenter.setUrlDetailStatus(GeneralHelper.getString(R.string.url_card_management_card_detail_v2));
            presenter.start();
            if (rekeningData != null)
                presenter.getStatusKartu(rekeningData.getAccount());
        }
    }

    public void setupViewBiFast() {
        if (mBiFastAccountResponse != null) {
            if (mBiFastAccountResponse.getEmailAlias() != null) {
                detailBiFastEmail = mBiFastAccountResponse.getEmailAlias();
            }
            if (mBiFastAccountResponse.getPhoneAlias() != null) {
                detailBiFastPhone = mBiFastAccountResponse.getPhoneAlias();
            }
            aliasEmail = mBiFastAccountResponse.getEmailAddress();
            aliasPhone = mBiFastAccountResponse.getPhoneNumber();


            if (!detailBiFastPhone.getAccount().equalsIgnoreCase("") && acountbiasa.equals(detailBiFastPhone.getAccount())) {
                switch (detailBiFastPhone.getStatus()) {
                    case "1":
                        binding.tvAliasBiFast.setText(detailBiFastPhone.getProxyValue());
                        break;
                    case "0":
                        binding.tvAliasBiFast.setText(GeneralHelper.getString(R.string.nonaktif));
                        break;
                    case "-1":
                        binding.tvAliasBiFast.setText( GeneralHelper.getString(R.string.select_nickname));
                        break;
                }
            }
            //jika alias email tersedia
            else if (!detailBiFastEmail.getAccount().equalsIgnoreCase("") && acountbiasa.equals(detailBiFastEmail.getAccount())) {
                switch (detailBiFastEmail.getStatus()) {
                    case "1":
                        binding.tvAliasBiFast.setText(detailBiFastEmail.getProxyValue());
                        break;
                    case "0":
                        binding.tvAliasBiFast.setText(GeneralHelper.getString(R.string.nonaktif));
                        break;
                    case "-1":
                        binding.tvAliasBiFast.setText(GeneralHelper.getString(R.string.select_nickname));
                        break;
                }
            } else if (!acountbiasa.equals(detailBiFastPhone.getAccount())) {
                binding.tvAliasBiFast.setText(GeneralHelper.getString(R.string.select_nickname));
            } else if (!acountbiasa.equals(detailBiFastEmail.getAccount())) {
                binding.tvAliasBiFast.setText(GeneralHelper.getString(R.string.select_nickname));
            }
        } else {
            binding.tvAliasBiFast.setText(GeneralHelper.getString(R.string.select_nickname));
        }
    }

    @Override
    public void onClick(View view) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        switch (view.getId()) {
            case R.id.ll_nama_alias:
                EditAlliasActivity.launchIntent(this, alias, acountbiasa, GeneralHelper.getString(R.string.url_update_alias));
                break;
            case R.id.rl_status_rekening:
            case R.id.ll_ubah_pin:
                GeneralHelper.showBottomDialog(this, Constant.COMING_SOON);
                break;
            case R.id.ll_status_kartu:
                DetailKelolaKartuReq request = new DetailKelolaKartuReq(rekeningData.getCardNumber(), rekeningData.getAccount());
                presenter.getDataDetailStatus(request);
                break;
            case R.id.btnCopy:
                myClipboard = (ClipboardManager) getSystemService(CLIPBOARD_SERVICE);
                String text;
                text = binding.tvNomorRekening.getText().toString();
                myClip = ClipData.newPlainText("text", text);
                myClipboard.setPrimaryClip(myClip);
                showSnackbarErrorMessage(GeneralHelper.getString(R.string.info_saldo_rekening_berhasil_disalin), BaseActivity.ALERT_CONFIRM, this, false);
                break;
            case R.id.btnSubmit:
                DialogSetDefault dialogSetDefault = new DialogSetDefault(this, GeneralHelper.getString(R.string.title_setdefault), GeneralHelper.getString(R.string.desc_setdefault), Constant.DIALOG_SET_DEFAULT);
                FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
                ft.add(dialogSetDefault, null);
                ft.commitAllowingStateLoss();
                break;
            case R.id.ll_alias_bi_fast:
                if (mBiFastAccountResponse != null) {
                    AliasBiFastActivity.launchIntent(this, aliasPhone, aliasEmail, detailBiFastPhone, detailBiFastEmail, accountString, nama, acountbiasa);
                } else fragmentView();
                break;
            case R.id.tv_detail_tertahan:
                presenter.getInfoSaldoHold();
                break;
            case R.id.ll_status_finansial:
                checkPermission();
                break;
            case R.id.rl_notification_setting:
                getNotificationSetting();
                break;
            default:
                break;
        }
    }

    private void getNotificationSetting() {
        presenter.setUrlGetListNotification(GeneralHelper.getString(R.string.url_v1_check_status_notif));
        presenter.getListNotificationSetting(new GetListNotificationSettingRequest(acountbiasa));
    }

    @Override
    public void onSuccessGetListNotification(NotificationSettingResponse response) {
        String jsonResponse = new Gson().toJson(response);
        Intent intent = new Intent(this, NotificationSettingActivity.class);
        intent.putExtra(Constant.TAG_CONTENT, jsonResponse);
        startActivity(intent);
    }

    protected void showDialogFinansial() {
        String title = "";
        String desc = "";
        String sButton = "";
        if (finansialStatus == 0) {
            presenter.setUrlFinansialRek(GeneralHelper.getString(R.string.url_financial_activate_check_pin));
            title = GeneralHelper.getString(R.string.aktifkan_finansial);
            desc = GeneralHelper.getString(R.string.desc_aktifkan_finansial);
            sButton = GeneralHelper.getString(R.string.aktifkan_2);
        } else {
            presenter.setUrlFinansialRek(GeneralHelper.getString(R.string.url_financial_deactivate_account));
            title = GeneralHelper.getString(R.string.nonaktifkan_finansial);
            desc = GeneralHelper.getString(R.string.desc_nonaktifkan_finansial);
            sButton = GeneralHelper.getString(R.string.nonaktifkan);
        }

        DialogExitCustom dialogNotice = new DialogExitCustom(this, title,
                desc, GeneralHelper.getString(R.string.batal2), sButton);
        FragmentTransaction ftsf = this.getSupportFragmentManager().beginTransaction();
        ftsf.add(dialogNotice, null);
        ftsf.commitAllowingStateLoss();
    }

    protected void checkPermission() {
        if (!hasPermissions(this, PERMISSIONS)) {
            ActivityCompat.requestPermissions(this, PERMISSIONS, PERMISSIONS_ALL);
        } else {
            showDialogFinansial();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean grantAll = true;
        if (grantResults.length > 0) {
            for (int i = 0; i < grantResults.length; i++) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    grantAll = false;
                    break;
                }
            }
        }

        if (!grantAll) {
            showAlertFinish(getString(R.string.notes_need_permission_resi));
        } else {
            showDialogFinansial();
        }
    }

    private void fragmentView() {
        String desc;
        if (errorMesssage != null) {
            desc = errorMesssage;
        } else
            desc = GeneralHelper.getString(R.string.update_your_phone_number);
        FragmentBottomDialog fragmentBottomDialog = new FragmentBottomDialog(this,
                Constant.COMING_SOON,
                GeneralHelper.getString(R.string.bi_fast_nickname_is_unavailable),
                desc,
                Constant.IMAGE_ALIAS_UNAVAIL, false);
        fragmentBottomDialog.setCancelable(false);
        fragmentBottomDialog.show(this.getSupportFragmentManager(), "");
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (data != null) {
            if (requestCode == Constant.REQ_EDIT_SAVED) {
                if (resultCode == RESULT_OK) {
                    newAllias = data.getStringExtra(Constant.TAG_ALIAS);
                    position = data.getStringExtra(Constant.TAG_POSITION);
                    message = GeneralHelper.getString(R.string.nickname_changed_successfully);
                    GeneralHelper.showSnackBarGreen(Objects.requireNonNull(this).findViewById(R.id.content), message);
                    this.setResult(RESULT_OK, data);
                    alias = newAllias;
                    binding.tvNamaAlias.setText(alias);
                    resultIntent.putExtra(Constant.TAG_ALIAS, alias);
                    resultIntent.putExtra(Constant.TAG_POSITION, position);
                }
            } else if (requestCode == Constant.REQ_BLOK) {
                if (resultCode == RESULT_OK) {
                    message = data.getStringExtra(Constant.TAG_VALUE);
                    String report = data.getStringExtra(Constant.TAG_TYPE);
                    if (report != null) {
                        if (report.equalsIgnoreCase("fromunblokir")) {
                            message = GeneralHelper.getString(R.string.card_activated_successfully);
                            GeneralHelper.showSnackBarGreen(Objects.requireNonNull(this).findViewById(R.id.content), message);
                            setHasil(false);
                        } else {
                            message = GeneralHelper.getString(R.string.card_deactivated_successfully);
                            GeneralHelper.showSnackBarGreen(Objects.requireNonNull(this).findViewById(R.id.content), message);
                            setHasil(true);
                        }
                    }
                    this.setResult(RESULT_OK, data);
                }
            } else if (requestCode == Constant.REQ_ALIAS) {
                if (resultCode == RESULT_OK) {
                    message = data.getStringExtra(Constant.TAG_MESSAGE);
                    if (message != null)
                        GeneralHelper.showSnackBarGreen(Objects.requireNonNull(this).findViewById(R.id.content), message);
                    switch (Objects.requireNonNull(data.getStringExtra(Constant.TAG_TYPE))) {
                        case "1":
                            binding.tvAliasBiFast.setText(data.getStringExtra(Constant.TAG_VALUE));
                            if (Objects.equals(data.getStringExtra(Constant.TAG_ALIAS_BIFAST), RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                detailBiFastPhone.setStatus(data.getStringExtra(Constant.TAG_TYPE));
                                detailBiFastPhone.setAccount(data.getStringExtra(Constant.TAG_ALIAS_AKUN));
                            } else if (Objects.equals(data.getStringExtra(Constant.TAG_ALIAS_BIFAST), "02")) {
                                detailBiFastEmail.setStatus(data.getStringExtra(Constant.TAG_TYPE));
                                detailBiFastEmail.setAccount(data.getStringExtra(Constant.TAG_ALIAS_AKUN));
                            }
                            break;
                        case "0":
                            binding.tvAliasBiFast.setText(GeneralHelper.getString(R.string.nonaktif));
                            if (Objects.equals(data.getStringExtra(Constant.TAG_ALIAS_BIFAST), "01")) {
                                detailBiFastPhone.setStatus(data.getStringExtra(Constant.TAG_TYPE));
                                detailBiFastPhone.setAccount(data.getStringExtra(Constant.TAG_ALIAS_AKUN));
                            } else if (Objects.equals(data.getStringExtra(Constant.TAG_ALIAS_BIFAST), "02")) {
                                detailBiFastEmail.setStatus(data.getStringExtra(Constant.TAG_TYPE));
                                detailBiFastEmail.setAccount(data.getStringExtra(Constant.TAG_ALIAS_AKUN));
                            }
                            break;
                        case "-1":
                            binding.tvAliasBiFast.setText(GeneralHelper.getString(R.string.select_nickname));
                            if (Objects.equals(data.getStringExtra(Constant.TAG_ALIAS_BIFAST), "01")) {
                                detailBiFastPhone.setStatus(data.getStringExtra(Constant.TAG_TYPE));
                                detailBiFastPhone.setAccount(data.getStringExtra(Constant.TAG_ALIAS_AKUN));
                            } else if (Objects.equals(data.getStringExtra(Constant.TAG_ALIAS_BIFAST), "02")) {
                                detailBiFastEmail.setStatus(data.getStringExtra(Constant.TAG_TYPE));
                                detailBiFastEmail.setAccount(data.getStringExtra(Constant.TAG_ALIAS_AKUN));
                            }
                            break;
                    }
                }
            } else if (requestCode == Constant.REQ_FINANSIAL) {
                if (resultCode == RESULT_OK) {
                    setResult(RESULT_OK);
                    finish();
                } else {
                    if (data.hasExtra(Constant.TAG_ERROR_MESSAGE))
                        GeneralHelper.showSnackBar(Objects.requireNonNull(this).findViewById(R.id.content), data.getStringExtra(Constant.TAG_ERROR_MESSAGE));
                }
            }
        }
    }

    public void skeletonArmy() {
        skeletonCard = Skeleton.bind(binding.ivCard)
                .shimmer(true)
                .angle(0)
                .duration(1500)
                .color(R.color.colorOppacityText)
                .load(R.layout.skeleton_card)
                .show();

        skeletonInfo = Skeleton.bind(binding.lldetail)
                .shimmer(true)
                .angle(0)
                .duration(1500)
                .color(R.color.colorOppacityText)
                .load(R.layout.skeleton_detail)
                .show();
    }

    /**
     * Callback Success enable kartu
     *
     * @param enableKartuResponse
     */
    @Override
    public void onResp00(EnableKartuResponse enableKartuResponse) {
        cardType = enableKartuResponse.getType();
        cardNumber = enableKartuResponse.getCardNumber();
        skeletonCard.hide();
        skeletonInfo.hide();

        if (enableKartuResponse.getCardNumber() != null) {
            noKartuMask = enableKartuResponse.getCardNumberString();
        }
        statusKartu = getCardStatusText(enableKartuResponse.getCardStatus());
        binding.tvStatusKartu.setText(statusKartu);
        binding.tvNoKartu.setText(noKartuMask);
        if (enableKartuResponse.getCardActive()) {
            binding.ivCard.setAlpha(1.0f);
            fromEnable = true;
        } else {
            binding.ivCard.setAlpha(0.3f);
            fromEnable = false;
        }

        if (noKartuMask.equalsIgnoreCase("-"))
            binding.llStatusKartu.setEnabled(false);
        else binding.llStatusKartu.setEnabled(true);

        if (accountStatus == 0) {
            binding.tvStatusRekening.setText(GeneralHelper.getString(R.string.not_primary_account));
            if (rekeningData.getCurrency() != null) {
                if (rekeningData.getCurrency().equalsIgnoreCase("Rp")) {
                    enableButton();
                } else
                    disableButton();
            }
        } else {
            binding.tvStatusRekening.setText(GeneralHelper.getString(R.string.primary_account));
            disableButton();
        }

        if (rekeningData.getCurrency() != null) {
            if (rekeningData.getCurrency().equalsIgnoreCase("Rp")) {
                enableButton();
            } else
                disableButton();
        }
    }

    private String getCardStatusText(String cardStatus) {
        String value = switch (cardStatus) {
            case "AA" -> GeneralHelper.getString(R.string.aktif);
            case "DS" -> GeneralHelper.getString(R.string.tidak_aktif);
            case "BL" -> GeneralHelper.getString(R.string.blocked);
            default -> GeneralHelper.getString(R.string.not_available);
        };
        return value;
    }

    //set kartu dan tulisan
    public void setHasil(boolean enable) {
        if (enable) {
            binding.tvStatusKartu.setText(GeneralHelper.getString(R.string.non_aktifkan));
            binding.ivCard.setAlpha(0.3f);
            fromEnable = false;
        } else {
            binding.tvStatusKartu.setText(GeneralHelper.getString(R.string.aktif));
            binding.ivCard.setAlpha(1.0f);
            fromEnable = true;
        }
    }

    @Override
    public void onException01(RestResponse restResponse) {
        binding.llStatusKartu.setEnabled(false);
        skeletonCard.hide();
        skeletonInfo.hide();
        binding.tvStatusKartu.setText(restResponse.getDesc());
        binding.tvNoKartu.setText(GeneralHelper.getString(R.string.hint_no_card));
        binding.ivCard.setAlpha(0.3f);
        fromEnable = false;
        if (accountStatus == 0) {
            binding.tvStatusRekening.setText(GeneralHelper.getString(R.string.not_primary_account));
            if (rekeningData.getCurrency() != null) {
                if (rekeningData.getCurrency().equalsIgnoreCase("Rp")) {
                    enableButton();
                } else
                    disableButton();
            }
        } else {
            binding.tvStatusRekening.setText(GeneralHelper.getString(R.string.primary_account));
            disableButton();
        }

        if (rekeningData.getCurrency() != null) {
            if (rekeningData.getCurrency().equalsIgnoreCase("Rp")) {
                enableButton();
            } else
                disableButton();
        }
    }

    @Override
    public void onException12(String message, String type) {
        showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
    }

    /**
     * Callback dapat dari rekening ubah default
     *
     * @param message
     */
    @Override
    public void onSuccessGetChangeDefault(String message) {
        resultIntent.putExtra(Constant.TAG_POSITION_DEFAULT, acountbiasa);
        binding.tvStatusRekening.setText(GeneralHelper.getString(R.string.primary_account));
        GeneralHelper.showSnackBarGreen(Objects.requireNonNull(this).findViewById(R.id.content), message);
        disableButton();
    }

    @Override
    public void onSuccessBiFast(BiFastAccountResponse biFastAccountResponse) {
        mBiFastAccountResponse = biFastAccountResponse;
        setupViewBiFast();
        binding.llAliasBiFast.setVisibility(View.VISIBLE);
    }

    @Override
    public void onGetSuccessDetail(DetailKelolaKartuRes detailKelolaKartuRes) {
        if (cardType.equalsIgnoreCase(Constant.VIRTUAL)) {
            VirtualCardDetailActivity.launchIntent(
                    this,
                    detailKelolaKartuRes.getCardNumber(),
                    false,
                    CardType.VIRTUAL_DEBIT
            );
        } else {
            DetailKelolaKartuNewActivity.Companion.launchIntent(
                    this,
                    detailKelolaKartuRes,
                    rekeningData.getAccount(),
                    0
            );
        }
    }

    @Override
    public void onErrorBiFast(String error) {
        errorMesssage = error;
    }

    @Override
    public void onSuccessGetFinansial(GeneralOtpResponse generalOtpResponse) {
        GeneralOtpActivity.launchIntentFinansial(
                this,
                generalOtpResponse,
                GeneralHelper.getString(R.string.kode_otp),
                GeneralHelper.getString(R.string.title_otp),
                GeneralHelper.getString(R.string.url_financial_activate_validate_sms),
                GeneralHelper.getString(R.string.url_financial_activate_resend_sms));
    }

    @Override
    public void onSuccessNonaktifFinansial() {
        GeneralHelper.showSnackBarGreen(Objects.requireNonNull(this).findViewById(R.id.content), GeneralHelper.getString(R.string.finansial_status_nonaktif));
        finansialStatus = 0;
        binding.tvStatusFinansial.setText(GeneralHelper.getString(R.string.nonaktif));
    }

    @Override
    public void onSuccessInfoSaldoHold(QuestionResponse questionResponse) {
        DetailPusatBantuanActivity.launchIntent(this, questionResponse, GeneralHelper.getString(R.string.toolbar_pusat_bantuan));
    }

    @Override
    public void onUpdateVersion(ForceUpdateResponse forceUpdate) {
        OpenBottomSheetGeneralFragment.INSTANCE.showDialogInformation(
                getSupportFragmentManager(),
                "",
                "ic_forced_update",
                forceUpdate.getTitle(),
                forceUpdate.getDescription(),
                createKotlinFunction0(openPlayStore),
                false,
                forceUpdate.getButton()
        );
    }

    Runnable openPlayStore = () -> {
        String appPackageName =
                "https://play.google.com/store/search?q=brimo&c=apps";
        try {
            startActivity(new Intent(
                    Intent.ACTION_VIEW,
                    Uri.parse("market://details?id=" + appPackageName)));
        } catch (ActivityNotFoundException e) {
            startActivity(new Intent(
                            Intent.ACTION_VIEW,
                            Uri.parse("https://play.google.com/store/apps/details?id=" + appPackageName)
                    )
            );
        }
    };

    public void disableButton() {
        binding.btnSubmit.setAlpha((float) 0.3);
        binding.btnSubmit.setEnabled(false);
    }

    public void enableButton() {
        binding.btnSubmit.setAlpha(1);
        binding.btnSubmit.setEnabled(true);
    }


    @Override
    public void onBackPressed() {
        setResult(Activity.RESULT_OK, resultIntent);
        super.onBackPressed();

    }

    @Override
    protected void onDestroy() {
        presenter.stop();
        binding = null;
        super.onDestroy();
    }

    @Override
    protected void onClickYesRC99(int reqId) {
        if (reqId == Constant.DIALOG_SET_DEFAULT) {
            presenter.setChangeDefault(acountbiasa);
        }
    }

    @Override
    public void onClickYes() {
        PinFragment pinFragment = new PinFragment(this, this);
        pinFragment.show();
    }

    @Override
    public void onSendPinComplete(String pin) {
        presenter.sendFinansialRek(new PinFinansialRequest(pin, acountbiasa), finansialStatus);
    }

    @Override
    public void onLupaPin() {
        if (isFromFastMenu) LupaPinFastActivity.launchIntent(this);
        else LupaPinActivity.launchIntent(this);
    }
}