package id.co.bri.brimo.domain.helpers

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.RenderEffect
import android.graphics.Shader
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.GradientDrawable
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.VibrationEffect
import android.os.Vibrator
import android.provider.Settings
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ImageSpan
import android.text.style.RelativeSizeSpan
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.inputmethod.InputMethodManager
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.Toolbar
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.google.android.material.snackbar.BaseTransientBottomBar
import com.google.android.material.snackbar.Snackbar
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.SnackbarCustomGenericBinding
import id.co.bri.brimo.domain.SnackBarPosition
import id.co.bri.brimo.domain.SnackBarType
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.ui.activities.newskinonboarding.OnboardingInputNumberForgetPassActivity
import id.co.bri.brimo.domain.config.Constant.ALERT_CONFIRM
import id.co.bri.brimo.domain.config.Constant.ALERT_ERROR
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import java.util.Arrays
import com.brimodsdk.BrimodSDKManager

object GeneralHelperNewSkin {
    const val TAG = "GeneralHelperNS"
    var progressDialog: Dialog? = null
    // Message type constants (matching BaseActivity)
    const val ALERT_CONFIRM = -1
    const val ALERT_ERROR = -2

    fun showBlur(activity: Activity){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val blurEffect = RenderEffect.createBlurEffect(40f, 40f, Shader.TileMode.CLAMP)
            activity.window.decorView.setRenderEffect(blurEffect)
        }
    }

    fun hideBlur(activity: Activity){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            activity.window.decorView.setRenderEffect(null)
        }
    }

    fun showErrorBottomDialog(activity: FragmentActivity, type: String?) {
        var title = ""
        var desc = ""
        var imageName = ""
        var btnText = ""
        var btnAction: () -> Unit = {}

        when (type) {
            Constant.COMING_SOON -> {
                title = GeneralHelper.getString(R.string.title_coming_soon)
                desc = GeneralHelper.getString(R.string.desc_coming_soon)
                imageName = Constant.IMAGE_COMING_SOON
            }

            Constant.KONEKSI_TERPUTUS -> {
//                title = GeneralHelper.getString(R.string.title_koneksi_terputus)
//                desc = GeneralHelper.getString(R.string.desc_koneksi_terputus)
                title = GeneralHelper.getString(R.string.title_koneksi_terputus_ns)
                desc = GeneralHelper.getString(R.string.desc_koneksi_terputus_ns)
                imageName = "ic_blocked_illustrator"
                btnText = GeneralHelper.getString(R.string.check_connection)
                btnAction = { openManageCellular(activity) }
            }

            Constant.SERVER_UNDER_MAINTENANCE -> {
                title = GeneralHelper.getString(R.string.title_server_under_maintenance)
                desc = GeneralHelper.getString(R.string.desc_server_under_maintenance)
                imageName = Constant.IMAGE_SERVER_UNDER_MAINTENANCE
                btnText = GeneralHelper.getString(R.string.ok)
            }

            Constant.TRANSAKSI_GAGAL -> {
                title = GeneralHelper.getString(R.string.title_transaki_gagal)
                desc = GeneralHelper.getString(R.string.desc_transaki_gagal)
                imageName = "ic_sad_ns"
                btnText = GeneralHelper.getString(R.string.ok)
            }

            Constant.NFC_GAGAL -> {
                title = GeneralHelper.getString(R.string.title_nfc_gagal)
                desc = GeneralHelper.getString(R.string.desc_nfc_gagal)
                imageName = Constant.IMAGE_NFC_GAGAL
            }

            Constant.AKUN_TERBLOKIR -> {
                title = GeneralHelper.getString(R.string.title_koneksi_terputus)
                desc = GeneralHelper.getString(R.string.desc_koneksi_terputus)
                imageName = Constant.IMAGE_KONEKSI_TERPUTUS
            }

            else -> {
                title = GeneralHelper.getString(R.string.title_transaki_gagal)
                desc = GeneralHelper.getString(R.string.desc_transaki_gagal)
                imageName = Constant.IMAGE_TRANSAKSI_GAGAL
            }
        }
        try {
            OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
                fragmentManager = activity.supportFragmentManager,
                imgPath = "",
                imgName = imageName,
                titleTxt = title,
                subTitleTxt = desc,
                btnFirstFunction = btnAction,
                firstBtnTxt = btnText,
                isClickableOutside = true,
                showPill = false
            )
        } catch (e: Exception) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "showErrorBottomDialogNewSkin: ", e)
            }
        }
    }

    fun showGeneralErrorBottomDialog(activity: FragmentActivity) {
        try {
            OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
                fragmentManager = activity.supportFragmentManager,
                imgPath = "",
                imgName = "ic_sad_illustration",
                titleTxt = "Perubahan Gagal Disimpan",
                subTitleTxt = "Silahkan coba beberapa saat lagi",
                btnFirstFunction = {},
//                isClickableOutside = false,
                firstBtnTxt = GeneralHelper.getString(R.string.retry),
                showPill = false
            )
        } catch (e: Exception) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "showBottomDialog: ", e)
            }
        }
    }

    fun showErrorBlokir(activity: FragmentActivity) {
        try {
            OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
                fragmentManager = activity.supportFragmentManager,
                imgPath = "",
                imgName = "ic_block_acc",
                titleTxt = "Akun Kamu Terkunci Sementara",
                subTitleTxt = "Akun kamu terkunci karena terlalu banyak percobaan yang gagal. Yuk, atur ulang akses kamu untuk melanjutkan aktivitas di Qitta.",
                btnFirstFunction = {
                    OnboardingInputNumberForgetPassActivity.launchIntent(activity, Constant.REQ_UBAH_PIN)
                },
                isClickableOutside = false,
                firstBtnTxt = "Atur Ulang PIN Sekarang"
            )
        } catch (e: Exception) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "showBottomDialog: ", e)
            }
        }
    }

    fun openManageCellular(activity: FragmentActivity) {
        try {
            val intent = Intent(Settings.ACTION_DATA_ROAMING_SETTINGS)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            activity.startActivity(intent)
        } catch (e: Exception) {
            val fallbackIntent = Intent(Settings.ACTION_SETTINGS)
            fallbackIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            activity.startActivity(fallbackIntent)
        }
    }

    @JvmStatic
    fun setToolbar(activity: Activity, toolbar: Toolbar, text: String?) {
        val textView = (toolbar.findViewById<TextView>(R.id.textTitle))
        textView.text = text
        textView.isSelected = true
        toolbar.setNavigationIcon(R.drawable.ic_arrow_left_toolbar_new_skin)
        toolbar.setNavigationOnClickListener { v: View? -> activity.onBackPressed() }
    }

    fun setToolbarWithoutNav(toolbar: Toolbar, text: String?) {
        val textView = toolbar.findViewById<TextView>(R.id.textTitle)
        textView.text = text
        textView.isSelected = true
        toolbar.navigationIcon = ContextCompat.getDrawable(toolbar.context, R.drawable.ic_dummy_transparent)
    }
    fun setToolbarRightText(activity: Activity, toolbar: Toolbar, text: String?, textRight: String?) {
        val textView = (toolbar.findViewById<TextView>(R.id.textTitle))
        textView.text = text
        textView.isSelected = true
        val textViewRight = (toolbar.findViewById<TextView>(R.id.textRight))
        textViewRight.text = textRight
        toolbar.setNavigationIcon(R.drawable.ic_arrow_left_toolbar_new_skin)
        toolbar.setNavigationOnClickListener { v: View? -> activity.onBackPressed() }
    }

    fun isContains(strings: Array<String?>, string: String): Boolean {
        return Arrays.asList(*strings).contains(string)
    }

    fun getColor(context: Activity, color: Int): Int {
        var getColor = 0
        try {
            getColor = ContextCompat.getColor(context, color)
        } catch (e: java.lang.Exception) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "BRImo getColor: ", e)
            }
        }
        return getColor
    }

    fun showLoadingDialog(context: Context?) {
        if (context != null && !(context as Activity).isFinishing && !context.isDestroyed) {
            progressDialog = Dialog(context)
            progressDialog?.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            progressDialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)
            progressDialog?.setContentView(R.layout.loading_new_skin)
            progressDialog?.setCancelable(false)
            progressDialog?.show()
        }
    }

    fun dismissLoadingDialog() {
        if (progressDialog != null) progressDialog?.dismiss()
    }

    fun triggerVibration(context: Context, type: Int) {
        val vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Android 8.0 ke atas
            var effect = VibrationEffect.createOneShot(200, VibrationEffect.DEFAULT_AMPLITUDE)
            when (type) {
                Constant.VIBRATE_ERROR -> {
                    val timings = longArrayOf(0, 100, 100, 100)
                    val amplitudes = intArrayOf(
                        0,
                        VibrationEffect.DEFAULT_AMPLITUDE,
                        0,
                        VibrationEffect.DEFAULT_AMPLITUDE
                    )
                    effect = VibrationEffect.createWaveform(timings, -1)
                }
            }
            vibrator.vibrate(effect)
        } else {
            // Android versi lama
            when (type) {
                Constant.VIBRATE_ERROR -> {
                    vibrator.vibrate(100)
                    Handler(Looper.getMainLooper()).postDelayed({
                        vibrator.vibrate(100)
                    }, 200)
                }

                else -> vibrator.vibrate(100)
            }
        }
    }

    fun showSnackBarGreen(viewParent: View, message: String) {
        val builder = SpannableStringBuilder()
        builder.append(" ")
        builder.setSpan(
            ImageSpan(GeneralHelper.helperContext, R.drawable.ic_check_white_rounded_transparent),
            builder.length - 1,
            builder.length,
            0
        )
        builder.append(" ").append(message)

        val snackbar = Snackbar.make(viewParent, builder, Snackbar.LENGTH_LONG)
            .setActionTextColor(Color.WHITE)
            .setAnimationMode(BaseTransientBottomBar.ANIMATION_MODE_FADE)

        val view = snackbar.view
        view.setBackgroundResource(R.drawable.bg_green_main_ns)

        val params = view.layoutParams as CoordinatorLayout.LayoutParams
        params.gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
        params.setMargins(20, 20, 20, 20)
        params.width = CoordinatorLayout.LayoutParams.MATCH_PARENT
        view.layoutParams = params
        snackbar.show()
    }

    fun showCustomSnackBar(
        viewParent: View,
        message: String,
        type: SnackBarType = SnackBarType.SUCCESS
    ) {
        val snackbar = Snackbar.make(viewParent, " ", Snackbar.LENGTH_LONG)
            .setAnimationMode(BaseTransientBottomBar.ANIMATION_MODE_FADE)

        val snackbarView = snackbar.view
        snackbarView.setBackgroundColor(Color.TRANSPARENT)

        val binding = SnackbarCustomGenericBinding.inflate(LayoutInflater.from(viewParent.context))
        binding.snackbarText.text = message

        when (type) {
            SnackBarType.SUCCESS -> {
                binding.snackbarIcon.setImageResource(R.drawable.ic_check_white_rounded_transparent)
                binding.snackbarLayoutCustom.setBackgroundResource(R.drawable.bg_green_main_ns)
            }
            SnackBarType.ERROR -> {
                binding.snackbarIcon.setImageResource(R.drawable.ic_check_white_rounded_transparent)
                binding.snackbarLayoutCustom.setBackgroundResource(R.drawable.rounded_dialog_grey_pin_newskin_error)
            }
            SnackBarType.WARNING -> {

            }
        }

        (snackbarView as? ViewGroup)?.apply {
            removeAllViews()
            addView(binding.root)
        }

        val params = snackbarView.layoutParams as? CoordinatorLayout.LayoutParams
        params?.apply {
            val margin16 = viewParent.context.resources.getDimensionPixelSize(R.dimen.size_16dp)
            val margin22 = viewParent.context.resources.getDimensionPixelSize(R.dimen.size_22dp)
            gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
            setMargins(margin16, margin22, margin16, margin16)
            width = CoordinatorLayout.LayoutParams.MATCH_PARENT
            snackbarView.layoutParams = this
        }

        snackbar.show()
    }

    fun showSnackBarRed(viewParent: View, message: String) {
        val builder = SpannableStringBuilder()
        builder.append(" ")
        builder.setSpan(
            ImageSpan(GeneralHelper.helperContext, R.drawable.ic_check_white_rounded_transparent),
            builder.length - 1,
            builder.length,
            0
        )
        builder.append(" ").append(message)

        val snackbar = Snackbar.make(viewParent, builder, Snackbar.LENGTH_LONG)
            .setActionTextColor(Color.WHITE)
            .setAnimationMode(BaseTransientBottomBar.ANIMATION_MODE_FADE)

        val view = snackbar.view
        view.setBackgroundResource(R.drawable.bg_red_main_ns)

        val params = view.layoutParams as CoordinatorLayout.LayoutParams
        params.gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
        params.setMargins(20, 20, 20, 20)
        params.width = CoordinatorLayout.LayoutParams.MATCH_PARENT
        view.layoutParams = params
        snackbar.show()
    }

    /**
     * Consolidated snackbar function that handles both success and error cases
     * @param viewParent The parent view to attach the snackbar to
     * @param message The message to display
     * @param messageType The type of message (ALERT_CONFIRM for success, ALERT_ERROR for error)
     */
    fun showSnackBar(viewParent: View, message: String?, messageType: Int) {
        if (message == null) return

        // Determine icon and background color based on message type
        val iconRes = when (messageType) {
            ALERT_CONFIRM -> R.drawable.ic_check_white_rounded_transparent
            ALERT_ERROR -> R.drawable.ic_danger_ns
            else -> R.drawable.ic_check_white_rounded_transparent
        }

        val backgroundColor = when (messageType) {
            ALERT_CONFIRM -> R.color.background_success_default_ns
            ALERT_ERROR -> R.color.background_error_default_ns
            else -> R.color.background_success_default_ns
        }

        try {
            // Create a basic snackbar
            val snackbar = Snackbar.make(viewParent, "", Snackbar.LENGTH_LONG)

            // Get the snackbar's view
            val snackbarView = snackbar.view

            // Inflate custom layout
            val customView = LayoutInflater.from(viewParent.context)
                .inflate(R.layout.snackbar_custom_green, null)

            // Set up the custom view
            val iconView = customView.findViewById<ImageView>(R.id.snackbar_icon)
            val textView = customView.findViewById<TextView>(R.id.snackbar_text)

            iconView.setImageResource(iconRes)
            textView.text = message

            // Cast to ViewGroup to manipulate children
            val snackbarLayout = snackbarView as ViewGroup

            // Add the custom view to the snackbar with proper layout params
            val layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            customView.layoutParams = layoutParams

            // Remove default views and add custom view
            snackbarLayout.removeAllViews()
            snackbarLayout.addView(customView)

            // Configure the snackbar layout parameters
            val params = snackbarView.layoutParams
            when (params) {
                is FrameLayout.LayoutParams -> {
                    params.gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
                    params.setMargins(20, 12, 20, 0)
                    snackbarView.layoutParams = params
                }
                is CoordinatorLayout.LayoutParams -> {
                    params.gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
                    params.setMargins(20, 80, 20, 0)
                    snackbarView.layoutParams = params
                }
            }

            // Set background
            val background = GradientDrawable()
            background.setColor(GeneralHelper.getColor(backgroundColor))
            background.cornerRadius = 50f
            snackbarView.background = background

            // Set minimum height
            snackbarView.minimumHeight = GeneralHelper.dpToPx(48, GeneralHelper.helperContext)

            snackbar.show()
        } catch (e: Exception) {
            // Fallback to simple snackbar if custom layout fails
            val fallbackSnackbar = Snackbar.make(viewParent, message, Snackbar.LENGTH_LONG)
            fallbackSnackbar.setBackgroundTint(GeneralHelper.getColor(backgroundColor))
            fallbackSnackbar.show()
        }
    }

    fun showSnackBarCustom(
        activity: Activity,
        message: String,
        type: SnackBarType = SnackBarType.SUCCESS,
        position: SnackBarPosition = SnackBarPosition.TOP
    ) {
        val rootView = activity.findViewById<ViewGroup>(android.R.id.content)

        val binding = SnackbarCustomGenericBinding.inflate(LayoutInflater.from(activity))

        binding.snackbarText.text = message

        when (type) {
            SnackBarType.SUCCESS -> {
                binding.snackbarIcon.setImageResource(R.drawable.ic_check_white_rounded_transparent)
                binding.snackbarLayoutCustom.setBackgroundResource(R.drawable.bg_green_main_ns)
            }
            SnackBarType.ERROR -> {
                binding.snackbarIcon.setImageResource(R.drawable.ic_error_snackbar)
                binding.snackbarLayoutCustom.setBackgroundResource(R.drawable.rounded_dialog_grey_pin_newskin_error)
            }
            SnackBarType.WARNING -> {
                // Tambahkan bila perlu
            }
        }

        val snackLayout = binding.root
        val params = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.WRAP_CONTENT
        )

        val margin16 = activity.resources.getDimensionPixelSize(R.dimen.size_16dp)
        val margin22 = activity.resources.getDimensionPixelSize(R.dimen.size_56dp)

        when (position) {
            SnackBarPosition.TOP -> {
                params.gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
                params.setMargins(margin16, margin22, margin16, margin16)
            }
            SnackBarPosition.BOTTOM -> {
                params.gravity = Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL
                params.setMargins(margin16, margin16, margin16, margin22)
            }
            SnackBarPosition.CENTER -> {
                params.gravity = Gravity.CENTER
                params.setMargins(margin16, margin16, margin16, margin16)
            }
        }

        snackLayout.layoutParams = params
        rootView.addView(snackLayout)

        // Auto dismiss after delay
        snackLayout.alpha = 0f
        snackLayout.animate().alpha(1f).setDuration(300).start()

        Handler(Looper.getMainLooper()).postDelayed({
            snackLayout.animate().alpha(0f).setDuration(300).withEndAction {
                rootView.removeView(snackLayout)
            }.start()
        }, 3000) // Durasi 3 detik
    }

    fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
            ?: return false

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
        } else {
            val networkInfo = connectivityManager.activeNetworkInfo
            return networkInfo != null && networkInfo.isConnected
        }
    }

    fun hideKeyboardIfVisible(activity: Activity) {
        val inputMethodManager = activity.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        val view = activity.currentFocus ?: View(activity)
        inputMethodManager.hideSoftInputFromWindow(view.windowToken, 0)
    }

    fun formatDecimalText(fullText: String?, decimalChar: Char = ',', decimalSize: Float = 0.8f): SpannableString {
        val text = fullText ?: ""
        val spannable = SpannableString(text)
        val decimalStart = text.indexOf(decimalChar)
        if (decimalStart != -1) {
            spannable.setSpan(
                RelativeSizeSpan(decimalSize),
                decimalStart,
                text.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        return spannable
    }

    fun navigateToReact(bundleName: String, appName: String, params: Map<String, Any>?, activity: Activity?) {
        val manager = BrimodSDKManager.getInstance()
        val paramsMap = params ?: emptyMap()
        manager.navigateToReact(bundleName, appName, paramsMap, activity)
    }

    fun formatPhoneNumber(input: String): String {
        return input.chunked(4).joinToString(" ")
    }

}