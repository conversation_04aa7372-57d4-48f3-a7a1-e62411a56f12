package id.co.bri.brimo.ui.customviews

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.animation.OvershootInterpolator
import androidx.core.widget.NestedScrollView
import kotlin.ranges.coerceIn

class BounceNestedScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : NestedScrollView(context, attrs) {

    private var previousY = 0f
    private var isBouncing = false
    private var overScrollOffset = 0f
    private val maxOverScroll = 250f
    private val bounceFactor = 0.5f

    init {
        overScrollMode = OVER_SCROLL_NEVER
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(ev: MotionEvent): Boolean {
        when (ev.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                previousY = ev.y
                isBouncing = false
                animate().cancel()
            }

            MotionEvent.ACTION_MOVE -> {
                val deltaY = ev.y - previousY
                previousY = ev.y

                val atTop = !canScrollVertically(-1)
                val atBottom = !canScrollVertically(1)

                if ((atTop && deltaY > 0) || (atBottom && deltaY < 0)) {
                    isBouncing = true
                    overScrollOffset = (overScrollOffset + deltaY * bounceFactor)
                        .coerceIn(-maxOverScroll, maxOverScroll)

                    translationY = overScrollOffset
                    return true
                }
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (isBouncing) {
                    animate()
                        .translationY(0f)
                        .setDuration(300)
                        .setInterpolator(OvershootInterpolator())
                        .withEndAction {
                            overScrollOffset = 0f
                        }
                        .start()
                    isBouncing = false
                    return true
                }
            }
        }

        return super.onTouchEvent(ev)
    }
}