package id.co.bri.brimo.ui.activities;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.HistoryBrizziAdapter;
import id.co.bri.brimo.contract.IPresenter.brizzi.IInforSaldoBrizziPresenter;
import id.co.bri.brimo.contract.IView.brizzi.IInfoSaldoBrizziView;
import id.co.bri.brimo.databinding.ActivityInquiryBrizziBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryBrizziResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brizzi.Brizzi;
import id.co.bri.brizzi.BrizziHistory;

public class InfoSaldoBrizzi extends BaseActivity implements IInfoSaldoBrizziView, View.OnClickListener {

    private ActivityInquiryBrizziBinding binding;
    String number;
    static String mUpadateSaldo;
    String mSaldo;


    private ArrayList<BrizziHistory> brizziHistoryObjectArrayList;

    @Inject
    IInforSaldoBrizziPresenter<IInfoSaldoBrizziView> cekSaldoPresenter;

    static Brizzi mbrizzi;
    protected static String[] str1;

    protected static boolean mIsFromTopUpOnline;
    protected static boolean mIsFromScan;
    protected static String errorMessage = null;
    protected static String mJourneyType = "";
    protected static InquiryBrizziResponse inquiryBrizziResponse;
    protected static Integer mState;

    protected HistoryBrizziAdapter historyBrizziAdapter;


    public static void launchIntent(Activity caller, Brizzi brizziCardObject, InquiryBrizziResponse inquiryBrizziResponses, String journeyType, boolean isFromFastMenus) {
        Intent intent = new Intent(caller, InfoSaldoBrizzi.class);
        mbrizzi = brizziCardObject;
        inquiryBrizziResponse = inquiryBrizziResponses;
        mJourneyType = journeyType;
        isFromFastMenu = isFromFastMenus;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityInquiryBrizziBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();

        GeneralHelper.setToolbar(this, binding.tbBriva.toolbar, GeneralHelper.getString(R.string.brizzi_titlebar));


        binding.btnSubmitTopUpSekarang.setOnClickListener(this);
        binding.btnAktivasi.setOnClickListener(this);


        if (mbrizzi != null) {

            binding.tvStatus.setText(mbrizzi.getCardData().getCardStatus());

            if (inquiryBrizziResponse.getPendingBalance() == 0) {
                binding.btnAktivasi.setEnabled(false);
                binding.btnAktivasi.setAlpha((float) 0.3);
            } else {
                binding.btnAktivasi.setEnabled(true);
                binding.btnAktivasi.setAlpha(1);
            }
            binding.tvSaldoPending.setText(GeneralHelper.formatNominal(String.valueOf(inquiryBrizziResponse.getPendingBalance())));
            mSaldo = mbrizzi.getCardData().getCardBalance();


            binding.tvSaldo.setText(GeneralHelper.formatNominal(mSaldo));
            cekErrorMessage();
            formatString();
            brizziHistoryObjectArrayList = mbrizzi.getCardData().getCardHistory();
            if (brizziHistoryObjectArrayList == null) {
                binding.tvNoHistory.setVisibility(View.VISIBLE);
                binding.rvHistoryBrizzi.setVisibility(View.GONE);

            } else {
                binding.rvHistoryBrizzi.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
                historyBrizziAdapter = new HistoryBrizziAdapter(brizziHistoryObjectArrayList, this);
                binding.rvHistoryBrizzi.setAdapter(historyBrizziAdapter);
                binding.rvHistoryBrizzi.setVisibility(View.VISIBLE);
            }


            if (mbrizzi.getCardData().getCardStatus().equalsIgnoreCase("Aktif")) {
                binding.rlCard.setBackgroundResource(R.drawable.brizzi_background);
                binding.tvCardNonAktif.setVisibility(View.INVISIBLE);
                binding.rlDetailPendingBrizzi.setVisibility(View.VISIBLE);
                binding.tvStatus.setText(mbrizzi.getCardData().getCardStatus());

            } else {
                binding.rlCard.setBackgroundResource(R.drawable.brizzi_background);
                binding.tvCardNonAktif.setVisibility(View.VISIBLE);
                binding.rlDetailPendingBrizzi.setVisibility(View.GONE);
                binding.tvStatus.setText(mbrizzi.getCardData().getCardStatus());
                binding.btnSubmitTopUpSekarang.setText("OK");
            }


        }


    }

    protected void injectDependency() {
        getActivityComponent().inject(this);
        if (cekSaldoPresenter != null) {
            cekSaldoPresenter.setView(this);
            cekSaldoPresenter.start();
            if (isFromFastMenu) {
                cekSaldoPresenter.setInquiryUrl(GeneralHelper.getString(R.string.url_inquiry_top_up_brizzi_fm));
                cekSaldoPresenter.setKonfirmasiUrl(GeneralHelper.getString(R.string.url_konfirmasi_top_up_brizzi_fm));
            } else {
                cekSaldoPresenter.setInquiryUrl(GeneralHelper.getString(R.string.url_inquiry_top_up_brizzi));
                cekSaldoPresenter.setKonfirmasiUrl(GeneralHelper.getString(R.string.url_konfirmasi_top_up_brizzi));
            }

        }
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode == Activity.RESULT_OK) {
            if (data != null) {
                this.setResult(RESULT_OK, data);
                this.finish();
            }
        } else {
            this.setResult(RESULT_CANCELED, data);
            if (data != null) {
                if (data.getStringExtra(Constant.TAG_ERROR_MESSAGE_AKTIVASI_BRIZZI) != null) {
                    errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE_AKTIVASI_BRIZZI);
                    cekErrorMessage();
                } else {
                    this.setResult(RESULT_CANCELED, data);
                    this.finish();
                }
            }

        }

    }

    protected void cekErrorMessage() {
        if (errorMessage != null) {
            //menampilkan snacknar error
            showSnackbarErrorMessage(errorMessage, ALERT_ERROR, this, false);

            //clear error message
            errorMessage = null;
        }
    }

    public void formatString() {
        StringBuilder stringBuilder = new StringBuilder(mbrizzi.getCardData().getCardNumber());

        for (int i = 4; i < stringBuilder.length(); i += 5) {
            stringBuilder.insert(i, " ");
        }
        binding.noBrizzi.setText(stringBuilder.toString());

    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        switch (id) {
            case R.id.btnSubmitTopUpSekarang:
                if (mbrizzi != null && mbrizzi.getCardData().getCardStatus().equalsIgnoreCase("Aktif")) {
                    cekSaldoPresenter.getDataInquiryTopUp(mbrizzi.getCardData().getCardNumber(), isFromFastMenu);
                } else
                    finish();
                break;
            case R.id.btn_aktivasi:
                TapBrizziAktivasiActivity.launchIntent(this, isFromFastMenu, mJourneyType, mIsFromScan, mState);
                break;
        }


    }


    public ParameterModel setParameter() {
        ParameterModel parameterModel = new ParameterModel();

        parameterModel.setStringLabelTujuan(GeneralHelper.getString(R.string.nomor_tujuan));
        parameterModel.setStringLabelNominal(GeneralHelper.getString(R.string.nominal));
        parameterModel.setStringButtonSubmit(GeneralHelper.getString(R.string.beli));
        parameterModel.setStringLabelMinimum(GeneralHelper.getString(R.string.payment));
        parameterModel.setDefaultIcon(R.drawable.brizzi);

        return parameterModel;
    }

    @Override
    public void onSuccessGetInquiryTopUp(GeneralInquiryResponse kaiInquiryResponse, String urlKonfrimasi, String urlPayment) {
        InquiryBrizziActivity.launchIntent(this, kaiInquiryResponse, GeneralHelper.getString(R.string.brizzi_titlebar), urlKonfrimasi, urlPayment, setParameter(), mbrizzi, isFromFastMenu, mJourneyType, mIsFromScan);
    }


    @Override
    protected void onDestroy() {
        cekSaldoPresenter.stop();
        binding = null;
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        cekSaldoPresenter.stop();
        if (mJourneyType.equals(Constant.CIAType.TYPE_COMPLAINT_IN_APPS_BRIZZI)){
            DashboardIBActivity.launchIntentSuccess(this, "", true);
        }
        super.onBackPressed();
    }
}