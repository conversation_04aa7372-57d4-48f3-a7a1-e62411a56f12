package id.co.bri.brimo.ui.activities.receipt;

import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.text.Html;
import android.util.Log;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.animation.Animation;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.FileProvider;
import androidx.core.text.HtmlCompat;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;
import com.google.gson.Gson;
import com.wajahatkarim3.easyflipview.EasyFlipView;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.inject.Inject;

import id.co.bri.brimo.BuildConfig;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.DataTransaksiRevampAdapter;
import id.co.bri.brimo.adapters.SmartRecommendationAdapter;
import id.co.bri.brimo.adapters.lifestyle.ekspedisi.OrderPosAjaDetailAdapter;
import id.co.bri.brimo.adapters.lifestyle.ekspedisi.PackageDetailAdapter;
import id.co.bri.brimo.adapters.receipt.ReceiptAccountDataAdapter;
import id.co.bri.brimo.adapters.receipt.ReceiptDataTotalAdapter;
import id.co.bri.brimo.adapters.receipt.ReceiptEticketDataAdapter;
import id.co.bri.brimo.adapters.receipt.ReceiptVoucherDataAdapter;
import id.co.bri.brimo.adapters.travel.DetailRutePesawatAdapter;
import id.co.bri.brimo.contract.IPresenter.dashboard.IDashboardActivityPresenter;
import id.co.bri.brimo.contract.IPresenter.dashboard.IDashboardActivityPresenter;
import id.co.bri.brimo.contract.IPresenter.general.IGeneralReceiptPresenter;
import id.co.bri.brimo.contract.IView.dashboard.IDashboardActivityView;
import id.co.bri.brimo.contract.IView.dashboard.IDashboardActivityView;
import id.co.bri.brimo.contract.IView.general.IGeneralReceiptView;
import id.co.bri.brimo.databinding.ActivityReceiptRevampBinding;
import id.co.bri.brimo.databinding.ActivityReceiptRevampNsBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.config.LifestyleConfig;
import id.co.bri.brimo.domain.config.MenuConfig;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.SizeHelper;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.domain.helpers.image.ImageHelper;
import id.co.bri.brimo.models.NotifikasiModel;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.request.InquiryReceiptRequest;
import id.co.bri.brimo.models.apimodel.request.smartrecom.SmartRecomFeedbackRequest;
import id.co.bri.brimo.models.apimodel.request.smartrecom.SmartRecomRequest;
import id.co.bri.brimo.models.apimodel.request.DetailVoucherStreamingRequest;
import id.co.bri.brimo.models.apimodel.request.lifestyle.shopping.ConfirmationMobelanjaRequest;
import id.co.bri.brimo.models.apimodel.request.smarttransfer.AnalyticSmartTransferRequest;
import id.co.bri.brimo.models.apimodel.request.smarttransfer.SimilarityAccountSmartTransferRequest;
import id.co.bri.brimo.models.apimodel.request.voucher.VoucherRequest;
import id.co.bri.brimo.models.apimodel.response.DataView;
import id.co.bri.brimo.models.apimodel.response.DetailListType;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryReceiptResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryRevampReceiptResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse;
import id.co.bri.brimo.models.apimodel.response.ValidateResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.PatternLifestyleTrackingResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.UrlTrackingMobelanjaResponse;
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse;
import id.co.bri.brimo.models.apimodel.response.smartrecom.SmartRecomDetail;
import id.co.bri.brimo.models.apimodel.response.smartrecom.SmartRecomResponse;
import id.co.bri.brimo.models.apimodel.response.travel.DetailRoute;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.Account;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.NonBriAccount;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferAccountListConsentResponse;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferConfirmAccBinding;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferGeneralResponse;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferUserConsentData;
import id.co.bri.brimo.models.apimodel.response.voucher.TutorialVoucherResponse;
import id.co.bri.brimo.security.MyCryptStatic;
import id.co.bri.brimo.ui.activities.InquiryGeneralCloseActivity;
import id.co.bri.brimo.ui.activities.InquiryGeneralOpenActivity;
import id.co.bri.brimo.ui.activities.InquiryKonfirmasiBrivaRevampCloseActivity;
import id.co.bri.brimo.ui.activities.InquiryPlnTokenActivity;
import id.co.bri.brimo.ui.activities.ProductWebviewActivity;
import id.co.bri.brimo.ui.activities.applyvcc.ApplyVccSofListActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.smarttransfer.KonfirmasiRekeningSmartTransferActivity;
import id.co.bri.brimo.ui.activities.ssc.SelfServiceActivity;
import id.co.bri.brimo.ui.activities.lifestyle.ekspedisi.WebviewEkspedisiActivity;
import id.co.bri.brimo.ui.activities.cc_sof.MerchantConnectActivity;
import id.co.bri.brimo.ui.activities.cc_sof.RekeningCcSofActivity;
import id.co.bri.brimo.ui.activities.lifestyle.shopping.WebviewShoppingActivity;
import id.co.bri.brimo.ui.activities.bukarekening.TabunganActivity;
import id.co.bri.brimo.ui.activities.dompetdigitalrevamp.InquiryDompetDigitalRevampActivity;
import id.co.bri.brimo.ui.activities.listrikrevamp.InquiryListrikRevampActivity;
import id.co.bri.brimo.ui.activities.ssc.SelfServiceActivity;
import id.co.bri.brimo.ui.activities.voucher.CaraRedeemVocActivity;
import id.co.bri.brimo.ui.activities.autograbfund.DashboardAutoGrabFundActivity;
import id.co.bri.brimo.ui.fragments.SmartTransferStoryFragment;
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;


public class ReceiptRevampActivity extends BaseActivity implements
        ActivityCompat.OnRequestPermissionsResultCallback,
        EasyFlipView.OnFlipAnimationListener,
        ReceiptVoucherDataAdapter.onClick,
        IGeneralReceiptView,
        View.OnClickListener {


    private static final String TAG = "ReceiptRevampActivity";
    //untuk save instance state
    private static final String TAG_PENDING = "pending_data";
    private static final String TAG_VALIDATE = "validate_data";

    private ActivityReceiptRevampNsBinding binding;
    ImageHelper imageHelper;

    private boolean isShowAll = false;
    private boolean isNoLihatLebih = false;
    private boolean isShared = false;
    private boolean isReadytoShare = false;

    protected static boolean mIsFromRencana = false;

    protected static boolean mIsSmartTransfer = false;
    protected static boolean mIsFromTransfer = false;

    private static int defaultIcon;

    private static boolean mIsFromKonfirmasi = false;

    private static String mAccOffUs = "";
    private static String mAccOffUsCode = "";

    private ReceiptRevampResponse mPendingrespon;
    private ValidateResponse mvalidateResponse;
    private SmartRecomResponse smartRecoResponse;
    protected MenuConfig.OnItemviewClickListener mOnClickListener;
    private String reqContentString = null;
    private NotifikasiModel notifikasiModel = null;
    private SkeletonScreen skeletonSmartRecom;

    private String mUrlStatusOrder = "";

    private SmartTransferUserConsentData mSmartTransferUserConsentData;
    private List<Account> listAccountConsent;

    @Inject
    IGeneralReceiptPresenter<IGeneralReceiptView> receiptPresenter;

    public static class TrxTypeReceipt {
        public static final String AutoGrabFund = "AGFPaymentAutomatic";
    }

    public static void launchIntent(Activity caller, ReceiptRevampResponse receiptRevampResponse, int icon) {
        Intent intent = new Intent(caller, ReceiptRevampActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.putExtra(TAG_PENDING, new Gson().toJson(receiptRevampResponse));
        mIsFromRencana = false;
        defaultIcon = icon;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK, setResultReceipt(receiptRevampResponse));
        caller.finish();
    }

    public static void launchIntent(Activity caller, ReceiptRevampResponse receiptRevampResponse, int icon, boolean isSmartTransfer, String accOffUs, String accOffUsCode) {
        Intent intent = new Intent(caller, ReceiptRevampActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(receiptRevampResponse));
        mIsFromRencana = false;
        mIsSmartTransfer = isSmartTransfer;
        mIsFromTransfer = true;
        mAccOffUs = accOffUs;
        mAccOffUsCode = accOffUsCode;
        defaultIcon = icon;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK, setResultReceipt(receiptRevampResponse));
        caller.finish();
    }

    public static void launchIntentReceipt(Activity caller, ReceiptRevampResponse receiptRevampResponse, int icon) {
        Intent intent = new Intent(caller, ReceiptRevampActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(receiptRevampResponse));
        mIsFromRencana = false;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK, setResultReceipt(receiptRevampResponse));
    }

    public static void launchIntent(boolean isFromRencana, Activity caller, ReceiptRevampResponse receiptRevampResponse) {
        Intent intent = new Intent(caller, ReceiptRevampActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(receiptRevampResponse));
        mIsFromRencana = isFromRencana;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.overridePendingTransition(R.anim.bottom_up, R.anim.nothing);
        caller.setResult(RESULT_OK, setResultReceipt(receiptRevampResponse));
        caller.finish();
    }

    public static void launchIntent(boolean isFromRencana, Activity caller, ReceiptRevampResponse receiptRevampResponse, boolean isFromAktifitas) {
        Intent intent = new Intent(caller, ReceiptRevampActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(receiptRevampResponse));
        mIsFromRencana = isFromRencana;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.overridePendingTransition(R.anim.bottom_up, R.anim.nothing);
        caller.setResult(RESULT_OK, setResultReceipt(receiptRevampResponse));
        if (!isFromAktifitas) {
            caller.finish();
        }
    }

    public static void launchIntent(boolean isFromRencana, boolean isFromS3FOpen, Activity caller, ReceiptRevampResponse receiptRevampResponse) {
        Intent intent = new Intent(caller, ReceiptRevampActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(receiptRevampResponse));
        mIsFromRencana = isFromRencana;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK, setResultReceipt(receiptRevampResponse));
        caller.finish();
    }

    public static void launchIntentFMpulsa(Activity caller, ReceiptRevampResponse receiptRevampResponse, boolean isFromFastMenu) {
        Intent intent = new Intent(caller, ReceiptRevampActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(receiptRevampResponse));

        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.overridePendingTransition(R.anim.bottom_up, R.anim.nothing);
        caller.setResult(RESULT_OK, setResultReceipt(receiptRevampResponse));
        caller.finish();
    }

    public static void launchIntentInvestasi(boolean isFromRencana, boolean isFromS3FOpen, boolean isFromKonfirmasi, Activity caller, ReceiptRevampResponse receiptRevampResponse) {
        Intent intent = new Intent(caller, ReceiptRevampActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(receiptRevampResponse));
        mIsFromRencana = isFromRencana;
        mIsFromKonfirmasi = isFromKonfirmasi;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK, setResultReceipt(receiptRevampResponse));
    }

    public static void launchIntentTrackingEmas(boolean isFromRencana, boolean isFromS3FOpen, boolean isFromKonfirmasi,Activity caller, ReceiptRevampResponse receiptRevampResponse) {
        Intent intent = new Intent(caller, ReceiptRevampActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(receiptRevampResponse));
        mIsFromRencana = isFromRencana;
        mIsFromKonfirmasi = isFromKonfirmasi;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK, setResultReceipt(receiptRevampResponse));
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(R.anim.bottom_up, R.anim.nothing);
        binding = ActivityReceiptRevampNsBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        imageHelper = new ImageHelper(this);

        //parsing data intent
        if (getIntent().getExtras() != null) {
            parseDataIntent(getIntent().getExtras());
        }


        //inject presenter
        injectDependency();

        parseDataReceipt();

        binding.btnShare.setOnClickListener(this);
        binding.btnClose.setOnClickListener(this);
        binding.llLhtLebih.setOnClickListener(this);
        binding.lihatSedikit.setOnClickListener(this);
        binding.lihatLebih.setOnClickListener(this);
        binding.llPusatBantuan.setOnClickListener(this);
        binding.flipFooterReceipt.setOnFlipListener(this);
        binding.flipLihatSedikit.setOnFlipListener(this);
        binding.flipButtonReceipt.setOnFlipListener(this);
        binding.rlCaraRedeem.setOnClickListener(this);
        binding.rlLihatBindingMerchant.setOnClickListener(this);

        skeletonSmartRecom = Skeleton.bind(binding.rvSmartRecommendation)
                .shimmer(true)
                .angle(20)
                .frozen(false)
                .duration(1200)
                .count(2)
                .load(R.layout.item_skeleton_smart_recom)
                .show();

        if (mPendingrespon != null && mPendingrespon.getProductCode() != null && !mPendingrespon.getProductCode().isEmpty()) {
            setEventAppsFlyerFirstInit();
        }
        showSuccessSmartTransfer(mIsSmartTransfer);
    }

    private void setEventAppsFlyerFirstInit() {
        String eventName = null;

        if (mPendingrespon != null) {
            String code = mPendingrespon.getProductCode();
            if (code != null && !code.isEmpty()) {
                if (code.equals(Constant.PLN_TYPE_CODE_PREPAID)) {
                    eventName = "listrik_token_sucess";
                } else if (code.equals(Constant.PLN_TYPE_CODE_POSTPAID)) {
                    eventName = "listrik_tagihan_sucess";
                } else if (code.equals(Constant.PLN_TYPE_CODE_NONTAGLIS)) {
                    eventName = "listrik_non_tagihan_sucess";
                } else if (code.equals(Constant.EMAS_SELL_TYPE)) {
                    eventName = "emas_success_jual";
                } else if (code.equals(Constant.EMAS_BUY_TYPE)) {
                    eventName = "emas_success_beli";
                }

                if (eventName != null) {
                    Map<String, Object> eventValue = new HashMap<>();
                    eventValue.put(Constant.CUSTOMER_ID, receiptPresenter.getPersistenceId());
                    trackAppsFlyerAnalyticEvent(eventName, eventValue);
                }
            }
        }
    }


    protected void parseDataReceipt() {
        if (mPendingrespon != null) {
            /*total_data_view*/
            totalDataView();
            /*additional_info*/
            additionalInfo();
            /*header_data_view*/
            headerTransaksi();
            /*eticket_data_view*/
            setEticketDataView();
            /*source_account_data_view*/
            sourceAccountDataView();
            /*billing_detail*/
            billingDetailView();
            /*voucher_data_view*/
            setVoucherDataView();
            /*transaction_data_view*/
            transactionDataViewSmall();
            /*detail_data_view*/
            transactionDataView();
            /*cara_redeem*/
            setCaraRedeem();
            /*order_detail_mobelanja*/
            orderDetailMobelanja();
            /*status_order_lifestyle*/
            statusOrder();
            /*pos aja*/
            orderDetailPos();
            /*amount_data_view*/
            /*detail_amount_data_view*/
            detailAmountDataView();
            amountDataView();
            /*detail_route*/
            detailRoute();
            /*footer*/
            footerview();
            /*title*/
            setTitleView();
            /*share*/
            setShareAndButton();
            //subtitle html
            setSubtitleHtml();
            //cek binding merchant
            lihatMerchantTerhubung();
            /*setup button redirect*/
            setupButtonRedirect();
            //webvie winfo
            setWebviewInfo();
        }
    }

    private void setWebviewInfo() {
        if (mPendingrespon.getWebviewInfo() != null && !mPendingrespon.getWebviewInfo().isEmpty()){
            binding.wvInfo.setVisibility(View.VISIBLE);
            int newHeightMeasureSpec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
            int newWidthMeasureSpec = binding.wvInfo.getMeasuredWidth();
            binding.wvInfo.measure(newWidthMeasureSpec,newHeightMeasureSpec );
            binding.wvInfo.requestDisallowInterceptTouchEvent(false);
            GeneralHelper.setWebViewStandart(binding.wvInfo,"",mPendingrespon.getWebviewInfo());
        }
    }

    private void totalDataView() {
        if (mPendingrespon.getTotalDataView() != null) {
            binding.tvPrice.setText(mPendingrespon.getTotalDataView().get(0).getValue());
            binding.rvTotalDataView.setVisibility(View.GONE);


//            binding.rvTotalDataView.setHasFixedSize(true);
//            binding.rvTotalDataView.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
//            ReceiptDataTotalAdapter DataTransaksiRevampAdapter = new ReceiptDataTotalAdapter(mPendingrespon.getTotalDataView(), ReceiptRevampActivity.this);
//            binding.rvTotalDataView.setAdapter(DataTransaksiRevampAdapter);
        }
    }

    private void additionalInfo() {
        if (mPendingrespon.getAdditionalInfo() != null && !mPendingrespon.getAdditionalInfo().isEmpty()) {
            binding.cvAdditionalInfo.setVisibility(View.VISIBLE);
            String additionalInfo = mPendingrespon.getAdditionalInfo();
            additionalInfo = GeneralHelper.addBoldTags(additionalInfo);
            binding.tvAdditionalInfo.setText(Html.fromHtml(additionalInfo));
        }
    }

    private void setupButtonRedirect(){
        if (mPendingrespon.getButtonRedirectText() != null && !mPendingrespon.getButtonRedirectText().isEmpty()){
            binding.mcvRedirectButton.setVisibility(View.VISIBLE);
            binding.tvRedirect.setText(mPendingrespon.getButtonRedirectText());
            binding.mcvRedirectButton.setOnClickListener(v -> {
                onClickRedirectReceipt();
            });
        }else{
            binding.mcvRedirectButton.setVisibility(View.GONE);
        }
    }

    private void onClickRedirectReceipt(){
        if (mPendingrespon.getJourneyType() != null && !mPendingrespon.getJourneyType().isEmpty()){
            switch (mPendingrespon.getJourneyType()){
                case TrxTypeReceipt.AutoGrabFund:{
                    onBackPressed();
                    DashboardAutoGrabFundActivity.launchIntent(this);
                }
            }
        }
    }

    private void headerTransaksi() {
        if (mPendingrespon.getHeaderDataView() != null) {
            binding.rvHeaderDataView.setHasFixedSize(true);
            binding.rvHeaderDataView.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            DataTransaksiRevampAdapter DataTransaksiRevampAdapter = new DataTransaksiRevampAdapter(mPendingrespon.getHeaderDataView(), ReceiptRevampActivity.this);
            binding.rvHeaderDataView.setAdapter(DataTransaksiRevampAdapter);
        }
    }

    private void sourceAccountDataView() {
        if (mPendingrespon.getSourceAccountDataView() != null) {
            binding.rvItemReceiptSourceAccountData.setHasFixedSize(true);
            binding.rvItemReceiptSourceAccountData.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            List<DetailListType> detailListTypes = new ArrayList<>();
            if (mPendingrespon.getSourceAccountDataView() != null) {
                detailListTypes.add(mPendingrespon.getSourceAccountDataView());
            }
            ReceiptAccountDataAdapter receiptAccountDataAdapter = new ReceiptAccountDataAdapter(detailListTypes, ReceiptRevampActivity.this, true, defaultIcon);
            binding.rvItemReceiptSourceAccountData.setAdapter(receiptAccountDataAdapter);
        }
    }

    private void billingDetailView() {
        if (mPendingrespon.getBillingDetail() != null) {
            binding.rvItemReceiptBillingDetail.setHasFixedSize(true);
            binding.rvItemReceiptBillingDetail.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            List<DetailListType> detailListTypesBilling = new ArrayList<>();
            if (mPendingrespon.getBillingDetail() != null) {
                detailListTypesBilling.add(mPendingrespon.getBillingDetail());
            }
            ReceiptAccountDataAdapter receiptAccountDataAdapter = new ReceiptAccountDataAdapter(detailListTypesBilling, ReceiptRevampActivity.this, false, defaultIcon);
            binding.rvItemReceiptBillingDetail.setAdapter(receiptAccountDataAdapter);
        }
    }

    private void setEticketDataView() {
        if (mPendingrespon.getEticketDataView() != null && mPendingrespon.getEticketDataView().size() > 0) {
            binding.rvEticketDataView.setVisibility(View.VISIBLE);
            binding.rvEticketDataView.setHasFixedSize(true);
            binding.rvEticketDataView.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            ReceiptEticketDataAdapter receiptEticketDataAdapter = new ReceiptEticketDataAdapter(mPendingrespon.getEticketDataView(), ReceiptRevampActivity.this);
            binding.rvEticketDataView.setAdapter(receiptEticketDataAdapter);
        }
    }

    private void setVoucherDataView() {
        if (mPendingrespon.getVoucherDataView() != null && mPendingrespon.getVoucherDataView().size() > 0) {
            binding.rvVoucherDataView.setVisibility(View.VISIBLE);
            binding.rvVoucherDataView.setHasFixedSize(true);
            binding.rvVoucherDataView.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            ReceiptVoucherDataAdapter receiptVoucherDataAdapter = new ReceiptVoucherDataAdapter(mPendingrespon.getVoucherDataView(), ReceiptRevampActivity.this, this);
            binding.rvVoucherDataView.setAdapter(receiptVoucherDataAdapter);
        }

    }

    private void transactionDataViewSmall() {
        ArrayList<DataView> dataViews = new ArrayList<>();
        for (int i = 0; ((i < mPendingrespon.getRowDataShow()) && (i < mPendingrespon.getTransactionDataView().size())); i++) {
            if (mPendingrespon.getTransactionDataView().get(i) != null) {
                dataViews.add(mPendingrespon.getTransactionDataView().get(i));
            }
        }
        if (dataViews.size() > 0) {
            binding.rvTransactionDataViewSmall.setVisibility(View.VISIBLE);
            binding.rvTransactionDataViewSmall.setHasFixedSize(true);
            binding.rvTransactionDataViewSmall.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            DataTransaksiRevampAdapter DataTransaksiRevampAdapter = new DataTransaksiRevampAdapter(dataViews, ReceiptRevampActivity.this);
            binding.rvTransactionDataViewSmall.setAdapter(DataTransaksiRevampAdapter);
        }
    }

    private void transactionDataView() {
        if (mPendingrespon.getTransactionDataView() != null) {
            binding.rvDataViewTransaction.setHasFixedSize(true);
            binding.rvDataViewTransaction.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            DataTransaksiRevampAdapter DataTransaksiRevampAdapter = new DataTransaksiRevampAdapter(mPendingrespon.getTransactionDataView(), ReceiptRevampActivity.this);
            binding.rvDataViewTransaction.setAdapter(DataTransaksiRevampAdapter);
        }
    }

    private void detailAmountDataView() {
        if (mPendingrespon.getDetailAmountDataView() != null) {
//            binding.llDetailAmountDataView.setVisibility(View.VISIBLE);
//            binding.rvDetailAmountDataView.setHasFixedSize(true);
//            binding.rvDetailAmountDataView.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
//            DataTransaksiRevampAdapter DataTransaksiRevampAdapter = new DataTransaksiRevampAdapter(mPendingrespon.getDetailAmountDataView(), ReceiptRevampActivity.this);
//            binding.rvDetailAmountDataView.setAdapter(DataTransaksiRevampAdapter);
        }
    }

    private void orderDetailPos() {
        if (mPendingrespon.getPackageDetail() != null) {
            binding.llDetailPosAja.setVisibility(View.VISIBLE);
            binding.tvDateLifestyle.setText(mPendingrespon.getPackageDetail().getTitle());
            binding.tvDuration.setText(mPendingrespon.getPackageDetail().getEstimation());
            binding.tvTitleSender.setText(mPendingrespon.getPackageDetail().getOriginTitle());
            binding.tvSenderName.setText(mPendingrespon.getPackageDetail().getOriginName());
            binding.tvSenderPhone.setText(mPendingrespon.getPackageDetail().getOriginPhone());
            binding.tvSenderAddress.setText(mPendingrespon.getPackageDetail().getOriginAddress());
            binding.tvTitleRecipient.setText(mPendingrespon.getPackageDetail().getDestinationTitle());
            binding.tvRecipientNamePos.setText(mPendingrespon.getPackageDetail().getDestinationName());
            binding.tvRecipientPhone.setText(mPendingrespon.getPackageDetail().getDestinationPhone());
            binding.tvRecipientAddress.setText(mPendingrespon.getPackageDetail().getDestinationAddress());
            binding.tvTransportClass.setText(mPendingrespon.getPackageDetail().getJenisLayanan());

            ViewTreeObserver vto = binding.llSender.getViewTreeObserver();
            vto.addOnGlobalLayoutListener(() -> {
                int height = binding.llSender.getMeasuredHeight();
                binding.separatorExpedition.setMinimumHeight(height);
            });

        }
    }

    private void amountDataView(){
        if (mPendingrespon.getAmountDataView() != null) {
            binding.rvAmountDataView.setHasFixedSize(true);
            binding.rvAmountDataView.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            DataTransaksiRevampAdapter DataTransaksiRevampAdapter = new DataTransaksiRevampAdapter(mPendingrespon.getAmountDataView(), ReceiptRevampActivity.this);
            binding.rvAmountDataView.setAdapter(DataTransaksiRevampAdapter);
        }
    }

    private void orderDetailMobelanja() {
        if (mPendingrespon.getOrderDetail() != null) {
            binding.llDetailMobelanja.setVisibility(View.VISIBLE);
            binding.rvOrderDetailMobelanja.setHasFixedSize(true);
            binding.rvOrderDetailMobelanja.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            DataTransaksiRevampAdapter dataTransaksiRevampAdapter = new DataTransaksiRevampAdapter(mPendingrespon.getOrderDetail(), ReceiptRevampActivity.this);
            binding.rvOrderDetailMobelanja.setAdapter(dataTransaksiRevampAdapter);
        }
    }

    private void detailRoute() {
        if (mPendingrespon.getDetailRoutes() != null) {
            binding.rvDetailRoute.setVisibility(View.VISIBLE);
            binding.rvDetailRoute.setHasFixedSize(true);
            binding.rvDetailRoute.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            DetailRutePesawatAdapter detailRutePesawatAdapter = new DetailRutePesawatAdapter(mPendingrespon.getDetailRoutes(), ReceiptRevampActivity.this, Constant.TravelMenu.FROM_RECEIPT_TRAVEL);
            binding.rvDetailRoute.setAdapter(detailRutePesawatAdapter);

            if (mPendingrespon.getDetailRoutes().size() == 1) {
                binding.rvDetailRoute.suppressLayout(true);
            }
        }
    }

    private void smartRecommendationDataView(SmartRecomResponse smartRecomResponse){
        binding.rvSmartRecommendation.setHasFixedSize(true);
        binding.rvSmartRecommendation.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.HORIZONTAL, false));
        SmartRecommendationAdapter smartRecommendationAdapter = new SmartRecommendationAdapter(this, smartRecomResponse.getRecommendations(), onSmartRecomClicked);
        binding.rvSmartRecommendation.setAdapter(smartRecommendationAdapter);
    }

    private void footerview() {
        if (mPendingrespon.getFooter() != null && !mPendingrespon.getFooter().isEmpty()) {
            binding.tvFooter.setVisibility(View.VISIBLE);
            binding.tvFooter.setText(mPendingrespon.getFooter());
        } else {
            binding.tvFooter.setVisibility(View.GONE);
        }
        String footerHtmlStr = mPendingrespon.getFooterHtml();
        if (mPendingrespon.getFooterHtml() != null && !footerHtmlStr.isEmpty()) {
            binding.wvFooter.setVisibility(View.VISIBLE);
            binding.wvFooter.setBackgroundColor(Color.TRANSPARENT);
            binding.wvFooter.setText(getParsedHtml(footerHtmlStr));
        } else {
            binding.wvFooter.setVisibility(View.GONE);
        }
    }

    private String getParsedHtml(String content) {
        return HtmlCompat.fromHtml(content, HtmlCompat.FROM_HTML_MODE_COMPACT).toString();
    }

    private void setTitleView() {
        if (mPendingrespon.getTitle() != null) {
            binding.tvTitle.setText(mPendingrespon.getTitle());
        }

        if (mPendingrespon.getTitleImage() != null) {
            String titleImage;
            if (mPendingrespon.getTitleImage().equalsIgnoreCase(Constant.RECEIPT58_REVAMP) || mPendingrespon.getTitleImage().equalsIgnoreCase(Constant.RECEIPT68_REVAMP)) {
                titleImage = Constant.RECEIPT68_REVAMP;
                int imageId = this.getResources().getIdentifier(titleImage, Constant.DEFTYPEIDENTIFYER, this.getPackageName());
                binding.imgCeklist2Img.setImageResource(imageId);
                binding.imgCeklist2Img.setVisibility(View.VISIBLE);
                binding.imgCeklist2.setVisibility(View.GONE);
                binding.imgCeklist.setImageResource(imageId);
            }
        }
        String dateTransaction = (mPendingrespon.getDateTransaction() != null) ? mPendingrespon.getDateTransaction() : "";
        binding.tvDate.setText(dateTransaction);

        String subtitle = (mPendingrespon.getSubtitle() != null) ? mPendingrespon.getSubtitle() : "";
        if (!subtitle.isEmpty()) {
            binding.tvDate.setVisibility(View.VISIBLE);
            binding.tvDate.setText(mPendingrespon.getSubtitle());
        }

        List<?> totalDataView = mPendingrespon.getTotalDataView();

//        if (totalDataView != null && !totalDataView.isEmpty()) {
//            Object first = totalDataView.get(0);
//
//            if (first instanceof Map) {
//                Map<?, ?> firstItem = (Map<?, ?>) first;
//
//                Object valueObj = firstItem.get("value");
//                if (valueObj != null) {
//                    String value = valueObj.toString();
//                    binding.tvPrice.setText(value);
//                }
//            }
//        }
    }

    private void setShareAndButton() {
        if (Boolean.parseBoolean(mPendingrespon.getShare())) {
            binding.btnShare.setVisibility(View.VISIBLE);
//            binding.buttonSeparator.setVisibility(View.VISIBLE);
        } else {
            binding.btnShare.setVisibility(View.GONE);
//            binding.buttonSeparator.setVisibility(View.GONE);
        }

        if (mPendingrespon.isHelpFlag() || mPendingrespon.isHelpCenter()) {
            binding.llPusatBantuan.setVisibility(View.VISIBLE);
        } else {
            binding.llPusatBantuan.setVisibility(View.GONE);
        }
    }


    protected void injectDependency() {
        getActivityComponent().inject(this);

        if (receiptPresenter != null) {
            receiptPresenter.setView(this);
            receiptPresenter.start();
            if (mPendingrespon.getVoucherGameId() != null && !mPendingrespon.getVoucherGameId().isEmpty()) {
                receiptPresenter.setUrlCaraRedeem(GeneralHelper.getString(R.string.url_cara_redeem_voucher_game));
            } else if (mPendingrespon.getStreamingId() != null && !mPendingrespon.getStreamingId().isEmpty()) {
                receiptPresenter.setUrlCaraRedeem(GeneralHelper.getString(R.string.url_cara_redeem_voucher_streaming));
            }
            receiptPresenter.setUrlTrackingMobelanja(GeneralHelper.getString(R.string.url_tracking_mobelanja));

            if(!isFromFastMenu) {
                receiptPresenter.setUrlGetRecom(GeneralHelper.getString(R.string.smart_recom_url_get));
                receiptPresenter.setUrlGetRecomFeedback(GeneralHelper.getString(R.string.smart_recom_feedback_url_get));
                receiptPresenter.setUrlCheckSimilaritySmartTransfer(GeneralHelper.getString(R.string.smart_transfer_get_similarity_account_url));
                receiptPresenter.setUrlAccListConsent(GeneralHelper.getString(R.string.smart_transfer_account_list_consent));
                receiptPresenter.setAnalyticSmartTransferUrl(GeneralHelper.getString(R.string.smart_transfer_analytic_approval_url));
                receiptPresenter.setUrlSmartTransferManageUserConsent(GeneralHelper.getString(R.string.smart_transfer_manage_user_consent_url));

                receiptPresenter.getSmartRecommendation();
                binding.llSmartRecom.setVisibility(View.VISIBLE);
                if(!mPendingrespon.getTransactionDataView().isEmpty()) {
                    //Validasi hanya untuk sementara
                    if(mIsFromTransfer && !mPendingrespon.getTransactionDataView().get(0).getValue().equalsIgnoreCase(GeneralHelper.getString(R.string.transfer_bank_bri))) {
                        receiptPresenter.checkSimilaritySmartTransfer(new SimilarityAccountSmartTransferRequest(mPendingrespon.getBillingDetail().getDescription().replaceAll("\\s", ""), mAccOffUsCode));
                    }
                }
                if(mIsSmartTransfer) {
                    try {
                        String strAmount = mPendingrespon.getAmountDataView().get(0).getValue().replaceAll(Constant.CURRENCY, "").replaceAll("\\.", "");
                        AnalyticSmartTransferRequest request = new AnalyticSmartTransferRequest(
                                "",
                                Integer.parseInt(strAmount),
                                mPendingrespon.getBillingDetail().getDescription().replaceAll("\\s", ""),
                                Constant.FLAG_BANK_BRI_CODE,
                                mAccOffUs,
                                mAccOffUsCode
                        );
                        receiptPresenter.sendAnalyticSmartTransferUrl(request);
                    } catch (Exception e){

                    }
                }
            }
        }

    }

    /**
     * Method digunakan untuk meng-extract data Intent
     *
     * @param extras Bundle savedInstanceState
     */
    protected void parseDataIntent(Bundle extras) {
        if (extras != null) {
            String pendingTemp = extras.getString(TAG_PENDING);
            if (pendingTemp != null) {
                mPendingrespon = new Gson().fromJson(pendingTemp, ReceiptRevampResponse.class);
            }

            String validationTemp = extras.getString(TAG_VALIDATE);
            if (pendingTemp != null) {
                mvalidateResponse = new Gson().fromJson(validationTemp, ValidateResponse.class);
            }
        }
    }

    /**
     * Get return Intent untuk Dashboard activity
     *
     * @return intent
     */
    public static Intent setResultReceipt(ReceiptRevampResponse response) {
        Intent intentReturn = new Intent();

        try {
            if (response.getTitleImage() != null) {
                if (response.getTitleImage().contains("00")) {
                    intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);
                    return intentReturn;
                } else {
                    intentReturn.putExtra(Constant.REQUEST_RECEIPT, false);
                    return intentReturn;
                }
            } else {
                intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);
                return intentReturn;
            }
        } catch (Exception e) {
            intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);
            return intentReturn;
        }
    }


    protected void lihatLebih() {
        animationFadeOut.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                // do nothing
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                binding.llShowMore.setVisibility(View.VISIBLE);
                binding.lihatLebih.setVisibility(View.GONE);
                binding.lihatSedikit.setVisibility(View.VISIBLE);
                binding.rvTransactionDataViewSmall.setVisibility(View.VISIBLE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
                // do nothing
            }
        });

        binding.lihatLebih.startAnimation(animationFadeOut);
        binding.flipLihatSedikit.startAnimation(animationFadeOut);

        isShowAll = true;
    }

    protected void lihatSedikit() {
        animationFadeOut.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                // do nothing
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                binding.llShowMore.setVisibility(View.GONE);
                binding.lihatLebih.setVisibility(View.VISIBLE);
                binding.lihatSedikit.setVisibility(View.GONE);
                binding.rvTransactionDataViewSmall.setVisibility(View.VISIBLE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
                // do nothing
            }
        });

        binding.llShowMore.startAnimation(animationFadeOut);
        binding.lihatSedikit.startAnimation(animationFadeOut);

        isShowAll = false;
    }

    /**
     * Generate File Image
     *
     * @return
     */
    private File generateImage() {
        File file;
        Bitmap bm;
        if (binding.receiptShare != null)
            bm = imageHelper.getBitmapFromView(binding.receiptShare, binding.receiptShare.getChildAt(0).getHeight(), binding.receiptShare.getChildAt(0).getWidth());
        else
            bm = imageHelper.getBitmapFromView(binding.layoutReceipt, binding.layoutReceipt.getChildAt(0).getHeight(), binding.layoutReceipt.getChildAt(0).getWidth());

        file = saveBitmap(bm, generateNameReceipt());
        return file;
    }

    /**
     * Method untuk digunakan untuk generate Nama File BRImo
     *
     * @return tag name FIle Receipt
     */
    protected String generateNameReceipt() {
        String tag = "";
        String dateTime = CalendarHelper.getCurrentTimeReceipt();
        try {
            tag = Constant.TAG_START_NAME + dateTime + Constant.TAG_END_NAME;
        } catch (Exception e) {
            if (!GeneralHelper.isProd())
                Log.e(TAG, "generateNameReceipt: ", e);

            tag = Constant.TAG_START_NAME + Constant.TAG_END_NAME;
        }
        return tag;
    }

    protected void toggleLihat() {
        if (!isShowAll) {
            lihatLebih();
        } else {
            lihatSedikit();
        }
    }

    private static File saveBitmap(Bitmap bm, String fileName) {
        final String path = Environment.getExternalStorageDirectory().getAbsolutePath() + Constant.URI_DOWNLOAD;
        File dir = new File(path);
        if (!dir.exists())
            dir.mkdirs();
        File file = new File(dir, fileName);

        try {
            FileOutputStream fOut = new FileOutputStream(file);
            bm.compress(Bitmap.CompressFormat.PNG, 90, fOut);
            fOut.flush();
            fOut.close();
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "saveBitmap: ", e);
            }
        }
        return file;
    }

    /**
     * Generate Share INtent to Another APPS
     *
     * @param file file image receipt
     */
    private void shareImage(File file) {
        Uri uri = FileProvider.getUriForFile(this,
                BuildConfig.APPLICATION_ID + ".fileprovider",
                file);
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_SEND);
        intent.setType("image/*");

        intent.putExtra(android.content.Intent.EXTRA_SUBJECT, "");
        intent.putExtra(android.content.Intent.EXTRA_TEXT, "");
        intent.putExtra(Intent.EXTRA_STREAM, uri);
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

        Intent chooser = Intent.createChooser(intent, "Download and share receipt");

        List<ResolveInfo> resInfoList = this.getPackageManager().queryIntentActivities(chooser, PackageManager.MATCH_DEFAULT_ONLY);

        for (ResolveInfo resolveInfo : resInfoList) {
            String packageName = resolveInfo.activityInfo.packageName;
            this.grantUriPermission(packageName, uri, Intent.FLAG_GRANT_WRITE_URI_PERMISSION | Intent.FLAG_GRANT_READ_URI_PERMISSION);
        }

        try {
            startActivity(chooser);
        } catch (ActivityNotFoundException e) {
            Toast.makeText(this, "Aplikasi tidak tersedia", Toast.LENGTH_SHORT).show();
        }
    }

    private void handleRecommendationType(SmartRecomDetail content) {
        if (content == null) return;

        Gson gson = new Gson();
        reqContentString = content.getRequestContent();
        notifikasiModel = gson.fromJson(reqContentString, NotifikasiModel.class);
        switch (content.getTrxType()) {
            case Constant.BlastType.REMINDER:
                handleReminderNotification();
                break;
            case Constant.BlastType.RECOMENDATION:
                handleRecommendationNotification();
                break;
            default:
                break;
        }
    }
    private void handleReminderNotification() {
        if (notifikasiModel.getTypeInquiryRevamp() != null && !notifikasiModel.getTypeInquiryRevamp().isEmpty()) {
            receiptPresenter.setInquiryUrl(MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlInquiryRevamp()));
            String requestContent = notifikasiModel.getRequestContent();
            String urlConfirmationRevamp = MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlConfirmationRevamp());
            String urlPaymentRevamp = MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlPaymentRevamp());
            String titleInquiry = notifikasiModel.getTitileInquiry();
            String typeInquiryRevamp = notifikasiModel.getTypeInquiryRevamp();

            if (typeInquiryRevamp.equals(Constant.PaymentReminderTypeInquiry.revampEwallet)) {
                handleEwalletPaymentReminder(urlConfirmationRevamp, urlPaymentRevamp, titleInquiry, typeInquiryRevamp);
            } else {
                receiptPresenter.getDataInquiry(new InquiryReceiptRequest(requestContent, notifikasiModel.getTypeInquiry(), urlConfirmationRevamp, urlPaymentRevamp, titleInquiry, typeInquiryRevamp));
            }
        } else {
            receiptPresenter.setInquiryUrl(MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlInquiry()));
            receiptPresenter.getDataInquiry(new InquiryReceiptRequest(notifikasiModel.getRequestContent(), notifikasiModel.getTypeInquiry(), MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlConfirmation()), MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlPayment()), notifikasiModel.getTitileInquiry(), notifikasiModel.getTypeInquiryRevamp()));
        }
    }

    private void handleRecommendationNotification() {
        if (notifikasiModel.getProductFeature() != null) {
            if (notifikasiModel.getProductFeature().equalsIgnoreCase("list_product")) {
                if (notifikasiModel.getProductType() != null) {
                    TabunganActivity.Companion.launchIntentRecomendation(this, notifikasiModel.getProductType(), true);
                }
            } else if (notifikasiModel.getProductFeature().equalsIgnoreCase("webview")) {
                ProductWebviewActivity.launchIntent(this, notifikasiModel.getUrl(), notifikasiModel.getAlertFeature(), notifikasiModel.getTitleFeature());
            } else if (notifikasiModel.getProductFeature().equalsIgnoreCase("menu")) {
                if (notifikasiModel.getProductType() != null) {
                    MenuConfig.onChangeMenu(Integer.parseInt(notifikasiModel.getProductType()), this, mOnClickListener);
                }
            }
        }
    }
    private void handleEwalletPaymentReminder(String urlConfirmationRevamp, String urlPaymentRevamp, String titleInquiry, String typeInquiryRevamp) {
        receiptPresenter.getDataInquiry(new InquiryReceiptRequest(notifikasiModel.getRequestContent(), notifikasiModel.getTypeInquiry(), urlConfirmationRevamp, urlPaymentRevamp, titleInquiry, typeInquiryRevamp));
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        switch (id) {
            case R.id.btn_close:
                if (mIsFromKonfirmasi) {
                    Intent data = new Intent();
                    data.putExtra(Constant.TAG_REFFNUM, mPendingrespon.getReferenceNumber());
                    this.setResult(RESULT_OK,data);
                    this.finish();
                    overridePendingTransition(R.anim.nothing, R.anim.bottom_down);
                } else {
                    onBackPressed();
                }
                break;
            case R.id.lihat_sedikit:
            case R.id.lihat_lebih:
                toggleLihat();
                break;
            case R.id.btn_share:
                if (!isShowAll) {
                    isShowAll = false;
                    toggleLihat();
                }
                binding.rlCaraRedeem.setVisibility(View.GONE);
                binding.flipLogoReceipt.flipTheView();
                binding.flipFooterReceipt.flipTheView();
                binding.flipButtonReceipt.flipTheView();
                if (!isNoLihatLebih)
                    binding.flipLihatSedikit.flipTheView();
                binding.llSmartRecom.setVisibility(View.GONE);
                showSuccessSmartTransfer(false);
                break;
            case R.id.ll_pusat_bantuan:
                SelfServiceActivity.launchIntent(this);
                break;
            case R.id.rl_cara_redeem:
                if (mPendingrespon.isVoucherGame()) {
                    int mId = Integer.parseInt(mPendingrespon.getVoucherGameId());
                    receiptPresenter.getCaraRedeemVoucherGame(new VoucherRequest(mId));
                } else if (mPendingrespon.isVoucherStreaming()) {
                    receiptPresenter.getCaraRedeemVoucherGame(new VoucherRequest(mPendingrespon.getStreamingId())
                    );
                } else if (mPendingrespon.getStatusOrder() != null &&
                        mPendingrespon.getStatusOrder().get(0).getStyle().equals(LifestyleConfig.Lifestyle.MOBELANJA)
                        && mPendingrespon.getOrderNumber() != null && !mPendingrespon.getOrderNumber().isEmpty()) {
                    receiptPresenter.getUrlTrackingMobelanja(new ConfirmationMobelanjaRequest(mPendingrespon.getOrderNumber()));
                } else {
                    if (mPendingrespon.getTrackingDataView() != null && !mUrlStatusOrder.isEmpty()) {
                        WebviewEkspedisiActivity.launchIntentStatus(
                                this,
                                mUrlStatusOrder,
                                GeneralHelper.getString(R.string.txt_kirim_barang),
                                LifestyleConfig.Lifestyle.STATUS
                        );
                    }
                }
                break;
            case R.id.rl_lihat_binding_merchant:
                MerchantConnectActivity.launchIntent(this, Objects.requireNonNull(mPendingrespon.getCardDataKki().getCardNumberToken()));
                break;
            default:
                break;
        }
    }

    /**
     * method override animasi ketika activity close
     */
    @Override
    public void onBackPressed() {
        super.onBackPressed();
        overridePendingTransition(R.anim.nothing, R.anim.bottom_down);
    }

    @Override
    public void onResume() {
        super.onResume();

        if (isShared) {
            binding.flipLogoReceipt.flipTheView();
            binding.flipFooterReceipt.flipTheView();
            binding.flipButtonReceipt.flipTheView();
            setCaraRedeem();
            statusOrder();
            if (!isNoLihatLebih)
                binding.flipLihatSedikit.flipTheView();
            isShared = false;
            if(!isFromFastMenu) {
                if (smartRecoResponse != null) {
                    if (!smartRecoResponse.getRecommendations().isEmpty()) {
                        binding.llSmartRecom.setVisibility(View.VISIBLE);
                    }
                }
                showSuccessSmartTransfer(mIsSmartTransfer);
            }
        }
    }

    /**
     * Callback ketika animasi Easy FLIP
     *
     * @param easyFlipView
     * @param newCurrentSide
     */
    @Override
    public void onViewFlipCompleted(EasyFlipView easyFlipView, EasyFlipView.FlipState newCurrentSide) {
        if (easyFlipView.getId() == R.id.flipFooterReceipt &&
                newCurrentSide.equals(EasyFlipView.FlipState.BACK_SIDE)) {
            try {
                if (!isReadytoShare) {
                    new Handler().postDelayed(this::requestPermission, 500);
                    isReadytoShare = true;
                }
            } catch (Exception e) {
                Log.e(TAG, "onMenuItemClick: ", e);
            }
        }
    }

    /**
     * Save data response Payment
     *
     * @param outState
     */
    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);

        if (mPendingrespon != null) {
            try {
                outState.putString(TAG_PENDING, new Gson().toJson(mPendingrespon));
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG, "onSaveInstanceState: ", e);
            }
        }

        if (mvalidateResponse != null) {
            try {
                outState.putString(TAG_VALIDATE, new Gson().toJson(mvalidateResponse));
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG, "onSaveInstanceState: ", e);
            }
        }
    }

    /**
     * Extract data response Payment for view response
     *
     * @param savedInstanceState
     */
    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);

        String pendingTemp = savedInstanceState.getString(TAG_PENDING);
        if (pendingTemp != null) {
            mPendingrespon = new Gson().fromJson(pendingTemp, ReceiptRevampResponse.class);
        }

        String validationTemp = savedInstanceState.getString(TAG_VALIDATE);
        if (pendingTemp != null) {
            mvalidateResponse = new Gson().fromJson(validationTemp, ValidateResponse.class);
        }
    }

    @Override
    protected void onDestroy() {
        if (receiptPresenter != null) {
            receiptPresenter.stop();
        }
        isFromFastMenu = false;
        mIsSmartTransfer = false;
        mIsFromTransfer = false;
        super.onDestroy();
    }


    protected void requestPermission() {
        if (!hasPermissions(this, PERMISSIONS)) {
            ActivityCompat.requestPermissions(this, PERMISSIONS, PERMISSIONS_ALL);
        } else {
            shareImage(generateImage());
            isShared = true;
            isReadytoShare = false;
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean grantAll = true;
        if (grantResults.length > 0) {
            for (int i = 0; i < grantResults.length; i++) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    grantAll = false;
                    break;
                }
            }
        }

        if (!grantAll) {
            showAlertFinish(getString(R.string.notes_need_permission_resi));
        } else {
            requestPermission();
        }
    }

    /**
     * Callback ketika click copy dari Adapter ReceiptVoucherDataAdapter utk menampilkan Snackbar
     *
     * @param dataValue copyed data
     */

    @Override
    public void onCopyClick(String label, String dataValue) {
        showSnackbarErrorMessageRevamp(label +" "+ GeneralHelper.getString(R.string.text_berhasil_disalin), ALERT_CONFIRM, this, false);
    }

    @Override
    public void onSuccessGetTutorial(@NonNull TutorialVoucherResponse tutorialVoucherResponse) {
        if (mPendingrespon.getVoucherGameId() != null && !mPendingrespon.getVoucherGameId().isEmpty()) {
            CaraRedeemVocActivity.launchIntent(this, mPendingrespon.getVoucherGameId(), tutorialVoucherResponse);
        } else if (mPendingrespon.getStreamingId() != null && !mPendingrespon.getStreamingId().isEmpty()) {
            CaraRedeemVocActivity.launchIntent(this, mPendingrespon.getStreamingId(), tutorialVoucherResponse);
        }
    }

    @Override
    public void onSuccessGetSmartRecom(SmartRecomResponse smartRecomResponse) {
        skeletonSmartRecom.hide();
        binding.llBottomField.setVisibility(View.VISIBLE);
        if (smartRecomResponse.getRecommendations().isEmpty()) {
            binding.llSmartRecom.setVisibility(View.GONE);
        } else {
            smartRecoResponse = smartRecomResponse;
            smartRecommendationDataView(smartRecomResponse);
        }
    }

    @Override
    public void onFailedGetSmartRecom() {
        runOnUiThread(()->{
            skeletonSmartRecom.hide();
            try {
                if(binding != null)
                    binding.llSmartRecom.setVisibility(View.GONE);
            } catch (Exception e) {
                if (!GeneralHelper.isProd()) {
                    Log.d("Smart Recommendation", "onFailedGetSmartRecom: " + e.getMessage());
                }
            }
        });
    }

    @Override
    public void onSuccessGetUrlTrackingMobelanja(UrlTrackingMobelanjaResponse urlTrackingMobelanjaResponse) {
        WebviewShoppingActivity.launchIntent(
                this,
                urlTrackingMobelanjaResponse.getUrlTracking(),
                GeneralHelper.getString(R.string.txt_belanja_harian),
                "",
                LifestyleConfig.Lifestyle.STATUS,
                "",
                "",
                ""
        );
    }

    protected void setCaraRedeem() {
        if (mPendingrespon.getVoucherDataView() != null) {
            for (int i = 0; i < mPendingrespon.getVoucherDataView().size(); i++) {
                if (mPendingrespon.getVoucherDataView() != null && mPendingrespon.getVoucherDataView().get(i).getStyle().equals("Redeem")) {
                    binding.rlCaraRedeem.setVisibility(View.VISIBLE);
                }
            }
        }
    }

    protected void statusOrder() {
        if (mPendingrespon.getStatusOrder() != null) {
            for (int pos = 0; pos < mPendingrespon.getStatusOrder().size(); pos++) {
                if (mPendingrespon.getStatusOrder() != null &&
                        mPendingrespon.getStatusOrder().get(pos).getStyle().equals(
                                LifestyleConfig.Lifestyle.MOBELANJA)) {
                    binding.ivVoucher.setImageResource(R.drawable.ic_voucher_redeem);
                    binding.rlCaraRedeem.setVisibility(View.VISIBLE);
                }
                binding.tvRedeem.setText(mPendingrespon.getStatusOrder().get(pos).getName());
            }
        } else if (mPendingrespon.getTrackingDataView() != null) {
            for (int pos = 0; pos < mPendingrespon.getTrackingDataView().size(); pos++) {
                if (mPendingrespon.getTrackingDataView() != null) {
                    binding.rlCaraRedeem.setVisibility(View.VISIBLE);
                    binding.ivVoucher.setImageResource(R.drawable.ic_document);
                    binding.tvRedeem.setText(mPendingrespon.getTrackingDataView().get(pos).getName());
                    mUrlStatusOrder = mPendingrespon.getTrackingDataView().get(pos).getValue();
                }
            }
        }
    }

    protected void lihatMerchantTerhubung() {
        if (mPendingrespon.getBindingResponse() != null) {
            if (receiptPresenter.isSuccessBinding(mPendingrespon.getBindingResponse())){
                binding.rlLihatBindingMerchant.setVisibility(View.VISIBLE);
                binding.rlSuccess.setVisibility(View.VISIBLE);
                binding.tvSuccessBinding.setText(mPendingrespon.getBindingText());
            } else {
                binding.rlLihatBindingMerchant.setVisibility(View.GONE);
                binding.rlWarning.setVisibility(View.VISIBLE);
                binding.tvWarningBinding.setText(mPendingrespon.getBindingText());
            }
        }
    }


    private void setSubtitleHtml() {
        if (mPendingrespon.getSubtitleHtml() != null) {
            String subtitleHtml = mPendingrespon.getSubtitleHtml();
            if (mPendingrespon.getSubtitleHtml() != null && !subtitleHtml.isEmpty()) {
                binding.wvSubtitleHtml.setVisibility(View.VISIBLE);
                binding.wvSubtitleHtml.setBackgroundColor(Color.TRANSPARENT);
                SizeHelper.setMarginsView(this, binding.cvContent, 20, 8, 20, 0);
                binding.wvSubtitleHtml.setText(getParsedHtml(subtitleHtml));
            } else {
                binding.wvSubtitleHtml.setVisibility(View.GONE);
            }
        } else {
            binding.wvSubtitleHtml.setVisibility(View.GONE);
        }
    }

    private final Function1<SmartRecomDetail, Unit> onSmartRecomClicked = new Function1<>() {
        @Override
        public Unit invoke(SmartRecomDetail detail) {
            handleRecommendationType(detail);
            receiptPresenter.sendSmartRecommendationFeedback(new SmartRecomFeedbackRequest("", smartRecoResponse.getResponseCode(), smartRecoResponse.getDs(), smartRecoResponse.getInquiryAt(), detail.getPriority(), detail.getType(), detail.getSubType()));
            return Unit.INSTANCE;
        }
    };

    public ParameterModel setParameter() {
        ParameterModel parameterModel = new ParameterModel();
        parameterModel.setStringLabelTujuan(GeneralHelper.getString(R.string.nomor_tujuan));
        parameterModel.setStringLabelNominal(GeneralHelper.getString(R.string.nominal_pembayaran));
        parameterModel.setStringButtonSubmit(GeneralHelper.getString(R.string.bayar));
        parameterModel.setStringLabelMinimum(GeneralHelper.getString(R.string.Pembayaran));
        return parameterModel;
    }

    @Override
    public void onSuccessGetInquiry(InquiryReceiptResponse inquiryReceiptResponse) {
        if (inquiryReceiptResponse.getTypeInquiry() != null) {
            if (inquiryReceiptResponse.getTypeInquiry().equalsIgnoreCase("0")) {
                InquiryGeneralCloseActivity.launchIntent(this, inquiryReceiptResponse.getGeneralInquiryResponse(), inquiryReceiptResponse.getUrlKonfirmasi(), inquiryReceiptResponse.getUrlPayment(), inquiryReceiptResponse.getTitle(), setParameter(), isFromFastMenu);
            } else if (inquiryReceiptResponse.getTypeInquiry().equalsIgnoreCase("1")) {
                InquiryGeneralOpenActivity.launchIntent(this, inquiryReceiptResponse.getGeneralInquiryResponse(), inquiryReceiptResponse.getUrlKonfirmasi(), inquiryReceiptResponse.getUrlPayment(), inquiryReceiptResponse.getTitle(), setParameter(), isFromFastMenu);
            } else if (inquiryReceiptResponse.getTypeInquiry().equalsIgnoreCase("2")) {
                InquiryPlnTokenActivity.launchIntent(this, inquiryReceiptResponse.getGeneralInquiryResponse(), inquiryReceiptResponse.getTitle(), inquiryReceiptResponse.getUrlKonfirmasi(), inquiryReceiptResponse.getUrlPayment(), setParameter(), isFromFastMenu);
            } else {
                InquiryGeneralCloseActivity.launchIntent(this, inquiryReceiptResponse.getGeneralInquiryResponse(), inquiryReceiptResponse.getUrlKonfirmasi(), inquiryReceiptResponse.getUrlPayment(), inquiryReceiptResponse.getTitle(), setParameter(), isFromFastMenu);
            }
        }
    }

    @Override
    public void onSuccessGetInquiryRevamp(InquiryRevampReceiptResponse inquiryRevampReceiptResponse) {
        if (inquiryRevampReceiptResponse.getTypeInquiryRevamp() != null && !inquiryRevampReceiptResponse.getTypeInquiryRevamp().isEmpty()){
            // payment reminder revamp
            if (inquiryRevampReceiptResponse.getTypeInquiryRevamp().equalsIgnoreCase(Constant.PaymentReminderTypeInquiry.revampListrikPostPaid)){
                InquiryBrivaRevampResponse inquiryBrivaRevampResponse = inquiryRevampReceiptResponse.getResponse().getData(InquiryBrivaRevampResponse.class);
                ParameterModel parameterModelRevamp = setParameter();
                parameterModelRevamp.setStringButtonSubmit(GeneralHelper.getString(R.string.lanjutkan));
                inquiryBrivaRevampResponse.setPageTitleString(GeneralHelper.getString(R.string.str_konfirmasi));
                InquiryKonfirmasiBrivaRevampCloseActivity.launchIntent(this, inquiryBrivaRevampResponse, inquiryRevampReceiptResponse.getUrlKonfirmasi(), inquiryRevampReceiptResponse.getUrlPayment(), isFromFastMenu, parameterModelRevamp, Constant.TRX_TYPE_LISTRIK);
            }else if(inquiryRevampReceiptResponse.getTypeInquiryRevamp().equalsIgnoreCase(Constant.PaymentReminderTypeInquiry.revampListrikPrepaid)){
                InquiryBrivaRevampResponse inquiryBrivaRevampResponse = inquiryRevampReceiptResponse.getResponse().getData(InquiryBrivaRevampResponse.class);
                ParameterModel parameterModelRevamp = setParameter();
                parameterModelRevamp.setStringButtonSubmit(GeneralHelper.getString(R.string.lanjutkan));
                InquiryListrikRevampActivity.launchIntent(this, isFromFastMenu, inquiryRevampReceiptResponse.getUrlKonfirmasi(), inquiryRevampReceiptResponse.getUrlPayment(), inquiryBrivaRevampResponse, parameterModelRevamp, Constant.PLN_TYPE_CODE_PREPAID, "");
            }else if(inquiryRevampReceiptResponse.getTypeInquiryRevamp().equalsIgnoreCase(Constant.PaymentReminderTypeInquiry.revampEwallet)){
                InquiryDompetDigitalResponse inquiryDompetDigitalResponse = inquiryRevampReceiptResponse.getResponse().getData(InquiryDompetDigitalResponse.class);
                ParameterModel parameterModelRevamp = setParameter();
                parameterModelRevamp.setStringButtonSubmit(GeneralHelper.getString(R.string.lanjutkan));
                InquiryDompetDigitalRevampActivity.launchIntent(this, inquiryDompetDigitalResponse, inquiryRevampReceiptResponse.getUrlKonfirmasi(), inquiryRevampReceiptResponse.getUrlPayment(), isFromFastMenu, parameterModelRevamp, "");
            }
        }else{
            // payment reminder non revamp
            GeneralInquiryResponse generalInquiryResponse = inquiryRevampReceiptResponse.getResponse().getData(GeneralInquiryResponse.class);
            if (inquiryRevampReceiptResponse.getTypeInquiry().equalsIgnoreCase(Constant.PaymentReminderTypeInquiry.inquiryOpen)){
                InquiryGeneralOpenActivity.launchIntent(this, generalInquiryResponse, inquiryRevampReceiptResponse.getUrlKonfirmasi(), inquiryRevampReceiptResponse.getUrlPayment(), inquiryRevampReceiptResponse.getTitle(), setParameter(), isFromFastMenu);
            }else if(inquiryRevampReceiptResponse.getTypeInquiry().equalsIgnoreCase(Constant.PaymentReminderTypeInquiry.listrikPrepaid)){
                InquiryBrivaRevampResponse inquiryBrivaRevampResponse = inquiryRevampReceiptResponse.getResponse().getData(InquiryBrivaRevampResponse.class);
                ParameterModel parameterModelRevamp = setParameter();
                parameterModelRevamp.setStringButtonSubmit(GeneralHelper.getString(R.string.lanjutkan));
                InquiryListrikRevampActivity.launchIntent(this, isFromFastMenu, inquiryRevampReceiptResponse.getUrlKonfirmasi(), inquiryRevampReceiptResponse.getUrlPayment(), inquiryBrivaRevampResponse, parameterModelRevamp, Constant.PLN_TYPE_CODE_PREPAID, "");
            }else{
                InquiryGeneralCloseActivity.launchIntent(this, generalInquiryResponse, inquiryRevampReceiptResponse.getUrlKonfirmasi(), inquiryRevampReceiptResponse.getUrlPayment(), inquiryRevampReceiptResponse.getTitle(), setParameter(), isFromFastMenu);
            }
        }
    }

    @Override
    public void onSuccessGetTrackingPattern(PatternLifestyleTrackingResponse patternLifestyleTrackingResponse) {
        //do nothing
    }

    @Override
    public void onExceptionEticketNotIssued() {
        //do nothing
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        for (Fragment fragment : getSupportFragmentManager().getFragments()) {
            fragment.onActivityResult(requestCode, resultCode, data);
        }
        if (requestCode == Constant.REQ_BINDING_MERCHANT) {
            ApplyVccSofListActivity.Companion.launchIntent(this);
            finish();
        }

        if(requestCode == Constant.REQ_PAYMENT){
            if (resultCode == RESULT_OK){
                setResult(RESULT_OK);
                finish();
            }
        }
    }
    @Override
    public void onShowBottomSheetCcAsSof() {
        //do nothing
    }

    @Override
    public void onSuccessChangeSof(String desc) {
        //do nothing
    }

    @Override
    public void onSuccessCheckSimilaritySmartTransfer(SmartTransferGeneralResponse checkSimilarityResponse) {
        if (checkSimilarityResponse.getSimilarity().getStatus() == 1) {
            mSmartTransferUserConsentData = checkSimilarityResponse.getConsentData();
            receiptPresenter.getAccountListConsent();
        }
    }

    @Override
    public void onSucessGetAccountListConsent(SmartTransferAccountListConsentResponse smartTransferAccountListConsentResponse) {
        listAccountConsent = smartTransferAccountListConsentResponse.getAccount();
        if (mSmartTransferUserConsentData.getConsent().getApproved() == 0) {
            binding.llBottomField.setVisibility(View.VISIBLE);
            binding.rlSmartTransferContainer.setVisibility(View.VISIBLE);
        } else if (mSmartTransferUserConsentData.getConsent().getApproved() == 1) {
            new Handler().postDelayed(this::showDialogSmartTransfer, 2500);
        }
    }

    @Override
    public void onSuccessSmartTransferManageUserConsent(SmartTransferConfirmAccBinding smartTransferConfirmAccBinding) {
        confirmSmartTransferAccount(smartTransferConfirmAccBinding.getListAccount());
    }

    private void onFirstLaunchSmartTransfer(String tnc) {
        List<Integer> imgResPath = Arrays.asList(
                R.drawable.highlight_smart_transfer_1,
                R.drawable.highlight_smart_transfer_2,
                R.drawable.highlight_smart_transfer_3);
        SmartTransferStoryFragment storyFragment = new SmartTransferStoryFragment(
                this,
                imgResPath,
                tnc,
                manageUserConsent);
        storyFragment.show(getSupportFragmentManager(), storyFragment.getTag());
    }

    public static Function0<Unit> createKotlinFunction0(Runnable action) {
        return () -> {
            action.run();
            return Unit.INSTANCE;
        };
    }

    private final Function1 manageUserConsent = new Function1() {
        @Override
        public Object invoke(Object object) {
            if (object instanceof Boolean)
                receiptPresenter.smartTransferManageUserConsent(true);
            return Unit.INSTANCE;
        }
    };

    Runnable firstBtnFunction = () -> {
        confirmSmartTransferAccount(mSmartTransferUserConsentData.getBankAccountList().getNonBri());
    };

    Runnable secondBtnFunction = () -> {

    };

    private void showDialogSmartTransfer() {
        OpenBottomSheetGeneralFragment.INSTANCE.showDialogConfirmation(
                getSupportFragmentManager(),
                "",
                "ic_tambah_smart_transfer",
                GeneralHelper.getString(R.string.title_suggestion_second_visit_smart_transfer),
                GeneralHelper.getString(R.string.desc_suggestion_second_visit_smart_transfer),
                createKotlinFunction0(firstBtnFunction),
                createKotlinFunction0(secondBtnFunction),
                false,
                GeneralHelper.getString(R.string.ya_tambahkan),
                GeneralHelper.getString(R.string.nanti_saja),
                true
        );
    }

    private void showSuccessSmartTransfer(boolean status){
        if(status)
            binding.rlSuccessSmartTransfer.setVisibility(View.VISIBLE);
        else
            binding.rlSuccessSmartTransfer.setVisibility(View.GONE);
    }

    private void confirmSmartTransferAccount(List<NonBriAccount> listNonBri){
        NonBriAccount nonBriAccount = null;
        for(NonBriAccount nonBri: listNonBri){
            if (nonBri.getAccountBankLain().equals(mPendingrespon.getBillingDetail().getDescription().replaceAll("\\s", ""))){
                nonBriAccount = nonBri;
                break;
            }
        }
        if(nonBriAccount != null) {
            KonfirmasiRekeningSmartTransferActivity.launchIntentSimilarity(
                    this,
                    nonBriAccount,
                    listAccountConsent
            );
        }
    }
}