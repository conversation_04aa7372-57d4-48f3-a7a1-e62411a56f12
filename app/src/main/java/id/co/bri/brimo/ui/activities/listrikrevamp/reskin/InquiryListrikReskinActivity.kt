package id.co.bri.brimo.ui.activities.listrikrevamp.reskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.nominal.NominalListrikAdapter
import id.co.bri.brimo.contract.IPresenter.base.InquiryConfirmation
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.IFormListrikReskinPresenter
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.IFormListrikReskinView
import id.co.bri.brimo.databinding.ActivityInquiryListrikReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.Amount
import id.co.bri.brimo.models.BillingDetailOpen
import id.co.bri.brimo.models.apimodel.request.InquiryPlnRequest
import id.co.bri.brimo.models.apimodel.response.DataPlnResponse
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.InboxResponse
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.PendingResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SaldoReponse
import id.co.bri.brimo.presenters.listrikrevamp.reskin.FavoriteType
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.fragments.SumberDanaFragment
import id.co.bri.brimo.util.RxBus
import io.reactivex.android.schedulers.AndroidSchedulers
import javax.inject.Inject
import kotlin.String

class InquiryListrikReskinActivity: NewSkinBaseActivity(), View.OnClickListener,
    SumberDanaFragment.SelectSumberDanaInterface, IFormListrikReskinView {
    private var _binding: ActivityInquiryListrikReskinBinding? = null
    protected val binding get() = _binding!!

    private lateinit var billingDetail: BillingDetailOpen

    private lateinit var nominalListrikAdapter: NominalListrikAdapter

    private lateinit var selectedAccount: AccountModel

    private var isClick = false

    private var counter: Int = 0

    private lateinit var nominalSelected: Amount

    protected var mListFailed: List<Int>? = null
    protected var mListAccountModel: List<AccountModel>? = null

    @Inject
    lateinit var presenter: IFormListrikReskinPresenter<IFormListrikReskinView>

    private lateinit var request: InquiryPlnRequest

    companion object {
        const val TAG = "InquiryListrikReskin"
        private var dataInquiry: InquiryBrivaRevampResponse?= null

        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean, inquiry: InquiryBrivaRevampResponse, reqInquiry: InquiryPlnRequest) {
            dataInquiry = inquiry

            isFromFastMenu = fromFastMenu
            caller.apply {
                startActivityForResult(Intent(
                    this,
                    InquiryListrikReskinActivity::class.java
                ).apply {
                    putExtras(Bundle().apply {
                        putParcelable("$TAG.request", reqInquiry)
                    })
                }, Constant.REQ_PAYMENT)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        _binding = ActivityInquiryListrikReskinBinding.inflate(layoutInflater)

        setContentView(binding.root)
        onBindIntentData()

        injectDependency()

        onBindView()

        RxBus.listen(InquiryListrikEvent::class.java)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { event ->
                finish()
            }
    }

    private fun onBindIntentData() {
        intent?.let {
            request = it.getParcelableExtra("$TAG.request")!!
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)

        presenter.apply {
            view = this@InquiryListrikReskinActivity
            start()
        }
    }

    private fun onBindView() {
        mListAccountModel = dataInquiry!!.accountModel
        selectedAccount = dataInquiry!!.accountModel[0]

        onChangeSourcePayment(selectedAccount)
        binding.lySumberDana.setOnClickListener(this)

        binding.bslContent.apply {
            setTextToolbar(this@InquiryListrikReskinActivity, "Token Listrik")
        }

        dataInquiry?.let {
            billingDetail = it.billingDetailOpen[0]

            nominalListrikAdapter = NominalListrikAdapter(it.amountOption, { item ->
                binding.btnSubmit.isEnabled = isClick && nominalSelected != null
                nominalSelected = item
            })
        }

        billingDetailShow()

        binding.rvNominal.apply {
            layoutManager = GridLayoutManager(context, 2)
            adapter = nominalListrikAdapter
        }

        binding.btnSubmit.setOnClickListener {
            dataInquiry?.let {
                presenter.getDataConfirmation(InquiryConfirmation(
                    refNum = it.referenceNumber,
                    accountNum = selectedAccount.acoount,
                    amount = nominalSelected.value.toString(),
                    save = "",
                    fromFast = false,
                ))
            }
        }
    }

    private fun billingDetailShow() {
        binding.tvNameCust.text = billingDetail.title
        binding.tvNumberCust.text = String.format(
            GeneralHelper.getString(R.string.transaction_detail_content),
            GeneralHelper.getString(R.string.opt_pln_token),
            dataInquiry!!.billingDetail[0].value
        )
        GeneralHelper.loadIconTransaction(
            this,
            billingDetail.iconPath,
            billingDetail.iconName.split("\\.".toRegex())
                .dropLastWhile { it.isEmpty() }.toTypedArray().get(0),
            binding.ivArea,
            GeneralHelper.getImageId(this, "bri")
        )
    }

    private fun onChangeSourcePayment(bankModel: AccountModel) {
        binding.tvNumberAccount.text = bankModel.acoountString
        selectedAccount = bankModel

        if(bankModel.saldoReponse != null) {
            val saldoReponse: SaldoReponse = bankModel.saldoReponse

            if (saldoReponse.getBalanceString().equals("TO", ignoreCase = true)) {
                binding.tvNominalAccount.text = GeneralHelper.getString(R.string.gagal_memuat)
            } else if (saldoReponse.getBalanceString().equals("12", ignoreCase = true)) {
                binding.tvNominalAccount.text = GeneralHelper.getString(R.string.empty)
            } else if (saldoReponse.balanceString.equals("05", ignoreCase = true)) {
                binding.tvNominalAccount.text = GeneralHelper.getString(R.string.empty)
            } else {
                val currency = saldoReponse.currency
                isClick = true

                val nominalIdr = GeneralHelper.formatNominalIDR(
                    saldoReponse.currency,
                    saldoReponse.balanceString
                )

                binding.tvNominalAccount.text = if (saldoReponse.signedBalanceString.isNotEmpty()
                ) saldoReponse.signedBalanceString else if (currency != null) nominalIdr.substring(
                    0,
                    nominalIdr.length - 3
                ) else saldoReponse.balanceString
            }
        }
    }

    override fun onClick(v: View?) {
        when(v?.id) {
            R.id.ly_sumber_dana -> {
                counter++
                val selectedIndex = mListAccountModel?.indexOfFirst {
                    selectedAccount.acoountString.equals(it.acoountString)
                }
                val fragmentSumberDana =
                    SumberDanaFragment(mListAccountModel, this, counter, mListFailed, selectedIndex!!, dataInquiry!!.payAmount!!.toInt(), isFromFastMenu)
                fragmentSumberDana.show(supportFragmentManager, Constant.TAG_PICK_ACCOUNT)
            }
        }
    }

    override fun onSelectSumberDana(bankModel: AccountModel) {
        onChangeSourcePayment(bankModel)
        binding.btnSubmit.isEnabled = nominalSelected != null
    }

    override fun onSendFailedList(list: MutableList<Int>?) {
        this.mListFailed = list
    }

    override fun onSuccessGetForm(data: DataPlnResponse) {
        // do nothing
    }

    override fun onSuccessInquiry(data: InquiryBrivaRevampResponse) {
        dataInquiry = data
    }

    override fun onSuccessGetConfirmation(brivaConfirmationResponse: GeneralConfirmationResponse?) {
        ConfirmOpenBillListrikReskinActivity.launchIntent(this@InquiryListrikReskinActivity, false, brivaConfirmationResponse!!, selectedAccount)
    }

    override fun onSuccessGetPayment(receiptRevampResponse: ReceiptRevampResponse) {
    }

    override fun onExceptionWrongIDPel() {

    }

    override fun onExceptionNotAvail(message: String) {

    }

    override fun onSuccess(
        data: RestResponse,
        type: FavoriteType
    ) {

    }
}

data class InquiryListrikEvent(
    val message: String
)