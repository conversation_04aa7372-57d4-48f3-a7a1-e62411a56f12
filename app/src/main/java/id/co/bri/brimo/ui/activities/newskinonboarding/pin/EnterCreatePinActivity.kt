package id.co.bri.brimo.ui.activities.newskinonboarding.pin

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import id.co.bri.brimo.databinding.ActivityEnterPinBinding
import id.co.bri.brimo.domain.SnackBarType
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.fragments.pin.PinEntryFragment
import id.co.bri.brimo.ui.fragments.pin.PinEntryListener

class EnterCreatePinActivity : NewSkinBaseActivity(), PinEntryListener {

    private lateinit var binding: ActivityEnterPinBinding

    private val launcher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            setResult(RESULT_OK)
            finish()
        } else if (result.resultCode == RESULT_CANCELED) {
            val isConfirmFailed = result.data?.getBooleanExtra("EXTRA_PIN_CONFIRM_FAILED", false) ?: false
            if (isConfirmFailed) {
                GeneralHelperNewSkin.showSnackBarCustom(
                    this,
                    "PIN tidak sama. Silakan buat ulang PIN baru.",
                    type = SnackBarType.ERROR
                )
            }
        }
    }


    private var requestCode: Int = -1

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEnterPinBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setStatusBarDarkIcons()
        requestCode = intent.getIntExtra("request_code", -1)
        setupView()
        injectDependency()
    }

    private fun injectDependency() {

    }

    private fun setupView() {
        setupPin()
    }

    @SuppressLint("CommitTransaction")
    private fun setupPin(){
        val title = if (requestCode == Constant.REQ_UBAH_PIN) "Ubah PIN" else "PIN"
        val pinFragment = PinEntryFragment().apply {
            headerTitle = title
            descriptionText = "Masukkan PIN baru"
            infoText = "PIN ini akan dipakai untuk konfirmasi transaksi.\n" +
                    "Gunakan PIN yang unik dan sulit ditebak."
            isForgotPinVisible = false
            setPinEntryListener(this@EnterCreatePinActivity)
        }

        supportFragmentManager.beginTransaction()
            .replace(binding.fragmentContainerPin.id, pinFragment)
            .commit()
    }

    override fun onPinComplete(pin: String) {
        val intent = Intent(this, EnterCreatePinConfirmActivity::class.java).apply {
            putExtra("EXTRA_CREATED_PIN", pin)
            putExtra("request_code", requestCode)
        }
        launcher.launch(intent)
    }

    override fun onPinError(errorMessage: String) {
    }

    override fun onForgotPinClicked() {
    }

    override fun isScreenshotDisabled(): Boolean = true

}