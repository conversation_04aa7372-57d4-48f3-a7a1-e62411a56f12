package id.co.bri.brimo.data.preference;

import android.content.Context;

import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimo.models.MenuModel;
import id.co.bri.brimo.models.apimodel.request.ListValidateTersimpan;
import id.co.bri.brimo.models.apimodel.request.ValidateTersimpanRequest;
import id.co.bri.brimo.util.singalarity.oauth2.OAuthTokenResult;

public interface BRImoPrefSource {

    boolean getAktivasiVoiceAssistant();

    boolean isLogged();

    String getUser();

    String getTokenKey();

    String getUsername();

    String getUserAlias();

    String getName();

    String getPhoneNumber();

    String getRandom();

    String getSeqNumber();

    String getCurrentSeqNumber();

    String getUserType();

    String getDeviceId();

    String getDeviceId2();

    String getSaldoRekeningUtama();

    String getSaldoRekeningUtamaString();

    String getSavedMenu();

    String getInstallId();

    int getCheckPoint();

    int getCheckPointDS();

    String getVerifikasiId();

    String getVerifikasiIdDS();

    String getMidMerchant();

    String getAccNumbMerch();

    String getAccountDefault();

    int getTimeStamp();

    int getExpiredTime();

    String getFirebaseToken();

    int getBlastId();

    String getVersionApp();

    int getVersionCodeApp();

    Boolean getPfmBubble();

    Boolean getVallasBubble();

    Boolean getSiklusFirstOpen();

    Boolean getFastMenuFirst();

    Boolean getDisablePopupNotif();

    Boolean getLoginFlag();

    Boolean getDialogBrifine();

    Boolean getChangeDeviceFlag();

    Boolean getFreshInstallFlag();

    Boolean getFirstRdn();

    Integer getBackConditionRDNSBN();

    Boolean getAsuransiBubble();

    Boolean getKseiDetail();

    Boolean getInvestasi();

    Boolean getInvestasiNoCurrency();

    Boolean getInvestasiNoAset();

    Boolean getRencanaBubble();

    Boolean getRencanaDetailBubble();

    Boolean getInfoRencanaBottom();

    ArrayList<ValidateTersimpanRequest> getValidateRequest();

    String getBannerImageUrl();

    String getTitleBanner();

    Boolean getChatBankingBubble();

    Boolean getBlockCardBubble();

    Boolean getProfileRevampBubble();

    Boolean getEmasBubble();

    Boolean getPengkinianBubble();

    boolean getBubbleAlertSaldo();

    boolean getSaldoHold();

    String getCheckPointRdn();

    boolean getUpdateTokenFirebase();

    boolean getBubbleNewOnboarding();

    String getTermCondition();

    Boolean getFirstKcic();

    Boolean getStatusAktivasi();

    String getBiometricType();

    Boolean getStatusBioChange();

    Boolean getStatusUpdateBio();

    String getValueKeyBiometric();

    Boolean getBottomBiometric();

    String getListFastMenu();

    String getListFastMenuDefault();

    String getListFastMenuDefaultToggle();

    String getDataGraphicDplk();

    void saveDataGrapichDplk(String response);

    Boolean isIndihomeFirstClick();

    // save
    void saveFirebaseToken(String newToken);

    void savePhoneNumber(Context context, String phoneNumber);

    void saveSeqNumber(Context context, int seqNumber);

    void saveUserType(String userType);

    void saveTokenKey(String tokenKey);

    void saveUsername(String userName);

    void saveSaldoRekeningUtama(String saldo);

    void saveSaldoRekeningUtamaString(String saldo);

    void saveCurrency(String currency);

    void saveNameRekeningUtama(String name);

    void saveBrizziValidate(ValidateTersimpanRequest validate);

    void saveBrizziValidateList(ListValidateTersimpan validate);

    void saveDeviceId(String deviceID);

    void saveDeviceId2(String deviceID2);

    void saveAccountDefault(String Account);

    void saveMenuList(List<MenuModel> menuModelList);

    boolean isContains(String value);

    void saveUserExist(boolean exist);

    void saveKseiDetail(boolean ksei);

    void saveInstallId(String installId);

    void saveCheckPoint(int chekpoint);

    void saveCheckPointDS(int checkPointDS);

    void saveBackGeneral(int backGeneral);

    void saveVerifikasiId(String verifId);

    void saveVerifikasiIdDS(String verifIdDs);

    void saveMidMerchant(String mIdMerchant);

    void saveAccNumberMerchant(String accNumb);

    void saveTimeStamp(int timeStamp);

    void saveExpiredTime(int timeExp);

    void saveBlastId(int blastId);

    void saveVersionApp(String version);

    void saveVersionCodeApp(int versionCode);

    void saveLoginFlag(boolean isLogin);

    void saveDialogBrifine(boolean isDialog);

    void saveChatBankingBubble(boolean isBubble);

    void saveBlockCardBubble(boolean isBubble);

    void saveProfileRevampBubble(boolean isBubble);

    void saveEmasBubble(boolean isBubble);

    void saveCheckPointRdn(String checkpoint);

    void savePengkinianBubble(boolean pengkinianFirst);

    void savebubbleAlertSaldo(boolean alertDialog);

    void saveSaldoHold(boolean isSaldoHold);

    void saveChangeDeviceFlag(boolean isChangeDevice);

    void saveFreshInstallFlag(boolean isFreshInstall);

    void saveUpdateTokenFirebase(boolean isChecked);

    void saveBubbleNewOnboarding(boolean newOnboarding);

    void saveTermCondition(String termCondition);

    void saveFirstKcic(boolean kcicFirst);

    void saveStatusAktivasi(boolean statusAKtivasi);

    void saveFaceAvailable(boolean faceAvailable);

    void saveFingerAvailable(boolean fingerAvailable);

    void saveBiometricType(String biometricType);

    void saveStatusBioChange(boolean statusBioChange);

    void saveValueKeyBiometric(String valueBiometric);

    void saveAktivasiVoiceAssistant(boolean newStatusAktivasiVoiceAssistant);

    void saveStatusUpdateBio(boolean statusUpdate);

    void saveBottomBiometric(boolean bottomBiometric);

    void saveListFastMenu(String fastMenu);

    void saveListFastMenuDefault(String fastMenuDefault);

    void saveListFastMenuDefaultToggle(String fastMenuDefault);

    void saveIndihomeFirstClick(boolean isFirst);

    //delete
    void deteleSavedMenu();

    void deleteSeqNumber();

    void deleteCheckPoint();

    void deleteCheckPointDS();

    void deleteBrizziValidate();

    void deleteBlastId();

    void deleteDataGrapichDplk();

    void savePfmBubble(boolean pfmFirst);

    void saveSiklusFirst(boolean pfmFirst);

    void saveVallasBubble(boolean vallasFirst);

    void saveFirstFastMenu(boolean fastMenuFirst);

    void saveFirstRdn(boolean rdnFrist);

    void saveFirstInvestasi(boolean investasiFirs);

    void saveFirstInvestasiNoCurency(boolean investasiFirs);

    void saveFirstInvestasiNoAset(boolean investasiFirs);

    void disablePopupNotif(boolean allowPopup);

    void saveAsuransiBubble(boolean asuransiFirst);

    void saveRencanaBubble(boolean rencanaFirst);

    void saveRencanaDetailBubble(boolean rencanaDetailFirst);


    //clear all data
    void clearAllData();

    void saveBannerImageUrl(String imageUrl);

    void saveTitleBanner(String title);

    Boolean getTFirstTimeTransferInternasional();

    void saveFirstTimeTransferInternasional(boolean TransferInternasionalFirst);

    Boolean getListPFMFirstOpen();

    void saveListPFMFirstOpen(boolean isFirst);

    Boolean getReportPFMFirstOpen();

    void saveReportPFMFirstOpen(boolean isFirst);

    void saveUserAlias(String userAlias);

    void deleteUsername();

    void deleteUserAlias();

    void deleteStatusAktivasi();

    void deleteValueKeyBiometric();

    // revamp dashboard
    void updateSaldoHide(boolean isHide);

    void updatePfmHide(boolean isHide);

    boolean isSaldoHide();

    boolean isPfmHide();

    void saveNickname(String nickname);

    String getNickname();

    void saveDBMenuRevamp(boolean isSaved);

    boolean isSavedDbRevamp();

    boolean getDepositoRevampBuble();

    void saveDepositoRevampBuble(Boolean firstDepositoRevamp);

    void saveInfoRencanaBottom(Boolean firstRencanaRevamp);

    //dashboard lifestyle
    void saveDateMenuUpdate(String updateDate);

    String getDateMenuUpdate();

    // Belanja Harian
    void savePauseTime(Long time);

    Long getPauseTime();

    //miniAPPS
    String getPayloadMiniApps();

    void saveMiniAppPayload(String payload);

    String getApplyVccDataForm();

    void setApplyVccDataForm(String applyCcDataFormModelJson);

    void setRiplayUrl (String riplayUrl);
    String getRiplayUrl ();

    String getApplyVccRequest();

    void setApplyVccRequest(String applyCcDataFormModelJson);

    //DKL Singalarity C2
    void saveDklC2(boolean isDklC2);

    boolean isDklC2();

    void saveInitC2TokenSuccess(boolean successInit);

    boolean isInitC2TokenSuccess();

    long getTimeIdle();

    void saveTimeIdle(long timeIdle);

    void saveFirstDashboardDplkRevamp(boolean isFirst);

    boolean getFirstDashboardDplkRevamp();

    void saveIsFirstAmbilFisik(boolean isFirst);

    boolean isFirstTimeAmbilFisik();

    void saveIsFirstTimeVisitTransfer(boolean isFirst);

    boolean isFirstTimeVisitTransfer();

    boolean getIsFirstTimeShowAgf(String page);

    void setIsFirstTimeShowAgf(String page, boolean isFirstTime);

    boolean getIsFirstTimeShowAft();

    void setIsFirstTimeShowAft(Boolean isFirstTimeShowAft);

    void saveListCcAlreadySelectOnQris(String json);

    String getListCcAlreadySelectOnQris();

    void saveCountCcQrisNotSof(int count);

    int getCountCcQrisNotSof();

    // Localization
    void saveLanguage(String language);

    String getLanguange();

    void saveOpenCampaign(String openUntil);

    String getOpenCampaignEvent();

    //DKL C2
    void saveTokenDklC2(OAuthTokenResult authTokenResult);

    OAuthTokenResult getTokenDklC2();

    // Dynamic App Icon
    void saveAppIconKey(String iconKey);

    String getAppIconKey();

    void saveAlertMaintenanceTokenId(String id);

    String getAlertMaintenanceTokenId();

    //SplitBill
    void saveIsBillCreated(boolean isBillCreated);

    Boolean isBillCreated();

    void saveBubbleDashboardRdnRevamp(boolean isShowBubble);

    boolean getBubbleDashboardRdnRevamp();
}