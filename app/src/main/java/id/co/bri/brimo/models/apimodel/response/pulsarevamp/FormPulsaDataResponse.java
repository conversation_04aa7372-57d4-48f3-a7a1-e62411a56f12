package id.co.bri.brimo.models.apimodel.response.pulsarevamp;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.apimodel.response.HistoryResponse;
import id.co.bri.brimo.models.apimodel.response.SavedResponse;

public class FormPulsaDataResponse {

	@SerializedName("provider")
	private List<ProviderItem> provider;

	@SerializedName("saved")
	private List<SavedResponse> saved;

	@SerializedName("history")
	private List<HistoryResponse> history;

	@SerializedName("account_list")
	@Expose
	private List<AccountModel> accountModel = null;
	@SerializedName("phone_number")
	@Expose
	private String phoneNumber;

	@SerializedName("reference_number")
	@Expose
	private String referenceNumber;



	public void setProvider(List<ProviderItem> provider){
		this.provider = provider;
	}

	public List<ProviderItem> getProvider(){
		return provider;
	}

	public void setSaved(List<SavedResponse> saved){
		this.saved = saved;
	}

	public List<SavedResponse> getSaved(){
		return saved;
	}

	public void setHistory(List<HistoryResponse> history){
		this.history = history;
	}

	public List<HistoryResponse> getHistory(){
		return history;
	}

	public List<AccountModel> getAccountModel() {
		return accountModel;
	}

	public void setAccountModel(List<AccountModel> accountModel) {
		this.accountModel = accountModel;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public String getReferenceNumber() {
		return referenceNumber;
	}

	public void setReferenceNumber(String referenceNumber) {
		this.referenceNumber = referenceNumber;
	}
}