package id.co.bri.brimo.ui.fragments;


import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.CounterPartAdapter;
import id.co.bri.brimo.adapters.SwiftAdapter;
import id.co.bri.brimo.contract.IPresenter.remittence.IJalurPresenter;
import id.co.bri.brimo.contract.IView.remittence.IJalurView;
import id.co.bri.brimo.databinding.FragmentPilihanJalurBinding;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.request.JalurRequest;
import id.co.bri.brimo.models.apimodel.response.CounterPartDatum;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.response.onExceptionWH;
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralResponse;
import id.co.bri.brimo.models.apimodel.response.JalurResponse;
import id.co.bri.brimo.models.apimodel.response.MessageResponse;
import id.co.bri.brimo.models.apimodel.response.SwiftDatum;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class FragmentJalur extends BottomSheetDialogFragment implements IJalurView, SwiftAdapter.ClickItem,
        CounterPartAdapter.ClickItem {

    private FragmentPilihanJalurBinding binding;

    private JalurRequest jalurRequest;
    private SwiftAdapter swiftAdapter;
    private CounterPartAdapter counterPartAdapter;
    private SkeletonScreen skeletonScreen;
    public SelectJalurSwift selectJalurSwift;
    public SelectJalurCounterPart selectJalurCounterPart;
    public OnError12 onError12;
    public OnSessionEnd onSessionEnd;
    public OnErrorTime onErrorTime;
    private MessageResponse messageResponse;
    public String senderCurrency, receiveCurrency, amount, amountSend, receiveCountry;
    JalurResponse jalurResponses = null;
    public String refNum;

    public FragmentJalur(SelectJalurSwift selectJalurSwift, SelectJalurCounterPart selectJalurCounterPart, OnError12 onError12, OnSessionEnd onSessionEnd, OnErrorTime onErrorTime, String sender, String receive, String amount, String amountSend, String country) {
        this.selectJalurSwift = selectJalurSwift;
        this.selectJalurCounterPart = selectJalurCounterPart;
        this.onError12 = onError12;
        this.onSessionEnd = onSessionEnd;
        this.onErrorTime = onErrorTime;
        this.senderCurrency = sender;
        this.receiveCurrency = receive;
        this.amount = amount;
        this.amountSend = amountSend;
        this.receiveCountry = country;
    }

    @Inject
    IJalurPresenter<IJalurView> presenter;

    public FragmentJalur() {
        // Default constructor
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentPilihanJalurBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        jalurRequest = new JalurRequest(senderCurrency, receiveCurrency, amount, amountSend, receiveCountry);
        injectDependency();

        skeletonScreen = Skeleton.bind(binding.llAll)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.skeleton_jalur)
                .show();

    }

    private void injectDependency() {
        ((BaseActivity) getActivity()).getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrl(GeneralHelper.getString(R.string.url_jalur_remittance));
            presenter.getDetailJalur(jalurRequest);
        }
    }

    @Override
    public void onRootedDevice() {

    }

    @Override
    public void showProgress() {

    }

    @Override
    public void hideProgress() {

    }

    @Override
    public void onSessionEnd(String message) {
        dismiss();
        onSessionEnd.onSessionEnd(message);
    }

    @Override
    public void onException(String message) {
        dismiss();
    }

    @Override
    public void onExceptionRevamp(String message) {

    }

    @Override
    public void onException06(ExceptionResponse response) {
        GeneralHelper.showDialogCustom(getActivity(), response);
    }

    @Override
    public void onException99(String message) {

    }

    @Override
    public void onExceptionFO(EmptyStateResponse response) {

    }

    @Override
    public void onExceptionLimitExceed(GeneralResponse response) {
        // do nothing
    }

    @Override
    public void onExceptionNoBackAction(String message) {
        // do nothing
    }

    @Override
    public void onExceptionStatusNotMatch() {
        // do nothing
    }

    @Override
    public void onSuccessGetJalur(JalurResponse jalurResponse) {
        refNum = jalurResponse.getRefNum();
        skeletonScreen.hide();
        binding.tvSwiftTitle.setText("Swift");
        if (!jalurResponse.getSwiftTitle().getSubtitle().equalsIgnoreCase("")) {
            binding.tvSwiftSubtitle.setText(jalurResponse.getSwiftTitle().getSubtitle());
        } else binding.tvSwiftSubtitle.setVisibility(View.GONE);

        binding.tvCounterpartTitle.setText(jalurResponse.getCounterpartTitle().getTitle());
        if (!jalurResponse.getCounterpartTitle().getSubtitle().equalsIgnoreCase("")) {
            binding.tvCounterpartSubtitle.setText(jalurResponse.getCounterpartTitle().getSubtitle());
        } else binding.tvCounterpartSubtitle.setVisibility(View.GONE);

        binding.rvSwift.setHasFixedSize(true);
        binding.rvSwift.setLayoutManager(new LinearLayoutManager(getActivity()));
        swiftAdapter = new SwiftAdapter(getContext(), jalurResponse.getSwiftData(), this);
        binding.rvSwift.setAdapter(swiftAdapter);

        if (jalurResponse.getCounterpartData().get(0).getCounterpartValue() == null) {
            binding.rlUnavailable.setVisibility(View.VISIBLE);
        }
        binding.rvJalur.setHasFixedSize(true);
        binding.rvJalur.setLayoutManager(new LinearLayoutManager(getActivity()));
        counterPartAdapter = new CounterPartAdapter(getContext(), jalurResponse.getCounterpartData(), this);
        binding.rvJalur.setAdapter(counterPartAdapter);
    }

    @Override
    public void onException12(String message) {
        dismiss();
        onError12.onException12Error(message);
    }

    @Override
    public void onFailedWorkHour(onExceptionWH onExceptionWH) {
        dismiss();
        onErrorTime.onErrorTime(onExceptionWH);
    }

    @Override
    public void onClickItem(CounterPartDatum counterPartDatum) {
        dismiss();
        selectJalurCounterPart.onClick(counterPartDatum, refNum);
    }

    @Override
    public void onClickItem(SwiftDatum swiftDatum) {
        dismiss();
        selectJalurSwift.onClick(swiftDatum, refNum);
    }

    public interface SelectJalurSwift {
        void onClick(SwiftDatum swiftDatum, String refnum);
    }

    public interface OnError12 {
        void onException12Error(String message);
    }

    public interface OnSessionEnd {
        void onSessionEnd(String message);
    }

    public interface OnErrorTime {
        void onErrorTime(onExceptionWH onExceptionWH);
    }

    public interface SelectJalurCounterPart {
        void onClick(CounterPartDatum counterPartDatum, String refnum);
    }
}