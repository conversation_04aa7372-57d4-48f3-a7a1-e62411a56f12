package id.co.bri.brimo.contract.IView.detailrekening;

import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.BiFastAccountResponse;
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.ForceUpdateResponse;
import id.co.bri.brimo.models.apimodel.response.notificationsetting.NotificationSettingResponse;
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.DetailKelolaKartuRes;
import id.co.bri.brimo.models.apimodel.response.EnableKartuResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralOtpResponse;
import id.co.bri.brimo.models.apimodel.response.QuestionResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;

public interface IInfoRekeningView extends IMvpView {
    void onResp00(EnableKartuResponse enableKartuResponse);

    void onException01(RestResponse restResponse);

    void onException12(String message, String type);

    void onSuccessGetChangeDefault(String message);

    void onSuccessBiFast(BiFastAccountResponse biFastAccountResponse);

    void onGetSuccessDetail(DetailKelolaKartuRes detailKelolaKartuRes);

    void onErrorBiFast(String errorMessage);

    void onSuccessGetFinansial(GeneralOtpResponse generalOtpResponse);

    void onSuccessNonaktifFinansial();

    void onSuccessInfoSaldoHold(QuestionResponse questionResponse);

    void onUpdateVersion(ForceUpdateResponse forceUpdate);

    void onSuccessGetListNotification(NotificationSettingResponse response);
}