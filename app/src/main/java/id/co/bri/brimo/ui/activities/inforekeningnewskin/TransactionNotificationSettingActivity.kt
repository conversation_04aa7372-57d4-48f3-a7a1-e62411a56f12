package id.co.bri.brimo.ui.activities.inforekeningnewskin

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.google.android.material.textfield.TextInputLayout
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.notificationsetting.INotificationSettingDetailPresenter
import id.co.bri.brimo.contract.IView.notificationsetting.INotificationSettingDetailView
import id.co.bri.brimo.databinding.ActivityTransactionNotificationSettingBinding
import id.co.bri.brimo.domain.SnackBarType
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.extension.showToast
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.domain.helpers.calendar.makeGone
import id.co.bri.brimo.domain.helpers.calendar.makeVisible
import id.co.bri.brimo.domain.helpers.textwatcher.AmountFormatWatcher
import id.co.bri.brimo.models.apimodel.request.notificationsetting.ChangeAmountNotificationRequest
import id.co.bri.brimo.models.apimodel.request.notificationsetting.FirstRegistrationNotificationRequest
import id.co.bri.brimo.models.apimodel.request.notificationsetting.GetDetailNotificationSettingRequest
import id.co.bri.brimo.models.apimodel.response.notificationsetting.NotificationDetail
import id.co.bri.brimo.models.apimodel.response.notificationsetting.NotificationOptions
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.newskinonboarding.OnboardingInputNumberForgetPassActivity
import id.co.bri.brimo.ui.fragments.PinFragmentNewSkin
import javax.inject.Inject

class TransactionNotificationSettingActivity : NewSkinBaseActivity(), PinFragmentNewSkin.SendPin, AmountFormatWatcher.onAmountChange, INotificationSettingDetailView {

    @Inject
    lateinit var presenter: INotificationSettingDetailPresenter<INotificationSettingDetailView>

    private lateinit var binding: ActivityTransactionNotificationSettingBinding
    private val gson = Gson()
    private lateinit var notification: NotificationOptions
    private lateinit var notificationDetail: NotificationDetail
    private var currentAction: ActionType? = null
    private var amountPlainText: Long = 0
    private var successText: String = ""
    private var isConfirmationDialogVisible = false
    private var isFirstRegistration: Boolean = false
    private var accountNumber: String? = null
    private var alias: String? = null
    private var name: String? = null
    private var cardImage: String? = null

    private enum class ActionType { FIRST_REGISTRATION, CHANGE_AMOUNT }

    var pinFragment: PinFragmentNewSkin? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTransactionNotificationSettingBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupInitialData()
        setupToolbar()
        injectDependency()
        setupView()

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                handleBackPressed()
            }
        })
    }

    private fun setupToolbar()  {

        val title = getString(R.string.txt_setting_notification_wa) + " " + notification.label

        GeneralHelperNewSkin.setToolbar(
            this,
            binding.toolbar.toolbar,
            title
        )
    }

    private fun setupInitialData() {
        notification = gson.fromJson(
            intent.getStringExtra(Constant.TAG_CONTENT),
            NotificationOptions::class.java
        )
        accountNumber = intent.getStringExtra(Constant.ACCOUNT_NUM)
        alias = intent.getStringExtra(Constant.USER_ALIAS)
        name = intent.getStringExtra(Constant.NAME)
        cardImage = intent.getStringExtra(Constant.IMAGE_LIST_TYPE)

        if (alias.equals("") || alias.equals("(Belum ada Nama Alias)")) {
            binding.viewSourceAccount.tvAccountName.text = name
        } else {
            binding.viewSourceAccount.tvAccountName.text = alias
        }

        binding.viewSourceAccount.tvAccountNumber.text = accountNumber

        val image = if (cardImage.isNullOrEmpty()) {
            R.drawable.dummy_britama
        } else {
            cardImage
        }

        Glide.with(this)
            .load(image)
            .into(binding.viewSourceAccount.ivCardLogo)

        notificationDetail = notification.details
        isFirstRegistration = !notificationDetail.status && notificationDetail.currentAmount == "0"
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
    }

    private fun setupView() {

        binding.apply {
            // dynamic value
//            txtNotificationDesc.text = notificationDetail.notificationInfoText
//            btnActivate.text = notificationDetail.saveButtonLabel
            etNominalMinimum.setText(GeneralHelper.formatNominal(notificationDetail.currentAmount))
            tvNominalDesc.text = notificationDetail.amountMinimumString
        }

        validateView()
        setClickAction()
        setupAmountView()
        validationButtonSubmit(amountPlainText)
    }

    @SuppressLint("SetTextI18n")
    private fun setupAmountView() {
        if (!notificationDetail.status && notificationDetail.currentAmount != "0") {
//            setAmountViewDisabledState()
            setButtonDisabledState()
        } else {
            binding.etNominalMinimum.isEnabled = true
            binding.inputNominal.hintTextColor = ContextCompat.getColorStateList(this, R.color.black_ns_600)
            binding.etNominalMinimum.setTextColor(getColor(R.color.black_ns_main))
            binding.imgClear.makeVisible()
        }
        for (data in notificationDetail.dataView) {
            if (data.value.contains("Rp") && data.style == "nominal") {
                binding.tvFeeDesc.text = "${getString(R.string.txt_setting_notification_wa_fee)} ${data.value}"
            }
        }
    }

    private fun setAmountViewDisabledState() {
        binding.inputNominal.hintTextColor = ContextCompat.getColorStateList(this, R.color.disable_default_ns)
        binding.etNominalMinimum.setTextColor(getColor(R.color.disable_default_ns))
        binding.etNominalMinimum.isEnabled = false
        binding.inputNominal.endIconMode = TextInputLayout.END_ICON_CLEAR_TEXT
    }

    private fun setButtonDisabledState() {
        binding.btnActivate.isEnabled = false
    }

    private fun setButtonEnabledState() {
        binding.btnActivate.isEnabled = true

        binding.btnActivate.setOnClickListener {
            currentAction = if (isFirstRegistration) {
                ActionType.FIRST_REGISTRATION
            } else {
                ActionType.CHANGE_AMOUNT
            }
            openPin()
        }
    }

    private fun openPin() {
        pinFragment = PinFragmentNewSkin(this, this)
        pinFragment?.show()
    }

    private fun setClickAction() {

        binding.imgClear.setOnClickListener {
            binding.etNominalMinimum.text?.clear()
            binding.etNominalMinimum.requestFocus()
            showNominalInvalidState()
        }

    }

    private fun validateView() {

        binding.etNominalMinimum.apply {
            addTextChangedListener(AmountFormatWatcher(this, this@TransactionNotificationSettingActivity, true))
        }

    }

    override fun onAmountChange(amount: String?) {
        amount?.let {
            val isInvalid = it.toIntOrNull()?.let { value -> value < 1 } != false
            if (!isInvalid) {
                showNominalValidState()
            } else {
                showNominalInvalidState()
            }
            val amountString = amount.replace("\\.".toRegex(), "")
            amountPlainText = amountString.toLongOrNull() ?: 0
            validationButtonSubmit(amountPlainText)
        }
    }

    private fun validationButtonSubmit(nominal: Long) {
        if (notificationDetail.status && (nominal == notificationDetail.currentAmount.toLong())) {
            setButtonDisabledState()
        } else if (!notificationDetail.status && nominal < notificationDetail.amountMinimum.toLong()) {
            setButtonDisabledState()
        } else if (!notificationDetail.status && nominal >= notificationDetail.amountMinimum.toLong()) {
            setButtonEnabledState()
        } else if (!notificationDetail.status && nominal >= notificationDetail.amountMinimum.toLong()) {
            setButtonDisabledState()
        } else if (notificationDetail.status && (nominal >= notificationDetail.amountMinimum.toLong()) && (nominal != notificationDetail.currentAmount.toLong())) {
            setButtonEnabledState()
        } else {
            setButtonDisabledState()
        }
    }

    private fun showNominalValidState() {
        binding.inputNominal.isSelected = false
        binding.tvNominalDesc.text = notificationDetail.amountMinimumString
        binding.tvNominalDesc.setTextColor(ContextCompat.getColor(this, R.color.black_ns_600))
        binding.btnActivate.isEnabled = true
        binding.imgClear.makeVisible()
    }

    private fun showNominalInvalidState() {
        binding.inputNominal.isSelected = true
        binding.tvNominalDesc.text = getString(R.string.txt_minimal_rp1_desc)
        binding.tvNominalDesc.setTextColor(ContextCompat.getColor(this, R.color.red_ns_main))
        binding.btnActivate.isEnabled = false
        binding.imgClear.makeGone()
    }

    private fun firstRegistration(pin: String?) {
        presenter.setUrlFirstRegistration(getString(R.string.url_v1_first_registration_notif))
        pin?.let {
            presenter.firstRegistration(
                FirstRegistrationNotificationRequest(
                    notificationDetail.accountNumber,
                    notification.type,
                    true,
                    amountPlainText.toString(),
                    it
                )
            )
        }
    }

    private fun changeAmount(pin: String?) {
        presenter.apply {
            setUrlChangeAmount(getString(R.string.url_v1_update_data_notif))
            pin?.let {
                changeAmount(
                    ChangeAmountNotificationRequest(
                        accountNumber = notificationDetail.accountNumber,
                        type = notification.type,
                        amount = amountPlainText.toString(),
                        pin = it
                    )
                )
            }
        }
    }

    private fun getDetails() {
        presenter.apply {
            setUrlGetDetailNotification(getString(R.string.url_v1_get_details_notif))
            getDetailNotificationSetting(
                GetDetailNotificationSettingRequest(
                    accountNumber = notificationDetail.accountNumber,
                    type = notification.type
                )
            )
        }
    }

    override fun setAmountListener() {
    }

    override fun onSendPinComplete(pin: String?) {
        when (currentAction) {
            ActionType.FIRST_REGISTRATION -> firstRegistration(pin)
            ActionType.CHANGE_AMOUNT -> changeAmount(pin)
            else -> Unit
        }
    }

    override fun onLupaPin() {
        OnboardingInputNumberForgetPassActivity.launchIntent(this, Constant.REQ_UBAH_PIN)
    }

    override fun onSuccessGetDetailNotification(response: NotificationDetail) {
        isConfirmationDialogVisible = true
        notification.details = response
        notificationDetail = response
        handleSuccess()
    }

    override fun onSuccessChangeStatus(desc: String) {

    }

    override fun onException(message: String) {
        if (isFirstRegistration) {
            GeneralHelperNewSkin.showCustomSnackBar(binding.content, getString(R.string.txt_success_active_failed_notification), SnackBarType.ERROR)
        } else {
            GeneralHelperNewSkin.showCustomSnackBar(binding.content, getString(R.string.txt_failed_change_nominal_notification), SnackBarType.ERROR)
        }
    }

    override fun onSuccessFirstRegistration(desc: String) {
        pinFragment?.dismiss()
        isFirstRegistration = false
        successText = desc
        handleSuccess()
//        getDetails()
    }

    override fun onSuccessChangeAmount(desc: String) {
        pinFragment?.dismiss()
        successText = desc
        handleSuccess()
//        getDetails()
    }

    private fun handleBackPressed() {
        val intent = Intent()
        setResult(Constant.RESULT_BACK, intent)
        finish()
    }

    private fun handleSuccess() {
        val intent = Intent()
        val message: String = if (isFirstRegistration) {
            getString(R.string.txt_success_active_notification)
        } else {
            getString(R.string.txt_success_change_nominal_notification)
        }
        val confirmationJson = gson.toJson(notification)
        intent.putExtra(Constant.TAG_CONTENT, confirmationJson)
        intent.putExtra(Constant.TAG_MESSAGE, message)
        intent.putExtra(Constant.ACCOUNT_NUM, notificationDetail.accountNumber)
        intent.putExtra(Constant.TYPE_FLAG, notification.type)
        setResult(RESULT_OK, intent)
        finish()
    }

    override fun onPause() {
        super.onPause()
        GeneralHelperNewSkin.hideKeyboardIfVisible(this)
    }

    companion object {

        @JvmStatic
        fun launchIntent(
            caller: Activity,
        ) {
            val intent = Intent(caller, TransactionNotificationSettingActivity::class.java)
            caller.startActivity(intent)
        }
    }

    override fun onSessionEnd(message: String?) {
        GeneralHelperNewSkin.showErrorBlokir(this)
    }

    override fun onExceptionRevamp(message: String?) {
        pinFragment?.onWrongPin(message)
    }
}