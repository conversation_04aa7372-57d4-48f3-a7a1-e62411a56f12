package id.co.bri.brimo.data.repository.menudashfav;

import java.util.List;

import id.co.bri.brimo.data.repository.menudashfav.local.MenuDashFavLocalSource;
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuDashFav;
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuFeatureModel;
import id.co.bri.brimo.models.daomodel.DashboardMenu.SubFeatureModel;
import io.reactivex.Completable;
import io.reactivex.Maybe;

public class MenuDashFavRepository implements MenuDashFavSource {

    MenuDashFavLocalSource menuDashFavLocalSource;

    public MenuDashFavRepository(MenuDashFavLocalSource menuDashFavLocalSource) {
        this.menuDashFavLocalSource = menuDashFavLocalSource;
    }

    @Override
    public Completable insertMenuDashAll(List<MenuDashFav> dashMenuFav) {
        return menuDashFavLocalSource.insertMenuDashAll(dashMenuFav);
    }

    @Override
    public Maybe<List<SubFeatureModel>> getDashMenuFav() {
        return menuDashFavLocalSource.getDashMenuFav();
    }

    @Override
    public Maybe<List<MenuFeatureModel>> getMenuFeatureFavList() {
        return menuDashFavLocalSource.getMenuFeatureFavList();
    }

    @Override
    public Completable deleteDashMenuFav() {
        return menuDashFavLocalSource.deleteDashMenuFav();
    }
}