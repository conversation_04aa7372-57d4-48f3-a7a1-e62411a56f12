package id.co.bri.brimo.contract.IView.pengelolaankartu;

import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.BindingNewAccountResponse;
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.DetailKelolaKartuRes;
import id.co.bri.brimo.models.apimodel.response.EmptyMutationResponse;
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.InitChangePinResponse;

public interface IDetailKelolaKartuView extends IMvpView {
    void onSuccessGetStatusCard(String desc);

    void onSuccessGetDetailCard(DetailKelolaKartuRes response, String snackbarResponse);

    void onSuccessEnableDisableTransaction(EmptyMutationResponse response);

    void onSuccessAccountBinding(BindingNewAccountResponse data);

    void onSuccessGetChangePinRefNum(String refNum, boolean isBypass);

    void onSuccessOrFailedBlockCard(String code, String message);

    void onErrorStatusCard();

    void onErrorEnableDisable();




}