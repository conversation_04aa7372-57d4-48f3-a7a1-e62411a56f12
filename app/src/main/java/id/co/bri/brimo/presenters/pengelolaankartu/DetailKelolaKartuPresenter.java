package id.co.bri.brimo.presenters.pengelolaankartu;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.pengelolaankartu.IDetailKelolaKartuPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.pengelolaankartu.IDetailKelolaKartuView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.BlokirRequest;
import id.co.bri.brimo.models.apimodel.request.blockcard.BlockCardRequest;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.AccountBindingReq;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.DetailKelolaKartuReq;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.EnableDisableTransactionRequest;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.InitChangePinRequest;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.InitChangePinRequest;
import id.co.bri.brimo.models.apimodel.response.EmptyMutationResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.BindingNewAccountResponse;
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.DetailKelolaKartuRes;
import id.co.bri.brimo.models.apimodel.response.EmptyMutationResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.InitChangePinResponse;
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.InitChangePinResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class DetailKelolaKartuPresenter<V extends IMvpView & IDetailKelolaKartuView> extends MvpPresenter<V> implements IDetailKelolaKartuPresenter<V> {

    protected String urlStatusCard, urlEnableDisableTransaction, urlGetDetailCard, urlAccountBinding, urlBlockCard;

    public DetailKelolaKartuPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                      BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource,
                                      TransaksiPfmSource transaksiPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlStatusCard(String urlStatusCard) {
        this.urlStatusCard = urlStatusCard;
    }

    @Override
    public void setUrlBlockCard(String urlBlockCard) {
        this.urlBlockCard = urlBlockCard;
    }

    @Override
    public void setBlokirKartu(BlokirRequest blokirRequest) {
        if (urlStatusCard != null || isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlStatusCard, blokirRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    getView().onSuccessGetStatusCard(response.getDesc());
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onErrorStatusCard();
                                }
                            })
            );
        }
    }

    @Override
    public void setUrlTransactionStatus(String url) {
        this.urlEnableDisableTransaction = url;
    }

    @Override
    public void enableDisableTransactionStatus(EnableDisableTransactionRequest request) {
        if (urlEnableDisableTransaction != null || isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlEnableDisableTransaction, request, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    EmptyMutationResponse mResponse = response.getData(EmptyMutationResponse.class);
                                    getView().onSuccessEnableDisableTransaction(mResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onErrorEnableDisable();
//                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void setUrlDetailCard(String url) {
        this.urlGetDetailCard = url;
    }

    @Override
    public void getDataDetailCard(DetailKelolaKartuReq request, String snackbarResponse) {
        if (urlGetDetailCard != null && isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(urlGetDetailCard, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            DetailKelolaKartuRes detailResponse = response.getData(DetailKelolaKartuRes.class);
                            getView().onSuccessGetDetailCard(detailResponse, snackbarResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void setUrlAccountBinding(String urlAccountBinding) {
        this.urlAccountBinding = urlAccountBinding;
    }

    @Override
    public void setAccountBinding(AccountBindingReq accountBindingReq) {
        if (urlAccountBinding != null || isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlAccountBinding, accountBindingReq, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    BindingNewAccountResponse data = response.getData(BindingNewAccountResponse.class);
                                    getView().onSuccessAccountBinding(data);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    public void getChangePinRefNum(String url, String cardNumber) {
        if (isViewAttached()) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            InitChangePinRequest request = new InitChangePinRequest(cardNumber);
            getCompositeDisposable().add(
                    getApiSource().getData(url, request, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().io())
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    InitChangePinResponse initChangePinResponse = restResponse.getData(InitChangePinResponse.class);
                                    String code = response.getCode();
                                    if (code.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        getView().onSuccessGetChangePinRefNum(
                                                initChangePinResponse.getRefNumber(),
                                                initChangePinResponse.isBypassOldPin()
                                        );
                                    } else {
                                        getView().onException(response.getDesc());
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void setBlockCard(BlockCardRequest request) {
        if (urlBlockCard != null || isViewAttached()) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlBlockCard, request, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    getView().onSuccessOrFailedBlockCard(response.getCode(), response.getDesc());
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(Constant.RE12)) {
                                        getView().onSuccessOrFailedBlockCard(restResponse.getCode(), restResponse.getDesc());
                                    } else {
                                        getView().onException(restResponse.getDesc());
                                    }
                                }
                            })
            );
        }
    }
}