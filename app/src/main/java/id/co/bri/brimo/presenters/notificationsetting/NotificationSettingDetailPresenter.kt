package id.co.bri.brimo.presenters.notificationsetting

import id.co.bri.brimo.contract.IPresenter.notificationsetting.INotificationSettingDetailPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.notificationsetting.INotificationSettingDetailView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.notificationsetting.ChangeAmountNotificationRequest
import id.co.bri.brimo.models.apimodel.request.notificationsetting.ChangeStatusNotificationRequest
import id.co.bri.brimo.models.apimodel.request.notificationsetting.FirstRegistrationNotificationRequest
import id.co.bri.brimo.models.apimodel.request.notificationsetting.GetDetailNotificationSettingRequest
import id.co.bri.brimo.models.apimodel.request.virtualdebitcard.ConfirmationCreateVDCRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.notificationsetting.NotificationDetail
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class NotificationSettingDetailPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), INotificationSettingDetailPresenter<V> where V : IMvpView, V : INotificationSettingDetailView {

    private lateinit var urlGetDetailNotification: String
    private lateinit var urlChangeStatusNotification: String
    private lateinit var urlFirstRegistrationNotification: String
    private lateinit var urlChangeAmountNotification: String

    private lateinit var request: ConfirmationCreateVDCRequest

    override fun setUrlGetDetailNotification(url: String) {
        this.urlGetDetailNotification = url
    }

    override fun setUrlChangeStatus(url: String) {
        this.urlChangeStatusNotification = url
    }

    override fun setUrlFirstRegistration(url: String) {
        this.urlFirstRegistrationNotification = url
    }

    override fun getDetailNotificationSetting(request: GetDetailNotificationSettingRequest) {
        if (urlGetDetailNotification.isEmpty() && !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlGetDetailNotification, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val notificationDetail =
                                response.getData(NotificationDetail::class.java)
                            getView().onSuccessGetDetailNotification(notificationDetail)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun changeStatus(request: ChangeStatusNotificationRequest) {
        if (urlChangeStatusNotification.isEmpty() && !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlChangeStatusNotification, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            getView().onSuccessChangeStatus(response.desc)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                getView().onExceptionRevamp(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }
                    })
            )
    }

    override fun firstRegistration(request: FirstRegistrationNotificationRequest) {
        if (urlFirstRegistrationNotification.isEmpty() && !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlFirstRegistrationNotification, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            getView().onSuccessFirstRegistration(response.desc)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                getView().onExceptionRevamp(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }
                    })
            )
    }

    override fun setUrlChangeAmount(url: String) {
        this.urlChangeAmountNotification = url
    }

    override fun changeAmount(request: ChangeAmountNotificationRequest) {
        if (urlChangeAmountNotification.isEmpty() && !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlChangeAmountNotification, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            getView().onSuccessChangeAmount(response.desc)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                getView().onExceptionRevamp(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }
                    })
            )
    }
}