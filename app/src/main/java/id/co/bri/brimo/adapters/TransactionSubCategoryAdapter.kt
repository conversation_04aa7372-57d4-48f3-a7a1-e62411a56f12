package id.co.bri.brimo.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.databinding.ItemCategoryTransactionBinding
import id.co.bri.brimo.databinding.ItemSourceOfAccountBinding
import id.co.bri.brimo.databinding.ItemSubCategoryTransactionBinding
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.InboxStatusModel
import id.co.bri.brimo.models.apimodel.response.ActivityGroup

class TransactionSubCategoryAdapter(
    private val category: ArrayList<ActivityGroup.ActivityType>,
    private val onItemClick: (ActivityGroup.ActivityType) -> Unit
) : RecyclerView.Adapter<TransactionSubCategoryAdapter.TransactionViewHolder>(){

    inner class TransactionViewHolder(
        private val binding: ItemSubCategoryTransactionBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(category: ActivityGroup.ActivityType) {
            binding.cbSubfilter.text = category.name

            binding.root.setOnClickListener {
                onItemClick(category)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TransactionViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val binding = ItemSubCategoryTransactionBinding.inflate(inflater, parent, false)
        return TransactionViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TransactionViewHolder, position: Int) {
        holder.bind(category[position])
    }

    override fun getItemCount(): Int = category.size

    fun setData(newList: List<ActivityGroup.ActivityType>) {
        category.clear()
        category.addAll(newList)
        notifyDataSetChanged()
    }

}