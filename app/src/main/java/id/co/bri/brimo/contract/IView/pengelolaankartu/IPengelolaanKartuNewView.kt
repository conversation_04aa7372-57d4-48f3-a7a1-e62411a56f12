package id.co.bri.brimo.contract.IView.pengelolaankartu

import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.ListPengelolaanKartuRes
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.DetailKelolaKartuRes
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.activationdebit.ProductBriefResponse


interface IPengelolaanKartuNewView : IMvpView {
    fun onSuccessCardList(pengelolaanKartuRes: ListPengelolaanKartuRes)
    fun onSuccessCardDetail(detailKelolaKartuRes: DetailKelolaKartuRes)
    fun onSuccessNoCard()
    fun onException12(desc: String)
    fun savePengelolaanKartu(pengelolaanKartuRes: ListPengelolaanKartuRes)
    fun onSuccessProductBrief(productBriefResponse: ProductBriefResponse)
    fun onMaxRetriesReached(message: String)
}