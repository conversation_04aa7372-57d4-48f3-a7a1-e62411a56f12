package id.co.bri.brimo.ui.fragments.bottomsheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentBottomSheetBackConfirmationBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.ui.widget.OnCloseClickListener

class BackConfirmationBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentBottomSheetBackConfirmationBinding
    private var onConfirmListener: (() -> Unit)? = null
    private var onCancelListener: (() -> Unit)? = null

    companion object {
        fun newInstance(
            onConfirm: () -> Unit,
            onCancel: () -> Unit
        ): BackConfirmationBottomSheetFragment {
            return BackConfirmationBottomSheetFragment().apply {
                onConfirmListener = onConfirm
                onCancelListener = onCancel
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentBottomSheetBackConfirmationBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.bbslRoot.setOnCloseClickListener(object : OnCloseClickListener {
            override fun onCloseClick() {
                dismiss()
            }
        })

        // Set up bottom sheet behavior
        val bottomSheet = dialog?.findViewById(R.id.design_bottom_sheet) as? FrameLayout
        bottomSheet?.let {
            val behavior = BottomSheetBehavior.from(it)
            behavior.state = BottomSheetBehavior.STATE_EXPANDED
            behavior.isDraggable = true
        }

        // Set up button click listeners
        setupButtonListeners()

        // Configure dialog properties
        dialog?.setCancelable(true)
        dialog?.setCanceledOnTouchOutside(true)
    }

    private fun setupButtonListeners() {
        // Cancel button
        binding.btnCancel.setOnClickListener {
            onCancelListener?.invoke()
            dismiss()
        }

        // Submit button (Confirm)
        binding.btnSubmit.setOnClickListener {
            onConfirmListener?.invoke()
            dismiss()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        onConfirmListener = null
        onCancelListener = null
    }
}
