package id.co.bri.brimo.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class ParameterModel {
    @SerializedName("stringLabelTujuan")
    @Expose
    private String stringLabelTujuan;
    @SerializedName("stringLabelNominal")
    @Expose
    private String stringLabelNominal;
    @SerializedName("stringButtonSubmit")
    @Expose
    private String stringButtonSubmit;
    @SerializedName("isSmartTransfer")
    @Expose
    private Boolean isSmartTransfer;
    @SerializedName("accOffUs")
    @Expose
    private String accOffUs;
    @SerializedName("accOffUsCode")
    @Expose
    private String accOffUsCode;

    private String stringLabelMinimum;

    private int defaultIcon;

    public String getStringLabelTujuan() {
        return stringLabelTujuan;
    }

    public void setStringLabelTujuan(String stringLabelTujuan) {
        this.stringLabelTujuan = stringLabelTujuan;
    }

    public String getStringLabelNominal() {
        return stringLabelNominal;
    }

    public void setStringLabelNominal(String stringLabelNominal) {
        this.stringLabelNominal = stringLabelNominal;
    }

    public String getStringButtonSubmit() {
        return stringButtonSubmit;
    }

    public void setStringButtonSubmit(String stringButtonSubmit) {
        this.stringButtonSubmit = stringButtonSubmit;
    }

    public String getStringLabelMinimum() {
        return stringLabelMinimum;
    }

    public void setStringLabelMinimum(String stringLabelMinimum) {
        this.stringLabelMinimum = stringLabelMinimum;
    }

    public int getDefaultIcon() {
        return defaultIcon;
    }

    public void setDefaultIcon(int defaultIcon) {
        this.defaultIcon = defaultIcon;
    }

    public Boolean getSmartTransfer() {
        return isSmartTransfer;
    }

    public void setSmartTransfer(Boolean smartTransfer) {
        isSmartTransfer = smartTransfer;
    }

    public String getAccOffUs() {
        return accOffUs;
    }

    public void setAccOffUs(String accOffUs) {
        this.accOffUs = accOffUs;
    }

    public String getAccOffUsCode() {
        return accOffUsCode;
    }

    public void setAccOffUsCode(String accOffUsCode) {
        this.accOffUsCode = accOffUsCode;
    }
}
