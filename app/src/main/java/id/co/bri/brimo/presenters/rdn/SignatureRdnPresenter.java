package id.co.bri.brimo.presenters.rdn;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.rdn.ISignatureRdnPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.rdn.ISignatureRdnView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.rdn.RdnNewCifRequest;
import id.co.bri.brimo.models.apimodel.request.rdn.RdnSignatureRequest;
import id.co.bri.brimo.models.apimodel.response.PendingResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class SignatureRdnPresenter <V extends IMvpView & ISignatureRdnView> extends MvpPresenter<V> implements ISignatureRdnPresenter<V> {

    private String url;
    private String urlNewCif;
    private String urlNewCifNew;

    public SignatureRdnPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    public void setUrlNewCif(String url){
        this.urlNewCif = url;
    }

    @Override
    public void setUrlNewCifNew(String url) {
     this.urlNewCifNew = url;
    }

    @Override
    public void onSendSignature(RdnSignatureRequest request) {
        if (isViewAttached()){
            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(url,request,seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq){

                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    RdnNewCifRequest rdnNewCifRequest = new RdnNewCifRequest(request.getPin(),request.getCheckpointId());
                                    onSendNewCif(rdnNewCifRequest);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));

        }
    }

    @Override
    public void onSendNewCif(RdnNewCifRequest request) {
        if (isViewAttached()){
            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlNewCifNew,request,seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq){

                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    PendingResponse pendingResponse = response.getData(PendingResponse.class);
                                    getView().onSuccessSendSignature(pendingResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())){
                                        getView().onSessionEnd(restResponse.getDesc());
                                    }else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                                        getView().onException93(restResponse.getDesc());
                                    }
                                    else{
                                        getView().onException(restResponse.getDesc());
                                    }
                                }
                            }));
        }
    }
}