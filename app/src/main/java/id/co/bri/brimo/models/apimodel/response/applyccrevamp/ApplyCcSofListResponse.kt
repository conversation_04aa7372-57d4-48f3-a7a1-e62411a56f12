package id.co.bri.brimo.models.apimodel.response.applyccrevamp

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.models.apimodel.response.DataView
import id.co.bri.brimo.models.apimodel.response.cc.DetailCcSofResponse
import kotlinx.parcelize.Parcelize

@Parcelize
data class ApplyCcSofListResponse(
    @SerializedName("account") var account: MutableList<Account>? = null,
    @SerializedName("pending_apply") val pendingApply: ApplyCcSubmitDataResponse.PendingApply? = null,
): Parcelable {
    @Parcelize
    data class Account(
        @SerializedName("name") val name: String? = null,
        @SerializedName("card_number_string") val cardNumberString: String? = null,
        @SerializedName("card_number_token") val cardNumberToken: String? = null,
        @SerializedName("currency") val currency: String? = null,
        @SerializedName("image_name") val imageName: String? = null,
        @SerializedName("image_path") val imagePath: String? = null,
        @SerializedName("image_detail_path") val imageDetailPath: String? = null,
        @SerializedName("card_block") val cardBlock: String? = null,
        @SerializedName("financial_status") val finansialStatus: Int? = null,
        @SerializedName("detail_type") val detailType: String? = null,
        @SerializedName("detail") val details: List<DataView> = emptyList(),
        @SerializedName("have_problem") val isHaveProblem: Boolean? = null,
        @SerializedName("problem_data") val problemData: DetailCcSofResponse.ProblemData? = null,
        @SerializedName("activated") val isActivated: Boolean? = null,
        @SerializedName("activable") val activable: Boolean = false,
        @SerializedName("activation_data") val activationData: DetailCcSofResponse.ActivationData? = null,
        @SerializedName("exp_date") val expiredDate: String? = null,
        @SerializedName("limit") val limit: String? = null,
        @SerializedName("limit_string") var limitString: String? = null,
        @SerializedName("usage") val usage: String? = null,
        @SerializedName("usage_string") val usageString: String? = null,
        @SerializedName("balance") var balance: String? = null,
        @SerializedName("balance_string") val balanceString: String? = null,
        @SerializedName("is_kkp") var isKkp: Boolean? = null,
        @SerializedName("is_error") val isError: Boolean? = null,
        @SerializedName("reference_number") val referenceNumber: String? = null,
        @SerializedName("enable_change_pin") val isEnableChangePin: Boolean? = null,
        var detailCcResponse: DetailCcSofResponse? = null,
    ): Parcelable, ApplyVccModel {
        override val id: Int
            get() = 1
    }
}

interface ApplyVccModel {
    val id: Int
    override fun equals(other: Any?) : Boolean
}

fun ApplyCcSofListResponse.Account.mapToDetailCC(): DetailCcSofResponse {
    return DetailCcSofResponse().apply {
        activable = <EMAIL>
        activated = isActivated
        detail = <EMAIL>
        haveProblem = isHaveProblem
        problemData = <EMAIL>
        activationData = <EMAIL>
        cardToken = <EMAIL>
        cardNumber = <EMAIL>
        expDate = <EMAIL>
        imageDetailPath = <EMAIL>
        imagePath = <EMAIL>
        imageName = <EMAIL>
        name = <EMAIL>
        currency = <EMAIL>
        balance = <EMAIL>?.toDoubleOrNull() ?: 0.0
        balanceString = <EMAIL>
        limit = <EMAIL>?.toDoubleOrNull() ?: 0.0
        limitString = <EMAIL>
        usage = <EMAIL>?.toDoubleOrNull() ?: 0.0
        usageString = <EMAIL>
        referenceNumber = <EMAIL>
        isChangePinEnable = <EMAIL>
        kkp = <EMAIL>
    }
}