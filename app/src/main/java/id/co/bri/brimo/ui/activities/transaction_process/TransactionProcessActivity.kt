package id.co.bri.brimo.ui.activities.transaction_process

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.activity.OnBackPressedCallback
import id.co.bri.brimo.databinding.ActivityTransactionProcessBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.reskin.ReceiptType
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.receipt.reskin.ReceiptReskinActivity

class TransactionProcessActivity: NewSkinBaseActivity() {
    private var _binding: ActivityTransactionProcessBinding? = null
    protected val binding get() = _binding!!

    companion object {
        private var receiptRevampResponse: ReceiptRevampResponse?= null

        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean, data: ReceiptRevampResponse) {
            isFromFastMenu = fromFastMenu
            receiptRevampResponse = data
            caller.apply {
                startActivityForResult(Intent(
                    this,
                    TransactionProcessActivity::class.java
                ), Constant.REQ_PAYMENT)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityTransactionProcessBinding.inflate(layoutInflater)

        setContentView(binding.root)
        onDelayHandler()

        injectDependency()

        onBindView()

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // Tidak ngapa-ngapain
            }
        })
    }

    private fun onBindView() {
    }

    private fun injectDependency() {
    }

    private fun onDelayHandler() {
        Handler(Looper.getMainLooper()).postDelayed({
            ReceiptReskinActivity.launchIntent(
                this, isFromFastMenu,
                receiptRevampResponse!!
            )
            finish()
        }, 3000)
    }

    override fun onBackPressed() {

    }
}