package id.co.bri.brimo.presenters.travel;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.travel.IHistoryTravelPendingPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.travel.IHistoryTravelPendingView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.*;
import id.co.bri.brimo.models.apimodel.request.travel.*;
import id.co.bri.brimo.models.apimodel.response.*;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class HistoryTravelPendingPresenter<V extends IMvpView & IHistoryTravelPendingView> extends MvpPresenter<V> implements IHistoryTravelPendingPresenter<V> {

    protected Object inquiryRequest = null;

    protected String url;
    protected String inqUrl;
    protected String inqUrlKai;

    protected String urlConfirmKcic;

    protected String urlPaymentKcic;

    protected String urlConfirmFlight;

    protected String urlPaymentFlight;

    protected Object confirmationKcicRequest = null;

    protected Object confirmationFlightRequest = null;

    public HistoryTravelPendingPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDetailTicket(String bookingId, HistoryTravelResponse.Transaction transaction) {
        inquiryRequest = new DetailTiketRequest(bookingId);
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getView().showProgress();
        getCompositeDisposable()
                .add(getApiSource().getData(url, inquiryRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                                onLoad = false;
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                DetailHistoryResponse detailHistoryResponse = response.getData(DetailHistoryResponse.class);
                                getView().onSuccessGetDetailTicket(detailHistoryResponse, transaction);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onLoad = false;
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void setUrlHistoryPending(String url) {
        this.url = url;
    }


    @Override
    public void setUrlInquiry(String url) {
        this.inqUrl = url;
    }

    @Override
    public void setUrlInquiryKai(String url) {
        this.inqUrlKai = url;
    }

    @Override
    public void setUrlConfirmationKcic(String urlKcic) {
        this.urlConfirmKcic = urlKcic;
    }

    @Override
    public void setUrlPaymentKcic(String urlPaymentKcic) {
        this.urlPaymentKcic = urlPaymentKcic;
    }

    @Override
    public void setUrlConfirmationFlight(String urlFlight) {
        this.urlConfirmFlight = urlFlight;
    }

    @Override
    public void setUrlPaymentFlight(String urlPaymentFlight) {
        this.urlPaymentFlight = urlPaymentFlight;
    }

    @Override
    public void getHistory() {
        if (url == null || !isViewAttached()) {
            return;
        }

        getView().isHideSkeleton(false);
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(getApiSource().getDataTanpaRequest(url,seqNum)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {


                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().isHideSkeleton(true);
                        getView().setDoneGetHistory(true);
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        getView().isHideSkeleton(true);
                        getView().setDoneGetHistory(true);
                        HistoryTravelPendingResponse historyTravelPendingResponse = response.getData(HistoryTravelPendingResponse.class);
                        getView().onSuccessGetHistory(historyTravelPendingResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        getView().isHideSkeleton(true);
                        getView().setDoneGetHistory(true);
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                })
        );
    }


    @Override
    public void getInquiry(HistoryTravelPendingResponse.PendingTransaction pendingTransaction) {

        if (inqUrl == null || !isViewAttached()) {
            return;
        }

        inquiryRequest = new InquiryBusObjectRequest(pendingTransaction.getBookingRefnum(),null);

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getView().showProgress();
        getCompositeDisposable()
                .add(getApiSource().getData(inqUrl, inquiryRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                                onLoad = false;
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                InquiryBusResponse inquiryBusResponse = response.getData(InquiryBusResponse.class);
                                getView().onSuccessInquiry(inquiryBusResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onLoad = false;
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void getInquiryKai(HistoryTravelPendingResponse.PendingTransaction pendingTransaction) {
        if (inqUrlKai == null || !isViewAttached()) {
            return;
        }

        inquiryRequest = new InquiryKaiObjectRequest(pendingTransaction.getIdBooking(), pendingTransaction.getBookingRefnum());

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getView().showProgress();
        getCompositeDisposable()
                .add(getApiSource().getData(inqUrlKai, inquiryRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                                onLoad = false;
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                InquiryTrainResponse inquiryTrainResponse = response.getData(InquiryTrainResponse.class);
                                getView().onSuccessInquiryTrain(inquiryTrainResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onLoad = false;
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));

    }

    @Override
    public void getConfirmationKcic(String brivaNumber, String corpCode, String qtyTicket, String expiredDate, String arrivalDate, String invoiceNumber) {
        getView().showProgress();
        if (urlConfirmKcic == null || !isViewAttached()) {
            return;
        }
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        confirmationKcicRequest = new TravelKcicRequest(brivaNumber, corpCode, qtyTicket, expiredDate, arrivalDate, invoiceNumber);

        getCompositeDisposable().add(
                getApiSource().getData(urlConfirmKcic, confirmationKcicRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                                onLoad = false;
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                InquiryBrivaRevampResponse inquiryBrivaRevampResponse =
                                        response.getData(InquiryBrivaRevampResponse.class);
                                getView().onSuccessConfirmationkcic(inquiryBrivaRevampResponse,
                                        urlPaymentKcic);

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {

                                onLoad = false;
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_94.getValue()))
                                    getView().onExceptionSnackbarBack(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );

    }

    @Override
    public void getConfirmationFlight(String bookingId) {
        getView().showProgress();
        if (urlConfirmFlight == null || !isViewAttached()) {
            return;
        }
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        confirmationFlightRequest = new TravelFlightRequest(bookingId);

        getCompositeDisposable().add(
                getApiSource().getData(urlConfirmFlight, confirmationFlightRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                                onLoad = false;
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                InquiryBrivaRevampResponse inquiryBrivaRevampResponse =
                                        response.getData(InquiryBrivaRevampResponse.class);
                                getView().onSuccessConfirmationFlight(inquiryBrivaRevampResponse,
                                        urlPaymentFlight);

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onLoad = false;
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );

    }

}