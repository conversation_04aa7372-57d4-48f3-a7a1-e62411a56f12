package id.co.bri.brimo.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.text.Editable
import android.text.TextWatcher
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.isVisible
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.inputpasswordnewskin.IInputPasswordNewSkinPresenter
import id.co.bri.brimo.contract.IView.inputpasswordnewskin.IInputPasswordNewSkinView
import id.co.bri.brimo.databinding.ActivityInputPasswordNewSkinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.BiometricUtils.Companion.hasBiometricEnrolled
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.domain.helpers.biometric.BiometricCallback
import id.co.bri.brimo.domain.helpers.biometric.BiometricUtility.displayPromptForEnroll
import id.co.bri.brimo.domain.helpers.biometric.BiometricUtility.displayPromptForLogin
import id.co.bri.brimo.domain.helpers.biometric.BiometricUtility.setBiometricType
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse
import id.co.bri.brimo.models.apimodel.response.LoginResponse
import id.co.bri.brimo.models.apimodel.response.PromoResponse
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.changepassnewskin.ChangePassActivity
import id.co.bri.brimo.ui.activities.newskinonboarding.pin.EnterCurrentPinPassActivity
import id.co.bri.brimo.ui.activities.waiting.WaitingActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.util.extension.view.addMinLengthCharValidation
import id.co.bri.brimo.util.extension.view.disableCopyPaste
import id.co.bri.brimo.util.extension.view.onTextChanged
import id.co.bri.brimo.util.extension.view.preventSpaceInput
import id.co.bri.brimo.util.extension.view.togglePasswordVisibility
import java.util.Objects
import javax.inject.Inject
import com.brimodsdk.BrimodSDKManager
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin.navigateToReact

class InputPasswordNewSkinActivity : NewSkinBaseActivity(),
    IInputPasswordNewSkinView,
    BiometricCallback {
    @Inject
    lateinit var presenter: IInputPasswordNewSkinPresenter<IInputPasswordNewSkinView>
    private lateinit var binding: ActivityInputPasswordNewSkinBinding
    var menuCode: String = ""
    var typeFlagLimit: String = ""
    var location: String = ""
    private val idleTimeoutMillis = 3 * 60 * 1000L
    var counter=0
    //    private var lastInteractionTime = 0L
    private val checkInterval = 1_000L
    private val handler = Handler(Looper.getMainLooper())
    private var isPasswordVisible = false
    private var isMNV = false
    private var refNum: String = ""
    private var promoId: String = ""

    private val launcherForgetPass = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            setResult(RESULT_OK)
            finish()
        }
    }

    private var btnForgetPass: Runnable = Runnable {
        val intent = EnterCurrentPinPassActivity.launchIntent(this, "from_login", Constant.REQ_FORGOT_PASS)
        launcherForgetPass.launch(intent)
    }

    var secBtnFunction: Runnable = Runnable {

    }

    companion object {
        @JvmStatic
        fun launchIntent(context: Context, location: String): Intent {
            return Intent(context, InputPasswordNewSkinActivity::class.java).apply {
                putExtra(Constant.LOCATION, location)
            }
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setTheme(R.style.AppThemeNewSkinFullTransparent)
        binding = ActivityInputPasswordNewSkinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        injectDependency()
        intentExtra()
        setupButtonLogin()
        setupView()
        startIdleTimer()
    }

    private fun intentExtra() {
        if (intent != null) {
            val extras = intent.extras
            if (extras != null) {
                location = extras.getString(Constant.LOCATION).toString()
            }
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlLogin(GeneralHelper.getString(R.string.url_login_v5))
        presenter.setUrlChange(GeneralHelper.getString(R.string.url_change_device_v2))
        presenter.setDetailItemPromoUrl(GeneralHelper.getString(R.string.url_detail_promo))
    }


    private fun setupView() = with(binding) {
        adjustViewOnKeyboardVisibility(layoutButton, root)

        inputPassword.preventSpaceInput()
        inputPassword.disableCopyPaste()
        setupTogglePasswordVisibility()
        observePassword()

        GeneralHelperNewSkin.setToolbar(
            this@InputPasswordNewSkinActivity,
            toolbar.toolbar,
            GeneralHelper.getString(R.string.login)
        )

        tvInisial.text = GeneralHelper.formatInitialName(presenter.getNameOfUser())
        tvName.text = "Hai, ${presenter.getNameOfUser()}"
        inputPassword.setOnFocusChangeListener { view, b ->
            if (b) showError(false, "")
        }
        inputPassword.addTextChangedListener(textWatcher)
        promoId = intent.getStringExtra("ID_PROMO_FAST_MENU") ?: ""

        tvForgetPass.setOnClickListener {
            OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
                supportFragmentManager,
                R.drawable.lock_password_new,
                "lock_password_new",
                resources.getString(R.string.forget_pass_ns),
                resources.getString(R.string.forget_pass_desc),
                createKotlinFunction0(btnForgetPass),
                createKotlinFunction0(secBtnFunction),
                true,
                resources.getString(R.string.btn_reset_pass),
                resources.getString(R.string.btn_cancel),
                false,
                showCloseButton = true
            )
        }
    }

    private fun setupButtonLogin() {
        val isBioUpdated =
            java.lang.Boolean.TRUE == presenter.getStatusUpdateBio()
        val isBioChanged =
            java.lang.Boolean.TRUE == presenter.getBioChanged()
        val hasBiometricEnrolled =
            hasBiometricEnrolled(baseContext)
        val isActivasi =
            java.lang.Boolean.TRUE == presenter.getStatusAktivasi()

        binding.btnLogin.setOnClickListener { _ ->
            presenter.getLocation(location)
            presenter.submitLogin()
        }

        binding.btnFingerprint.isVisible = isActivasi

        binding.btnFingerprint.setOnClickListener { _ ->
            if (!isBioUpdated) {
                if (hasBiometricEnrolled) {
                    setBiometricType(this)
                    displayPromptForEnroll(this, false)
                }
            } else if (isBioChanged) {
                openBioChanged()
            } else {
                if (hasBiometricEnrolled) {
                    if (isActivasi) {
                        displayPromptForLogin(this)
                    } else {
                        openInfoBiometric()
                    }
                } else {
                    presenter.updateStatusAktivasi(false)
                    openInfoBiometric()
                }
            }
        }
    }

    private fun openInfoBiometric() {
        val title = String.format(
            GeneralHelper.getString(R.string.title_info_biometric_off),
            presenter.getBioType()
        )
        val subtitle = String.format(
            GeneralHelper.getString(R.string.desc_info_biometric_off),
            presenter.getBioType()
        )

        OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
            fragmentManager = supportFragmentManager,
            imgPath = "",
            imgName = "ic_sad_illustration",
            titleTxt = title,
            subTitleTxt = subtitle,
            firstBtnTxt = GeneralHelper.getString(R.string.oke)
        )
    }

    private fun openBioChanged() {
        val title = String.format(
            GeneralHelper.getString(R.string.title_bio_change),
            presenter.getBioType()
        )

        OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
            fragmentManager = supportFragmentManager,
            imgPath = "",
            imgName = "ic_notice_hexagon_new_skin",
            titleTxt = title,
            subTitleTxt = GeneralHelper.getString(R.string.desc_bio_change),
            firstBtnTxt = GeneralHelper.getString(R.string.oke)
        )
    }

    private val textWatcher: TextWatcher = object : TextWatcher {
        override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            //do nothing
        }

        override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            checkInput()
        }

        override fun afterTextChanged(editable: Editable) {
            //do nothing
        }
    }

    private fun checkInput() {
        val passwordText = binding.inputPassword.text?.toString() ?: ""
        val isValid = passwordText.length >= 8
        updateButtonState(isValid, binding.btnLogin)
    }


    private fun setupTogglePasswordVisibility() = with(binding) {
        inputPassword.inputLayout.setEndIconOnClickListener {
            isPasswordVisible = !isPasswordVisible
            inputPassword.togglePasswordVisibility(isPasswordVisible)
        }
    }

    override val password: String
        get() = Objects.requireNonNull(binding.inputPassword.text).toString()

    override fun onSuccessLogin() {
        if (promoId.isEmpty()) {
            val intentLogin = Intent(this, DashboardIBActivity::class.java).apply {
                putExtra(Constant.LINK_CODE, menuCode)
                putExtra(Constant.TYPE_FLAG, typeFlagLimit)
                flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
            }
            startActivity(intentLogin)
            finish()
        } else {
            presenter.getDetailPromoItem(promoId)
        }
    }


    override fun showError(isVisible: Boolean, desc: String) {
        if (isVisible) {
            if (counter==0){
                GeneralHelperNewSkin.triggerVibration(this, Constant.VIBRATE_ERROR)
                binding.inputPassword.showError(desc)
                binding.btnLogin.isEnabled = false
            }else if (counter==1){
                GeneralHelperNewSkin.triggerVibration(this, Constant.VIBRATE_ERROR)
                binding.inputPassword.showError(desc)
                OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
                    supportFragmentManager,
                    R.drawable.ic_warning_illustration,
                    "ic_warning_illustration",
                    "Percobaan Login Gagal, Tersisa 1 Kali Kesempatan",
                    "Pastikan password yang dimasukkan sudah benar. Jika lupa, silakan atur ulang password.",
                    {

                    },
                    {
                        ChangePassActivity.launchIntent(this,"from_login")
                    },
                    false,
                    "Coba Lagi",
                    "Atur Ulang Password",
                    false,
                    showCloseButton = true
                )
                binding.btnLogin.isEnabled = false
            }else{
                GeneralHelperNewSkin.triggerVibration(this, Constant.VIBRATE_ERROR)
                binding.inputPassword.showError(desc)
                binding.btnLogin.isEnabled = false

                OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
                    fragmentManager = supportFragmentManager,
                    imgPath = "",
                    imgName = "ic_blocked_illustrator",
                    titleTxt = "Akun Kamu Terkunci Sementara",
                    subTitleTxt = "Kamu terlalu sering memasukkan password yang salah. Tenang, cukup atur ulang password kamu agar bisa mengakses akun kembali.",
                    btnFirstFunction = { ChangePassActivity.launchIntent(this,"from_login") },
                    isClickableOutside = false,
                    firstBtnTxt = "Atur Ulang Password Sekarang",
                    showCloseButton = false,
                    showPill = false

                )

//                OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
//                    supportFragmentManager,
//                    R.drawable.ic_blocked_illustrator,
//                    "ic_blocked_illustrator",
//                    "Akun kamu terkunci sementara",
//                    "Kamu terlalu sering memasukkan password yang salah. Tenang, cukup atur ulang password kamu agar bisa mengakses akun kembali.\"",
//                    {
//                        ChangePassActivity.launchIntent(this,"from_login")
//                    },
//                    isClickableOutside = false,
//                    firstBtnTxt = "Atur Ulang Password Sekarang",
//                    secondBtnTxt = "",
//                    showCloseButton = true
//                )

                binding.btnLogin.isEnabled = false
            }
            counter+=1
        }
    }

    override fun onExceptionLoginExceed(response: ExceptionResponse?) {
        OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
            supportFragmentManager,
            R.drawable.ic_blocked_illustrator,
            "ic_blocked_illustrator",
            "Akun kamu terkunci sementara",
            "Kamu terlalu sering memasukkan password yang salah. Tenang, cukup atur ulang password kamu agar bisa mengakses akun kembali.\"",
            {
                ChangePassActivity.launchIntent(this,"from_login")
            },
            {

            },
            false,
            "Atur Ulang Password Sekarang",
            withBgSecondBtn = false,
            showCloseButton = true
        )

//        OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
//            supportFragmentManager,
//            R.drawable.lock_password,
//            "ic_account_saved",
//            "Percobaan Login Kamu Sudah Habis",
//            "Akun kamu dibatasi karena kesalahan login. Atur ulang password kamu terlebih dahulu.",
//            createKotlinFunction0(firstBtnFunction),
//            createKotlinFunction0(secondBtnFunction),
//            false,
//            GeneralHelper.getString(R.string.toolbar_lupa_password),
//            GeneralHelper.getString(R.string.back_to_login),
//            false
//        )
    }

    override fun onDeviceChanged(desc: String?, loginResponse: LoginResponse?) {
//        isPinReady = loginResponse!!.isPinReady
        if (loginResponse != null) {
            refNum = loginResponse.refNum

            //TODO: NEED TO DELETE AFTER DEMO
            startActivity(Intent(this,AnotherDeviceDetectedNewSkinActivity::class.java))
        }
    }

    override fun onChangeDevice(otpExpiredSeconds: Int?) {
        WaitingActivity.launchIntent(this, true, otpExpiredSeconds, refNum)
    }

    override fun onSuccessGetDetailItem(promoResponse: PromoResponse?) {
        val dashboardIntent = Intent(this, DashboardIBActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
        }
        val params = mapOf(
            "id" to promoId,
        )
        startActivity(dashboardIntent)
        Handler(Looper.getMainLooper()).postDelayed({
            navigateToReact(
                bundleName = "8081:main",
                appName = "PromoDetail",
                params = params,
                activity = this
            )
            finish()
        }, 300)
    }

    override fun onChangeMNV(desc: String?, loginResponse: LoginResponse?) {
//        isPinReady = loginResponse!!.isPinReady
        if (loginResponse != null) {
            refNum = loginResponse.refNum
        }
        isMNV = true

        //TODO: NEED TO DELETE AFTER DEMO
        startActivity(Intent(this,AnotherDeviceDetectedNewSkinActivity::class.java))

//        val dialogExitCustom: DialogExitCustom = DialogExitCustom(
//            this,
//            GeneralHelper.getString(R.string.title_dialog_login_mnv), desc,
//            GeneralHelper.getString(R.string.btnBatal), GeneralHelper.getString(R.string.lanjutkan)
//        )
//        val ft = this.supportFragmentManager.beginTransaction()
//        ft.add(dialogExitCustom, null)
//        ft.commitAllowingStateLoss()
    }

    var firstBtnFunction: Runnable = Runnable { GeneralHelper.showToast(this, "Lupa Password") }

    var secondBtnFunction: Runnable = Runnable { finish() }

    private fun startIdleTimer() {
        resetIdleTimer()
        handler.post(checkIdleRunnable)
    }

    private val checkIdleRunnable = object : Runnable {
        override fun run() {
            val currentTime = SystemClock.elapsedRealtime()
            if (currentTime - mLastClickTime > idleTimeoutMillis) {
                onUserIdleTimeout()
            } else {
                handler.postDelayed(this, checkInterval)
            }
        }
    }

    override fun onUserInteraction() {
        super.onUserInteraction()
        resetIdleTimer()
    }

    private fun resetIdleTimer() {
        mLastClickTime = SystemClock.elapsedRealtime()
    }

    private fun onUserIdleTimeout() {
        OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
            fragmentManager = supportFragmentManager,
            imgPath = "",
            imgName = "ic_clock_illustration",
            titleTxt = GeneralHelper.getString(R.string.txt_time_is_up),
            subTitleTxt = "Karena login tidak diteruskan, kamu kembali ke halaman awal.",
            btnFirstFunction = { finish() },
            isClickableOutside = true,
            showPill = false,
            showCloseButton = false,
            firstBtnTxt = GeneralHelper.getString(R.string.mengerti),
            onDismiss = {
                finish()
            }
        )
    }

    private fun observePassword() = with(binding) {
        inputPassword.onTextChanged { s ->
            val input = s?.toString() ?: ""
            val isValidChar = input.length >= 8
            val noSpace = input.isNotEmpty() && !input.contains(" ")

            val allValid = isValidChar && noSpace

            btnLogin.isEnabled = allValid

        }

        inputPassword.addMinLengthCharValidation(
            minLength = 8,
            errorText = "Minimal harus 8 digit angka"
        )
    }

    override fun onDestroy() {
        super.onDestroy()
        handler.removeCallbacks(checkIdleRunnable)
    }

    override fun onAuthenticationFailed() {
        //do nothing
    }

    override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
        when (errorCode) {
            Constant.ERROR_NEGATIVE_BUTTON, Constant.ERROR_USER_CANCELED, Constant.ERROR_CANCELED, Constant.ERROR_LOCKOUT -> {
                if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
                    return
                }
                mLastClickTime = SystemClock.elapsedRealtime()
            }
        }
    }

    override fun onAuthenticationSuccess() {
        runOnUiThread { presenter.loginFingerprint() }
    }

    override fun onBiometricChanged() {
        openBioChanged()
        presenter.updateBioChange(true)
    }

    override fun isScreenshotDisabled(): Boolean = true

    override fun onResume() {
        super.onResume()
        binding.inputPassword.hideError()
        binding.inputPassword.setText("")
    }
}