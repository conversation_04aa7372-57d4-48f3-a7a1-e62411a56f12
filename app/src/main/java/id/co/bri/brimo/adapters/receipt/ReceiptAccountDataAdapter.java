package id.co.bri.brimo.adapters.receipt;


import android.content.Context;
import android.content.res.ColorStateList;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ItemReceiptAccountDataBinding;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.DetailListType;

public class ReceiptAccountDataAdapter extends RecyclerView.Adapter<ReceiptAccountDataAdapter.ViewHolder> {
    List<DetailListType> mDetailListTypeList;
    Context context;

    private final boolean mIsSource;

    private boolean mIsBindingKki = false;

    private final int defaultIcon;

    private String mIsBindingType = "";

    public ReceiptAccountDataAdapter(List<DetailListType> detailListTypes, Context context, boolean isSource, int defaultIcon){
        this.mDetailListTypeList = detailListTypes;
        this.context = context;
        this.mIsSource = isSource;
        this.defaultIcon = defaultIcon;
    }

    public ReceiptAccountDataAdapter(List<DetailListType> detailListTypes, Context context, boolean isSource, String isBindingType, int defIcon){
        this.mDetailListTypeList = detailListTypes;
        this.context = context;
        this.mIsSource = isSource;
        this.mIsBindingType = isBindingType;
        this.defaultIcon = defIcon;
    }

    @NonNull
    @Override
    public ReceiptAccountDataAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(ItemReceiptAccountDataBinding.inflate(LayoutInflater.from(context), parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ReceiptAccountDataAdapter.ViewHolder holder, int position) {
        DetailListType detailListType = mDetailListTypeList.get(position);
        if (mIsSource) {
            holder.binding.rlSumberdana.setBackgroundTintList(ColorStateList.valueOf(GeneralHelper.getColor(R.color.primary_blue80)));
            holder.binding.tvInisial.setTextColor(GeneralHelper.getColor(R.color.neutral_baseWhite));
            holder.binding.tvLabelAcc.setText(GeneralHelper.getString(R.string.txt_sumber_dana));
        } else {
            holder.binding.rlSumberdana.setBackground(ContextCompat.getDrawable(context, R.drawable.round_history_revamp));
            holder.binding.tvInisial.setTextColor(GeneralHelper.getColor(R.color.primary_blue80));
            holder.binding.tvLabelAcc.setText(GeneralHelper.getString(R.string.recipient));
        }

        if (!mIsBindingType.isEmpty()){
            holder.binding.tvLabelAcc.setVisibility(View.GONE);
        }

        //Set Image Circle
        if (detailListType.getListType().equals("image")) {
            holder.binding.llLogo.setVisibility(View.VISIBLE);
            holder.binding.rlSumberdana.setVisibility(View.GONE);
            //load icon transaction
            if (mIsBindingType.equalsIgnoreCase("card")){
                holder.binding.llLogo.setVisibility(View.GONE);
                holder.binding.llKartu.setVisibility(View.VISIBLE);
                setViewKki(detailListType,holder.binding.ivKartu);
            } else if (mIsBindingType.equalsIgnoreCase("merch")){
                holder.binding.llKartu.setVisibility(View.GONE);
                setViewKki(detailListType, holder.binding.ivIcon);
            } else {
                GeneralHelper.loadIconTransaction(
                        context,
                        detailListType.getIconPath(),
                        detailListType.getIconName(),
                        holder.binding.ivIcon,
                        defaultIcon);
            }
        } else {
            holder.binding.llLogo.setVisibility(View.GONE);
            holder.binding.rlSumberdana.setVisibility(View.VISIBLE);
            //Set Initial
            String title = detailListType.getTitle();
            holder.binding.tvInisial.setText(GeneralHelper.formatInitialName(title));
        }
        holder.binding.tvTitle.setText(detailListType.getTitle());
        holder.binding.tvSubtitle.setText(detailListType.getSubtitle());
        holder.binding.tvDescription.setText(detailListType.getDescription());
    }

    private void setViewKki(DetailListType detailListType, ImageView ivLogo){
        GeneralHelper.loadIconTransaction(
                context,
                detailListType.getIconPath(),
                detailListType.getIconName(),
                ivLogo,
                defaultIcon);
    }

    @Override
    public int getItemCount() {
        return mDetailListTypeList.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ItemReceiptAccountDataBinding binding;

        public ViewHolder(@NonNull ItemReceiptAccountDataBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }
}