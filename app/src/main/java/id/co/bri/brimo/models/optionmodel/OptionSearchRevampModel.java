package id.co.bri.brimo.models.optionmodel;

public class OptionSearchRevampModel {
    // this var used for local image name
    String optionIconName;
    //this var used for option name appear in text view
    String optionName;
    // this var used for server image url
    String optionIconPath;
    // this var used for default image if not found in local and server
    int defaultIcon;
    // this var used for get position of position from original model
    int positionModel;
    // this var used for get position of id of original model
    String codeModel;
    // this var used for flagging whether if option is using icon or not
    Boolean isIconAble;

    Boolean isActive = true;

    String filter = "";
    String header = "";

    public OptionSearchRevampModel(String optionIconName, String optionName, String optionIconPath, int defaultIcon, int positionModel, String codeModel, Boolean isIconAble) {
        this.optionIconName = optionIconName;
        this.optionName = optionName;
        this.optionIconPath = optionIconPath;
        this.defaultIcon = defaultIcon;
        this.positionModel = positionModel;
        this.codeModel = codeModel;
        this.isIconAble = isIconAble;
    }

    public OptionSearchRevampModel(String optionIconName, String optionName, String optionIconPath, int defaultIcon, int positionModel, String codeModel, Boolean isIconAble, Boolean isActive) {
        this.optionIconName = optionIconName;
        this.optionName = optionName;
        this.optionIconPath = optionIconPath;
        this.defaultIcon = defaultIcon;
        this.positionModel = positionModel;
        this.codeModel = codeModel;
        this.isIconAble = isIconAble;
        this.isActive = isActive;
    }

    public OptionSearchRevampModel(String optionIconName, String optionName, String optionIconPath, int defaultIcon, int positionModel, String codeModel, Boolean isIconAble, Boolean isActive, String filter, String header) {
        this.optionIconName = optionIconName;
        this.optionName = optionName;
        this.optionIconPath = optionIconPath;
        this.defaultIcon = defaultIcon;
        this.positionModel = positionModel;
        this.codeModel = codeModel;
        this.isIconAble = isIconAble;
        this.isActive = isActive;
        this.filter = filter;
        this.header = header;
    }

    public String getOptionIconName() {
        return optionIconName;
    }

    public void setOptionIconName(String optionIconName) {
        this.optionIconName = optionIconName;
    }

    public String getOptionName() {
        return optionName;
    }

    public void setOptionName(String optionName) {
        this.optionName = optionName;
    }

    public int getDefaultIcon() {
        return defaultIcon;
    }

    public void setDefaultIcon(int defaultIcon) {
        this.defaultIcon = defaultIcon;
    }

    public String getOptionIconPath() {
        return optionIconPath;
    }

    public void setOptionIconPath(String optionIconPath) {
        this.optionIconPath = optionIconPath;
    }

    public int getPositionModel() {
        return positionModel;
    }

    public void setPositionModel(int positionModel) {
        this.positionModel = positionModel;
    }

    public String getCodeModel() {
        return codeModel;
    }

    public void setCodeModel(String codeModel) {
        this.codeModel = codeModel;
    }

    public Boolean getIconAble() {
        return isIconAble;
    }

    public void setIconAble(Boolean iconAble) {
        isIconAble = iconAble;
    }

    public Boolean getActive() {
        return isActive;
    }

    public void setActive(Boolean active) {
        isActive = active;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public String getHeader() {
        return header;
    }

    public void setHeader(String header) {
        this.header = header;
    }
}
