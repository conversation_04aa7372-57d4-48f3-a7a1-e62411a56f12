package id.co.bri.brimo.ui.fragments

import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.TextView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.kizitonwose.calendarview.model.CalendarDay
import com.kizitonwose.calendarview.model.DayOwner
import com.kizitonwose.calendarview.ui.DayBinder
import com.kizitonwose.calendarview.ui.ViewContainer
import com.kizitonwose.calendarview.utils.next
import com.kizitonwose.calendarview.utils.previous
import com.kizitonwose.calendarview.utils.yearMonth
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentCalendarMutationBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.extension.visible
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.calendar.daysOfWeekFromLocale
import id.co.bri.brimo.domain.helpers.calendar.makeInVisible
import id.co.bri.brimo.domain.helpers.calendar.makeVisible
import id.co.bri.brimo.domain.helpers.calendar.setTextColorRes
import org.threeten.bp.DayOfWeek
import org.threeten.bp.LocalDate
import org.threeten.bp.YearMonth
import java.util.Locale

class CalendarMutationFragment(onSelectDate: OnSelectDate) : BottomSheetDialogFragment() {

    private val TAG = "SetCalendarFragment"

    private var _binding: FragmentCalendarMutationBinding? = null
    private val binding get() = _binding!!

    private val today = LocalDate.now()
    private val monthTitleFormatter =
        org.threeten.bp.format.DateTimeFormatter.ofPattern("MMMM")
    private val monthTitleFormatterId =
        org.threeten.bp.format.DateTimeFormatter.ofPattern("MMMM", Locale("id", "ID"))

    private var tempDate: LocalDate? = null
    private var startDate: LocalDate? = null
    private var endDate: LocalDate? = null
    private var startDateString: String? = null
    private var endDateString: String? = null

    private var selectDate: OnSelectDate? = onSelectDate

    lateinit var daysOfWeek: Array<DayOfWeek>

    private var radiusUpdated = false
    private var multipleDays = false

    private var getStartDate = false
    private var getEndDate = false
    private var maxToday = false
    private var debetDate = false
    private var isCustomCalendar = false
    private var isCalendarForRdn = false

    private var threeMonthsBefore: LocalDate? = null
    private var threeMonthsAfter: LocalDate? = null

    private var secondOpt = false
    private var tempStartDate: LocalDate? = null
    private var tempEndDate: LocalDate? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme)
    }

    interface OnSelectDate {
        fun onSelectStart(dateSelect: LocalDate)
        fun onSelectEnd(dateSelect: LocalDate)
        fun onSelectRange(startDateSelect: LocalDate, endDateSelect: LocalDate)
        fun onDismissDate()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentCalendarMutationBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val bundle: Bundle? = arguments
        multipleDays = bundle!!.getBoolean(Constant.TAG_PICK_DATE)
        getStartDate = bundle!!.getBoolean(Constant.TAG_PICK_START_DATE)
        getEndDate = bundle!!.getBoolean(Constant.TAG_PICK_END_DATE)
        maxToday = bundle!!.getBoolean(Constant.TAG_MAX_TODAY)
        startDateString = bundle!!.getString(Constant.TAG_START_DATE)
        endDateString = bundle!!.getString(Constant.TAG_END_DATE)
        debetDate = bundle!!.getBoolean(Constant.TAG_DEBET_DATE)
        isCustomCalendar = bundle!!.getBoolean(Constant.TAG_CUSTOM_CALENDAR)
        isCalendarForRdn = bundle.getBoolean(Constant.TAG_CALENDAR_FOR_RDN)

        daysOfWeek = daysOfWeekFromLocale()

        // set current Start Date
        if (startDateString != null && !startDateString.equals("")) {
            try {
                startDate = LocalDate.parse(startDateString)
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "startDate LocalDate.parse: ", e)
                }
            }
        }

        // set current End Date
        if (endDateString != null && !endDateString.equals("")) {
            try {
                endDate = LocalDate.parse(endDateString)
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "endDate LocalDate.parse: ", e)
                }
            }
        }

        tempStartDate = startDate
        tempEndDate = endDate

        if (tempStartDate != null && tempEndDate != null) {
            secondOpt = true
        }
        if (isCustomCalendar) {
            binding.llInfoBorder.visible()
        }
        if (isCalendarForRdn) {
            binding.tvInfo.text = GeneralHelper.getString(R.string.calendar_caution_90_days)
        }

        setupCalendar()
        setupButton()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = BottomSheetDialog(requireContext(), theme)
        dialog.setOnShowListener {

            val bottomSheetDialog = it as BottomSheetDialog
            val parentLayout =
                bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            parentLayout?.let { it ->
                val behaviour = BottomSheetBehavior.from(it)
//                setupFullHeight(it)
                behaviour.state = BottomSheetBehavior.STATE_EXPANDED
            }
        }
        return dialog
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        selectDate?.onDismissDate()
    }

    private fun setupFullHeight(bottomSheet: View) {
        val layoutParams = bottomSheet.layoutParams
        layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
        bottomSheet.layoutParams = layoutParams
    }

    private fun setupButton() {
        binding.ivBack.setOnClickListener {
            dismiss()
            selectDate?.onDismissDate()
        }

        binding.btPilihDurasi.setOnClickListener {
            val startDate = startDate
            val endDate = endDate

            if (multipleDays) {
                if (startDate != null && endDate != null) {
                    returnDatePicked(startDate, endDate)
                } else if (startDate == null) {
                    if (endDate != null)
                        returnEndDatePicked(endDate)
                    else
                        showSnackbarErrorMessage(GeneralHelper.getString(R.string.please_select_a_date_first))
                } else if (endDate == null) {
                    if (startDate != null)
                        returnSingleDatePicked(startDate)
                    else
                        showSnackbarErrorMessage(GeneralHelper.getString(R.string.please_select_a_date_first))
                } else if (startDate != null && startDate.isBefore(today.plusDays(1)) && endDate == null) {
                    returnSingleDatePicked(startDate)
                } else if (endDate != null && endDate.isBefore(today.plusDays(1)) && startDate == null) {
                    returnEndDatePicked(endDate)
                } else {
                    showSnackbarErrorMessage(GeneralHelper.getString(R.string.please_select_a_date_first))
                }
            } else {
                if (getStartDate || getEndDate) {
                    if (startDate == null) {
                        if (endDate != null)
                            returnEndDatePicked(endDate)
                        else
                            showSnackbarErrorMessage(GeneralHelper.getString(R.string.please_select_a_date_first))
                    } else if (endDate == null) {
                        if (startDate != null)
                            returnSingleDatePicked(startDate)
                        else
                            showSnackbarErrorMessage(GeneralHelper.getString(R.string.please_select_a_date_first))
                    } else {
                        returnDatePicked(startDate, endDate)
                    }
                } else if (startDate != null) {
                    returnSingleDatePicked(startDate)
                } else {
                    showSnackbarErrorMessage(GeneralHelper.getString(R.string.please_select_a_date_first))
                }
            }
        }
    }

    /**
     * Returning picked date value into previous activity using bundle
     */
    private fun returnDatePicked(startDate: LocalDate, endDate: LocalDate) {
        selectDate?.onSelectRange(startDate, endDate)
        dismiss()
    }

    private fun returnSingleDatePicked(startDate: LocalDate) {
        selectDate?.onSelectStart(startDate)
        dismiss()
    }

    private fun returnEndDatePicked(endDate: LocalDate) {
        selectDate?.onSelectEnd(endDate)
        dismiss()
    }


    private fun setupCalendar() {
        setupCalendarView()
        setupCalendarNavigation()
    }

    private fun setupCalendarNavigation() {
        binding.ibPreviousMonthFrag.setOnClickListener {
            binding.calendarView.findFirstVisibleMonth()?.let {
                binding.calendarView.smoothScrollToMonth(it.yearMonth.previous)
            }
        }

        binding.ibNextMonthFrag.setOnClickListener {
            binding.calendarView.findFirstVisibleMonth()?.let {
                binding.calendarView.smoothScrollToMonth(it.yearMonth.next)
            }
        }
    }

    private fun setupCalendarView() {
        setupMonth()
        setupCalendarBinder()
    }

    private fun setupCalendarBinder() {
        class DayViewContainer(view: View) : ViewContainer(view) {
            // Will be set when this container is bound. See the dayBinder.
            lateinit var day: CalendarDay
            val textView = view.findViewById<TextView>(R.id.tvCalendarDay)
            val roundBgView = view.findViewById<View>(R.id.calendarDayView)
            val leftGreyArea = view.findViewById<View>(R.id.leftGreyArea)
            val rightGreyArea = view.findViewById<View>(R.id.rightGreyArea)

            init {
                view.setOnClickListener {
                    if (day.owner == DayOwner.THIS_MONTH && (day.date == today || day.date.isBefore(
                            today
                        ) || multipleDays || debetDate) && isWithinAllowedRange(day.date)
                    ) {
                        val selectedDate = day.date
                        if (getStartDate) {
                            when {
                                // Kasus: startDate dan endDate sudah ada
                                startDate != null && endDate != null -> {
                                    if (selectedDate.isBefore(endDate) || selectedDate.isEqual(
                                            endDate
                                        )
                                    ) {
                                        if (secondOpt) {
                                            startDate = selectedDate
                                            secondOpt = false
                                        } else {
                                            startDate = selectedDate
                                            endDate = null
                                        }
                                    } else if (selectedDate.isAfter(endDate)) {
                                        if (!secondOpt) {
                                            startDate = selectedDate
                                            endDate = null
                                            secondOpt = false
                                        } else {
                                            startDate = selectedDate
                                            endDate = selectedDate
                                        }
                                    } else {
                                        startDate = selectedDate
                                        endDate = null
                                    }
                                }

                                // Kasus: hanya startDate yang sudah ada
                                startDate != null && endDate == null -> {
                                    if (selectedDate.isBefore(startDate) || selectedDate.isEqual(
                                            startDate
                                        )
                                    ) {
                                        endDate = startDate
                                        startDate = selectedDate
                                    } else if (selectedDate.isAfter(startDate)) {
                                        secondOpt = false
                                        endDate = selectedDate
                                    } else {
                                        startDate = selectedDate
                                        endDate = null
                                    }
                                }

                                // Kasus: startDate belum dipilih
                                startDate == null -> {
                                    startDate = selectedDate
                                    endDate = null
                                }

                                // Kasus: startDate dan endDate sudah dipilih sebelumnya
                                else -> {
                                    if (selectedDate.isBefore(startDate) || selectedDate.isEqual(
                                            startDate
                                        )
                                    ) {
                                        startDate = selectedDate
                                        endDate = null
                                    } else {
                                        endDate = selectedDate
                                    }
                                }
                            }
                        } else if (getEndDate) {
                            when {
                                // Kasus: startDate dan endDate sudah ada
                                startDate != null && endDate != null -> {
                                    if (selectedDate.isBefore(startDate) || selectedDate.isEqual(
                                            startDate
                                        )
                                    ) {
                                        if (secondOpt) {
                                            startDate = selectedDate
                                            endDate = selectedDate
                                            secondOpt = false
                                        } else {
                                            startDate = selectedDate
                                            endDate = null
                                        }
                                    } else if (selectedDate.isAfter(startDate)) {
                                        endDate = selectedDate
                                    } else if (selectedDate.isAfter(endDate)) {
                                        if (secondOpt) {
                                            startDate = selectedDate
                                            endDate = null
                                        } else {
                                            endDate = selectedDate
                                        }
                                    } else {
                                        // Reset startDate to selectedDate, keep endDate unchanged
                                        startDate = selectedDate
                                    }
                                }

                                // Kasus: startDate sudah ada
                                startDate != null && endDate == null -> {
                                    if (selectedDate >= startDate) {
                                        // If selectedDate is after or equal to startDate, set endDate
                                        endDate =
                                            if (endDate == selectedDate) null else selectedDate
                                        secondOpt = false
                                    } else {
                                        // If selectedDate is before startDate, reset startDate and endDate
                                        startDate = selectedDate
                                        endDate = null
                                    }
                                }

                                // Kasus: startDate belum dipilih
                                startDate == null -> {
                                    startDate = selectedDate
                                    endDate = null
                                }
                            }
                        }
                        binding.calendarView.notifyCalendarChanged()
                    }
                }
            }
        }

        setupScrollListener()

        binding.calendarView.dayBinder = object : DayBinder<DayViewContainer> {
            override fun create(view: View): DayViewContainer {
                return DayViewContainer(view)
            }

            override fun bind(container: DayViewContainer, data: CalendarDay) {
                container.day = data
                val view = container.view
                val textView = container.textView
                val roundBgView = container.roundBgView
                val leftGreyArea = container.leftGreyArea
                val rightGreyArea = container.rightGreyArea
                //Make default container to be empty before binding the dates in
                textView.text = null
                textView.background = null
                roundBgView.makeInVisible()
                leftGreyArea.makeInVisible()
                rightGreyArea.makeInVisible()

                //Set date to container which correctly are dates of corresponding month
                if (data.owner == DayOwner.THIS_MONTH) {
                    textView.text = data.day.toString()

                    //Disable date after today's date
                    if (data.date.isAfter(today) && maxToday || data.date.isBefore(
                            today.minusDays(
                                364
                            )
                        )
                    ) {
                        textView.setTextColorRes(R.color.black_ns_600)
                        textView.isClickable = false
                        textView.isFocusable = false
                    } else if (data.date.isBefore(today.minusDays(-1)) && debetDate || data.date.isAfter(
                            today.plusDays(27)
                        ) && debetDate
                    ) {
                        textView.setTextColorRes(R.color.black_ns_600)
                        textView.isClickable = false
                        textView.isFocusable = false
                    } else {
                        threeMonthsBefore = startDate?.minusMonths(3)
                        threeMonthsAfter = startDate?.plusMonths(3)

                        when {
                            //jika tanggal sama
                            startDate == data.date && endDate == data.date -> {
                                textView.setTextColorRes(R.color.white)
                                roundBgView.makeVisible()
                                leftGreyArea.makeInVisible()
                                rightGreyArea.makeInVisible()
                                roundBgView.setBackgroundResource(R.drawable.example_4_single_selected_bg)
                            }

                            // If only one day picked startDate
                            startDate == data.date && endDate == null -> {
                                textView.setTextColorRes(R.color.white)
                                roundBgView.makeVisible()
                                roundBgView.setBackgroundResource(R.drawable.example_4_single_selected_bg)

                                if (getStartDate && tempDate != null) {
                                    rightGreyArea.makeVisible()
                                }
                            }

                            // If only one day picked endDate
                            startDate == null && endDate == data.date -> {
                                textView.setTextColorRes(R.color.white)
                                roundBgView.makeVisible()
                                roundBgView.setBackgroundResource(R.drawable.example_4_single_selected_bg)

                                if (getEndDate && tempDate != null) {
                                    rightGreyArea.makeVisible()
                                }
                            }

                            // If date range has been picked, set start date background to provide half oval shape
                            data.date == startDate -> {
                                textView.setTextColorRes(R.color.white)
                                if (multipleDays || getEndDate || getStartDate) {
                                    rightGreyArea.makeVisible()
                                }

                                roundBgView.makeVisible()
                                roundBgView.setBackgroundResource(R.drawable.example_4_single_selected_bg)
                            }

                            // If date range has been picked, set background of date between start date and end date to grey area
                            startDate != null && endDate != null && (data.date > startDate && data.date < endDate) -> {
                                textView.setTextColorRes(R.color.black_ns_main)
                                textView.setBackgroundResource(R.drawable.bg_selected_date_range)
                            }

                            // If date range has been picked and then change start date, set background of date between new start date and end date to grey area
                            tempDate != null && getStartDate && (data.date > startDate && data.date < tempDate) -> {
                                textView.setTextColorRes(R.color.black_ns_main)
                                textView.setBackgroundResource(R.drawable.bg_selected_date_range)
                            }

                            // If date range has been picked and then change end date, set background of date between start date and new end date to grey area
                            tempDate != null && getEndDate && (data.date < endDate && data.date > tempDate) -> {
                                textView.setTextColorRes(R.color.black_ns_main)
                                textView.setBackgroundResource(R.drawable.bg_selected_date_range)
                            }

                            // If date range has been picked, set end date background to provide half oval shape
                            data.date == endDate -> {
                                textView.setTextColorRes(R.color.white)
                                if (multipleDays || getEndDate || getStartDate) {
                                    leftGreyArea.makeVisible()
                                }

                                roundBgView.makeVisible()
                                roundBgView.setBackgroundResource(R.drawable.example_4_single_selected_bg)
                            }

                            // If date range has been picked, set new start date background to provide half oval shape
                            tempDate != null && data.date == tempDate && getStartDate -> {
                                textView.setTextColorRes(R.color.white)
                                leftGreyArea.makeVisible()
                                roundBgView.makeVisible()
                                roundBgView.setBackgroundResource(R.drawable.example_4_single_selected_bg)
                            }

                            // If date range has been picked, set new end date background to provide half oval shape
                            tempDate != null && data.date == tempDate && getEndDate -> {
                                textView.setTextColorRes(R.color.white)
                                leftGreyArea.makeVisible()
                                roundBgView.makeVisible()
                                roundBgView.setBackgroundResource(R.drawable.example_4_single_selected_bg)
                            }


                            // Add circle to today's date
                            data.date == today && !debetDate -> {
                                textView.setTextColorRes(R.color.colorButtonOrange)
                                roundBgView.makeVisible()
                                roundBgView.setBackgroundResource(R.drawable.example_4_today_bg)
                            }

                            data.date >= today && !multipleDays && !debetDate -> {
                                textView.setTextColorRes(R.color.black_ns_600)
                            }

                            data.date <= today && !multipleDays && debetDate -> {
                                textView.setTextColorRes(R.color.black_ns_600)
                            }

                            data.date >= today.plusDays(28) && !multipleDays && debetDate -> {
                                textView.setTextColorRes(R.color.black_ns_600)
                            }

                            else -> textView.setTextColorRes(R.color.black_ns_main)
                        }

                        view.isClickable = true

                        if (startDate == null && isCalendarForRdn && isCustomCalendar) {
                            if (data.date.isBefore(today?.minusDays(90))) {
                                textView.setTextColorRes(R.color.black_ns_600)
                                textView.isClickable = false
                                view.isClickable = false
                            }
                        }

                        if (startDate != null && isCustomCalendar) {
                            val maxDays = if (isCalendarForRdn) 90 else 120

                            if (endDate != null && isCalendarForRdn) {
                                if (data.date < endDate?.minusDays(maxDays.toLong()) || data.date > endDate?.plusDays(
                                        maxDays.toLong()
                                    )
                                ) {
                                    textView.setTextColorRes(R.color.black_ns_600)
                                    textView.isClickable = false
                                    view.isClickable = false
                                }
                            }

                            if (data.date < startDate?.minusDays(maxDays.toLong()) || data.date > startDate?.plusDays(
                                    maxDays.toLong()
                                )
                            ) {
                                textView.setTextColorRes(R.color.black_ns_600)
                                textView.isClickable = false
                                view.isClickable = false
                            }
                        }
                    }
                } else {

                    // This part is to make the coloured selection background continuous
                    // on the blank in and out dates across various months and also on dates(months)
                    // between the start and end dates if the selection spans across multiple months.

                    val startDate = startDate
                    val endDate = endDate
                    if (startDate != null && endDate != null) {
                        // Mimic selection of inDates that are less than the startDate.
                        // Example: When 26 Feb 2019 is startDate and 5 Mar 2019 is endDate,
                        // this makes the inDates in Mar 2019 for 24 & 25 Feb 2019 look selected.
                        if ((data.owner == DayOwner.PREVIOUS_MONTH
                                    && startDate.monthValue == data.date.monthValue
                                    && endDate.monthValue != data.date.monthValue) ||
                            // Mimic selection of outDates that are greater than the endDate.
                            // Example: When 25 Apr 2019 is startDate and 2 May 2019 is endDate,
                            // this makes the outDates in Apr 2019 for 3 & 4 May 2019 look selected.
                            (data.owner == DayOwner.NEXT_MONTH
                                    && startDate.monthValue != data.date.monthValue
                                    && endDate.monthValue == data.date.monthValue) ||

                            // Mimic selection of in and out dates of intermediate
                            // months if the selection spans across multiple months.
                            (startDate < data.date && endDate > data.date
                                    && startDate.monthValue != data.date.monthValue
                                    && endDate.monthValue != data.date.monthValue)
                        ) {
                            textView.setBackgroundResource(R.drawable.example_4_continuous_selected_bg_middle)
                        }
                    }

                }
            }
        }
    }

    private fun isWithinAllowedRange(date: LocalDate): Boolean {
        val oneYearAgo = today.minusYears(1)
        return !date.isBefore(oneYearAgo) && !date.isAfter(today)
    }


    /**
     * Setup scroll listener of calendar view and change the month-year display according to its adapter position
     */
    private fun setupScrollListener() {
        binding.calendarView.monthScrollListener = { month ->
            val monthTitle =
                if (isCustomCalendar) monthTitleFormatterId.format(month.yearMonth) else monthTitleFormatter.format(
                    month.yearMonth
                )
            val title = "${monthTitle} ${month.yearMonth.year}"
            binding.tvMonthYearFrag.text = title
        }
    }

    private fun setupMonth() {
        val totalPreviousMonthsDisplayed = 12
        val totalNextMonthsDisplayed = 0
        val currentMonth = YearMonth.now()
        val startMonth = currentMonth.minusMonths(totalPreviousMonthsDisplayed.toLong())
        val endMonth = currentMonth.plusMonths(totalNextMonthsDisplayed.toLong())

        binding.calendarView.setup(startMonth, endMonth, daysOfWeek.first())

        if (getStartDate && startDate != null) {
            val startMonth = startDate!!.yearMonth
            binding.calendarView.scrollToMonth(startMonth)
        } else if (getEndDate && endDate != null) {
            val endMonth = endDate!!.yearMonth
            binding.calendarView.scrollToMonth(endMonth)
        } else {
            binding.calendarView.scrollToMonth(currentMonth)
        }
    }

    fun showSnackbarErrorMessage(message: String?) {
        GeneralHelper.showSnackBar(binding.content, message!!)
    }
}