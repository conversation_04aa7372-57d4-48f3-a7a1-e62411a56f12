package id.co.bri.brimo.ui.activities.changepinnewskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.recyclerview.widget.GridLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.PinNumberAdapterNewSkin
import id.co.bri.brimo.adapters.baseadapter.base.BasePinAdapter.PinAdapterListener
import id.co.bri.brimo.adapters.pinadapter.OtpInputAdapterNewSkin
import id.co.bri.brimo.contract.IPresenter.ubahpin.IUbahPinPresenter
import id.co.bri.brimo.contract.IView.ubahpin.IUbahPinView
import id.co.bri.brimo.databinding.ActivityChangePinNewskinBinding
import id.co.bri.brimo.di.modules.fragment.PinAllModule
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.ErrorResponseNewSkin
import id.co.bri.brimo.ui.activities.LoginActivity
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.UbahPinActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.newskinonboarding.OnboardingInputNumberForgetPassActivity
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers.Companion.getPinNumberList
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation
import javax.inject.Inject

class ChangePinActivity : BaseActivity(), PinNumberAdapterNewSkin.OnPinNumberListener,
    PinAdapterListener,
    IUbahPinView {

    private lateinit var binding: ActivityChangePinNewskinBinding

    @Inject
    lateinit var ubahPinPresenter: IUbahPinPresenter<IUbahPinView?>
    private var otpInputAdapter: OtpInputAdapterNewSkin? = null

    companion object {
        fun launchIntent(caller: Activity) {
            val intent = Intent(caller, ChangePinActivity::class.java)
            caller.startActivityForResult(intent, Constant.REQ_UBAH_PIN)
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityChangePinNewskinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)

        injectDependency()
        setupView()
    }

    private fun injectDependency() {
        activityComponent
            .plusPinComponent(PinAllModule())
            .inject(this)
        if (ubahPinPresenter != null) {
            ubahPinPresenter.setView(this)
            ubahPinPresenter.setUrl(GeneralHelper.getString(R.string.v5_change_pin_check))
            ubahPinPresenter.start()
        }
    }

    private fun setupView() {
//        otpInputAdapter = OtpInputAdapterNewSkin(this)
//        val pinNumberAdapter = PinNumberAdapterNewSkin(getPinNumberList(this))
//        val pinOtpLayoutManager = GridLayoutManager(this, 6)
//        val pinPadLayoutManager: GridLayoutManager = GridLayoutManager(this, 3)
//
//        pinNumberAdapter.onPinNumberListener = this
//        otpInputAdapter!!.setListener(this)
//        binding.tvLupaPin.setOnClickListener { v -> OnboardingInputNumberForgetPassActivity.launchIntent(this, true) }
//
//        binding.rvBox.layoutManager = pinOtpLayoutManager
//        binding.rvBox.adapter = otpInputAdapter
//
//        binding.rvInput.layoutManager = pinPadLayoutManager
//        binding.rvInput.adapter = pinNumberAdapter
//        binding.llBack.setOnClickListener {
//            onBackPressed()
//        }
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_UBAH_PIN && resultCode == Activity.RESULT_OK) {
            setResult(Activity.RESULT_OK, data)
            finish()
        }
        if (requestCode == Constant.REQ_UBAH_PIN && resultCode == UbahPinActivity.SESSION_EXP_CODE) {
            if (data != null && data.hasExtra(Constant.DESCRIPTION)) {
                onException(data.getStringExtra(Constant.DESCRIPTION))
            }
        }
        if (requestCode == Constant.REQ_UBAH_PIN && resultCode == Activity.RESULT_CANCELED) {
            otpInputAdapter!!.deleteAllPin()
        }

        if (resultCode == Activity.RESULT_CANCELED && data != null) {
            showSnackbarErrorMessageRevamp(
                data.getStringExtra(Constant.TAG_ERROR_MESSAGE), ALERT_ERROR,
                this, false
            )
        }
    }


    private val onBackPressedCallback: OnBackPressedCallback =
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val intent = Intent()
                intent.putExtra(Constant.CHECK_POINT, 9)
                setResult(Activity.RESULT_CANCELED, intent)
                finish()
            }
        }

    override fun onDestroy() {
        ubahPinPresenter.stop()
        super.onDestroy()
    }

    override fun onPinClicked(pinNumber: Int) {
//        binding.tvError.visibility = View.GONE
        otpInputAdapter!!.addPin(pinNumber.toString())
        otpInputAdapter!!.resetError()
    }

    override fun onDeleteClicked() {
        otpInputAdapter!!.deletePin()
//        binding.tvError.visibility = View.GONE
        otpInputAdapter!!.resetError()
    }

    override fun notifyChanges() {
        otpInputAdapter!!.notifyDataSetChanged()
    }

    override fun onComplete(string: String) {
        ChangeNewPinActivity.launchIntent(this, "")

//        ubahPinPresenter.setPin(string)
//        ubahPinPresenter.ubahPin()
    }

    override fun onSuccess(data: String?) {
        ChangeNewPinActivity.launchIntent(this, data)
    }

    override fun onException50(msg: String?) {
        var firstBtnFunction: Runnable = Runnable {
            LupaPinActivity.launchIntent(this)
        }
        val secBtnFunction = Runnable {
            LoginActivity.launchIntentWithDialogNewSkin(this, msg, false)
        }
        showDialogConfirmation(
            supportFragmentManager,
            R.drawable.ic_fail_newskin,
            "ic_account_saved",
            "Kamu Telah Mencapai Batas Maksimum Percobaan Verifikasi PIN",
            "Akun diblokir sementara. Untuk keamanan, silakan atur ulang PIN kamu terlebih dahulu.",
            createKotlinFunction0(firstBtnFunction),
            createKotlinFunction0(secBtnFunction),
            false,
            "Atur Ulang PIN",
            "Kembali ke Login",
            false
        )
    }


    override fun resetInputPin() {
        otpInputAdapter!!.deleteAllPin()
//        binding.tvError.visibility = View.GONE
        otpInputAdapter!!.resetError()
    }

    override fun onErrorPin(desc: String?) {
        otpInputAdapter!!.setErrorState(true)
//        binding.tvError.visibility = View.VISIBLE
//        binding.tvError.text = desc
    }

    override fun onExceptionErrorAttemps(resp: ErrorResponseNewSkin) {
        var firstBtnFunction: Runnable = Runnable {
            LupaPinActivity.launchIntent(this)
        }
        val secBtnFunction = Runnable {
            LoginActivity.launchIntentWithDialogNewSkin(this, resp.title, false)
        }
        OpenBottomSheetGeneralFragment.showDialogConfirmation(
            supportFragmentManager,
            resp.image_url,
            "ic_account_saved",
            resp.title,
            resp.description,
            createKotlinFunction0(firstBtnFunction),
            createKotlinFunction0(secBtnFunction),
            false,
            "Atur Ulang PIN",
            "Kembali ke Login",
            false
        )
    }

    override fun onErrorBlock(resp: ErrorResponseNewSkin) {
        var firstBtnFunction: Runnable = Runnable {
            LupaPinActivity.launchIntent(this)
        }
        val secBtnFunction = Runnable {
            LoginActivity.launchIntentWithDialogNewSkin(this, resp.title, false)
        }
        OpenBottomSheetGeneralFragment.showDialogConfirmation(
            supportFragmentManager,
            resp.image_url,
            "ic_account_saved",
            resp.title,
            resp.description,
            createKotlinFunction0(firstBtnFunction),
            createKotlinFunction0(secBtnFunction),
            false,
            "Atur Ulang PIN",
            "Kembali ke Login",
            false
        )
    }
}