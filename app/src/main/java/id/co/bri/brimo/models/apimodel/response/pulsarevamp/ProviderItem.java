package id.co.bri.brimo.models.apimodel.response.pulsarevamp;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import id.co.bri.brimo.models.apimodel.response.DataList;
import id.co.bri.brimo.models.apimodel.response.DataListRevamp;
import id.co.bri.brimo.models.apimodel.response.PulsaList;

public class ProviderItem implements Parcelable {

	@SerializedName("code")
	private String code;

	@SerializedName("pulsa_code")
	private String pulsaCode;

	@SerializedName("prefix")
	private List<String> prefix;

	@SerializedName("icon_path")
	private String iconPath;

	@SerializedName("name")
	private String name;

	@SerializedName("icon_name")
	private String iconName;

	@SerializedName("data_code")
	private String dataCode;

	@SerializedName("pulsa_list")
	@Expose
	private List<PulsaList> pulsaList = null;

	@SerializedName("data_list")
	@Expose
	private List<DataListRevamp> dataList = null;

 	private List<DataListRevamp> dataListCustom = new ArrayList<>();
	@SerializedName("omni_url")
	@Expose
	private String omniUrl;

	@SerializedName("omni_desc")
	@Expose
	private String omniDesc;

	protected ProviderItem(Parcel in) {
		code = in.readString();
		pulsaCode = in.readString();
		prefix = in.createStringArrayList();
		iconPath = in.readString();
		name = in.readString();
		iconName = in.readString();
		dataCode = in.readString();
		pulsaList = in.createTypedArrayList(PulsaList.CREATOR);
		dataList = in.createTypedArrayList(DataListRevamp.CREATOR);
		dataListCustom = in.createTypedArrayList(DataListRevamp.CREATOR);
		omniUrl = in.readString();
		omniDesc = in.readString();
	}

	@Override
	public void writeToParcel(Parcel dest, int flags) {
		dest.writeString(code);
		dest.writeString(pulsaCode);
		dest.writeStringList(prefix);
		dest.writeString(iconPath);
		dest.writeString(name);
		dest.writeString(iconName);
		dest.writeString(dataCode);
		dest.writeTypedList(pulsaList);
		dest.writeTypedList(dataList);
		dest.writeTypedList(dataListCustom);
		dest.writeString(omniUrl);
		dest.writeString(omniDesc);
	}

	@Override
	public int describeContents() {
		return 0;
	}

	public static final Creator<ProviderItem> CREATOR = new Creator<ProviderItem>() {
		@Override
		public ProviderItem createFromParcel(Parcel in) {
			return new ProviderItem(in);
		}

		@Override
		public ProviderItem[] newArray(int size) {
			return new ProviderItem[size];
		}
	};

	public void setCode(String code){
		this.code = code;
	}

	public String getCode(){
		return code;
	}

	public void setPulsaCode(String pulsaCode){
		this.pulsaCode = pulsaCode;
	}

	public String getPulsaCode(){
		return pulsaCode;
	}

	public void setPrefix(List<String> prefix){
		this.prefix = prefix;
	}

	public List<String> getPrefix(){
		return prefix;
	}

	public void setIconPath(String iconPath){
		this.iconPath = iconPath;
	}

	public String getIconPath(){
		return iconPath;
	}

	public void setName(String name){
		this.name = name;
	}

	public String getName(){
		return name;
	}

	public void setIconName(String iconName){
		this.iconName = iconName;
	}

	public String getIconName(){
		return iconName;
	}

	public void setDataCode(String dataCode){
		this.dataCode = dataCode;
	}

	public String getDataCode(){
		return dataCode;
	}

	@Override
 	public String toString(){
		return 
			"ProviderItem{" + 
			"code = '" + code + '\'' + 
			",pulsa_code = '" + pulsaCode + '\'' + 
			",prefix = '" + prefix + '\'' + 
			",icon_path = '" + iconPath + '\'' + 
			",name = '" + name + '\'' + 
			",icon_name = '" + iconName + '\'' + 
			",data_code = '" + dataCode + '\'' + 
			"}";
		}

	public List<PulsaList> getPulsaList() {
		return pulsaList;
	}

	public void setPulsaList(List<PulsaList> pulsaList) {
		this.pulsaList = pulsaList;
	}

	public List<DataListRevamp> getDataList() {
		return dataList;
	}

	public void setDataList(List<DataListRevamp> dataList) {
		this.dataList = dataList;
	}

	public String getOmniUrl() {
		return omniUrl;
	}

	public void setOmniUrl(String omniUrl) {
		this.omniUrl = omniUrl;
	}

	public String getOmniDesc() {
		return omniDesc;
	}

	public void setOmniDesc(String omniDesc) {
		this.omniDesc = omniDesc;
	}
	public List<DataListRevamp> getDataListCustom() {
		return dataListCustom;
	}

	public void setDataListCustom(List<DataListRevamp> dataListCustom) {
		this.dataListCustom = dataListCustom;
	}
}