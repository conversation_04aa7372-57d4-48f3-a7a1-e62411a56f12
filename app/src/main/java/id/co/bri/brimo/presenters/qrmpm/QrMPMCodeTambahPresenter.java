package id.co.bri.brimo.presenters.qrmpm;

import android.util.Log;

import id.co.bri.brimo.contract.IPresenter.qrmpm.IQrMPMCodeTambahPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.qrmpm.IQrMPMCodeTambahView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.BaseFormRequest;
import id.co.bri.brimo.models.apimodel.request.FastGenerateQrRequest;
import id.co.bri.brimo.models.apimodel.request.GenerateQrRequest;
import id.co.bri.brimo.models.apimodel.response.GenerateQrResponse;
import id.co.bri.brimo.models.apimodel.response.QrResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class QrMPMCodeTambahPresenter<V extends IMvpView & IQrMPMCodeTambahView>
        extends MvpPresenter<V> implements IQrMPMCodeTambahPresenter<V> {

    private static final String TAG = "FormQRPresenter";
    protected String formUrl;
    protected String generateUrl;
    protected String statusUrl;
    protected String deleteUrl;
    protected Object requestObject = null;

    public QrMPMCodeTambahPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getData(boolean isFromFastMenu) {
        if (formUrl == null || !isViewAttached()) {
            Log.d(TAG, "getData: form null");
            return;
        }

        if (isFromFastMenu)
            requestObject = getFastMenuRequest();
        else
            requestObject = new BaseFormRequest("");

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(formUrl, requestObject, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().isHideSkeleton(true);
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().isHideSkeleton(true);
//                                try {
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    QrResponse qrResponse = response.getData(QrResponse.class);
                                    getView().onSuccesGetData(qrResponse);
                                } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    GenerateQrResponse generateQrResponse = response.getData(GenerateQrResponse.class);
                                    getView().onGenerateRequest(generateQrResponse, statusUrl, deleteUrl);
                                } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())) {
                                    getView().onException12(response.getDesc());
                                }
//                                }catch (Exception e){
//                                    getView().onException("Koneksi Terputus");
//                                }


                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().isHideSkeleton(true);
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void getDataPayment(String pin, String account, boolean fromFastMenu) {
        if (generateUrl == null) {
            Log.d(TAG, "getDataPayment: urlInformasi payment null");
            return;
        }

        if (!isViewAttached()) {
            Log.d(TAG, "getDataPayment: view null");
            return;
        }

        if (isViewAttached()) {
            //initiate param with getter from view
            getView().showProgress();

            if (fromFastMenu)
                requestObject = new FastGenerateQrRequest(getFastMenuRequest(), pin, account);
            else
                requestObject = new GenerateQrRequest(pin, account);

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getData(generateUrl, requestObject, seqNum)//function(param)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().isHideSkeleton(true);
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().isHideSkeleton(true);
                            getView().hideProgress();
                            GenerateQrResponse generateQrResponse = response.getData(GenerateQrResponse.class);
                            getView().onGenerateRequest(generateQrResponse, statusUrl, deleteUrl);


                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().isHideSkeleton(true);
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
//                            else if (restResponse.getCode().equalsIgnoreCase("93"))
//                                getView().onException93(restResponse.getDesc());
//                            else if (restResponse.getCode().equalsIgnoreCase("01"))
//                                getView().onException01(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void start() {
        super.start();
        this.getDefaultSaldo();
    }

    protected void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtama();
        if (!saldoText.equals("")) {
            saldo = Double.valueOf(saldoText);
        }

        String akunDefault = getBRImoPrefRepository().getAccountDefault();
        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();
        boolean saldoHold = getBRImoPrefRepository().getSaldoHold();

        getView().setDefaultSaldo(saldo, saldoString, akunDefault, saldoHold);
    }

    @Override
    public void setFormUrl(String formUrl) {
        this.formUrl = formUrl;
    }

    @Override
    public void setGenerateUrl(String generateUrl) {
        this.generateUrl = generateUrl;
    }

    @Override
    public void setStatusUrl(String statusUrl) {
        this.statusUrl = statusUrl;
    }

    @Override
    public void setDeleteUrl(String deleteUrl) {
        this.deleteUrl = deleteUrl;
    }
}