package id.co.bri.brimo.presenters;

import android.util.Log;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.dashboard.IProfilePresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.dashboard.IProfileView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.RevokeSessionRequest;
import id.co.bri.brimo.models.apimodel.request.bilingual.PrefrencesLanguageRequest;
import id.co.bri.brimo.models.apimodel.request.login.AktivasiBiometricRequest;
import id.co.bri.brimo.models.apimodel.request.voiceassistant.AktivasiVoiceAssistantRequest;
import id.co.bri.brimo.models.apimodel.request.voiceassistant.AktivasiVoiceAssistantRequest;
import id.co.bri.brimo.models.apimodel.request.smartrecom.SmartRecomRequest;
import id.co.bri.brimo.models.apimodel.request.smarttransfer.ManageUserSmartTransferConsentRequest;
import id.co.bri.brimo.models.apimodel.response.BripoinResponse;
import id.co.bri.brimo.models.apimodel.response.BripointDetailAccountResponse;
import id.co.bri.brimo.models.apimodel.response.ChatBankingResponse;
import id.co.bri.brimo.models.apimodel.response.ProfileResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;

import id.co.bri.brimo.models.apimodel.response.biometric.EnrollBiometricResponse;
import id.co.bri.brimo.models.apimodel.response.bripoin.BripoinCouponResponse;
import id.co.bri.brimo.models.apimodel.response.voiceassistant.SetAktivasiVoiceAssistantResponse;
import id.co.bri.brimo.models.apimodel.response.voiceassistant.GetAktivasiVoiceAssistantResponse;
import id.co.bri.brimo.models.apimodel.response.voiceassistant.SetAktivasiVoiceAssistantResponse;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferConfirmAccBinding;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferUserConsentData;
import id.co.bri.brimo.models.apimodel.response.voip.CategoryVoipRes;
import id.co.bri.brimo.models.apimodel.response.profilerevamp.InfoTetangBrimoResp;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class ProfilePresenter<V extends IMvpView & IProfileView> extends MvpPresenter<V> implements IProfilePresenter<V> {

    private static final String TAG = "ProfilePresenter";

    protected String formAkunUrl;
    protected String logoutUrl;
    protected String urlProfilBripoint;
    protected String urlBripoint;
    protected String urlChatBanking;
    protected String urlVoip;
    protected String urlFastMenu = "";
    private String urlRevoke;
    private String mUrlPrefrences;
    private String urlUpdateAktivasiVoiceAssistant;

    private String urlBiometric;
    private String urlRemoveBio;
    private String urlBripoinCoupon;
    protected String urlCheckUserConsent = "";
    protected String urlSmartTransferManageUserConsent = "";

    protected String urlTentangBrimo;

    public ProfilePresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                            BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                            TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlBripoinCoupon(String urlBripoinCoupon) {
        this.urlBripoinCoupon = urlBripoinCoupon;
    }

    @Override
    public void onGetDetailAkun() {
        if (formAkunUrl == null || !isViewAttached()) {
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataTanpaRequest(formAkunUrl, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onRefreshSwipe();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().onRefreshSwipe();
                                //TO-DO onSuccess
                                ProfileResponse profileResponse = response.getData(ProfileResponse.class);
                                getView().onSuccessGetProfile(profileResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().onRefreshSwipe();
                                getView().onException(restResponse.getDesc());
                            }
                        })
        );

    }

    @Override
    public void setFormAkunUrl(String formAkunUrl) {
        this.formAkunUrl = formAkunUrl;
    }

    @Override
    public void logOut() {
        if (logoutUrl == null || !isViewAttached()) {
            if (!GeneralHelper.isProd()) {
                Log.d(TAG, "logOut: urlInformasi empty");
            }
            return;
        }
        //initiate param with getter from view
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getView().showProgress();
        Disposable disposable = getApiSource().onLogout(logoutUrl, seqNum)//function(param)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onRefreshSwipe();
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        updateLoginFlag(false);
                        getView().onRefreshSwipe();
                        getView().hideProgress();
                        getView().onLogOut(response.getDesc());
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        updateLoginFlag(false);
                        getView().onRefreshSwipe();
                        getView().hideProgress();
                        getView().onLogOut("");
                    }
                });

        getCompositeDisposable().add(disposable);

    }

    @Override
    public void setUrlProfilBripoint(String urlProfilBripoint) {
        this.urlProfilBripoint = urlProfilBripoint;
    }

    @Override
    public void onGetProfilBripoint() {
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataTanpaRequest(urlProfilBripoint, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onRefreshSwipe();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().onRefreshSwipe();
                                BripointDetailAccountResponse briPointResponse = response.getData(BripointDetailAccountResponse.class);
                                getView().onSuccessLoadProfilBripoint(briPointResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().onRefreshSwipe();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onExceptionProfil(restResponse.getDesc());

                            }
                        })
        );
    }

    @Override
    public void setUrlBripoint(String urlBripoint) {
        this.urlBripoint = urlBripoint;
    }

    @Override
    public void onGetBripoint() {
        if (isViewAttached()) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(
                    getApiSource().getDataTanpaRequest(urlBripoint, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().single())
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onRefreshSwipe();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().onRefreshSwipe();
                                    BripoinResponse briPointResponse = response.getData(BripoinResponse.class);
                                    getView().onGetPoint(briPointResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().onRefreshSwipe();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onExceptionBriPoin(restResponse.getDesc());

                                }
                            })
            );
        }
    }

    @Override
    public void setUrlChatBanking(String urlChatBanking) {
        this.urlChatBanking = urlChatBanking;
    }

    @Override
    public void onGetChatBanking() {
        if (isViewAttached()) {
            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(
                    getApiSource().getDataTanpaRequest(urlChatBanking, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    ChatBankingResponse chatBankingResponse = response.getData(ChatBankingResponse.class);
                                    getView().onSuccessChatBanking(chatBankingResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException12(restResponse.getDesc());

                                }
                            })
            );
        }
    }

    @Override
    public void setUrlVoip(String url) {
        this.urlVoip = url;
    }

    @Override
    public void getVoip() {
        if (urlVoip == null && !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getDataTanpaRequest(urlVoip, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                CategoryVoipRes categoryVoipRes = response.getData(CategoryVoipRes.class);
                                getView().onSuccessGetListVoip(categoryVoipRes);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        }));
    }

    @Override
    public void getInitiateResource() {
        getView().onInitiateResourceSuccess(getBRImoPrefRepository().getUsername(),
                getBRImoPrefRepository().getTokenKey());
    }

    @Override
    public void setUrlEnrollBiometric(String urlBio) {
        urlBiometric = urlBio;
    }

    @Override
    public void setUrlRemoveBiometric(String urlRemove) {
        urlRemoveBio = urlRemove;
    }

    @Override
    public void updateStatusAktivasi(boolean statusAktivasi) {
        getBRImoPrefRepository().saveStatusAktivasi(statusAktivasi);
    }

    @Override
    public void getRemoveBiometric(String pin, String checkSum) {
        if (urlRemoveBio == null && !isViewAttached()) return;

        getView().showProgress();
        AktivasiBiometricRequest aktivasiBiometricRequest = new AktivasiBiometricRequest(pin, checkSum);

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlRemoveBio, aktivasiBiometricRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase("00"))
                                    getView().onSuccessRemoveBiometric();
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase("12")){
                                    getView().onException12Bio(restResponse.getDesc());
                                } else {
                                    getView().onExceptionBio(restResponse.getDesc());
                                }
                            }
                        })
        );
    }

    @Override
    public void getDataBiometric(String pin, String checkSum) {
        if (urlBiometric == null && !isViewAttached()) return;

        getView().showProgress();
        AktivasiBiometricRequest aktivasiBiometricRequest = new AktivasiBiometricRequest(pin, checkSum);

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlBiometric, aktivasiBiometricRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase("00")){
                                    EnrollBiometricResponse enrollBiometricResponse = response.getData(EnrollBiometricResponse.class);
                                    getView().onSuccessAktifBiometric(enrollBiometricResponse);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase("12")){
                                    getView().onException12Bio(restResponse.getDesc());
                                } else {
                                    getView().onExceptionBio(restResponse.getDesc());
                                }
                            }
                        })
        );
    }

    @Override
    public void updateBioChanged(Boolean bioChanged) {
        getBRImoPrefRepository().saveStatusBioChange(bioChanged);
    }

    @Override
    public void updateStatusUpdateBio(Boolean statusUpdate) {
        getBRImoPrefRepository().saveStatusUpdateBio(statusUpdate);
    }

    @Override
    public void setUrlRevoke(String urlRevoke) {
        this.urlRevoke = urlRevoke;
    }

    @Override
    public void revokeSession(RevokeSessionRequest request) {
        if (urlRevoke == null || !isViewAttached())
            return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlRevoke, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getBripoinCoupon() {
        if (urlBripoinCoupon == null || !isViewAttached())
            return;

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataTanpaRequest(urlBripoinCoupon, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            protected void onFailureHttp(String message) {
                                getView().onException(message);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                BripoinCouponResponse bripoinCouponResponse = response.getData(BripoinCouponResponse.class);
                                getView().onSuccessBripoinCoupon(bripoinCouponResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().onFailedBripoinCoupon(restResponse.getCode());
                            }
                        })
        );
    }

    @Override
    public void setUrlCheckSmartTransfer(String urlCheckUserConsent) {
        this.urlCheckUserConsent = urlCheckUserConsent;
    }

    @Override
    public void checkSmartTransfer() {
        if (isViewAttached()){
            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            SmartRecomRequest request = new SmartRecomRequest(getBRImoPrefRepository().getUsername());

            getCompositeDisposable().add(getApiSource().getData(urlCheckUserConsent, request, seqNum)
                    .subscribeOn(getSchedulerProvider().io())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            SmartTransferUserConsentData smartTransferUserConsentData = response.getData(SmartTransferUserConsentData.class);
                            if (smartTransferUserConsentData.getConsent().getApproved() == 0)
                                getView().onSuccessGetSmartTransferFirstVisit(smartTransferUserConsentData.getTnc());
                            else getView().onSuccessGetSmartTransferUserConsent(smartTransferUserConsentData);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase("NW"))
                                getView().onFailedGetSmartTransferUserConsent();
                            else
                                getView().onException12(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void setUrlSmartTransferManageUserConsent(String urlManageUserConsent) {
        urlSmartTransferManageUserConsent = urlManageUserConsent;
    }

    @Override
    public void smartTransferManageUserConsent(Boolean status) {
        if (isViewAttached()){
            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            ManageUserSmartTransferConsentRequest request = new ManageUserSmartTransferConsentRequest(status, getBRImoPrefRepository().getUsername());

            getCompositeDisposable().add(getApiSource().getData(urlSmartTransferManageUserConsent, request, seqNum)
                    .subscribeOn(getSchedulerProvider().io())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            SmartTransferConfirmAccBinding smartTransferConfirmAccBinding = response.getData(SmartTransferConfirmAccBinding.class);
                            getView().onSuccessSmartTransferManageUserConsent(smartTransferConfirmAccBinding);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase("05"))
                                getView().onSessionEnd(restResponse.getDesc());
                            else {
                                getView().onException12(restResponse.getDesc());
                            }
                        }
                    }));
        }
    }

    @Override
    public String onGetBiometricType() {
        return getBRImoPrefRepository().getBiometricType();
    }

    @Override
    public String getValueKeyBiometric() {
        return getBRImoPrefRepository().getValueKeyBiometric();
    }

    @Override
    public Boolean getBioChanged() {
        return getBRImoPrefRepository().getStatusBioChange();
    }

    @Override
    public Boolean getStatusAktivasi() {
        return getBRImoPrefRepository().getStatusAktivasi();
    }

    @Override
    public void start() {
        super.start();
        onGetProfilBripoint();
        onGetBripoint();
    }

    @Override
    public void setLogoutUrl(String logoutUrl) {
        this.logoutUrl = logoutUrl;
    }

    @Override
    public void setUrlPrefrences(String urlPrefrences) {
        mUrlPrefrences = urlPrefrences;
    }

    //need to be review
    @Override
    public void updatePrefrencesLanguage(String id) {
       getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        PrefrencesLanguageRequest request = new PrefrencesLanguageRequest(getFastMenuRequest(), id);
        getCompositeDisposable().add(
                getApiSource().getData(mUrlPrefrences, request, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                getBRImoPrefRepository().saveLanguage(id);
                                getView().onSuccessChangeLanguage();
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                onApiError(restResponse);
                            }
                        })
        );
    }

@Override
    public void setUrlTentangBrimo(String urlTentangBrimo) {
        this.urlTentangBrimo = urlTentangBrimo;
    }

    @Override
    public void getDataTentangBrimo() {
        if (urlTentangBrimo == null && !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getDataTanpaRequest(urlTentangBrimo, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                InfoTetangBrimoResp infoTetangBrimo = response.getData(InfoTetangBrimoResp.class);
                                getView().onSuccessDataTentangBrimo(infoTetangBrimo);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                getView().onException(restResponse.getDesc());
                            }
                        }));
    }

//    @Override
//    public void setUrlUpdateAktivasiVoiceAssistant(String url) {
//        this.urlUpdateAktivasiVoiceAssistant = url;
//    }
//
//    @Override
//    public Boolean getAktivasiVoiceAssistant() {
//        return getBRImoPrefRepository().getAktivasiVoiceAssistant();
//    }
//
//    @Override
//    public void updateAktivasiVoiceAssistant(Boolean newStatus, String pin) {
//        if (isViewAttached()) {
//            String seqNum = getBRImoPrefRepository().getSeqNumber();
//            AktivasiVoiceAssistantRequest aktivasiVoiceAssistantRequest = new AktivasiVoiceAssistantRequest(getBRImoPrefRepository().getUsername(), pin, newStatus);
//
//            getView().showProgress();
//            getCompositeDisposable().add(
//                    getApiSource().getData(urlUpdateAktivasiVoiceAssistant, aktivasiVoiceAssistantRequest, seqNum)
//                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
//                            .subscribeOn(Schedulers.io())
//                            .observeOn(AndroidSchedulers.mainThread())
//                            .subscribeWith(new ApiObserver(getView(),seqNum) {
//
//                                @Override
//                                protected void onFailureHttp(String type) {
//                                    getView().hideProgress();
//                                    getView().onException(type);
//                                }
//
//                                @Override
//                                protected void onApiCallSuccess(RestResponse response) {
//                                    getView().hideProgress();
//                                    SetAktivasiVoiceAssistantResponse setAktivasiVoiceAssistantResponse = response.getData(SetAktivasiVoiceAssistantResponse.class);
//
//                                    if (setAktivasiVoiceAssistantResponse.getResultRow() == 1) {
//                                        getBRImoPrefRepository().saveAktivasiVoiceAssistant(newStatus);
//                                        getView().onSuccessSetAktivasiVoiceAssistant(newStatus);
//                                    } else {
//                                        getView().onExceptionAktivasiVoiceAssistant(newStatus);
//                                    }
//                                }
//
//                                @Override
//                                protected void onApiCallError(RestResponse restResponse) {
//                                    getView().hideProgress();
//                                    if (restResponse.getCode().equalsIgnoreCase("05"))
//                                        getView().onSessionEnd(restResponse.getDesc());
//                                    else if (restResponse.getCode().equalsIgnoreCase("12"))
//                                        getView().onException12(restResponse.getDesc());
//                                    else
//                                        getView().onException(restResponse.getDesc());
//                                }
//                            })
//            );
//        }
//    }
}