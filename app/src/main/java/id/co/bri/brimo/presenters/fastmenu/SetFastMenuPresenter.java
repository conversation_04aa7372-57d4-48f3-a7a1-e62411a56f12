package id.co.bri.brimo.presenters.fastmenu;


import static id.co.bri.brimo.domain.helpers.GeneralHelper.getString;

import android.os.Build;
import android.util.Log;

import androidx.core.util.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.fastmenu.ISetFastMenuPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.fastmenu.ISetFastMenuView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.fastmenu.FastMenuSource;
import id.co.bri.brimo.data.repository.fastmenudefault.FastMenuDefaultSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.MenuConfig;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.EditFastMenuPinRequest;
import id.co.bri.brimo.models.apimodel.request.EditFastMenuRevampRequest;
import id.co.bri.brimo.models.apimodel.response.FastMenuRevampResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.daomodel.FastMenu;
import id.co.bri.brimo.models.daomodel.FastMenuDefault;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.CompletableObserver;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.annotations.NonNull;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class SetFastMenuPresenter<V extends IMvpView & ISetFastMenuView> extends MvpPresenter<V> implements ISetFastMenuPresenter<V> {


    private static final String TAG = "SetFastMenuPresenter";
    private FastMenuSource fastMenuSource;

    private FastMenuDefaultSource fastMenuDefaultSource;
    protected String pinUrl;
    protected Object request;
    protected String urlFastMenu = "";
    protected String logoutUrl;
    private List<FastMenuDefault> fastMenuDefaultList = new ArrayList<>();
    private boolean isDelete = false;
    private boolean isUpdate = false;
    private final List<FastMenuDefault> allFastMenuList = new ArrayList<>();
    private boolean isFromDrawer;
    protected String namaPengguna;
    protected String kataSandi;
    private boolean isLogOut = false;
    private boolean isBack = false;
    private boolean containsUnavailableListToggled = false;
    private Disposable disposable = null;
    private Boolean isNfcAvailable = false;
    private String location = "";

    public SetFastMenuPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource, FastMenuSource fastMenuSource, FastMenuDefaultSource fastMenuDefaultSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        this.fastMenuSource = fastMenuSource;
        this.fastMenuDefaultSource = fastMenuDefaultSource;
    }

    @Override
    public void getDefaultFastMenu() {
        getCompositeDisposable().add(fastMenuSource.getFastMenu()
                .subscribeOn(getSchedulerProvider().single())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribe(fastMenus -> {
                    if (fastMenus != null) {
                        getView().onLoadDefaultFastmenu(fastMenus);
                    }
                }, throwable -> {
                    if (!GeneralHelper.isProd()) {
                        Log.e(TAG, "getDefaultFastMenu: ", throwable);
                    }
                }));

    }

    @Override
    public void getSavedFastMenu() {
        getCompositeDisposable().add(fastMenuDefaultSource.getFastMenuDefault()
                .subscribeOn(getSchedulerProvider().single())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribe(fastMenuDefaults -> {
                }, throwable -> {
                    if (!GeneralHelper.isProd()) {
                        Log.e(TAG, "getSavedFastMenu: ", throwable);
                    }
                }));


    }

    // Get Fast Menu From Local DB
    @Override
    public void getSavedFastMenuFinal(FastMenuRevampResponse response) {
        getCompositeDisposable().add(fastMenuDefaultSource.getAllFastMenuDefaultFinal()
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribe(fastMenuDefaults -> {
                    if (fastMenuDefaults != null) {
                        allFastMenuList.clear();
                        allFastMenuList.addAll(fastMenuDefaults);

                        List<FastMenuDefault> mList;
                        mList = compareDataDefault(MenuConfig.fetchFastMenuWithNfcMenu(isNfcAvailable), allFastMenuList);

                        // Comparing list from BE and local DB.
                        Pair<List<FastMenuDefault>, List<FastMenuDefault>> listPair;
                        listPair = compareDatas(mList, response);

                        fastMenuDefaultList.clear();
                        fastMenuDefaultList.addAll(listPair.first);

                        getView().onSuccessComparing(listPair);
                    }
                }, throwable -> {
                    if (!GeneralHelper.isProd()) {
                        Log.e(TAG, "getSavedFastMenuFinal: ", throwable);
                    }
                }));
    }

    @Override
    public void getDeleteFastMenu(List<FastMenu> fastMenuList) {
        fastMenuSource.deleteAll()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new CompletableObserver() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        // Do nothing.
                    }

                    @Override
                    public void onComplete() {
                        getView().onSuccessDeleted(fastMenuList);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // Do nothing.
                    }
                });

    }

    @Override
    public void deleteFastMenuDefault(List<FastMenuDefault> fastMenuDefaultList) {
        fastMenuDefaultSource.deleteAll()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new CompletableObserver() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        // Do nothing.
                    }

                    @Override
                    public void onComplete() {
                        getView().onSuccessDeletedFastMenuDefault(fastMenuDefaultList);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // Do nothing.
                    }
                });
    }

    @Override
    public void deleteFastMenuDefaultCompared(List<FastMenuDefault> fastMenuDefaultList) {
        if (isDelete) {
            return;
        }

        fastMenuDefaultSource.deleteAll()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new CompletableObserver() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        // Do nothing.
                    }

                    @Override
                    public void onComplete() {
                        isDelete = true;
                        getView().onSuccessDeletedFastMenuDefaultCompared(fastMenuDefaultList);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // Do nothing.
                    }
                });
    }


    @Override
    public void getDeletedById(String id) {
        fastMenuSource.deleteById(id)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new CompletableObserver() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        // Do nothing.
                    }

                    @Override
                    public void onComplete() {
                        getView().onSuccessDeletedById();
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // Do nothing.
                    }
                });
    }

    @Override
    public void getUpdateFastMenu(List<FastMenu> fastMenuList) {
        fastMenuSource.saveFastMenu(fastMenuList)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new CompletableObserver() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        // Do nothing.
                    }

                    @Override
                    public void onComplete() {
                        getView().onResultSuccess();
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // Do nothing.
                    }
                });

    }

    @Override
    public void getUpdateFastMenuDefault(List<FastMenuDefault> fastMenuListDefault) {
        fastMenuDefaultSource.saveFastMenu(fastMenuListDefault)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new CompletableObserver() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        // Do nothing.
                    }

                    @Override
                    public void onComplete() {
                        getView().onResultSuccess();
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // Do nothing.
                    }
                });
    }

    @Override
    public void getUpdateFastMenuDefaultCompared(List<FastMenuDefault> fastMenuListDefault) {
        List<FastMenuDefault> menuList = new ArrayList<>(fastMenuListDefault);

        if (isUpdate) {
            return;
        }

        fastMenuDefaultSource.saveFastMenu(menuList)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new CompletableObserver() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        // Do nothing.
                    }

                    @Override
                    public void onComplete() {
                        getSavedFastMenuFinal();
                        isUpdate = true;
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        getView().onAutomaticallyBack();
                    }
                });
    }

    @Override
    public void getUpdateFastMenuDefaultInitial(List<FastMenuDefault> fastMenuListDefault) {
        fastMenuDefaultSource.saveFastMenu(fastMenuListDefault)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new CompletableObserver() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        // Do nothing.
                    }

                    @Override
                    public void onComplete() {
                        // Do nothing.
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // Do nothing.
                    }
                });
    }

    @Override
    public void setPinUrl(String pinUrl) {
        this.pinUrl = pinUrl;
    }

    @Override
    public void setUrlFastMenu(String urlFastMenu) {
        this.urlFastMenu = urlFastMenu;
    }

    @Override
    public void getSimpan(String fastMenuList, String pin) {
        if (pinUrl == null) {
            Log.d(TAG, "getDataPayment: urlInformasi payment null");
            return;
        }

        if (!isViewAttached()) {
            Log.d(TAG, "getDataPayment: view null");
            return;
        }

        if (isViewAttached()) {
            //set flag Loading

            getView().showProgress();
            request = new EditFastMenuPinRequest(fastMenuList, pin);
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            disposable = getApiSource().getData(pinUrl, request, seqNum)//function(param)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            getView().onSuccessPinSimpan();

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onException12(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void getSimpanFastMenuRevamp(String fastMenuList, String pin) {
        if (pinUrl == null) {
            return;
        }

        if (!isViewAttached()) {
            return;
        }

        if (isViewAttached()) {
            getView().showProgress();
            request = new EditFastMenuRevampRequest(fastMenuList, pin, getFastMenuRequest());
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            disposable = getApiSource().getData(pinUrl, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onExceptionEditFastMenuRevamp(errorMessage, true);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                getView().onSuccessPinSimpan();
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onException12(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_04.getValue()))
                                getView().onExceptionNotAllowed(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });
            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void updateListFastMenuDefault(String fastMenuDefault) {
        getBRImoPrefRepository().saveListFastMenuDefault(fastMenuDefault);
    }

    @Override
    public String fetchListFastMenuDefault() {
        return getBRImoPrefRepository().getListFastMenuDefault();
    }

    @Override
    public void getSavedFastMenuFinal() {
        getCompositeDisposable().add(fastMenuDefaultSource.getAllFastMenuDefaultFinal()
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribe(fastMenuDefaults -> {
                    if (fastMenuDefaults != null) {
                        getView().onLoadSavedFastmenu(fastMenuDefaults);
                    }
                }, throwable -> {
                    if (!GeneralHelper.isProd()) {
                        Log.e(TAG, "getSavedFastMenuFinal: ", throwable);
                    }
                }));
    }

    @Override
    public void setFromDrawer(boolean isFromDrawer) {
        this.isFromDrawer = isFromDrawer;
    }

    @Override
    public void start() {
        super.start();
        if (isFromDrawer) {
            onGetFastMenuDefaultWithRequest();
        } else {
            onGetFastMenuDefaultEmptyRequest();
        }
    }

    public void onGetFastMenuDefaultEmptyRequest() {
        if (urlFastMenu.isEmpty())
            return;

        if (isViewAttached()) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(
                    getApiSource().getDataTanpaRequest(urlFastMenu, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().hideSkeleton(false);
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    FastMenuRevampResponse fmResponse = response.getData(FastMenuRevampResponse.class);
                                    getSavedFastMenuFinal(fmResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    getView().hideSkeleton(false);
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                        getView().onExceptionBackToPrevious(restResponse.getDesc());
                                    } else {
                                        getView().onException(restResponse.getDesc());
                                    }
                                }
                            })
            );
        }
    }

    public void onGetFastMenuDefaultWithRequest() {
        if (getView() == null)
            return;

        String mNamaPengguna = "";

        if (getBRImoPrefRepository().getUserAlias() != null && !getBRImoPrefRepository().getUserAlias().isEmpty()) {
            mNamaPengguna = getBRImoPrefRepository().getUserAlias();
        } else {
            mNamaPengguna = namaPengguna;
        }

        if (!validateValueUsername(mNamaPengguna) || !validateValueRequest(kataSandi)) {
            String message = getString(R.string.isi_username_dan_password);
            getView().onException(message);
            return;
        }

        String finalNamaPengguna = mNamaPengguna;

        if (urlFastMenu.isEmpty())
            return;

        if (isViewAttached()) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().validateUserLogin(urlFastMenu, finalNamaPengguna, kataSandi, location, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    if (response.getCode().equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        FastMenuRevampResponse fmResponse = response.getData(FastMenuRevampResponse.class);
                                        getSavedFastMenuFinal(fmResponse);
                                    } else if (response.getCode().equals(RestResponse.ResponseCodeEnum.RC_09.getValue())) {
                                        getView().onExceptionEditFastMenuChangeDevice(response.getDesc());
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                        getView().onExceptionBackToPrevious(restResponse.getDesc());
                                    } else {
                                        getView().onException(restResponse.getDesc());
                                    }
                                }
                            })
            );
        }
    }

    private Pair<List<FastMenuDefault>, List<FastMenuDefault>> compareDatas(List<FastMenuDefault> defaultList, FastMenuRevampResponse response) {
        List<FastMenuDefault> listLocal = new ArrayList<>(defaultList);
        List<FastMenuRevampResponse.FastMenuRevampModel> listResponse = new ArrayList<>(response.getFastMenuRevampModelList());

        List<FastMenuDefault> listResult = new ArrayList<>();
        List<FastMenuDefault> listNotAvailable = new ArrayList<>();

        int counter = 0;
        int position;

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            List<String> listId = listResponse.stream().map(FastMenuRevampResponse.FastMenuRevampModel::getId).collect(Collectors.toList());

            for (int i = 0; i < listLocal.size(); i++) {
                boolean found = false;
                for (String id : listId) {
                    if (listLocal.get(i).getKode().equalsIgnoreCase(id)) {
                        if (listLocal.get(i).isToggled()) {
                            position = counter;
                        } else {
                            position = -1;
                        }
                        listLocal.get(i).setAvailable(true);
                        listLocal.get(i).setPosition(position);
                        listResult.add(new FastMenuDefault(listLocal.get(i).getId(), listLocal.get(i).getKode(), listLocal.get(i).getMenuName(),
                                listLocal.get(i).getGambarMenu(), listLocal.get(i).getMenu(), listLocal.get(i).getTag(), listLocal.get(i).isFlagNew(),
                                listLocal.get(i).getPosition(), listLocal.get(i).isToggled(), listLocal.get(i).isAvailable()));
                        found = true;
                        counter++;
                        break;
                    }
                }

                if (!found) {
                    listNotAvailable.add(new FastMenuDefault(listLocal.get(i).getId(), listLocal.get(i).getKode(), listLocal.get(i).getMenuName(),
                            listLocal.get(i).getGambarMenu(), listLocal.get(i).getMenu(), listLocal.get(i).getTag(), listLocal.get(i).isFlagNew(),
                            listLocal.get(i).getPosition(), listLocal.get(i).isToggled(), listLocal.get(i).isAvailable()));
                    listLocal.get(i).setAvailable(false);
                    listLocal.get(i).setToggled(false); // To re-check.
                    listLocal.get(i).setPosition(-1);
                    listResult.add(new FastMenuDefault(listLocal.get(i).getId(), listLocal.get(i).getKode(), listLocal.get(i).getMenuName(),
                            listLocal.get(i).getGambarMenu(), listLocal.get(i).getMenu(), listLocal.get(i).getTag(), listLocal.get(i).isFlagNew(),
                            listLocal.get(i).getPosition(), listLocal.get(i).isToggled(), listLocal.get(i).isAvailable()));
                }
            }
        }

        return new Pair<>(listResult, listNotAvailable);
    }

    private List<FastMenuDefault> compareDataDefault(List<FastMenuDefault> mConfigList, List<FastMenuDefault> mLocalList) {
        List<FastMenuDefault> configList = new ArrayList<>(mConfigList);
        List<FastMenuDefault> localList = new ArrayList<>(mLocalList);
        List<FastMenuDefault> resultList = new ArrayList<>(localList);

        for (int i = 0; i < configList.size(); i++) {
            boolean found = false;
            for (int j = 0; j < localList.size(); j++) {
                if (configList.get(i).getKode().equalsIgnoreCase(localList.get(j).getKode())) {
                    found = true;
                    break;
                }
            }

            if (!found) {
                configList.get(i).setAvailable(false);
                configList.get(i).setToggled(false);
                configList.get(i).setPosition(-1);
                resultList.add(new FastMenuDefault(configList.get(i).getId(), configList.get(i).getKode(), configList.get(i).getMenuName(),
                        configList.get(i).getGambarMenu(), configList.get(i).getMenu(), configList.get(i).getTag(), configList.get(i).isFlagNew(),
                        configList.get(i).getPosition(), configList.get(i).isToggled(), configList.get(i).isAvailable()));
            }
        }

        return resultList;
    }

    public void setNamaPengguna(String namaPengguna) {
        this.namaPengguna = namaPengguna;
    }

    public void setKataSandi(String kataSandi) {
        this.kataSandi = kataSandi;
    }

    @Override
    public void setLogoutUrl(String url) {
        this.logoutUrl = url;
    }

    @Override
    public void logOut(boolean isBack, boolean containsUnavailableListToggled) {
        if (logoutUrl == null || !isViewAttached()) {
            if (!GeneralHelper.isProd()) {
                Log.d(TAG, "logoutUrl is null.");
            }
            return;
        }

        if (isViewAttached()) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().onLogout(logoutUrl, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            updateLoginFlag(false);
                            getView().hideProgress();
                            getView().onLogOut(response.getDesc());
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            updateLoginFlag(false);
                            getView().hideProgress();
                            getView().onLogOut("");
                        }
                    })
            );
            this.isLogOut = true;
            this.isBack = isBack;
            this.containsUnavailableListToggled = containsUnavailableListToggled;
        }
    }

    @Override
    public boolean getLogoutStatus() {
        return isLogOut;
    }

    @Override
    public boolean getBackStatus() {
        return isBack;
    }

    @Override
    public boolean containsUnavailableListToggled() {
        return containsUnavailableListToggled;
    }

    @Override
    public void checkAvailabilityNfc(boolean isNfcAvailable) {
        this.isNfcAvailable = isNfcAvailable;
    }

    @Override
    public void getLocation(String location) {
        this.location = location;
    }

    @Override
    public void resetUpdate() {
        isUpdate = false;
        isDelete = false;
    }

    @Override
    public void stop() {
        super.stop();
        if (disposable != null) {
            disposable.dispose();
        }
    }
}