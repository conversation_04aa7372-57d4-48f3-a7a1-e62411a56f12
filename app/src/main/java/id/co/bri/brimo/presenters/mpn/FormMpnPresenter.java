package id.co.bri.brimo.presenters.mpn;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.mpn.IFormMpnPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.mpn.IFormMpnView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.InquiryKaiRequest;
import id.co.bri.brimo.models.apimodel.response.FormMpnResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class FormMpnPresenter <V extends IMvpView & IFormMpnView> extends MvpPresenter<V> implements IFormMpnPresenter<V> {
    protected String formurl, inquiryUrl;

    public FormMpnPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlForm(String urlForm) {
        formurl = urlForm;
    }

    @Override
    public void getDataForm() {
        if (formurl == null || !isViewAttached()) {
//            Log.d(TAG, "getDataInquiry: view atau inquiry url null");
            return;
        }
        //view.showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Disposable disposable = getApiSource().getDataForm(formurl,seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {


                    @Override
                    protected void onFailureHttp(String errorMessage) {
//                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
//                        getView().hideProgress();
                        FormMpnResponse formMpnResponse = response.getData(FormMpnResponse.class);
                        getView().onSuccessgetDataForm(formMpnResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
//                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                });
        getCompositeDisposable().add(disposable);
    }

    @Override
    public void getDataInquiry(String noBilling) {
        if (isViewAttached()) {
            getView().showProgress();

            InquiryKaiRequest inquiryRequest = new InquiryKaiRequest(noBilling);
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(inquiryUrl, inquiryRequest, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();

                            GeneralInquiryResponse responseMpn = response.getData(GeneralInquiryResponse.class);
                            getView().onSuccessInquiry(responseMpn);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                getView().onException99(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void setInquiryUrl(String url) {
        inquiryUrl = url;
    }
}