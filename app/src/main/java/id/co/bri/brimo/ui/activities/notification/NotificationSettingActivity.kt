package id.co.bri.brimo.ui.activities.notification

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.notificationsetting.NotificationSettingListAdapter
import id.co.bri.brimo.databinding.ActivityNotificationSettingBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.notificationsetting.NotificationOptions
import id.co.bri.brimo.models.apimodel.response.notificationsetting.NotificationSettingResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity

class NotificationSettingActivity : BaseActivity() {

    private lateinit var binding: ActivityNotificationSettingBinding
    private val notificationSettingListAdapter = NotificationSettingListAdapter()
    private val gson = Gson()
    private lateinit var notification: NotificationSettingResponse
    private lateinit var titleToolbar: String
    private lateinit var selectedItem: NotificationOptions

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityNotificationSettingBinding.inflate(layoutInflater)
        setContentView(binding.root)

        notification = gson.fromJson(
            intent.getStringExtra(Constant.TAG_CONTENT), NotificationSettingResponse::class.java
        )

        setupView()
    }

    private fun setupView() {
        //set blue bar
        setStatusColor(R.color.primary_blue80)

        titleToolbar = notification.title
        GeneralHelper.setToolbarRevamp(
            this, binding.toolbar.toolbar, titleToolbar
        )
        binding.tvSubtitle.text = notification.subTitle
        Glide.with(binding.root.context)
            .load(notification.notificationIcon)
            .into(binding.ivNotification)
        setupAdapter(notification.notificationOptions)
    }

    private fun setupAdapter(notificationOptions: MutableList<NotificationOptions>) {
        binding.rvType.apply {
            View.VISIBLE
            layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            adapter = notificationSettingListAdapter
            notificationSettingListAdapter.notificationOptions = notificationOptions
            notificationSettingListAdapter.clickTypeListener { option, isEnabled, _ ->
                handleToDetails(option)
            }
        }
    }

    private fun handleToDetails(notification: NotificationOptions) {
        selectedItem = notification
        val responseJson = gson.toJson(notification)
        val intent = Intent(this, NotificationSettingDetailActivity::class.java)
        intent.putExtra(Constant.TAG_CONTENT, responseJson)
        startActivityIntent.launch(intent)
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                // There are no request codes
                // val data: Intent? = result.data
            } else {
                if (result.data != null) {
                    setResult(RESULT_CANCELED, result.data)
                    finish()
                }
            }
        }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            val gson = Gson()
            val updatedItem =
                gson.fromJson(
                    data?.getStringExtra(Constant.TAG_CONTENT),
                    NotificationOptions::class.java
                )
            if (updatedItem != null) {
                val index =
                    notification.notificationOptions.indexOfFirst { it.type == updatedItem.type }
                if (index != -1) {
                    notification.notificationOptions[index] = updatedItem
                    notificationSettingListAdapter.notifyItemChanged(index)
                }
            }
        }
    }
}