package id.co.bri.brimo.ui.activities;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.SystemClock;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListRekeningAdapter2;
import id.co.bri.brimo.contract.IPresenter.saldo.IRekeningPresenter;
import id.co.bri.brimo.contract.IView.saldo.IRekeningView;
import id.co.bri.brimo.data.preference.BRImoPrefRepository;
import id.co.bri.brimo.databinding.ActivityRekeningBinding;
import id.co.bri.brimo.databinding.FragmentBottomSheetInfoRekeningRencanaBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.SharedPreferencesHelper;
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase;
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder;
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseListener;
import id.co.bri.brimo.models.VallasModel;
import id.co.bri.brimo.models.apimodel.response.ListRekeningResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.britamajunio.DetailRekeningJunioActivity;
import id.co.bri.brimo.ui.activities.britamarencana.DetailRencanaActivity;
import id.co.bri.brimo.ui.activities.bukarekening.TabunganActivity;
import id.co.bri.brimo.ui.activities.bukarekeningreskin.TabunganNewSkinActivity;
import id.co.bri.brimo.ui.activities.dashboardInvestasi.DashboardInvestasiActivity;
import id.co.bri.brimo.ui.activities.detailkartunewskin.HomeCardActivity;
import id.co.bri.brimo.ui.activities.inforekeningnewskin.InformasiRekeningActivity;
import id.co.bri.brimo.ui.activities.simpedes.InfoSimpedesActivity;
import id.co.bri.brimo.ui.activities.transactionlimitinformation.TransactionLimitInformationActivity;
import id.co.bri.brimo.ui.customviews.datepicker.CustomLinearLayoutManager;
import id.co.bri.brimo.ui.fragments.VallasBottomFragment;
import id.co.bri.brimo.ui.fragments.bottomsheet.BottomSheetCustomViewGeneralFragment;

public class RekeningActivity extends BaseActivity implements IRekeningView, SwipeRefreshLayout.OnRefreshListener,
        VallasBottomFragment.SelectVallasInterfance,
        ListRekeningAdapter2.SetTotalSaldoInterface,
        View.OnClickListener,
        ListRekeningAdapter2.ClickItemListener,
        ListRekeningAdapter2.OnBubbleCaseShow {

    private ActivityRekeningBinding binding;
    private static final String TAG = "RekeningActivity";

    @Inject
    IRekeningPresenter<IRekeningView> activityPresenter;

    ArrayList<ListRekeningResponse.Account> listitems = new ArrayList<>();
    ListRekeningAdapter2 listRekeingAdapter;

    private double totalSaldo;
    private boolean isLoading = false;
    private String currentCurrency = "Rp";
    private SkeletonScreen skeletonScreen;
    private List<String> valasList = new ArrayList<>();
    private List<ListRekeningResponse.Account> listFiltered;
    protected boolean checkDefault = false;
    BRImoPrefRepository brImoPrefRepository = new BRImoPrefRepository(this);
    protected ListRekeningResponse.Account accountRekening;
    protected BubbleShowCaseBuilder lihatRekValas;
    protected BubbleShowCaseBuilder lihatRekSimpedes;
    protected BubbleShowCaseBuilder lihatRekJunio;
    protected BubbleShowCaseBuilder lihatRekGeneralAccount;
    protected boolean isBubbleSimpedes = false;
    protected boolean isBubbleGeneralAccount = false;
    protected boolean isBubbleJunio = false;
    protected boolean isGetSaldo = false;

    protected View viewSimpedes;
    protected View viewBubbleGeneralAccount;
    protected View viewBubbleJunio;
    protected String sAccount;

    protected CustomLinearLayoutManager linearLayoutManager;
    protected boolean isTouchS3f = false;

    private long mLastClickDuration = 0;

    public static void launchIntent(Activity caller) {
        Intent intent = new Intent(caller, RekeningActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_SET_DEFAULT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityRekeningBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupRecyclerView();
        setupSkeletonScreen();
        setupSwipeRefresh();
        setupClickListeners();

        //inject presenter
        injectDependency();

        //set up toolbar
        GeneralHelper.setToolbar(this, binding.tbRekening.getRoot(), GeneralHelper.getString(R.string.balance_info));
    }

    private void setupRecyclerView() {
        linearLayoutManager = new CustomLinearLayoutManager(this);
        binding.rvRekening.setLayoutManager(linearLayoutManager);
        binding.rvRekening.setHasFixedSize(true);
        binding.rvRekening.setOnTouchListener((v, event) -> true);
    }

    private void setupSkeletonScreen() {
        listRekeingAdapter = new ListRekeningAdapter2(this, listitems, this, this, this);
        skeletonScreen = Skeleton.bind(binding.rvRekening)
                .adapter(listRekeingAdapter)
                .shimmer(true)
                .angle(5)
                .frozen(false)
                .duration(1200)
                .load(R.layout.item_skeleton_list_rekening)
                .show(); //default count is 10
    }

    private void setupSwipeRefresh() {
        binding.swipeRefreshRekening.setOnRefreshListener(this);
    }

    private void setupClickListeners() {
        binding.btnRekeningBaru.setOnClickListener(this);
        binding.llLimit.setOnClickListener(this);
    }

    /**
     * Bubble Show Case Valas Toolbar
     */
    private void addBubbleShowCase() {
        try {
            lihatRekValas = new BubbleShowCaseBuilder(this) //Activity instance
                    .title(GeneralHelper.getString(R.string.bubble_case_valas_title)) //Any title for the bubble view
                    .description(GeneralHelper.getString(R.string.bubble_case_valas_subtitle))
                    .backgroundColor(Color.WHITE)
                    .textColor(Color.BLACK)
                    .buttonTitle(GeneralHelper.getString(R.string.noLoginButton))
                    .targetView(findViewById(R.id.item_layout_background))
                    .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
                    .listener(new BubbleShowCaseListener() {
                        @Override
                        public void onSkipActionClick(@NonNull BubbleShowCase bubbleShowCase) {
                            //Do Nothing
                        }

                        @Override
                        public void onTargetClick(@NonNull BubbleShowCase bubbleShowCase) {
                            // do nothing
                        }

                        @Override
                        public void onCloseActionImageClick(@NonNull BubbleShowCase bubbleShowCase) {
                            brImoPrefRepository.saveVallasBubble(true);
                            if (viewSimpedes != null) {
                                BubbleShowSimpedes(viewSimpedes);

                            }
                            if (viewBubbleJunio != null) {
                                BubbleShowJunio(viewBubbleJunio);
                            }

                            if (Boolean.FALSE.equals(brImoPrefRepository.getInfoRencanaBottom())) {
                                showBottomInfoRencana();
                            }
                        }

                        @Override
                        public void onBackgroundDimClick(@NonNull BubbleShowCase bubbleShowCase) {
                            // do nothing
                        }

                        @Override
                        public void onBubbleClick(@NonNull BubbleShowCase bubbleShowCase) {
                            // do nothing
                        }
                    });
        } catch (Exception e) {
            // do nothing
        }
    }

    /**
     * On update TOTAL Saldo dan Saldo tiap rekekning
     *
     * @param accountList
     * @param isRefreshed
     */
    @Override
    public void onGetSaldo(List<ListRekeningResponse.Account> accountList, boolean isRefreshed) {
        totalSaldo = 0.0;

        for (ListRekeningResponse.Account account : accountList) {

            if (account.getCurrency().equalsIgnoreCase(accountList.get(0).getCurrency())) {
                if (account.getSaldoReponse() != null) {
                    if (account.getSaldoReponse().getBalance() != null) {
                        totalSaldo = totalSaldo + account.getSaldoReponse().getBalance();
                    }
                } else {
                    binding.totalSaldoIb.setText("-");
                }
            }

            currentCurrency = accountList.get(0).getCurrency();
            updateTotalSaldo(currentCurrency, totalSaldo);
        }

        listitems = (ArrayList<ListRekeningResponse.Account>) accountList;
        listRekeingAdapter.notifyDataSetChanged();

        isGetSaldo = true;
    }

    /**
     * Method digunakan untuk mengupdate total Saldo
     *
     * @param currency
     * @param total
     */
    protected void updateTotalSaldo(String currency, double total) {
        String tempCurrency;

        if (total < 0) {
            tempCurrency = "-" + currency;
            binding.totalSaldoIb.setText(GeneralHelper.formatNominalIDR(total));
        } else if (totalSaldo == 0.0) {
            tempCurrency = currency;
            binding.totalSaldoIb.setText("-");
        } else if (total == 0.0) {
            tempCurrency = currency;
            binding.totalSaldoIb.setText("-");
        } else {
            tempCurrency = currency;
            binding.totalSaldoIb.setText(GeneralHelper.formatNominalIDR(total));
        }

        binding.tvCurrency.setText(tempCurrency);

    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void onListRekening(ListRekeningResponse listRekeningResponse) {
        skeletonScreen.hide();
        listitems = listRekeningResponse.getAccount();
        binding.tbRekening.itemLayoutBackground.setVisibility(View.VISIBLE);

        checkSimpedesImpianPopupShow();
        setupRecyclerViewTouchListener();
        updateValasListAndDefaultAccount();
        updateAdapterAndFilter();
        binding.rvRekening.scheduleLayoutAnimation();

        // Show tutorial
        addBubbleShowCase();
        showBubbleIfNeeded();

        binding.tbRekening.itemLayoutBackground.setOnClickListener(view1 -> {
            if (SystemClock.elapsedRealtime() - mLastClickDuration < 1000) {
                return;
            }
            mLastClickDuration = SystemClock.elapsedRealtime();
            VallasBottomFragment vallasBottomFragment = new VallasBottomFragment(valasList, listitems, this);
            vallasBottomFragment.show(getSupportFragmentManager(), "");
        });
    }

    private void checkSimpedesImpianPopupShow() {
        for (ListRekeningResponse.Account account : listitems) {
            if (Boolean.TRUE.equals(account.getS3DreamPopupShow())) {
                isTouchS3f = true;
                break;
            }
        }
    }

    private void setupRecyclerViewTouchListener() {
        if (!isTouchS3f) {
            binding.rvRekening.setOnTouchListener((v, event) -> false);
        }
    }

    private void updateValasListAndDefaultAccount() {
        for (ListRekeningResponse.Account account : listitems) {
            if (!GeneralHelper.isContains(valasList, account.getCurrency())) {
                valasList.add(account.getCurrency());
            }

            for (VallasModel vallasModel : fetchVallas()) {
                if (GeneralHelper.isContains(vallasModel.getKode_mata(), account.getCurrency()) && account.getDefault() == 1) {
                    checkDefault = true;
                    binding.tbRekening.ivVallas.setImageResource(vallasModel.getImageView());
                    binding.tbRekening.etVallas.setText(vallasModel.getNamaMataUang());
                }
            }

            if (!checkDefault) {
                for (VallasModel vallasModel : fetchVallas()) {
                    if (GeneralHelper.isContains(vallasModel.getKode_mata(), account.getCurrency())) {
                        binding.tbRekening.ivVallas.setImageResource(vallasModel.getImageView());
                        binding.tbRekening.etVallas.setText(vallasModel.getNamaMataUang());
                    }
                }
            }
        }
    }

    private void updateAdapterAndFilter() {
        if (listitems.get(0).getDefault() == 1) {
            listRekeingAdapter.updateAdapter(listitems.get(0).getAccount(), true);
        }
        listRekeingAdapter.setItems(listitems);
        listRekeingAdapter.getFilter().filter(listitems.get(0).getCurrency());
        listRekeingAdapter.notifyDataSetChanged();
    }

    private void showBubbleIfNeeded() {
        if (Boolean.FALSE.equals(brImoPrefRepository.getVallasBubble())) {
            lihatRekValas.show();
        } else if (Boolean.TRUE.equals(brImoPrefRepository.getVallasBubble()) && Boolean.FALSE.equals(brImoPrefRepository.getInfoRencanaBottom())) {
            showBottomInfoRencana();
        }
    }

    /**
     * Callback ketika semua saldo selesai didapatkan
     */
    @Override
    public void onGetSaldoComplete() {
        isLoading = false;
        binding.swipeRefreshRekening.setEnabled(true);
        isBubbleGeneralAccount = true;
        isBubbleSimpedes = true;
        isBubbleJunio = true;
    }

    /**
     * Callback ketika mendapatkan RC General Error dari Backaend
     *
     * @param message
     */
    @Override
    public void onException12(String message) {
        Intent intentReturn = new Intent();
        intentReturn.putExtra(Constant.TAG_ERROR_MESSAGE, message);
        setResult(RESULT_CANCELED, intentReturn);
        finish();

    }

    @Override
    public void onExceptionTotalSaldo() {
        binding.totalSaldoIb.setText("-");
    }

    @Override
    public void enableButton(boolean enable) {
        binding.tbRekening.etVallas.setEnabled(enable);
    }

    @Override
    public void showSkeleton() {

    }

    @Override
    public void hideSkeleton() {

    }


    private List<VallasModel> fetchVallas() {
        List<VallasModel> vallasModels = new ArrayList<>();


        vallasModels.add(new VallasModel("AED", R.drawable.vallas_aed, Constant.VALLAS_DIRHAM));
        vallasModels.add(new VallasModel("AUD", R.drawable.vallas_aud, Constant.VALLAS_DOLLAR_AUSTRALIA));
        vallasModels.add(new VallasModel("CNY", R.drawable.vallas_cny, Constant.VALLAS_YUAN));
        vallasModels.add(new VallasModel("EUR", R.drawable.vallas_eur, Constant.VALLAS_EURO));
        vallasModels.add(new VallasModel("GBP", R.drawable.vallas_gbp, Constant.VALLAS_POUND));
        vallasModels.add(new VallasModel("HKD", R.drawable.vallas_hkd, Constant.VALLAS_DOLLAR_HONGKONG));
        vallasModels.add(new VallasModel("JPY", R.drawable.vallas_jpy, Constant.VALLAS_YEN));
        vallasModels.add(new VallasModel("Rp", R.drawable.vallas_idr, Constant.VALLAS_RUPIAH));
        vallasModels.add(new VallasModel("SAR", R.drawable.vallas_sar, Constant.VALLAS_RIYAL));
        vallasModels.add(new VallasModel("SGD", R.drawable.vallas_sgd, Constant.VALLAS_DOLLAR_SINGAPURE));
        vallasModels.add(new VallasModel("USD", R.drawable.vallas_usd, Constant.VALLAS_DOLLAR));
        return vallasModels;
    }

    @Override
    public void showProgress() {
        isLoading = true;
        binding.swipeRefreshRekening.setEnabled(false);
    }

    @Override
    public void hideProgress() {
        binding.swipeRefreshRekening.setRefreshing(false);

        binding.pbSaldo.setVisibility(View.GONE);
        onAnimator(binding.layoutTotalSaldo, true, ANIMATE_SHOW, Constant.REQUEST_SALDO);
    }

    @Override
    public void onSessionEnd(String message) {
        FastMenuActivity.launchIntentSessionEnd(this, message);
    }

    @Override
    public void onException(String message) {

        binding.totalSaldoIb.setText("-");

        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showDialogGagalBack(this, message);
        else
            showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
    }

    private void injectDependency() {
        getActivityComponent().inject(this);

        if (activityPresenter != null) {
            activityPresenter.setView(this);
            activityPresenter.start();
        }
    }


    @Override
    public void onRefresh() {
        if (!isLoading) {
            if (listFiltered == null) {
                skeletonScreen = Skeleton.bind(binding.rvRekening)
                        .adapter(listRekeingAdapter)
                        .shimmer(true)
                        .angle(20)
                        .frozen(false)
                        .duration(1200)
                        .load(R.layout.item_skeleton_list_rekening)
                        .show(); //default count is 10
                activityPresenter.getAccountWithSaldo();
            } else {
                for (ListRekeningResponse.Account account : listFiltered)
                    account.setSaldoReponse(null);
                listRekeingAdapter.notifyDataSetChanged();
                activityPresenter.getSaldo(listFiltered, true);
            }

            isBubbleSimpedes = false;
            isBubbleGeneralAccount = false;
            isBubbleJunio = false;
        }
    }

    @Override
    public void onSelectVallas(VallasModel vallasModel) {
        listRekeingAdapter.getFilter().filter(vallasModel.getKode_mata());
        binding.tbRekening.ivVallas.setImageResource(vallasModel.getImageView());
        if (vallasModel.getKode_mata().equalsIgnoreCase("Rp")) {
            binding.tbRekening.etVallas.setText("Rp");
        } else
            binding.tbRekening.etVallas.setText(vallasModel.getNamaMataUang());

    }

    @Override
    public void onSetTotalSaldo(double total, String currency, List<ListRekeningResponse.Account> listFiltered) {
        currentCurrency = currency;
        updateTotalSaldo(currentCurrency, total);
        this.listFiltered = listFiltered;
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        switch (id) {
            case R.id.btn_rekening_baru:
                if (SystemClock.elapsedRealtime() - mLastClickDuration < 1000) {
                    return;
                }
                mLastClickDuration = SystemClock.elapsedRealtime();
                TabunganNewSkinActivity.Companion.launchIntent(this);
                break;
            case R.id.ll_limit:
                handleGoToInformationLimit();
                break;
            default:
                break;
        }
    }

    private void handleGoToInformationLimit() {
        Intent newIntent = new Intent(this, TransactionLimitInformationActivity.class);
        startActivity(newIntent);
    }

    @Override
    public void onClickRekening(ListRekeningResponse.Account rekening) {
        if (rekening != null) {
            if (rekening.getDetailType() != null) {
                detailRekeningRouting(rekening);
            } else InformasiRekeningActivity.Companion.launchIntent(this, rekening);
        }
    }

    private void detailRekeningRouting(ListRekeningResponse.Account rekening) {
        switch (rekening.getDetailType()) {
            case "s3f":
                if (rekening.getSaldoReponse() != null) {
                    if (rekening.getSaldoReponse().getBalance() != null) {
                        InfoSimpedesActivity.launchIntent(this, new Gson().toJson(rekening));
                    }
                }
                break;
            case "rencana":
                DetailRencanaActivity.launchIntent(this, rekening);
                break;
            case "junio":
                DetailRekeningJunioActivity.launchIntent(this, rekening);
                break;
            default:
                InformasiRekeningActivity.Companion.launchIntent(this, rekening);
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data == null) return;

        switch (requestCode) {
            case Constant.REQ_EDIT_SAVED:
                handleEditSavedResult(resultCode, data);
                break;
            case Constant.REQ_PAYMENT:
                handlePaymentResult(resultCode, data);
                break;
            case Constant.REQ_FINANSIAL:
                handleFinansialResult(resultCode, data);
                break;
            case Constant.REQ_BUKA_REKENING:
                handleBukaRekeningResult();
                break;
            default:
                break;
        }
    }

    private void handleEditSavedResult(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            String newText = data.getStringExtra(Constant.TAG_ALIAS);
            String position = data.getStringExtra(Constant.TAG_POSITION);
            String positionDefault = data.getStringExtra(Constant.TAG_POSITION_DEFAULT);
            boolean isRefreshSaldo = false;

            if (listFiltered != null) {
                for (ListRekeningResponse.Account account : listFiltered) {
                    if (account.getAccount().equalsIgnoreCase(position)) {
                        account.setAlias(newText);
                    }

                    if (positionDefault != null) {
                        if (account.getAccount().equalsIgnoreCase(positionDefault)) {
                            account.setDefault(1);
                            isRefreshSaldo = true;
                        } else {
                            account.setDefault(0);
                        }
                        listRekeingAdapter.updateAdapter(positionDefault, false);
                        setResult(RESULT_OK);
                    }
                }

                for (int i = 0; i < listFiltered.size(); i++) {
                    if (listFiltered.get(i).getAccount().equals(positionDefault)) {
                        Collections.swap(listFiltered, i, 0);
                    }
                }

                if (isRefreshSaldo) {
                    activityPresenter.getSaldo(listFiltered, true);
                }
                listRekeingAdapter.notifyDataSetChanged();
                setResult(RESULT_OK, data);
            }
        }
    }

    private void handlePaymentResult(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            setResult(RESULT_OK, data);
            finish();
        } else if (resultCode == RESULT_CANCELED && data.getStringExtra(Constant.TAG_TITLE) != null) {
            showSnackbarErrorMessage(data.getStringExtra(Constant.TAG_ERROR_MESSAGE), ALERT_ERROR, this, false);
        } else {
            setResult(RESULT_CANCELED, data);
            finish();
        }
    }

    private void handleFinansialResult(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            setResult(RESULT_OK);
            finish();
        } else {
            setResult(RESULT_CANCELED, data);
            finish();
        }
    }

    private void handleBukaRekeningResult() {
        skeletonScreen = Skeleton.bind(binding.rvRekening)
                .adapter(listRekeingAdapter)
                .shimmer(true)
                .angle(20)
                .frozen(false)
                .duration(1200)
                .load(R.layout.item_skeleton_list_rekening)
                .show(); //default count is 10
        activityPresenter.getAccountWithSaldo();
    }

    @Override
    protected void onDestroy() {
        if (activityPresenter != null) {
            SharedPreferencesHelper.remove(this, Constant.VALLAS_LIST);
        }

        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        activityPresenter.stop();
        super.onBackPressed();
    }

    @Override
    public void onShow(View view, ListRekeningResponse.Account account) {
        if (isBubbleGeneralAccount) {
            viewBubbleGeneralAccount = view;
            sAccount = new Gson().toJson(account);
            accountRekening = account;

            if (isGetSaldo && brImoPrefRepository.getVallasBubble()) {
                BubbleShowGeneralAccount(view);
            }
        }
        if (isBubbleSimpedes) {
            viewSimpedes = view;
            sAccount = new Gson().toJson(account);
            accountRekening = account;

            if (isGetSaldo && Boolean.TRUE.equals(brImoPrefRepository.getVallasBubble())) {
                BubbleShowSimpedes(view);
            }
        }

    }

    protected void BubbleShowGeneralAccount(View view) {
        try {
            lihatRekGeneralAccount = new BubbleShowCaseBuilder(this) //Activity instance
                    .title("Hore, Rekening Baru!") //Any title for the bubble view
                    .description("Sekarang kamu bisa beraktivitas finansial di BRImo menggunakan rekening baru BritAma-mu.")
                    .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM)
                    .backgroundColor(Color.WHITE)
                    .textColor(Color.BLACK)
                    .buttonTitle("OK")
                    .titleTextSize(13) //Title text size in SP (default value 16sp)
                    .descriptionTextSize(13) //Subtitle text size in SP (default value 14sp)
                    .targetView(view)
                    .listener(new BubbleShowCaseListener() {
                        @Override
                        public void onSkipActionClick(@NonNull BubbleShowCase bubbleShowCase) {
                            //Do Nothing
                        }

                        @Override
                        public void onTargetClick(@NonNull BubbleShowCase bubbleShowCase) {
                            // Do nothing
                            intentSimpedes();
                        }

                        @Override
                        public void onCloseActionImageClick(@NonNull BubbleShowCase bubbleShowCase) {
                            intentSimpedes();
                        }

                        @Override
                        public void onBackgroundDimClick(@NonNull BubbleShowCase bubbleShowCase) {
                            // Do nothing
                        }

                        @Override
                        public void onBubbleClick(@NonNull BubbleShowCase bubbleShowCase) {
                            // Do nothing
                        }
                    });

            lihatRekGeneralAccount.show();
        } catch (Exception e) {

        }
    }

    protected void BubbleShowSimpedes(View view) {
        try {
            lihatRekSimpedes = new BubbleShowCaseBuilder(this) //Activity instance
                    .title("Hmm, langkahmu belum selesai") //Any title for the bubble view
                    .description("Segera selesaikan langkahmu untuk dapat menggunakan Simpedes Bisa")
                    .buttonTitle("Lanjut")
                    .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM)
                    .backgroundColor(Color.WHITE)
                    .textColor(Color.BLACK)
                    .titleTextSize(13) //Title text size in SP (default value 16sp)
                    .descriptionTextSize(13) //Subtitle text size in SP (default value 14sp)
                    .targetView(view)
                    .listener(new BubbleShowCaseListener() {
                        @Override
                        public void onSkipActionClick(@NonNull BubbleShowCase bubbleShowCase) {
                            //Do Nothing
                        }

                        @Override
                        public void onTargetClick(@NonNull BubbleShowCase bubbleShowCase) {
                            // Do nothing
                            intentSimpedes();
                        }

                        @Override
                        public void onCloseActionImageClick(@NonNull BubbleShowCase bubbleShowCase) {
                            intentSimpedes();
                        }

                        @Override
                        public void onBackgroundDimClick(@NonNull BubbleShowCase bubbleShowCase) {
                            // Do nothing
                            // bubbleShowCase.dismiss();
                        }

                        @Override
                        public void onBubbleClick(@NonNull BubbleShowCase bubbleShowCase) {
                            // Do nothing
                        }
                    });

            lihatRekSimpedes.show();
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "BubbleShowJunio: ", e);
            }
        }
    }

    protected void BubbleShowJunio(View view) {
        try {
            lihatRekJunio = new BubbleShowCaseBuilder(this) //Activity instance
                    .title("Hore, Rekening Baru!") //Any title for the bubble view
                    .description("Sekarang kamu bisa beraktivitas finansial di BRImo menggunakan rekening barumu!")
                    .buttonTitle("Lanjut")
                    .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM)
                    .backgroundColor(Color.WHITE)
                    .textColor(Color.BLACK)
                    .titleTextSize(13) //Title text size in SP (default value 16sp)
                    .descriptionTextSize(13) //Subtitle text size in SP (default value 14sp)
                    .targetView(view)
                    .listener(new BubbleShowCaseListener() {
                        @Override
                        public void onSkipActionClick(@NonNull BubbleShowCase bubbleShowCase) {
                            //Do Nothing
                        }

                        @Override
                        public void onTargetClick(@NonNull BubbleShowCase bubbleShowCase) {
                            // Do nothing
                        }

                        @Override
                        public void onCloseActionImageClick(@NonNull BubbleShowCase bubbleShowCase) {
                            finish();
                        }

                        @Override
                        public void onBackgroundDimClick(@NonNull BubbleShowCase bubbleShowCase) {
                            // Do nothing
                            // bubbleShowCase.dismiss();
                        }

                        @Override
                        public void onBubbleClick(@NonNull BubbleShowCase bubbleShowCase) {
                            // Do nothing
                        }
                    });

            lihatRekJunio.show();
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "BubbleShowJunio: ", e);
            }
        }
    }

    private void intentSimpedes() {
        if (accountRekening != null && accountRekening.getSaldoReponse().getBalance() != null && !sAccount.isEmpty())
            InfoSimpedesActivity.launchIntent(this, sAccount);
    }

    private void showBottomInfoRencana(){
        brImoPrefRepository.saveInfoRencanaBottom(true);
        FragmentBottomSheetInfoRekeningRencanaBinding bottomSheet = FragmentBottomSheetInfoRekeningRencanaBinding.inflate(getLayoutInflater());
        BottomSheetCustomViewGeneralFragment bts = new BottomSheetCustomViewGeneralFragment(
                bottomSheet.getRoot(), true, true, () -> null);
        if (!getSupportFragmentManager().isStateSaved()) {
            bts.show(getSupportFragmentManager(), "");
        }
        bottomSheet.confirmBtn.setOnClickListener(view -> {
            DashboardInvestasiActivity.launchIntent(this);
            bts.dismissNow();
        });
        bottomSheet.cancelBtn.setOnClickListener(view -> {
            bts.dismissNow();
        });
    }
}