package id.co.bri.brimo.ui.activities.newskinonboarding.pin

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ActivityEnterPinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.changepassnewskin.ChangePassActivity.Companion.stringRefNumber
import id.co.bri.brimo.ui.activities.newskinonboarding.OnboardingOtpNewSkinActivity
import id.co.bri.brimo.ui.activities.newskinonboarding.pass.EnterPassActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.NewSkinOTPChannel
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.ui.fragments.pin.PinEntryFragment
import id.co.bri.brimo.ui.fragments.pin.PinEntryListener

class EnterCurrentPinPassActivity : NewSkinBaseActivity(), PinEntryListener {

    private lateinit var binding: ActivityEnterPinBinding

    private val correctPin = "121212"
    private var pinErrorCount = 0

    private val launcher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            setResult(RESULT_OK)
            finish()
        }
    }

    private var btnForgetPin: Runnable = Runnable {
        val intent = Intent(this, EnterPassActivity::class.java)
        intent.putExtra("request_code", Constant.REQ_FORGOT_PIN)
        launcher.launch(intent)
    }

    var secBtnFunction: Runnable = Runnable {

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEnterPinBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setStatusBarDarkIcons()
        setupView()
        injectDependency()

    }

    private fun injectDependency() {

    }

    private fun setupView() {
        setupPin()
    }

    @SuppressLint("CommitTransaction")
    private fun setupPin(){
        val pinFragment = PinEntryFragment().apply {
            headerTitle = "PIN"
            descriptionText = "Masukkan PIN kamu saat ini"
            infoText = ""
            isForgotPinVisible = true
            setPinEntryListener(this@EnterCurrentPinPassActivity)
        }

        supportFragmentManager.beginTransaction()
            .replace(binding.fragmentContainerPin.id, pinFragment)
            .commit()
    }

    private fun showOtpChannel(reqCode: Int) {
        val maskNumber = "+6281345602416"
        val message = getString(
            R.string.otp_channel_phone_verif_method_message,
            maskNumber
        )
        NewSkinOTPChannel(this, supportFragmentManager, message) {
            val intent = OnboardingOtpNewSkinActivity.launchIntentReq(
                this@EnterCurrentPinPassActivity,
                method = "",
                requestCode = reqCode
            )
            launcher.launch(intent)
        }
    }

    override fun onPinComplete(pin: String) {
        if (pin == correctPin) {
            pinErrorCount = 0
            val requestCode = intent.getIntExtra("request_code", 0)
            showOtpChannel(requestCode)
        } else {
            pinErrorCount++

            val fragment = supportFragmentManager.findFragmentById(binding.fragmentContainerPin.id)
            if (fragment is PinEntryFragment) {
                when (pinErrorCount) {
                    1 -> fragment.setErrorText("PIN salah, silakan coba lagi.")
                    2 -> fragment.setErrorText("PIN salah. Tersisa 1 kesempatan lagi.")
                }
            }

            if (pinErrorCount == 3) {
                showBottomSheetBlock()
            }
        }
    }


    override fun onPinError(errorMessage: String) {
    }

    override fun onForgotPinClicked() {
        OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
            supportFragmentManager,
            R.drawable.ic_warning_illustration,
            "ic_warning_illustration",
            "Yakin kamu ingin mengubah PIN?",
            "Jika kamu ubah PIN di aplikasi ini, maka otomatis menggantikan PIN yang digunakan di BRImo. Lanjut ubah PIN?",
            createKotlinFunction0(btnForgetPin),
            createKotlinFunction0(secBtnFunction),
            false,
            "Ya, Ubah PIN",
            "Batal",
            false,
            showCloseButton = true
        )
    }

    private fun showBottomSheetBlock(){
        OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
            fragmentManager = supportFragmentManager,
            imgPath = "",
            imgName = "lock_password_new",
            titleTxt = "Atur Ulang PIN Dibatasi Sementara",
            subTitleTxt = "Silakan coba lagi dalam 29:58. Tenang, kamu tetap bisa gunakan fitur lainnya seperti biasa. saat lagi",
            btnFirstFunction = { finish() },
            isClickableOutside = false,
            showCloseButton = false,
            firstBtnTxt = GeneralHelper.getString(R.string.mengerti)
        )
    }

    companion object {
        fun launchIntent(caller: Context, ref: String, requestCode: Int): Intent {
            stringRefNumber = ref
            return Intent(caller, EnterCurrentPinPassActivity::class.java).apply {
                putExtra("request_code", requestCode)
            }
        }
    }

    override fun isScreenshotDisabled(): Boolean = true

}
