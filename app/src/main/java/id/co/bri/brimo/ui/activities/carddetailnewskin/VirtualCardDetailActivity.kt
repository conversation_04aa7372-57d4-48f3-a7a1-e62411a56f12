package id.co.bri.brimo.ui.activities.carddetailnewskin

import android.annotation.SuppressLint
import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.view.View
import android.view.WindowManager
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.content.IntentCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.virtualdebitcard.CardSettingVDCAdapter
import id.co.bri.brimo.adapters.virtualdebitcard.ClickAction
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.providers.CardType
import id.co.bri.brimo.ui.activities.pengelolaankartu.ChangePINDebitActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.contract.IPresenter.virtualdebitcard.IDetailVDCPresenter
import id.co.bri.brimo.contract.IView.virtualdebitcard.IDetailVDCView
import id.co.bri.brimo.databinding.ActivityVirtualCardDetailBinding
import id.co.bri.brimo.domain.SnackBarType
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.domain.helpers.calendar.makeGone
import id.co.bri.brimo.domain.helpers.calendar.makeVisible
import id.co.bri.brimo.models.apimodel.request.virtualdebitcard.EditLabelVDCRequest
import id.co.bri.brimo.models.apimodel.request.virtualdebitcard.EnableDisableTransactionVDCRequest
import id.co.bri.brimo.models.apimodel.request.virtualdebitcard.GenerateCvvVDCRequest
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.InitChangePinResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.CvvVirtualCardData
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.DetailVDCResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.GenerateCvvVDCResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.SettingVirtualCardData
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.detailkartunewskin.ChangePINDebitNewSkinActivity
import id.co.bri.brimo.ui.activities.inforekeningnewskin.ListCardTypesNewSkinActivity
import id.co.bri.brimo.ui.activities.virtualdebitcard.DetailVDCActivity
import id.co.bri.brimo.ui.activities.virtualdebitcard.EditLabelVDCFragment
import id.co.bri.brimo.ui.activities.virtualdebitcard.OnBoardingVDCActivity.Companion.CHANGE_PIN_KEY
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation
import id.co.bri.brimo.ui.fragments.PinFragmentNewSkin
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation

import javax.inject.Inject

class VirtualCardDetailActivity : NewSkinBaseActivity(), IDetailVDCView, PinFragmentNewSkin.SendPin,
    DialogInformation.OnActionClick {

    @Inject
    lateinit var presenter: IDetailVDCPresenter<IDetailVDCView>
    private lateinit var binding: ActivityVirtualCardDetailBinding

    private val gson = Gson()

    private lateinit var detailVDC: DetailVDCResponse

    private val settingCardAdapter = CardSettingVDCAdapter()

    private var isOpenCVV = false
    private var isFromTransactionListener = false
    private var isEnableChangePin: Boolean? = null

    private lateinit var selectedCardSetting: SettingVirtualCardData
    private var enableTransaction = false
    private var fromPage: String = ""
    private var cardNumber: String = ""
    private var cardType: CardType? = null

    private var skeletonCardVirtual: SkeletonScreen? = null
    private var skeletonContentRekening: SkeletonScreen? = null
    private var pinFragment: PinFragmentNewSkin? = null
    private var typeStatus : String? = null

    private var lockViewExp = ""
    private var lockViewCvv = ""
    private var label = ""
    private var isGenerateCvv = false
    private lateinit var myClipboard: ClipboardManager
    private lateinit var myClip: ClipData
    private var isDisableEnable: Boolean? = null

    private val changePinLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()){ result ->
        when(result.resultCode) {
            Constant.REQ_PIN_CHANGE_SUCCESS -> {
                val data = result.data?.getStringExtra(ChangePINDebitActivity.CHANGE_PIN_MESSAGE)
                GeneralHelper.showSnackBarGreen(binding.content, data)
                binding.scrollView.scrollTo(0, 0)
            }
            Constant.REQ_PIN_CHANGE_MAX_RETRY -> {
                setResult(result.resultCode, result.data)
                finish()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (GeneralHelper.isProd()) {
            window.setFlags(
                WindowManager.LayoutParams.FLAG_SECURE,
                WindowManager.LayoutParams.FLAG_SECURE
            )
        }
        binding = ActivityVirtualCardDetailBinding.inflate(layoutInflater)
        injectDependency()
        setContentView(binding.root)

        fromPage = intent.getStringExtra(Constant.TAG_TYPE).toString()
        cardType = IntentCompat.getSerializableExtra(
            intent,
            ChangePINDebitActivity.CARD_TYPE_EXTRA,
            CardType::class.java
        )

        isEnableChangePin = intent.getBooleanExtra(DetailVDCActivity.EXTRA_ENABLE_CHANGE_PIN, false)
        initSkeleton()
        setupToolbar()
        if (cardType == CardType.VIRTUAL_DEBIT) {
            cardNumber = intent.getStringExtra(Constant.CARD_NUMBER).toString()
            getDetailVDC("")
        } else {
            detailVDC = gson.fromJson(intent.getStringExtra(Constant.TAG_CONTENT), DetailVDCResponse::class.java)
            cardNumber = detailVDC.virtualCardData.cardNumber
            setupView()
        }

        if (intent.hasExtra(NEW_LABEL)) {
            if (this::detailVDC.isInitialized) {
                intent.getStringExtra(NEW_LABEL)?.let { editLabelVDC(it) }
            } else {
                Handler().postDelayed({
                    intent.getStringExtra(NEW_LABEL)?.let { editLabelVDC(it)}
                }, 1000)
            }
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
    }

    private fun initSkeleton() {

        skeletonCardVirtual =
            Skeleton.bind(binding.ivCardSlider).shimmer(true).angle(20).duration(1200)
                .color(R.color.white).load(R.layout.item_skeleton_card_image_management).show()

        skeletonContentRekening =
            Skeleton.bind(binding.viewContentRekening).shimmer(true).angle(20).duration(1200)
                .color(R.color.white).load(R.layout.item_skeleton_content_card_detail).show()

    }

    private fun setupToolbar() {
        //set blue bar
        if (Build.VERSION.SDK_INT >= 21) {
            val window = window
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            window.statusBarColor = resources.getColor(R.color.primary_blue80)
        }

        GeneralHelper.setToolbarRevamp(
            this, binding.toolbar.toolbar, getString(R.string.info_vdc_toolbar)
        )
    }

    private fun setupView() {
        Glide.with(this)
            .load(detailVDC.virtualCardData.imageCardVertical)
            .diskCacheStrategy(DiskCacheStrategy.NONE)
            .skipMemoryCache(true)
            .placeholder(R.drawable.bg_vdc_vertical)
            .into(binding.ivCardSlider)

        binding.tvSof.text = detailVDC.virtualCardData.productType
        binding.tvAccountNumberTxt.text = detailVDC.virtualCardData.accountString
        if (detailVDC.settingVirtualCardData.isEmpty()) binding.rvSettingCard.visibility =
            View.GONE else setupSettingCard()

        binding.swipeRefresh.apply {
            setOnRefreshListener {
                initSkeleton()
                getDetailVDC("")
            }
            isRefreshing = false
        }

        binding.ivEyeOpen.setOnClickListener {
            binding.tvExpire.text = lockViewExp
            binding.tvCvv.text = lockViewCvv
            binding.ivEyeOpen.makeGone()
            binding.ivEyeClose.makeVisible()
        }

        binding.ivEyeClose.setOnClickListener {
            isGenerateCvv = true
            openPin()
        }

        binding.ivEdit.setOnClickListener {
            VirtualDebitCardLabelActivity.launchIntentForEdit(this, cardNumber)
        }
        binding.tvLabel.text = label

        binding.ivCopy.setOnClickListener {
            myClipboard = getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
            myClip = ClipData.newPlainText("text", cardNumber)
            myClipboard.setPrimaryClip(myClip)
            showSnackbarErrorMessage(
                GeneralHelper.getString(R.string.kartu_berhasil_disalin),
                ALERT_CONFIRM,
                this,
                false
            )
        }

    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setupSettingCard() {
        binding.rvSettingCard.apply {
            View.VISIBLE
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            adapter = settingCardAdapter
        }
        settingCardAdapter.settingVirtualCardList = detailVDC.settingVirtualCardData.toMutableList()
        var textTitle = ""
        var subtextTitle = ""
        var buttonTextFirst = ""
        var buttonTextSecond = ""

        settingCardAdapter.setOnClickListener { action ->
            when (action) {
                is ClickAction.Transaction -> {
                    isFromTransactionListener = true
                    enableTransaction = action.setting.status
                    selectedCardSetting = action.setting
                    when (action.setting.type) {
                        CARDSTATUS -> {
                            if (action.setting.status) {
                                textTitle = ContextCompat.getString(this, R.string.title_nonactive_card)
                                subtextTitle = ContextCompat.getString(this, R.string.subtitle_nonactive_card)
                                buttonTextFirst = ContextCompat.getString(this, R.string.ya_nonaktifkan)
                                buttonTextSecond =  ContextCompat.getString(this, R.string.btn_cancel)
                            } else {
                                textTitle = ContextCompat.getString(this, R.string.title_active_card)
                                subtextTitle = ContextCompat.getString(
                                    this,
                                    R.string.card_active_confirmation_subtitle_debit
                                )
                                buttonTextFirst = ContextCompat.getString(this, R.string.ya_aktifkan)
                                buttonTextSecond = ContextCompat.getString(this, R.string.btn_cancel)
                            }
                            confirmDialog(textTitle, subtextTitle, CARDSTATUS, buttonTextFirst, buttonTextSecond, R.drawable.ic_question_new_ns)
                        }
                        Constant.ECOMM -> {
                            if (action.setting.status) {
                                textTitle = ContextCompat.getString(this, R.string.title_trx_online)
                                subtextTitle = ContextCompat.getString(this, R.string.subtitle_trx_online)
                                buttonTextFirst = ContextCompat.getString(this, R.string.ya_nonaktifkan)
                                buttonTextSecond =  ContextCompat.getString(this, R.string.btn_cancel)
                            } else {
                                textTitle = ContextCompat.getString(this, R.string.card_active_confirmation_title_online_trans)
                                subtextTitle = ContextCompat.getString(this, R.string.card_active_confirmation_subtitle_online_trans)
                                buttonTextFirst = ContextCompat.getString(this, R.string.ya_aktifkan)
                                buttonTextSecond =  ContextCompat.getString(this, R.string.btn_cancel)
                            }
                            confirmDialog(textTitle, subtextTitle, ECOMM, buttonTextFirst, buttonTextSecond, R.drawable.ic_question_new_ns)
                        }
                    }
                }
                is ClickAction.Action -> openEditLabel(detailVDC.virtualCardData.labelCard, action.action)
                is ClickAction.ChangePin -> {
                    if (isEnableChangePin == null) return@setOnClickListener
                    if (isEnableChangePin == true) {
                        presenter.getChangePinRefNum(
                            getString(R.string.url_card_management_init_change_pin),
                            cardNumber
                        )
                    }
                }
            }
        }

        settingCardAdapter.notifyDataSetChanged()
    }

    private fun openEditLabel(label: String, title: String) {
        val dialogFragment = EditLabelVDCFragment(label, title)
        dialogFragment.show(supportFragmentManager, "")
        dialogFragment.isCancelable = true
        dialogFragment.setEditLabelListener { newLabel ->
            editLabelVDC(newLabel)
        }
    }

     private fun editLabelVDC(newLabel: String) {
        val request = EditLabelVDCRequest(
            accountNumber = detailVDC.virtualCardData.account,
            labelCard = newLabel
        )

        if (presenter != null) {
            presenter.setUrlEditLabelVDC(getString(R.string.url_v1_update_label_vdc))
            presenter.updateLabelVDC(request)
        }
    }

    private fun enableDisableTransaction(
        mStatus: Boolean, settingVdc: SettingVirtualCardData, pin: String
    ) {
        val request = EnableDisableTransactionVDCRequest(
            type = settingVdc.type,
            pin = pin,
            status = mStatus,
            cardNumber = cardNumber
        )

        if (presenter != null) {
            presenter.setUrlEnableDisableTransactionVDC(getString(R.string.url_v1_enable_disable_transaction_vdc))
            presenter.enableDisableTransactionVDC(request)
        }
    }

    private fun openPin() {
        pinFragment = PinFragmentNewSkin(this, this)
        pinFragment?.show()
    }

    private fun generateCVV(pin: String) {
        var request = GenerateCvvVDCRequest(
            cardNumber = cardNumber, pin = pin
        )
        if (presenter != null) {
            presenter.setUrlGenerateCVV(getString(R.string.url_v1_generate_cvv_vdc))
            presenter.generateCVV(request)
        }
    }

    private fun getDetailVDC(snackbarResponse: String) {
        binding.scrollView.scrollTo(0, 0)
        if (presenter != null) {
            presenter.setUrlDetailVDC(getString(R.string.url_v1_detail_vdc))
            presenter.getDetailVDC(cardNumber = cardNumber, snackbarResponse)
        }
    }

    private fun showError() {
        var firstBtnFunction: Runnable = Runnable {

        }
        OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
            supportFragmentManager,
            "",
            "ic_fail_newskin2",
            ContextCompat.getString(this, R.string.failed_load_page_title),
            ContextCompat.getString(this, R.string.failed_load_page_subtitle),
            createKotlinFunction0(firstBtnFunction),
            false,
            ContextCompat.getString(this, R.string.refresh)
        )
    }

    private fun confirmDialog(title: String, subtitle: String, type: String, primaryButtonText: String, secondButtonText: String, image: Int) {
        typeStatus = type
        val firstBtnFunction = Runnable {
            openPin()
        }
        val secBtnFunction = Runnable {

        }
        showDialogConfirmation(
            supportFragmentManager,
            image,
            "ic_lock_ns",
            title,
            subtitle,
            createKotlinFunction0(firstBtnFunction),
            createKotlinFunction0(secBtnFunction),
            false,
            primaryButtonText,
            secondButtonText,
            false
        )
    }


    override fun onSuccessGenerateCVV(response: GenerateCvvVDCResponse) {
        binding.ivEyeClose.makeGone()
        binding.ivEyeOpen.makeVisible()
        binding.tvExpire.text = response.cvvData.expiredDate
        binding.tvCvv.text = response.cvvData.cvv
    }

    override fun onSuccessEnableDisableVDC(response: EmptyStateResponse) {
        isDisableEnable = true
        if (response.title.equals("Status Kartu Tidak Aktif")) {
                getDetailVDC(GeneralHelper.getString(R.string.success_nonactive_card))
            } else {
                getDetailVDC(GeneralHelper.getString(R.string.txt_status_active))
            }
    }

    override fun onSuccessGetDetailVDC(response: DetailVDCResponse, snackbarResponse: String) {
            if (snackbarResponse != "") {
                showSnackbarErrorMessage(
                    snackbarResponse,
                    ALERT_CONFIRM,
                    this,
                    false
                )
            }
            skeletonCardVirtual?.hide()
            skeletonContentRekening?.hide()
            binding.swipeRefresh.isRefreshing = false
            detailVDC = response
            if (cardType != CardType.VIRTUAL_DEBIT) {
                isEnableChangePin = response.settingVirtualCardData.find {
                    it.type == CHANGE_PIN_KEY
                }?.status ?: false
            }
            binding.tvNumber.text = cardNumber
            lockViewExp = response.virtualCardData.expiredDate
            lockViewCvv = response.virtualCardData.cvv
            label = response.virtualCardData.labelCard
            binding.tvExpire.text = lockViewExp
            binding.tvCvv.text = lockViewCvv
            setupView()

    }

    override fun showSafetyMode(response: GeneralResponse) {
    }

    override fun onSuccessUpdateLabelVDC(response: String) {
        getDetailVDC("")
        showSnackbarErrorMessage(response, ALERT_CONFIRM, this, false)
    }

    override fun onSuccessGetChangePinRefNum(response: InitChangePinResponse) {
        changePinLauncher.launch(
            ChangePINDebitNewSkinActivity.launch(
                context = this,
                cardNumber = cardNumber,
                refNum = response.refNumber,
                isByPassOldPin = response.isBypassOldPin,
                cardType = CardType.VIRTUAL_DEBIT
            )
        )
    }

    override fun onSendPinComplete(pin: String?) {
        pinFragment?.dismiss()
        if (isGenerateCvv) {
            if (pin != null) {
                generateCVV(pin)
                isGenerateCvv = false
            }
        }  else {
            when (typeStatus) {
                CARDSTATUS -> {
                    if (pin != null) {
                        enableDisableTransaction(enableTransaction, selectedCardSetting, pin)
                    }
                }

                Constant.ECOMM -> {
                    if (pin != null) {
                        enableDisableTransaction(enableTransaction, selectedCardSetting, pin)
                    }
                }

                BLOCKCARD -> {
                    binding.swipeRefresh.visibility = View.GONE
                    binding.flEmpty.visibility = View.VISIBLE
                }
            }
        }
    }

    override fun onLupaPin() {
    }

    override fun onClickAction() {
    }

    override fun onDestroy() {
        window.clearFlags(WindowManager.LayoutParams.FLAG_SECURE)
        super.onDestroy()
        presenter.stop()
    }

    override fun onException(message: String) {
        showSnackbarErrorMessage(
            GeneralHelper.getString(R.string.txt_status_inactive),
            ALERT_ERROR,
            this,
            false
        )
    }

    override fun onBackPressed() {
        val intent = Intent()
        intent.putExtra(Constant.TAG_TYPE, fromPage)
        if (fromPage == "fromSuccess") {
            setResult(RESULT_CANCELED, intent)
        } else {
            setResult(RESULT_OK, intent)
        }
        Handler().postDelayed({
            super.onBackPressed()
        }, 100)
    }

    companion object {

        const val EXTRA_ENABLE_CHANGE_PIN = "extra_enable_change_pin"
        const val CARDSTATUS = "CARDSTATUS"
        const val ECOMM = "ECOMM"
        const val BLOCKCARD = "BLOCKCARD"
        const val NEW_LABEL = "new_label"



        @JvmStatic
        fun launchIntent(
            caller: Activity,
            cardNumber: String,
            enableChangePin: Boolean,
            cardType: CardType
        ) {
            val intent = Intent(caller, VirtualCardDetailActivity::class.java).also {
                it.putExtra(Constant.CARD_NUMBER, cardNumber)
                it.putExtra(EXTRA_ENABLE_CHANGE_PIN, enableChangePin)
                it.putExtra(ChangePINDebitActivity.CARD_TYPE_EXTRA, cardType)
            }
            caller.startActivity(intent)
        }

        fun launchIntentFromEdit(
            caller: Activity,
            newLabel: String,
            cardNumber: String,
            enableChangePin: Boolean,
            cardType: CardType
        ) {
            val intent = Intent(caller, VirtualCardDetailActivity::class.java).also {
                it.putExtra(NEW_LABEL, newLabel)
                it.putExtra(ChangePINDebitActivity.CARD_TYPE_EXTRA, cardType)
                it.putExtra(Constant.CARD_NUMBER, cardNumber)
                it.putExtra(EXTRA_ENABLE_CHANGE_PIN, enableChangePin)
            }
            caller.startActivity(intent)
        }
    }

}