package id.co.bri.brimo.util.extension.view

import android.text.Editable
import android.view.View
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.lifecycle.lifecycleScope
import id.co.bri.brimo.R
import id.co.bri.brimo.domain.extension.afterTextChanged
import id.co.bri.brimo.ui.customviews.forminput.FormInputDefaultView
import id.co.bri.brimo.util.extension.type.orZero
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Adds a validation rule to the [FormInputDefaultView] requiring a minimum of 3 characters.
 *
 * @param errorText The error message to be shown when the validation fails.
 *                  Defaults to a localized string resource.
 */
fun FormInputDefaultView.addMin3CharsValidation(
    errorText: String = context.getString(R.string.form_validation_min3char_error_text)
) {
    addMinLengthCharValidation(
        minLength = 3,
        errorText = errorText
    )
}

/**
 * Adds a minimum character length validation to the [FormInputDefaultView].
 *
 * @param minLength The required minimum number of characters.
 * @param errorText The error message to show if validation fails.
 * @param debounceDelayOnError Delay before showing the error message (in milliseconds).
 * @param isOnValid Callback triggered with validation result: true if valid, false otherwise.
 */
fun FormInputDefaultView.addMinLengthCharValidation(
    minLength: Int,
    errorText: String,
    debounceDelayOnError: Long = 3000L,
    isOnValid: (Boolean) -> Unit = {},
) {
    addCustomValidation(
        errorText = errorText,
        debounceDelayOnError = debounceDelayOnError,
        validCondition = { text ->
            val length = text?.length.orZero()
            length >= minLength
        },
        isOnValid = isOnValid
    )
}

/**
 * Adds a custom validation rule to the [FormInputDefaultView].
 *
 * @param errorText The error message to display when validation fails.
 * @param debounceDelayOnError The delay (in milliseconds) before showing the error.
 * @param isIncludeEmptySpace Whether to consider empty input during validation.
 * @param validCondition A lambda that returns true if the input is valid.
 * @param isOnValid Callback triggered with the result of the validation.
 */
fun FormInputDefaultView.addCustomValidation(
    errorText: String,
    debounceDelayOnError: Long = 3000L,
    isIncludeEmptySpace: Boolean = false,
    validCondition: (Editable?) -> Boolean,
    isOnValid: (Boolean) -> Unit = {}
) {
    var debounceJob: Job? = null
    val scope = findViewTreeLifecycleOwner()?.lifecycleScope ?: CoroutineScope(Dispatchers.Main)

    editText.afterTextChanged { editable ->
        debounceJob?.cancel()
        hideError()

        if (!isEnabled || !isAttachedToWindow) return@afterTextChanged
        if (!isIncludeEmptySpace && editable.isNullOrEmpty()) {
            isOnValid(false)
            return@afterTextChanged
        }

        if (!validCondition(editable)) {
            debounceJob = scope.launch {
                delay(debounceDelayOnError)
                if (!isAttachedToWindow) return@launch
                showError(errorText)
            }
            isOnValid(false)
        } else {
            isOnValid(true)
        }
    }

    editText.addOnFocusChangeListener { _, hasFocus ->
        if (!hasFocus) {
            debounceJob?.cancel()

            if (!isIncludeEmptySpace && editText.text.isNullOrEmpty()) {
                hideError()
                return@addOnFocusChangeListener
            }
            showErrorIf(errorText, !validCondition(editText.text))
        } else {
            hideError()
        }
    }

    addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
        override fun onViewAttachedToWindow(v: View) {}
        override fun onViewDetachedFromWindow(v: View) {
            debounceJob?.cancel()
        }
    })
}

/**
 * Filters the input of the [FormInputDefaultView] to allow only digit characters.
 * Any non-digit characters will be automatically removed from the input.
 */
fun FormInputDefaultView.addDigitOnlyFilter() {
    editText.afterTextChanged { editable ->
        editable ?: return@afterTextChanged
        for (i in editable.length - 1 downTo 0) {
            if (!editable[i].isDigit()) {
                editable.delete(i, i + 1)
            }
        }
    }
}

/**
 * Forces all characters entered into the [FormInputDefaultView] to be uppercase.
 * Automatically replaces any lowercase letters with their uppercase equivalents.
 */
fun FormInputDefaultView.addAllCapsFilter() {
    editText.afterTextChanged { editable ->
        editable ?: return@afterTextChanged

        for (i in editable.indices) {
            val originalChar = editable[i]
            val uppercasedChar = originalChar.uppercaseChar()

            if (originalChar != uppercasedChar) {
                editable.replace(i, i + 1, uppercasedChar.toString())
            }
        }
    }
}
