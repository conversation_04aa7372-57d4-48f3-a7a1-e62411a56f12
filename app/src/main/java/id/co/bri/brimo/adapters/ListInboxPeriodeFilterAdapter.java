package id.co.bri.brimo.adapters;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ItemCategoryTransactionBinding;
import id.co.bri.brimo.databinding.ItemSegmentFilterBinding;
import id.co.bri.brimo.models.apimodel.response.PeriodeResponse;

import java.util.List;

public class ListInboxPeriodeFilterAdapter extends RecyclerView.Adapter<ListInboxPeriodeFilterAdapter.ViewHolder> {

    protected List<PeriodeResponse> periodeResponseList;
    protected Context context;
    protected ClickItem clickItem;
    private int selectedPosition = -1;

    public ListInboxPeriodeFilterAdapter(List<PeriodeResponse> periodeResponseList, Context context, ClickItem clickItem) {
        this.periodeResponseList = periodeResponseList;
        this.context = context;
        this.clickItem = clickItem;

        // Set default value
        for (int i = 0; i < periodeResponseList.size(); i++) {
            if ("ALL".equalsIgnoreCase(periodeResponseList.get(i).getCode())) {
                periodeResponseList.get(i).setSelected(true);
                selectedPosition = i;
                break;
            }
        }
    }

    public interface ClickItem {
        void itemClickedPeriode(PeriodeResponse periodeResponseList);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(ItemCategoryTransactionBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false));
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, @SuppressLint("RecyclerView") int position) {
        holder.binding.rbToday.setChecked(periodeResponseList.get(position).isSelected());
        holder.binding.rbToday.setText(periodeResponseList.get(position).getName());
        holder.binding.rbToday.setOnClickListener(view -> {
            if (selectedPosition != -1 && selectedPosition != position) {
                periodeResponseList.get(selectedPosition).setSelected(false);
            }
            periodeResponseList.get(position).setSelected(true);
            selectedPosition = position;

            notifyDataSetChanged();
            clickItem.itemClickedPeriode(periodeResponseList.get(position));
        });
    }

    @Override
    public int getItemCount() {
        return (periodeResponseList != null) ? periodeResponseList.size() : 0;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ItemCategoryTransactionBinding binding;

        public ViewHolder(@NonNull ItemCategoryTransactionBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    public void setSelectedPeriode(String code) {
        for (int i = 0; i < periodeResponseList.size(); i++) {
            PeriodeResponse item = periodeResponseList.get(i);
            if (item.getCode().equalsIgnoreCase(code)) {
                item.setSelected(true);
                selectedPosition = i;
            } else {
                item.setSelected(false);
            }
        }
        notifyDataSetChanged();
    }

}