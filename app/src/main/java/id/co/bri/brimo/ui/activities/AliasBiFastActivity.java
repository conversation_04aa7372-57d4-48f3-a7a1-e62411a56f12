package id.co.bri.brimo.ui.activities;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.fragment.app.FragmentTransaction;

import java.util.Objects;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.bifast.IBiFastPresenter;
import id.co.bri.brimo.contract.IView.bifast.IBiFastView;
import id.co.bri.brimo.databinding.ActivityAliasBiFastBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.request.BiFastUpdateRequest;
import id.co.bri.brimo.models.apimodel.response.AliasBiFastResponse;
import id.co.bri.brimo.models.apimodel.response.DetailBiFast;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogSetDefault;
import id.co.bri.brimo.ui.fragments.FragmentBottomDialog;

public class AliasBiFastActivity extends BaseActivity implements View.OnClickListener, DialogSetDefault.DialogDefaultListener, IBiFastView {

    private ActivityAliasBiFastBinding binding;

    private boolean isPhoneClick = false, isEmailClick = false;

    Context context;
    private static final int[] colorArray = {R.color.colorStatusBerhasil, R.color.red};
    private boolean isActive = true;
    private String titleActive, subActive;
    private static String mAccount, mNama, mAccountBiasa, mAliasPhone, mAliasEmail;
    private static DetailBiFast mDetailBiFastPhone;
    private static DetailBiFast mDetailBiFastEmail;

    public static void launchIntent(Activity caller, String aliasPhone, String aliasEmail, DetailBiFast detailBiFastPhone, DetailBiFast detailBiFastEmail, String account, String nama, String accountBiasa) {
        Intent intent = new Intent(caller, AliasBiFastActivity.class);
        mAccount = account;
        mNama = nama;
        mAccountBiasa = accountBiasa;
        mAliasPhone = aliasPhone;
        mAliasEmail = aliasEmail;
        mDetailBiFastPhone = detailBiFastPhone;
        mDetailBiFastEmail = detailBiFastEmail;
        caller.startActivityForResult(intent, Constant.REQ_ALIAS);
    }

    @Inject
    IBiFastPresenter<IBiFastView> presenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityAliasBiFastBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.bifast_toolbar));
        binding.tvAktifBtn.setOnClickListener(this);
        binding.tvDeleteBtn.setOnClickListener(this);
        setUpView();
        injectDependency();
//        checkBoxListener();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrl(GeneralHelper.getString(R.string.url_update_alias_bifast));
        }
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.tv_aktif_btn:
                if (isActive) {
                    titleActive = GeneralHelper.getString(R.string.title_nonaktif);
                    subActive = GeneralHelper.getString(R.string.subtitle_nonaktif);
                } else {
                    titleActive = GeneralHelper.getString(R.string.title_aktif);
                    subActive = GeneralHelper.getString(R.string.subtitle_aktif);
                }
                DialogSetDefault dialogSetDefault = new DialogSetDefault(this, titleActive, subActive, GeneralHelper.getString(R.string.txt_btn_no),  GeneralHelper.getString(R.string.btn_yes), Constant.REQ_AKTIF_ALIAS);
                FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
                ft.add(dialogSetDefault, null);
                ft.commitAllowingStateLoss();
                break;
            case R.id.tv_delete_btn:
                DialogSetDefault dialogDelete = new DialogSetDefault(this, GeneralHelper.getString(R.string.title_hapus_alias), GeneralHelper.getString(R.string.subtitle_hapus_alias),   GeneralHelper.getString(R.string.txt_btn_no),  GeneralHelper.getString(R.string.btn_yes), Constant.REQ_DEL_ALIAS);
                FragmentTransaction ft2 = this.getSupportFragmentManager().beginTransaction();
                ft2.add(dialogDelete, null);
                ft2.commitAllowingStateLoss();
                break;
            case R.id.ll_nohp:
                BiFastUpdateRequest biFastUpdateRequest = new BiFastUpdateRequest(mAliasPhone, mAccountBiasa, "UPDATE");
                presenter.getUpdateBiFast(biFastUpdateRequest);
                isPhoneClick = true;
                break;
            case R.id.ll_email:
//                fragmentView();
                BiFastUpdateRequest biFastUpdateEmail = new BiFastUpdateRequest(mAliasEmail, mAccountBiasa, "UPDATE");
                presenter.getUpdateBiFast(biFastUpdateEmail);
                isEmailClick = true;
                break;
        }
    }

    public void setUpView() {
        binding.llNohp.setOnClickListener(this);
        binding.llEmail.setOnClickListener(this);

        binding.tvSubtitleEmail.setText(mAliasEmail);
        binding.tvSubtitlePhone.setText(mAliasPhone);

        if (mDetailBiFastPhone.getAccount().equals(mAccountBiasa) && (mDetailBiFastPhone.getStatus().equals("1") || mDetailBiFastPhone.getStatus().equals("0"))) {
            switch (mDetailBiFastPhone.getStatus()) {
                case "1":
                    binding.tvName.setText(mNama);
                    binding.noRekening.setText(String.format("%s - BRI", mAccount));
                    setActive();
                    binding.tvTitle.setText(GeneralHelper.getString(R.string.bi_fast_alias_phone_number));
                    binding.ivIcon.setImageResource(R.drawable.ic_phone);
                    binding.tvSubtitle.setText(mDetailBiFastPhone.getProxyValue());
                    binding.cvStatus.setCardBackgroundColor(this.getResources().getColor(colorArray[0]));
                    break;
                case "0":
                    binding.tvName.setText(mNama);
                    binding.noRekening.setText(String.format("%s - BRI", mAccount));
                    setInActive();
                    binding.tvTitle.setText(GeneralHelper.getString(R.string.bi_fast_alias_phone_number));
                    binding.ivIcon.setImageResource(R.drawable.ic_phone);
                    binding.tvSubtitle.setText(mDetailBiFastPhone.getProxyValue());
                    binding.cvStatus.setCardBackgroundColor(this.getResources().getColor(colorArray[1]));
                    break;
                case "-1":
                case "-2":
                    binding.llAvailable.setVisibility(View.GONE);
                    binding.llUnavailable.setVisibility(View.VISIBLE);
                    break;
            }
        } else if (mDetailBiFastEmail.getAccount().equals(mAccountBiasa)) {
            switch (mDetailBiFastEmail.getStatus()) {
                case "1":
                    binding.tvName.setText(mNama);
                    binding.noRekening.setText(String.format("%s - BRI", mAccount));
                    setActive();
                    binding.tvTitle.setText(GeneralHelper.getString(R.string.email));
                    binding.ivIcon.setImageResource(R.drawable.ic_email);
                    binding.tvSubtitle.setText(mDetailBiFastEmail.getProxyValue());
                    binding.cvStatus.setCardBackgroundColor(this.getResources().getColor(colorArray[0]));
                    break;
                case "0":
                    binding.tvName.setText(mNama);
                    binding.noRekening.setText(String.format("%s -  BRI", mAccount));
                    setInActive();
                    binding.tvTitle.setText(GeneralHelper.getString(R.string.email));
                    binding.ivIcon.setImageResource(R.drawable.ic_email);
                    binding.tvSubtitle.setText(mDetailBiFastEmail.getProxyValue());
                    binding.cvStatus.setCardBackgroundColor(this.getResources().getColor(colorArray[1]));
                    break;
                case "-1":
                case "-2":
                    binding.llAvailable.setVisibility(View.GONE);
                    binding.llUnavailable.setVisibility(View.VISIBLE);
                    break;
            }
        } else if (!mDetailBiFastPhone.getAccount().equals(mAccountBiasa)) {
            binding.llAvailable.setVisibility(View.GONE);
            binding.llUnavailable.setVisibility(View.VISIBLE);
        } else if (!mDetailBiFastEmail.getAccount().equals(mAccountBiasa)) {
            binding.llAvailable.setVisibility(View.GONE);
            binding.llUnavailable.setVisibility(View.VISIBLE);
        } else {
            binding.llAvailable.setVisibility(View.GONE);
            binding.llUnavailable.setVisibility(View.VISIBLE);
        }
    }

    public void setActive() {
        binding.llAvailable.setVisibility(View.VISIBLE);
        binding.llUnavailable.setVisibility(View.GONE);
        binding.tvAktifBtn.setText(GeneralHelper.getString(R.string.nonaktifkan));
        binding.tvStatusInbox.setText(GeneralHelper.getString(R.string.aktif));
        isActive = true;
    }

    public void setInActive() {
        binding.llAvailable.setVisibility(View.VISIBLE);
        binding.llUnavailable.setVisibility(View.GONE);
        binding.tvAktifBtn.setText(GeneralHelper.getString(R.string.aktifkan_2));
        binding.tvStatusInbox.setText(GeneralHelper.getString(R.string.nonaktif));
        isActive = false;
    }

    public void checkBoxListener() {
        binding.checkbox1.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (binding.checkbox1.isChecked())
                    binding.llNohp.setBackgroundResource(R.drawable.rounded_blue_line);
                else
                    binding.llNohp.setBackgroundResource(R.drawable.rounded_line_grey);
            }
        });
        binding.checkbox2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
//                fragmentView();
                binding.checkbox2.setChecked(false);
            }
        });

    }

    @Override
    protected void onClickYesRC99(int reqId) {

    }

    @Override
    public void onClickNoDefault(int reqId) {
        if (reqId == Constant.REQ_AKTIF_ALIAS) {
            if (isActive) {
//                GeneralHelper.showSnackBarGreen(Objects.requireNonNull(this).findViewById(R.id.content), "Alias berhasil dinonaktifkan");
//                tvStatus.setText("Nonaktif");
//                tvBtnAktif.setText("Aktifkan");
//                cvStatus.setCardBackgroundColor(this.getResources().getColor(colorArray[1]));
//                isActive = false;
                BiFastUpdateRequest biFastAktifRequest = new BiFastUpdateRequest(binding.tvSubtitle.getText().toString(), mAccountBiasa, "INACTIVE");
                presenter.getUpdateBiFast(biFastAktifRequest);
            } else {
//                GeneralHelper.showSnackBarGreen(Objects.requireNonNull(this).findViewById(R.id.content), "Alias berhasil diaktifkan");
//                tvStatus.setText("Aktif");
//                tvBtnAktif.setText(GeneralHelper.getString(R.string.nonaktifkan));
//                cvStatus.setCardBackgroundColor(this.getResources().getColor(colorArray[0]));
//                isActive = true;
                BiFastUpdateRequest biFastInaktifRequest = new BiFastUpdateRequest(binding.tvSubtitle.getText().toString(), mAccountBiasa, "ACTIVATE");
                presenter.getUpdateBiFast(biFastInaktifRequest);
            }
        } else if (reqId == Constant.REQ_DEL_ALIAS) {
            BiFastUpdateRequest biFastDelRequest = new BiFastUpdateRequest(binding.tvSubtitle.getText().toString(), mAccountBiasa, "DELETE");
            presenter.getUpdateBiFast(biFastDelRequest);
        }
    }

    private void fragmentView(String message) {
        FragmentBottomDialog fragmentBottomDialog = new FragmentBottomDialog(this,
                Constant.COMING_SOON,
                "Mohon Maaf",
//                "Alias yang kamu pilih telah terdaftar di rekening lain, silahkan gunakan alias yang belum terdaftar di rekening manapun.",
                message,
                Constant.IMAGE_ALIAS_UNAVAIL, false);
        fragmentBottomDialog.setCancelable(false);
        fragmentBottomDialog.show(this.getSupportFragmentManager(), "");
    }

    @Override
    public void onSuccessGetUpdate(AliasBiFastResponse aliasBiFastResponse, String message, BiFastUpdateRequest biFastUpdateRequest) {
        if (!biFastUpdateRequest.getType().equalsIgnoreCase("UPDATE")) {
            GeneralHelper.showSnackBarGreen(Objects.requireNonNull(this).findViewById(R.id.content), message);
        }
        Intent resultIntent = new Intent();
        resultIntent.putExtra(Constant.TAG_TYPE, aliasBiFastResponse.getAliasDetail().getStatus());
        resultIntent.putExtra(Constant.TAG_VALUE, aliasBiFastResponse.getAlias());
        resultIntent.putExtra(Constant.TAG_ALIAS_BIFAST, aliasBiFastResponse.getAliasDetail().getProxyType());
        resultIntent.putExtra(Constant.TAG_ALIAS_AKUN, aliasBiFastResponse.getAliasDetail().getAccount());
        switch (aliasBiFastResponse.getAliasDetail().getStatus()) {
            case "0":
                binding.tvStatusInbox.setText(GeneralHelper.getString(R.string.nonaktif));
                binding.tvAktifBtn.setText(GeneralHelper.getString(R.string.aktifkan_2));
                binding.cvStatus.setCardBackgroundColor(this.getResources().getColor(colorArray[1]));
                isActive = false;
                break;
            case "1":
                if (biFastUpdateRequest.getType().equalsIgnoreCase("UPDATE")) {
                    resultIntent.putExtra(Constant.TAG_MESSAGE, message);
                    setResult(Activity.RESULT_OK, resultIntent);
                    finish();
                } else {
                    binding.tvStatusInbox.setText(GeneralHelper.getString(R.string.aktif));
                    binding.tvAktifBtn.setText(GeneralHelper.getString(R.string.nonaktifkan));
                    binding.cvStatus.setCardBackgroundColor(this.getResources().getColor(colorArray[0]));
                    isActive = true;
                }
//                if (isEmailClick) {
//                    llEmail.setBackgroundResource(R.drawable.rounded_blue_line);
//                    cbEmail.setChecked(true);
//                    llNoHp.setBackgroundResource(R.drawable.rounded_line_grey);
//                    cbPhone.setChecked(false);
//                    isEmailClick = false;
//                } else if (isPhoneClick) {
//                    llNoHp.setBackgroundResource(R.drawable.rounded_blue_line);
//                    cbPhone.setChecked(true);
//                    llEmail.setBackgroundResource(R.drawable.rounded_line_grey);
//                    cbEmail.setChecked(false);
//                    isPhoneClick = false;
//                }
                break;
            case "-1":
                binding.llAvailable.setVisibility(View.GONE);
                binding.llUnavailable.setVisibility(View.VISIBLE);
                break;
        }
        setResult(Activity.RESULT_OK, resultIntent);
    }

    @Override
    public void onFailedSetUpdate(String message, BiFastUpdateRequest biFastUpdateRequest) {
        fragmentView(message);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}