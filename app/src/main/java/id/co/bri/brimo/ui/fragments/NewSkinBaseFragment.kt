package id.co.bri.brimo.ui.fragments

import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatButton
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin

open class NewSkinBaseFragment : BaseFragment() {
    open fun isScreenshotDisabled(): Boolean = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (isScreenshotDisabled()) {
            requireActivity().window.setFlags(
                WindowManager.LayoutParams.FLAG_SECURE,
                WindowManager.LayoutParams.FLAG_SECURE
            )
        }
    }

    override fun onException(message: String) {
        GeneralHelperNewSkin.triggerVibration(baseActivity, Constant.VIBRATE_ERROR)
        if (GeneralHelperNewSkin.isContains(
                Constant.LIST_TYPE_GAGAL_GANGGUAN_SISTEM,
                message
            )
        ) GeneralHelperNewSkin.showErrorBottomDialog(requireActivity(), message)
        else GeneralHelperNewSkin.showGeneralErrorBottomDialog(requireActivity())
    }

    fun updateButtonState(isEnabled: Boolean, button: AppCompatButton) {
        button.apply {
            this.isEnabled = isEnabled
        }
    }

    override fun showProgress() {
        GeneralHelperNewSkin.showLoadingDialog(baseActivity)
    }

    override fun hideProgress() {
        GeneralHelperNewSkin.dismissLoadingDialog()
    }

    fun showDialogDummyShowCase(
        context: Context,
        title: String,
        message: String,
        positiveText: String,
        neutralText: String,
        negativeText: String,
        onPositive: () -> Unit,
        onNeutral: () -> Unit,
        onNegative: () -> Unit
    ) {
        AlertDialog.Builder(context)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton(positiveText) { dialog, _ ->
                onPositive()
                dialog.dismiss()
            }
            .setNeutralButton(neutralText) { dialog, _ ->
                onNeutral()
                dialog.dismiss()
            }
            .setNegativeButton(negativeText) { dialog, _ ->
                onNegative()
                dialog.dismiss()
            }
            .show()
    }

}