package id.co.bri.brimo.ui.activities;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.SystemClock;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.TypedValue;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.baseadapter.ViewPagerAdapter;
import id.co.bri.brimo.contract.IPresenter.IAskPresenter;
import id.co.bri.brimo.contract.IView.IAskView;
import id.co.bri.brimo.databinding.ActivityAskBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.NotifikasiModel;
import id.co.bri.brimo.models.apimodel.response.OnboardingBrimoResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingRekeningActivity;
import id.co.bri.brimo.ui.activities.registrasirevamp.RegistrasiDokumenActivity;
import id.co.bri.brimo.ui.customviews.BilingualUI;
import id.co.bri.brimo.ui.customviews.bottomsheetdialog.BottomSheetDialogGeneral;
import id.co.bri.brimo.ui.customviews.bottomsheetdialog.BottomSheetDialogType;
import id.co.bri.brimo.ui.fragments.BottomDialogFragment;
import id.co.bri.brimo.ui.fragments.BrimoOnBoardingFragment;
import id.co.bri.brimo.util.LocaleUtilKt;
import kotlin.Unit;

public class AskActivity extends BaseActivity implements
        IAskView,
        BottomDialogFragment.OnFragmentInteractionListener {

    private ActivityAskBinding binding;

    @Inject
    IAskPresenter<IAskView> presenter;

    private static final String TAG = "AskActivity";
    //data Notifikasi
    private Bundle extras = null;
    private String notifikasiString = null;
    private NotifikasiModel notifikasiModel = null;

    private static final int AUTO_SCROLL_THRESHOLD_IN_MILLI = 3500;
    private ViewPagerAdapter adapterRdn2;

    private static final String TAG_ONBOARDING = "onboarding";
    private static final String TAG_FRAGMENT = "fragment";

    private static final String TAG_IMAGE = "image";
    private static boolean DC = false;
    private static OnboardingBrimoResponse brimoResponse;
    private BottomDialogFragment dialogFragment;
    private OnboardingBrimoResponse dataStatic;
    private float height = 0;
    private long mLastClickTime = 0;

    public static void launchIntent(Activity caller, boolean dc) {
        Intent intent = new Intent(caller, AskActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        DC = dc;
        caller.startActivity(intent);
        caller.finish();
    }

    public static void launchIntent(Activity caller, String response) {
        Intent intent = new Intent(caller, AskActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        brimoResponse = new Gson().fromJson(response, OnboardingBrimoResponse.class);
        caller.startActivity(intent);
        caller.finish();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityAskBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setHelperContext(this);
        injectDependency();

        showSuccessSnackbarChangeLanguage();
        final DisplayMetrics displayMetrics = getResources().getDisplayMetrics();
        height = displayMetrics.heightPixels / displayMetrics.density;
        setLayout(height);

        if (brimoResponse == null) {
            setViewDC();
        } else {
            setView();
        }

        onClickKontakKami();
        handleEventClickListener();
        initSwitchButton();
        intentExtra();
    }

    private void initSwitchButton(){
        binding.switchButton.setText(LocaleUtilKt.getSavedLanguage(this));
        binding.switchButton.setOnClickListener( () -> {
            new BilingualUI(this, getSupportFragmentManager(), LocaleUtilKt.getSavedLanguage(this),(value) ->{
                brimoResponse= null;
                binding.vpRdn.addOnPageChangeListener(null);
                LocaleUtilKt.applyLanguageContext(this, value);
                recreatedActivity();
                return Unit.INSTANCE;
            });
            return null;
        });
    }


    private void intentExtra() {
        if (getIntent().getExtras() != null) {
            parseDataNotifFastMenu(getIntent());
        }

        if (getIntent().hasExtra(Constant.FEATURE_REGIS) || getIntent().hasExtra(Constant.ONBOARDING_BRIMO)) {
            Intent intentYa = new Intent(AskActivity.this, LoginActivity.class);
            startActivityForResult(intentYa, Constant.REQ_CREATE_PIN);
        }

        if (getIntent().hasExtra("registrasi")) {
            dialogFragment.show(getSupportFragmentManager(), "");
        }
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.getPersistenceId();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.getPersistenceId();
        }
    }

    private void setViewDC() {
        dataStatic = new Gson().fromJson(createData(), OnboardingBrimoResponse.class);

        if (dataStatic == null || dataStatic.getBrimoOnboard().isEmpty()) return;

        binding.tvTitle.setText(dataStatic.getBrimoOnboard().get(0).getTitle());
        binding.tvDesc.setText(dataStatic.getBrimoOnboard().get(0).getDescription());
        dialogFragment = new BottomDialogFragment(
                dataStatic.getBottomDrawerOnboard(),
                this,
                GeneralHelper.getString(R.string.string_btn_blue),
                GeneralHelper.getString(R.string.string_btn_white)
        );

        List<String> stringList = new ArrayList<>();
        stringList.add("onboardsatu");
        stringList.add("onboarddua");
        stringList.add("onboardtiga");

        adapterRdn2 = new ViewPagerAdapter(getSupportFragmentManager());
        for (int i = 0; i < dataStatic.getBrimoOnboard().size(); i++) {
            adapterRdn2.addFragment(addFragment(dataStatic.getBrimoOnboard().get(i), stringList.get(i)), "");
        }
        binding.vpRdn.setAdapter(adapterRdn2);
        binding.vpRdn.startAutoScroll();
        binding.vpRdn.setInterval(AUTO_SCROLL_THRESHOLD_IN_MILLI);
        binding.vpRdn.setAutoScrollDurationFactor(6);
        binding.vpRdn.setCycle(true);
        binding.vpRdn.setStopScrollWhenTouch(true);

        binding.vpRdn.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                if (!isFinishing() && binding != null) {
                    binding.tvTitle.setText(dataStatic.getBrimoOnboard().get(position).getTitle());
                    binding.tvDesc.setText(dataStatic.getBrimoOnboard().get(position).getDescription());
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });

        binding.dotsIndicator.setViewPager(binding.vpRdn);
    }

    private void setLayout(float i) {
        if (i < 700) {
            binding.tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 20);
            binding.tvDesc.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);

            LinearLayout.LayoutParams param = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    0.5f
            );
            binding.ly1.setLayoutParams(param);
            binding.dotsIndicator.setPadding(0, 0, 0, 30);
            RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            lp.setMargins(0, 0, 0, 90);
            binding.bgOnboarding.setLayoutParams(lp);
        }
    }

    private void setView() {
        if (brimoResponse.getBrimoOnboard() != null) {
            binding.tvTitle.setText(brimoResponse.getBrimoOnboard().get(0).getTitle());
            binding.tvDesc.setText(brimoResponse.getBrimoOnboard().get(0).getDescription());
            dialogFragment = new BottomDialogFragment(
                    brimoResponse.getBottomDrawerOnboard(),
                    this,
                    GeneralHelper.getString(R.string.string_btn_blue),
                    GeneralHelper.getString(R.string.string_btn_white)
            );

            adapterRdn2 = new ViewPagerAdapter(getSupportFragmentManager());
            for (int i = 0; i < brimoResponse.getBrimoOnboard().size(); i++) {
                adapterRdn2.addFragment(addFragment(brimoResponse.getBrimoOnboard().get(i), "onboardsatu"), "");
            }

            binding.vpRdn.setAdapter(adapterRdn2);
            binding.vpRdn.startAutoScroll();
            binding.vpRdn.setInterval(AUTO_SCROLL_THRESHOLD_IN_MILLI);
            binding.vpRdn.setAutoScrollDurationFactor(6);
            binding.vpRdn.setCycle(true);
            binding.vpRdn.setStopScrollWhenTouch(true);

            binding.vpRdn.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
                @Override
                public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

                }

                @Override
                public void onPageSelected(int position) {
                    try {
                        if (!isFinishing() && binding != null ) {
                            if (brimoResponse!=null) {
                                binding.tvTitle.setText(brimoResponse.getBrimoOnboard().get(position).getTitle());
                                binding.tvDesc.setText(brimoResponse.getBrimoOnboard().get(position).getDescription());
                            }
                        }
                    }catch (NullPointerException  e){
                        if (!GeneralHelper.isProd()){
                            Log.e(TAG, "onPageSelected: "+e.getMessage() );
                        }
                    }
                }

                @Override
                public void onPageScrollStateChanged(int state) {
                }
            });

            binding.dotsIndicator.setViewPager(binding.vpRdn);
        }
    }

    private void onClickKontakKami() {
        binding.llKontakKami.setOnClickListener(view -> {
            KontakKamiActivity.launchIntentFreshInstall(this, false, false);
        });
    }

    private void handleEventClickListener() {
        binding.btnYa.setOnClickListener(view -> {
            Intent intentYa = new Intent(AskActivity.this, LoginActivity.class);
            startActivityForResult(intentYa, Constant.REQ_CREATE_PIN);
        });

        binding.btnTidak.setOnClickListener(view -> {
            if (dialogFragment != null) dialogFragment.show(getSupportFragmentManager(), "");
        });
    }

    private void parseDataNotifFastMenu(Intent intent) {
        if (intent != null) {

            // Get data from notifikasi
            extras = intent.getExtras();

            if (extras != null) {
                notifikasiString = extras.getString(Constant.TAG_NOTIF);

                if (notifikasiString != null) {
                    if (!notifikasiString.isEmpty()) {
                        try {
                            Gson gson = new Gson();
                            notifikasiModel = gson.fromJson(notifikasiString, NotifikasiModel.class);

                            if (notifikasiModel.getImagePopup() != null && !notifikasiModel.getImagePopup().isEmpty()) {
                                if (notifikasiModel.getType().equalsIgnoreCase("promo_blast")) {
                                    Intent intent1 = new Intent(AskActivity.this, DetailPromoActivity.class);
                                    intent1.putExtra(Constant.TAG_NOTIF, notifikasiString);
                                    startActivity(intent1);
                                } else if (notifikasiModel.getType().equalsIgnoreCase(Constant.REGISTRATION_BRIMO)) {
                                    Intent intent1 = new Intent(AskActivity.this, RegistrasiDokumenActivity.class);
                                    intent1.putExtra(Constant.TAG_NOTIF, notifikasiString);
                                    startActivity(intent1);
                                }
                            }
                            notifikasiModel = null;
                            notifikasiString = null;
                        } catch (Exception e) {
                            if (!GeneralHelper.isProd())
                                Log.e(TAG, "parseDataNotif: ", e);
                        }
                    }
                }
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_CREATE_PIN) {
            if (resultCode == RESULT_OK)
                finish();

        } else if (resultCode == RESULT_CANCELED && data != null) {
            Intent intentYa = new Intent(AskActivity.this, LoginActivity.class);
            startActivityForResult(intentYa, Constant.REQ_CREATE_PIN);
        }
        recreate();
    }

    private Fragment addFragment(OnboardingBrimoResponse.BrimoOnboard onBoarding, String image) {
        Bundle bundle = new Bundle();
        bundle.putString(TAG_FRAGMENT, new Gson().toJson(onBoarding));
        bundle.putString(TAG_IMAGE, image);
        BrimoOnBoardingFragment fragment = BrimoOnBoardingFragment.newInstance(this, height);
        fragment.setArguments(bundle);
        return fragment;
    }


    @Override
    public void onClickBlue() {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        startActivity(new Intent(this, RegistrasiDokumenActivity.class));
        dialogFragment.dismiss();
    }

    @Override
    public void onClickWhite() {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 3000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        Intent intent = new Intent(this, OnboardingRekeningActivity.class);
        intent.putExtra(Constant.TAG_ACTIVITY, true);
        startActivity(intent);
        dialogFragment.dismiss();
    }

    private String createData() {
        return GeneralHelper.getString(R.string.data_oboarding);
    }

    @Override
    protected void onDestroy() {
        presenter.stop();
        super.onDestroy();
    }


    public void showSuccessSnackbarChangeLanguage() {
        if (getIntent().getBooleanExtra(Constant.IS_CHANGE_LANGUAGE, false)) {
            GeneralHelper.showSnackBarGreen(
                    findViewById(R.id.content),
                    GeneralHelper.getString(R.string.txt_language_change_successfully)
            );
            brimoResponse = null;
            DC = true;
        }
        getIntent().removeExtra(Constant.IS_CHANGE_LANGUAGE);
    }

}