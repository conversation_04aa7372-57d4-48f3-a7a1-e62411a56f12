package id.co.bri.brimo.contract.IView.pengelolaankartu;

import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.DetailLimitCardResponse;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SubmitCustomLimitResponse;

public interface IDetailLimitCardView extends IMvpView {
    void onSuccessGetDetails(DetailLimitCardResponse response);

    void onSuccessSubmitLimit(SubmitCustomLimitResponse response);

    void onMaxLimitTrial(EmptyStateResponse response);

    void onErrorChange(RestResponse response);
}
