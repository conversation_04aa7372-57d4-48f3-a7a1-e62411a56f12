package id.co.bri.brimo.adapters.dashboard

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemMenuFourBinding
import id.co.bri.brimo.domain.config.MenuConfig
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.revamp.dashboard.KategoriMenuModel

class MenuDefaultAdapter(
    private var mListKategori: List<KategoriMenuModel>
) : RecyclerView.Adapter<MenuDefaultAdapter.ViewHolder>() {

    inner class ViewHolder(val binding: ItemMenuFourBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding =
            ItemMenuFourBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun getItemCount(): Int {
        return mListKategori.size
    }

    @SuppressLint("DiscouragedApi")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.binding.namaMenu.setTextColor(GeneralHelper.getColor(R.color.neutralDark40))

        val currentKategori = mListKategori[position]

        currentKategori.let {
            holder.binding.namaMenu.text = it.kategori_nama
            val id = GeneralHelper.getImageId(
                holder.binding.root.context,
                it.icon
            )
            holder.binding.Gambar.setImageResource(id)

            holder.binding.rlMenu.setOnClickListener {
                MenuConfig.onMenuDefaultClick(
                    holder.binding.root.context as Activity?,
                    mListKategori[position].id
                )
            }
        }
    }
}