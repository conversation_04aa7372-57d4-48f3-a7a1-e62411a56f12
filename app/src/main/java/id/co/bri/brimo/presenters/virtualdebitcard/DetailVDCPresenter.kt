package id.co.bri.brimo.presenters.virtualdebitcard

import id.co.bri.brimo.contract.IPresenter.virtualdebitcard.IDetailVDCPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.virtualdebitcard.IDetailVDCView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.InquiryBrizziRequest
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.InitChangePinRequest
import id.co.bri.brimo.models.apimodel.request.virtualdebitcard.EditLabelVDCRequest
import id.co.bri.brimo.models.apimodel.request.virtualdebitcard.EnableDisableTransactionVDCRequest
import id.co.bri.brimo.models.apimodel.request.virtualdebitcard.GenerateCvvVDCRequest
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.InitChangePinResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.DetailVDCResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.GenerateCvvVDCResponse
import id.co.bri.brimo.presenters.MvpPresenter
import id.co.bri.brimo.util.extension.getDataWithOrWithoutRequest
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class DetailVDCPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IDetailVDCPresenter<V> where V : IMvpView, V : IDetailVDCView {

    private lateinit var urlGenerateCVV: String
    private lateinit var urlEnableDisableTransactionVDC: String
    private lateinit var urlGetDetailVirtualCard: String
    private lateinit var urlUpdateLabelVDC: String

    private lateinit var requestDetailVDC: InquiryBrizziRequest

    override fun setUrlGenerateCVV(url: String) {
        this.urlGenerateCVV = url
    }

    override fun generateCVV(request: GenerateCvvVDCRequest) {
        if (urlGenerateCVV.isEmpty() && !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlGenerateCVV, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)) {
                                val responseGenerateCvv =
                                    response.getData(GenerateCvvVDCResponse::class.java)
                                getView().onSuccessGenerateCVV(responseGenerateCvv)
                            } else if (response.code.equals("US")) {
                                val responseSM = response.getData(GeneralResponse::class.java)
                                getView().showSafetyMode(responseSM)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                getView().onSessionEnd(restResponse.desc)
                            else getView().onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun setUrlEnableDisableTransactionVDC(url: String) {
        this.urlEnableDisableTransactionVDC = url
    }

    override fun enableDisableTransactionVDC(request: EnableDisableTransactionVDCRequest) {
        if (urlEnableDisableTransactionVDC.isEmpty() && !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlEnableDisableTransactionVDC, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val responseSuccess =
                                response.getData(EmptyStateResponse::class.java)
                            getView().onSuccessEnableDisableVDC(responseSuccess)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                getView().onSessionEnd(restResponse.desc)
                            else getView().onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun setUrlDetailVDC(url: String) {
        this.urlGetDetailVirtualCard = url
    }

    override fun getDetailVDC(cardNumber: String, snackBarResponse: String) {
        if (urlGetDetailVirtualCard.isEmpty() && !isViewAttached) return

        requestDetailVDC = InquiryBrizziRequest(cardNumber)

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlGetDetailVirtualCard, requestDetailVDC, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val responseDetailVDC =
                                response.getData(DetailVDCResponse::class.java)
                            getView().onSuccessGetDetailVDC(responseDetailVDC, snackBarResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                getView().onSessionEnd(restResponse.desc)
                            else getView().onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun setUrlEditLabelVDC(url: String) {
        this.urlUpdateLabelVDC = url
    }

    override fun updateLabelVDC(request: EditLabelVDCRequest) {
        if (urlUpdateLabelVDC.isEmpty() && !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlUpdateLabelVDC, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            getView().onSuccessUpdateLabelVDC(response.desc)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                getView().onSessionEnd(restResponse.desc)
                            else getView().onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun getChangePinRefNum(url: String?, cardNumber: String?) {
        val request = InitChangePinRequest(cardNumber.orEmpty())
        view.getDataWithOrWithoutRequest(
            url,
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            true,
            request,
            onApiCallError = { restResponse ->
                view.onException(restResponse.desc)
            },
            onFailureHttp = {
                view.onException(it)
            }
        ) {
            view.onSuccessGetChangePinRefNum(it.getData(InitChangePinResponse::class.java))
        }
    }
}