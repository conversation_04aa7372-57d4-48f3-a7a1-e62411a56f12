package id.co.bri.brimo.models.apimodel.response.applyccrevamp

import com.google.gson.annotations.SerializedName

data class GetOtpGeneralResponse(
    @SerializedName("server_id") val serverId: String? = null,
    @SerializedName("duration_sec") val durationSec: Int? = null,
    @SerializedName("skip") val skip: Boolean? = null,
    @SerializedName("reference_number") val referenceNumber: String? = null,
    @SerializedName("cellphone_number") val cellphoneNumber: String? = null,
)