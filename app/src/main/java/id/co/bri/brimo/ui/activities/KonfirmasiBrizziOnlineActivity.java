package id.co.bri.brimo.ui.activities;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.DetailTransaksiAdapter;
import id.co.bri.brimo.adapters.TotalKonfirmasiAdapter;
import id.co.bri.brimo.contract.IView.general.IGeneralConfirmationView;
import id.co.bri.brimo.databinding.ActivityKonfirmasiBrizziOnlineBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.apimodel.response.DetailListType;
import id.co.bri.brimo.models.apimodel.response.TrackingDataResponse;
import id.co.bri.brimo.models.apimodel.response.onExceptionWH;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.ListAllCashbackFilterResponse;
import id.co.bri.brimo.models.apimodel.response.PaymentTarikResponse;
import id.co.bri.brimo.models.apimodel.response.PendingResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.tartun.ReceiptTarikActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import id.co.bri.brimo.ui.fragments.PinFragment;
import id.co.bri.brizzi.Brizzi;

public class KonfirmasiBrizziOnlineActivity extends BaseActivity implements IGeneralConfirmationView, PinFragment.SendPin, View.OnClickListener, DialogExitCustom.DialogDefaultListener {

    private ActivityKonfirmasiBrizziOnlineBinding binding;

    private static final String TAG = "KonfirmasiBrizziOnlineA";

    DetailTransaksiAdapter detailTransaksiAdapter;
    TotalKonfirmasiAdapter totalKonfirmasiAdapter;
    DetailListType sourceAccountDataViews;
    DetailListType billingDetails;

    protected GeneralConfirmationResponse mConfirmResponse;
    protected String mUrlPayment;
    protected String mTitle;
    protected ParameterKonfirmasiModel mParameterKonfirmasiModel;
    static Brizzi mbrizzi;
    protected static String errorMessage = null;
    protected static String mJourneyType;

    protected static String TAG_KONFIRMASI_DATA = "konfirmasi_data";
    protected static String TAG_PARAMS_KONFIRM_DATA = "params_konfirm_data";
    protected static String TAG_URL_PAYMENT = "url_payment";
    protected static String TAG_TITLE = "url_title";

    public static void launchIntent(Activity caller, GeneralConfirmationResponse generalConfirmationResponse, String urlPayment, String title, ParameterKonfirmasiModel parameterKonfirmasiModel, Brizzi brizzi, String journeyType) {
        Intent intent = new Intent(caller, KonfirmasiBrizziOnlineActivity.class);

        mbrizzi = brizzi;
        mJourneyType = journeyType;

        if (generalConfirmationResponse != null) {
            try {
                intent.putExtra(TAG_KONFIRMASI_DATA, new Gson().toJson(generalConfirmationResponse));
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG_KONFIRMASI_DATA, "launchIntent: ", e);
            }
        }

        if (parameterKonfirmasiModel != null) {
            try {
                intent.putExtra(TAG_PARAMS_KONFIRM_DATA, new Gson().toJson(parameterKonfirmasiModel));
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG_PARAMS_KONFIRM_DATA, "launchIntent: ", e);
            }
        }

        //set URL service
        try {
            intent.putExtra(TAG_URL_PAYMENT, urlPayment);
            intent.putExtra(TAG_TITLE, title);
        } catch (Exception e) {
            if (!GeneralHelper.isProd())
                Log.e(TAG, "launchIntent: ", e);
        }

        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityKonfirmasiBrizziOnlineBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        //parsing data intent
        if (getIntent().getExtras() != null) {
            parseDataIntentKonfirmasi(getIntent().getExtras());
        }

        setupView();

        GeneralHelper.setToolbar(this, binding.tbKonfirmasi.toolbar, GeneralHelper.getString(R.string.str_konfirmasi));
        cekErrorMessage();

        binding.btnSubmit.setOnClickListener(this);


        if (Build.VERSION.SDK_INT >= 21) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.setStatusBarColor(getResources().getColor(R.color.toolbar_blue));
        }


        if (GeneralHelper.isProd())
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE,
                    WindowManager.LayoutParams.FLAG_SECURE);

    }

    /**
     * Method digunakan untuk meng-extract data Intent dari Form Activity
     *
     * @param savedInstanceState Bundle savedInstanceState
     */
    protected void parseDataIntentKonfirmasi(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            try {

                //get data konfirmasi dari intent
                String tempConfirm = savedInstanceState.getString(TAG_KONFIRMASI_DATA);
                if (tempConfirm != null) {
                    mConfirmResponse = new Gson().fromJson(tempConfirm, GeneralConfirmationResponse.class);
                }

                //get data parameter dari intent
                String tempParamsKonfirm = savedInstanceState.getString(TAG_PARAMS_KONFIRM_DATA);
                if (tempParamsKonfirm != null) {
                    mParameterKonfirmasiModel = new Gson().fromJson(tempParamsKonfirm, ParameterKonfirmasiModel.class);
                }

                mUrlPayment = savedInstanceState.getString(TAG_URL_PAYMENT);
                mTitle = savedInstanceState.getString(TAG_TITLE);

            } catch (Exception e) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "parseDataIntentInquiry: ", e);
                }
            }
        }
    }


    protected void setupView() {

        try {
            sourceAccountDataViews = mConfirmResponse.getSourceAccountDataView();

            //Detail Rekening Asal
            binding.tvNama.setText(sourceAccountDataViews.getTitle());
            binding.tvNorek.setText(sourceAccountDataViews.getSubtitle());
            binding.tvInisial.setText(GeneralHelper.formatInitialName(sourceAccountDataViews.getTitle()));

            //Detail Nominal
            binding.rvDetailPayment.setHasFixedSize(true);
            binding.rvDetailPayment.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            detailTransaksiAdapter = new DetailTransaksiAdapter(mConfirmResponse.getAmountDataView(), this);
            binding.rvDetailPayment.setAdapter(detailTransaksiAdapter);

            binding.rvTotalPayment.setHasFixedSize(true);
            binding.rvTotalPayment.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            totalKonfirmasiAdapter = new TotalKonfirmasiAdapter(mConfirmResponse.getTotalDataView(), this);
            binding.rvTotalPayment.setAdapter(totalKonfirmasiAdapter);
        } catch (Exception e) {

        }


        //Detail Nama dan Billing
        if (mConfirmResponse.getBillingDetail() != null) {
            parseDetailTagihan();
        } else {
            binding.llDetailBilling.setVisibility(View.GONE);
        }


        binding.lblTujuan.setText(mParameterKonfirmasiModel.getStringLabelTujuan());
        binding.btnSubmit.setText(mParameterKonfirmasiModel.getStringButtonSubmit());

    }

    /**
     * Cek data dari intent jika ada message error dari Tap BRIZZI
     */
    protected void cekErrorMessage() {
        if (errorMessage != null) {
            //menampilkan snacknar error
            showSnackbarErrorMessage(errorMessage, ALERT_ERROR, this, false);

            //clear error message
            errorMessage = null;
        }
    }

    @Override
    public void onSuccessGetPayment(PendingResponse paymentResponse) {
        if (paymentResponse.getImmediatelyFlag().equals(true)) {
            if (paymentResponse.getTitleImage().equalsIgnoreCase(Constant.RECEIPT68) || paymentResponse.getTitleImage().equalsIgnoreCase(Constant.RECEIPT58)) {
                ReceiptPendingActivity.launchIntent(this, paymentResponse);
            } else if (paymentResponse.getTitleImage().equalsIgnoreCase(Constant.RECEIPT00)) {
                ReceiptActivity.launchIntent(this, paymentResponse);
            }
        }
        //else PendingTransferBankLainActivity.launchIntent(this, paymentResponse, isFromFastMenu);
    }

    @Override
    public void onSuccessGetPaymentRevamp(ReceiptRevampResponse receiptRevampResponse) {
        // do nothing
    }

    @Override
    public void onSuccessGetPaymentRevampTracking(ReceiptRevampResponse receiptRevampResponse) {
        // do nothing
    }

    @Override
    public void onSuccesGetTarikTunai(PaymentTarikResponse paymentTarikResponse) {
        ReceiptTarikActivity.launchIntent(this, paymentTarikResponse);
    }

    protected void parseDetailTagihan() {
        billingDetails = mConfirmResponse.getBillingDetail();

        if (billingDetails != null) {

            //Set Image Circle
            if (billingDetails.getListType().equals("image")) {
                binding.llLogo.setVisibility(View.VISIBLE);
                binding.rlInisial.setVisibility(View.GONE);
                //load icon transaction
                GeneralHelper.loadIconTransaction(
                        this,
                        billingDetails.getIconPath(),
                        billingDetails.getIconName(),
                        binding.ivIcon,
                        mParameterKonfirmasiModel.getDefaultIcon());
            } else {
                binding.llLogo.setVisibility(View.GONE);
                binding.rlInisial.setVisibility(View.VISIBLE);
                //Set Initial
                String title = billingDetails.getTitle();
                binding.tvInisialTujuan.setText(GeneralHelper.formatInitialName(title));
            }

            //View
            binding.tvTujuan.setText(billingDetails.getTitle());
            binding.tvNomorPayment.setText(billingDetails.getSubtitle());
        }
    }


    @Override
    public void onExceptionTrxExpired(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onException(String message) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showDialogGagalBack(this, message);
        else
            showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
    }

    @Override
    public void onSendPinComplete(String pin) {
        CekBrizziDuaActivity.launchIntent(this, mConfirmResponse, mUrlPayment, pin, "BRIZZI", mParameterKonfirmasiModel, mbrizzi, isFromFastMenu);
    }

    @Override
    public void onLupaPin() {
        //TO DO routing
        if (isFromFastMenu) LupaPinFastActivity.launchIntent(this);
        else LupaPinActivity.launchIntent(this);
    }

    @Override
    public void onBackPressed() {
        DialogExitCustom dialogExitCustom = new DialogExitCustom(this::onClickYes, GeneralHelper.getString(R.string.cancel_trx), GeneralHelper.getString(R.string.cancel_trx_confirm));
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onClick(View view) {
        PinFragment pinFragment = new PinFragment(this, this);
        pinFragment.show();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED);
                if (data != null) {
                    if (data.getStringExtra(Constant.TAG_ERROR_PIN_BRIZZI) != null) {
                        errorMessage = data.getStringExtra(Constant.TAG_ERROR_PIN_BRIZZI);
                        cekErrorMessage();
                    } else {
                        this.setResult(RESULT_CANCELED, data);
                        this.finish();
                    }
                }


            }
        } else if (requestCode == Constant.REQ_FORGET_PIN) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK);
                this.finish();
            }
        }
    }

    @Override
    public void onException01(String message) {
        GeneralHelper.showDialogGagalBackDescBerubah(this, Constant.TRANSAKSI_GAGAL, message);
        setResult(RESULT_OK);

    }

    @Override
    public void onSuccessGetCashback(ListAllCashbackFilterResponse responses) {
        // do nothing
    }

    @Override
    public void onCashbackBlank(String message) {
        // do nothing
    }

    @Override
    public void onExceptionWH(onExceptionWH onOnExceptionWH) {
        // do nothing, only for invesment when transaction out of office our
    }

    @Override
    public void setDefaultSaldo(double saldoPref, String saldoStringPref, String accountPref, boolean saldoHoldPref) {
        // do nothing
    }


    @Override
    public void onClickYes() {
        super.onBackPressed();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}