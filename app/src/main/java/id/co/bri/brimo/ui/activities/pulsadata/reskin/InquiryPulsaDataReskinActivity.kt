package id.co.bri.brimo.ui.activities.pulsadata.reskin

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.google.android.material.tabs.TabLayoutMediator
import id.co.bri.brimo.databinding.ActivityInquiryPulsaDataReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.ProviderItem
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.fragments.bill.PulsaReskinFragment
import android.os.Parcelable
import android.view.View
import com.google.android.material.tabs.TabLayout
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.pulsarevamp.reskin.IPulsaDataReskinPresenter
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.AccountListEntity
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.IPulsaDataReskinView
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.request.KonfirmasiPulsaRequest
import id.co.bri.brimo.models.apimodel.response.DataListRevamp
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.PulsaList
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SaldoReponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.presenters.listrikrevamp.reskin.FavoriteType
import id.co.bri.brimo.ui.fragments.SumberDanaFragment
import id.co.bri.brimo.ui.fragments.bill.DataReskinFragment
import id.co.bri.brimo.util.RxBus
import kotlinx.parcelize.Parcelize
import java.io.InputStreamReader
import javax.inject.Inject
import androidx.core.graphics.toColorInt

class InquiryPulsaDataReskinActivity: NewSkinBaseActivity(), View.OnClickListener,
    SumberDanaFragment.SelectSumberDanaInterface, IPulsaDataReskinView {
    private var _binding: ActivityInquiryPulsaDataReskinBinding? = null
    protected val binding get() = _binding!!

    private lateinit var bindInquiry: ReqInquiry

    private var isClick = false
    private var useApi = true

    private var counter: Int = 0

    private var nominalSelected: PulsaList ?= null

    private var nominalDataSelected: DataListRevamp ?= null

    protected var mListFailed: List<Int>? = null
    protected var mListAccountModel: List<AccountModel>? = null

    private lateinit var selectedAccount: AccountModel
    private var buyType: String = "pulsa"

    @Inject
    lateinit var presenter: IPulsaDataReskinPresenter<IPulsaDataReskinView>

    private var hideCurrency = false

    companion object {
        const val TAG = "InquiryPulsaDataReskinActivity"

        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean, reqInquiry: ReqInquiry) {
            isFromFastMenu = fromFastMenu
            val bundle = Bundle()

            Intent(caller, InquiryPulsaDataReskinActivity::class.java).let { intent ->
                intent.putExtras(bundle.apply{
                    putParcelable(TAG, reqInquiry)
                })
                caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityInquiryPulsaDataReskinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        onBindIntentData()

        injectDependency()
        GeneralHelper.setToolbarNs(this, binding.toolbar.toolbar, "Pulsa & Paket Data")

        onBindView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)

        presenter.apply {
            view = this@InquiryPulsaDataReskinActivity
            if(useApi) {
                if(!isFromFastMenu) getAccountList()
            }
            start()
        }
    }

    private fun onBindView() {
        binding.ivShowhideCurrency.visibility = if(isFromFastMenu) View.GONE else View.VISIBLE

        binding.ivShowhideCurrency.setOnClickListener {
            hideCurrency = hideCurrency.toggle()
            binding.ivShowhideCurrency.setImageResource(if(!hideCurrency) R.drawable.icon_unhide_eye else R.drawable.icon_hide_eye)
            onChangeSourcePayment(selectedAccount)
        }
        if(!useApi) {
            mListAccountModel = parseFromJson()
            selectedAccount = parseFromJson()[0]

            onChangeSourcePayment(selectedAccount)
        }

        binding.lyTotalDetail.setOnClickListener(this)
        binding.btnSubmit.setOnClickListener(this)
        binding.bivNoPelanggan.apply {
            setEnabled(false)
            setText(bindInquiry.phone)
            setExpandHint(true)
            setStartIconWithUrl(bindInquiry.identityPhone.iconPath)
        }
        val tabTitles = listOf("Pulsa", "Paket Data")
        val adapter = object: FragmentStateAdapter(this) {
            override fun getItemCount() = tabTitles.size
            override fun createFragment(position: Int): Fragment {
                return when (position) {
                    0 -> {
                        PulsaReskinFragment(bindInquiry.identityPhone.pulsaList, { sSelected ->
                            nominalSelected = sSelected
                            nominalDataSelected = null
                            onChangeSourcePayment(selectedAccount)
                        })
                    }
                    else -> {
                        DataReskinFragment(bindInquiry.identityPhone.dataList, bindInquiry.identityPhone.omniUrl, { sSelected ->
                            nominalDataSelected = sSelected
                            nominalSelected = null
                            onChangeSourcePayment(selectedAccount)
                        })
                    }
                }
            }
        }

        binding.viewPager.adapter = adapter

        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = tabTitles[position]
        }.attach()

        binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                buyType = if(tab.position == 0) "pulsa" else "data"
                RxBus.send(EventInquiryPulsaData(isClear = true, type = buyType))
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {}
            override fun onTabReselected(tab: TabLayout.Tab) {}
        })
    }

    private fun onBindIntentData() {
        intent?.let {
            bindInquiry = it.getParcelableExtra(TAG)!!

            if(isFromFastMenu) {
                mListAccountModel = bindInquiry.accountList
                onChangeSourcePayment(bindInquiry.accountList[0])
            }
        }
    }

    private fun parseFromJson(): List<AccountModel> {
        val inputStream = assets.open("data/mockjsonaccount.json")
        val reader = InputStreamReader(inputStream)

        val gson = Gson()
        val listType = object : TypeToken<List<AccountModel>>() {}.type

        return gson.fromJson(reader, listType)
    }

    override fun onClick(v: View?) {
        when(v?.id) {
            R.id.ly_total_detail -> {
                counter++
                val selectedIndex = mListAccountModel?.indexOfFirst {
                    selectedAccount.acoountString.equals(it.acoountString)
                }

                val amount = if(nominalSelected==null) 0 else nominalSelected?.value
                val amountData = if(nominalDataSelected==null) 0 else nominalDataSelected?.amount

                val fragmentSumberDana =
                    SumberDanaFragment(mListAccountModel, this, counter, mListFailed, selectedIndex!!, if(buyType == "pulsa") amount?:0 else amountData?:0, isFromFastMenu)
                fragmentSumberDana.show(supportFragmentManager, Constant.TAG_PICK_ACCOUNT)
            }
            R.id.btnSubmit -> {
                presenter.getDataConfirmationPulsa(KonfirmasiPulsaRequest(
                    bindInquiry.referrenceNumber,
                    if(buyType.contains("pulsa")) bindInquiry.identityPhone.pulsaCode else bindInquiry.identityPhone.dataCode,
                    "0${bindInquiry.phone}",
                    if(buyType.contains("pulsa")) nominalSelected?.value.toString() else nominalDataSelected?.amount.toString(),
                    "",
                    if(buyType.contains("pulsa")) nominalSelected?.value.toString() else nominalDataSelected?.id,
                    buyType,
                    ""
                ))
            }
        }
    }

    override fun onSelectSumberDana(bankModel: AccountModel) {
        onChangeSourcePayment(bankModel)
        binding.btnSubmit.isEnabled = true
    }

    override fun onSendFailedList(list: MutableList<Int>?) {
        this.mListFailed = list
    }

    private fun onChangeSourcePayment(bankModel: AccountModel) {
        binding.tvNumberAccount.text = if(hideCurrency) maskAccountNumber(bankModel.acoountString) else bankModel.acoountString
        selectedAccount = bankModel

        GeneralHelper.loadIconTransaction(
            this,
            bankModel.imagePath,
            bankModel.imageName,
            binding.ivRekening,
            R.drawable.img_card_bg)

        if(bankModel.saldoReponse != null) {
            val saldoReponse: SaldoReponse = bankModel.saldoReponse

            if (saldoReponse.getBalanceString().equals("TO", ignoreCase = true)) {
                binding.tvNominalAccount.text = GeneralHelper.getString(R.string.gagal_memuat)
            } else if (saldoReponse.getBalanceString().equals("12", ignoreCase = true)) {
                binding.tvNominalAccount.text = GeneralHelper.getString(R.string.empty)
            } else if (saldoReponse.balanceString.equals("05", ignoreCase = true)) {
                binding.tvNominalAccount.text = GeneralHelper.getString(R.string.empty)
            } else {
                val currency = saldoReponse.currency
                isClick = true

                val nominalIdr = GeneralHelper.formatNominalIDR(
                    saldoReponse.currency,
                    saldoReponse.balanceString
                )

                val balance = saldoReponse.balance.toInt()
                val payAmount = if(nominalSelected!=null) nominalSelected?.value else if(nominalDataSelected!=null) nominalDataSelected?.amount else 0

                binding.tvWarningCurrency.apply {
                    visibility = if(balance < (payAmount ?: 0)) View.VISIBLE else View.GONE
                    fadeIn()
                }
                binding.tvNominalAccount.apply {
                    setTextColor(if(balance < (payAmount ?: 0)) "#E84040".toColorInt() else "#181C21".toColorInt())
                    fadeIn()
                }

                binding.btnSubmit.isEnabled = balance >= (payAmount ?: 0)

                if(hideCurrency) {
                    binding.tvNominalAccount.text = maskAmount(saldoReponse.balance)
                } else {
                    binding.tvNominalAccount.text = if (saldoReponse.signedBalanceString.isNotEmpty()
                    ) saldoReponse.signedBalanceString else if (currency != null) nominalIdr.substring(
                        0,
                        nominalIdr.length - 3
                    ) else saldoReponse.balanceString
                }
            }
        }
    }

    override fun onSuccessGetData(formPulsaDataResponse: FormPulsaDataResponse) {
    }

    override fun onSuccessGetDataConfirmation(response: GeneralConfirmationResponse) {
        finish()
        ConfirmationPulsaDataActivity.launchIntent(this, isFromFastMenu, response, selectedAccount)
    }

    override fun onSuccessGetPayment(receiptRevampResponse: ReceiptRevampResponse) {

    }

    override fun onException12(message: String) {
    }

    override fun onException93(message: String) {
    }

    override fun onExceptionGetDataForm(message: String) {
    }

    override fun fromFastmenu(): Boolean {
        return isFromFastMenu
    }

    override fun onSuccess(
        data: RestResponse,
        type: FavoriteType
    ) {

    }

    override fun onSuccessAccountList(dataList: MutableList<AccountModel>) {
        mListAccountModel = dataList
        onChangeSourcePayment(dataList[0])
    }

    fun maskAccountNumber(account: String): String {
        return if (account.length >= 11) {
            val first4 = account.substring(0, 4)
            val last3 = account.takeLast(3)
            "$first4 **** **** $last3"
        } else {
            "**** **** ****"
        }
    }

    fun maskAmount(amount: Double): String {
        return "Rp" + "•".repeat(8)
    }
}

// Fade Out
fun View.fadeIn(duration: Long = 300) {
    this.apply {
        alpha = 0f
        animate()
            .alpha(1f)
            .setDuration(duration)
            .setListener(null)
    }
}

fun Boolean.toggle() = !this

@Parcelize
data class ReqInquiry(
    val phone: String,
    val identityPhone: ProviderItem,
    val referrenceNumber: String,
    val accountList: List<AccountModel> = mutableListOf()
): Parcelable

data class EventInquiryPulsaData(
    val isClear: Boolean = false,
    val type: String = ""
)