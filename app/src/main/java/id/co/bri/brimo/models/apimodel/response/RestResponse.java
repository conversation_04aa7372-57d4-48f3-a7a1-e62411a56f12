package id.co.bri.brimo.models.apimodel.response;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.List;

/**
 * Created by kuthux on 3/7/17.
 */

public class RestResponse {
    @SerializedName("code")
    protected String code;
    @SerializedName("description")
    protected String desc;
    @SerializedName("data")
    protected Object data;

    public static final String CODE_ALERT = "12";
    public static final String CODE_ALERT_FINISH = "93";
    public static final String CODE_SESSION_END = "05";

    public RestResponse(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public RestResponse(String code, String desc, Object data) {
        this.code = code;
        this.desc = desc;
        this.data = data;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String toString() {
        return
                "code:'" + code + '\'' +
                        ", desc:'" + desc + '\'' +
                        ", data:'" + data + '\''
                ;
    }

    public boolean isSuccessful() {
        if (code == null) return false;

        if (code.equals(CODE_ALERT) || code.equals(CODE_ALERT_FINISH) || code.equals(CODE_SESSION_END))
            return false;
        else
            return true;
    }

    public Object getData() {
        return data;
    }

    public <T> T getData(Class<T> type) {
        Gson gson = new Gson();
        return gson.fromJson(gson.toJson(data), type);
    }

    public <T> List<T> getDataList(Class<T> innerClass) {
        Gson gson = new Gson();
        Type type = TypeToken.getParameterized(List.class, innerClass).getType();
        return gson.fromJson(gson.toJson(data), type);
    }

    public RestResponse getRestResponse() {
        if (data == null)
            return new RestResponse(code, desc);
        else
            return new RestResponse(code, desc, data);
    }

    public enum ResponseCodeEnum {

        RC_SUCCESS("00"),
        R_01("R1"),
        RC_01("01"),
        RC_02("02"),
        RC_03("03"),
        RC_04("04"),
        RC_SESSION_END("05"),
        RC_HIT_EXCEEDED("06"),
        RC_08("08"),
        RC_09("09"),
        RC_10("10"),
        RC_12("12"),
        RC_ACCOUNT_NOT_FOUND("14"),
        RC_REFRESH("21"),
        RC_58("58"),
        RC_59("59"),
        RC_60("60"),
        RC_61("61"),
        RC_LIMIT_EXCEEDED("61"),
        RC_70("70"),
        RC_78("78"),
        RC_79("79"),
        RC_88_BILL_ALREADY_PAID("88"),
        RC_89("89"),
        RC_TRX_EXPIRED("93"),
        RC_94("94"),
        RC_99("99"),
        RC_ENABLE_SINGALARITY("426"),
        RC_DISABLE_SINGALARITY("428"),
        RC_S1("S1"),
        RC_S2("S2"),
        RC_FL("FL"),
        RC_SM("SM"),
        RC_C1("C1"),
        RC_C2("C2"),
        RC_LOGIN_EXCEED("Q2"),
        RC_OP("OP"),
        RC_NF("NF"),
        RC_TO("TO"),
        RC_FO("FO"),
        RC_S3E("S3E"),
        RC_STATUS_NOT_MATCH("SM"),
        RC_INSUFFICIENT_BALANCE_RDN("IL"),
        MAX_ATTEMPT_ACTIVATION("TA"),
        RC_IC("IC"),
        RC_NB("NB"),
        RC_NR("NR"),
        RC_ALREADY_PAID("88"),
        RC_UV("UV");

        private final String value;

        ResponseCodeEnum(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}