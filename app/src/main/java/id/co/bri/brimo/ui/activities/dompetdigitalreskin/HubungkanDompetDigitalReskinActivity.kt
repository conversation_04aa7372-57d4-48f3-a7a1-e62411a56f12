package id.co.bri.brimo.ui.activities.dompetdigitalreskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.saldodompetdigital.IHubungkanDompetDigitalPresenter
import id.co.bri.brimo.contract.IView.saldodompetdigital.IHubungkanDompetDigitalView
import id.co.bri.brimo.databinding.ActivityHubungkanDompetDigitalReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.models.apimodel.response.EwalletBindingResponse
import id.co.bri.brimo.models.apimodel.response.EwalletProductResponse
import id.co.bri.brimo.models.apimodel.response.SyaratKetentuanResponse
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.saldodompetdigital.WebViewBindingActivity
import javax.inject.Inject

class HubungkanDompetDigitalReskinActivity : NewSkinBaseActivity(),
    IHubungkanDompetDigitalView, View.OnClickListener {

    private lateinit var binding: ActivityHubungkanDompetDigitalReskinBinding
    private var selectedWallet: EwalletProductResponse? = null
    private var cellphoneNumber: String = ""

    @Inject
    lateinit var presenter: IHubungkanDompetDigitalPresenter<IHubungkanDompetDigitalView>

    companion object {
        private const val EXTRA_WALLET_JSON = "extra_wallet_json"
        private const val EXTRA_CELLPHONE_NUMBER = "extra_cellphone_number"

        fun launchIntent(
            caller: Activity,
            wallet: EwalletProductResponse,
            cellphoneNumber: String = "",
            requestCode: Int
        ) {
            val intent = Intent(caller, HubungkanDompetDigitalReskinActivity::class.java)
            intent.putExtra(EXTRA_WALLET_JSON, Gson().toJson(wallet))
            intent.putExtra(EXTRA_CELLPHONE_NUMBER, cellphoneNumber)
            caller.startActivityForResult(intent, requestCode)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityHubungkanDompetDigitalReskinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Get wallet data from intent
        val walletJson = intent.getStringExtra(EXTRA_WALLET_JSON)
        selectedWallet = walletJson?.let {
            Gson().fromJson(it, EwalletProductResponse::class.java)
        }

        cellphoneNumber = intent.getStringExtra(EXTRA_CELLPHONE_NUMBER) ?: ""

        injectDependency()
        setupView(cellphoneNumber)
        setupButtonClick()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlEwalletBinding(GeneralHelper.getString(R.string.url_ewallet_binding))
    }

    private fun setupView(cellphoneNumber: String) {
        selectedWallet?.let { wallet ->
            val toolbarTitle = GeneralHelper.getString(R.string.ewallet)
            GeneralHelperNewSkin.setToolbar(this, binding.toolbar.toolbar, toolbarTitle)

            // Set title text
            binding.tvTitle.text = String.format(
                getString(R.string.txt_hubungkan_e_wallet),
                wallet.title ?: ""
            )

            // Load wallet icon
            GeneralHelper.loadIconTransaction(
                this,
                wallet.iconPath ?: "",
                wallet.iconName?.split(".")?.get(0) ?: "",
                binding.ivLogoDompet,
                GeneralHelper.getImageId(this, "ic_menu_qna_dompet_digital")
            )

            val phoneNumber = if (cellphoneNumber.isNotEmpty()) {
                cellphoneNumber
            } else {
                ""
            }
            binding.tvNoPelanggan.text = phoneNumber

            // Set description text
            binding.tvDesc.text = String.format(
                getString(R.string.txt_desc_hubungkan_e_wallet),
                wallet.title ?: ""
            )
        }
    }

    private fun setupButtonClick() {
        binding.btnSubmit.setOnClickListener(this)
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.btnSubmit -> {
                selectedWallet?.let { wallet ->
                    // Call binding API
                    presenter.bindingEwallet(
                        wallet.code ?: "",
                        wallet.country_code ?: ""
                    )
                }
            }
        }
    }

    // IHubungkanDompetDigitalView implementation
    override fun onSuccessGetTnc(syaratKetentuanResponse: SyaratKetentuanResponse) {
        // Do nothing
    }

    override fun onException12(message: String) {
        hideProgress()
        showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
    }

    override fun onSuccessBinding(ewalletBindingResponse: EwalletBindingResponse) {
        hideProgress()

        // Check status_code to determine the next action
        if (ewalletBindingResponse.statusCode == "200") {
            // Navigate to success binding activity
            selectedWallet?.let { wallet ->
                SuccessBindingDompetDigitalReskinActivity.launchIntent(
                    this,
                    wallet,
                    ewalletBindingResponse,
                    cellphoneNumber
                )
            }
        } else {
            // Handle other status codes - redirect to WebView for additional steps
            // This follows the same pattern as the original HubungkanDompetDigitalActivity
            WebViewBindingActivity.launchIntent(this, ewalletBindingResponse)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            Constant.REQ_WALLET_BINDING_SUCCESS -> {
                if (resultCode == RESULT_OK) {
                    // Wallet binding was successful, pass result back to FormDompetDigitalReskinActivity
                    setResult(RESULT_OK, data)
                    finish()
                }
            }
        }
    }

    override fun onException01(ewalletBindingResponse: EwalletBindingResponse) {
        hideProgress()
        // Handle binding error
        if (ewalletBindingResponse.extrasResponse != null) {
            val extrasResponse = ewalletBindingResponse.extrasResponse
            showSnackbarErrorMessage(
                extrasResponse.desc ?: "Terjadi kesalahan saat menghubungkan e-wallet",
                ALERT_ERROR,
                this,
                false
            )
        } else {
            showSnackbarErrorMessage(
                "Terjadi kesalahan saat menghubungkan e-wallet",
                ALERT_ERROR,
                this,
                false
            )
        }
    }
//
//    override fun onException(message: String) {
//        hideProgress()
//        showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
//    }

    override fun onSessionEnd(message: String) {
        hideProgress()
        super.onSessionEnd(message)
    }

    override fun showProgress() {
        super.showProgress()
    }

    override fun hideProgress() {
        super.hideProgress()
    }
}
