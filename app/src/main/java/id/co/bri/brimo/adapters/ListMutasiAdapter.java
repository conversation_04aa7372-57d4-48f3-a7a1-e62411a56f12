package id.co.bri.brimo.adapters;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ItemMutasiBinding;
import id.co.bri.brimo.models.apimodel.response.MutasiResponse;
import id.co.bri.brimo.models.apimodel.response.TransaksiMutasiResponse;

import java.util.List;

import io.rmiri.skeleton.master.AdapterSkeleton;

public class ListMutasiAdapter extends AdapterSkeleton<MutasiResponse, ListMutasiAdapter.MyViewHolder> {

    private List<TransaksiMutasiResponse> transaksiMutasiResponses;
    private Activity activity;


    public ListMutasiAdapter(Activity activity, List<TransaksiMutasiResponse> transaksiMutasiResponse) {
        this.activity = activity;
        this.transaksiMutasiResponses = transaksiMutasiResponse;
    }

    @NonNull
    @Override
    public ListMutasiAdapter.MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new MyViewHolder(ItemMutasiBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ListMutasiAdapter.MyViewHolder holder, int position) {

        if (transaksiMutasiResponses.get(position).getType().equals("C")) {
            holder.binding.tvNominalMutasi.setTextColor(ContextCompat.getColor(activity, R.color.green_ns_main));
        } else {
            holder.binding.tvNominalMutasi.setTextColor(ContextCompat.getColor(activity, R.color.ns_black900));
        }

        holder.binding.tvTransaksi.setText(transaksiMutasiResponses.get(position).getRemark());
        holder.binding.tvDetailTransaksi.setText(transaksiMutasiResponses.get(position).getRemark());
        holder.binding.tvTanggalTransaksi.setText(transaksiMutasiResponses.get(position).getDate());
        holder.binding.tvNominalMutasi.setText(transaksiMutasiResponses.get(position).getAmountString());
        holder.binding.tvTimeMutasi.setText(transaksiMutasiResponses.get(position).getTime());
    }

    public static class MyViewHolder extends RecyclerView.ViewHolder {

        ItemMutasiBinding binding;

        public MyViewHolder(ItemMutasiBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

    public int getItemCount() {
        return transaksiMutasiResponses.size();
    }
}