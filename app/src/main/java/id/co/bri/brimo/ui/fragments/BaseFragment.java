package id.co.bri.brimo.ui.fragments;

import android.Manifest;
import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.appsflyer.AppsFlyerLib;
import com.appsflyer.attribution.AppsFlyerRequestListener;

import java.util.Map;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.di.components.ActivityComponent;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralResponse;
import id.co.bri.brimo.ui.activities.FastMenuActivity;
import id.co.bri.brimo.ui.activities.FastMenuNewSkinActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.transactionlimitinformation.TransactionLimitInformationActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment;
import id.co.bri.brimo.ui.widget.BrimoAppsWidget;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;

public class BaseFragment extends Fragment implements IMvpView {

    public static final int ALERT_CONFIRM = -1;
    public static final int ALERT_ERROR = -2;
    public static final int ALERT_ERROR_AKUN = -3;
    private BaseActivity mActivity;
    private int shortAnimationDuration = 0;
    protected long mLastClickTime = 0;
    private static final String UPDATE_WIDGET = "android.appwidget.action.APPWIDGET_UPDATE";

    public static String[] storage_permissions = {
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE
    };

    @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
    public static String[] storage_permissions_33 = {
            Manifest.permission.READ_MEDIA_IMAGES,
            Manifest.permission.READ_MEDIA_VIDEO
    };

    public static String[] permissions() {
        String[] permission;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permission = storage_permissions_33;
        } else {
            permission = storage_permissions;
        }
        return permission;
    }

    protected String[] PERMISSIONS = permissions();
    protected static final int PERMISSIONS_ALL = 1;

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (context instanceof BaseActivity) {
            BaseActivity activity = (BaseActivity) context;
            this.mActivity = activity;
        }
    }

    public ActivityComponent getActivityComponent() {
        if (mActivity != null) {
            return mActivity.getActivityComponent();
        }
        return null;
    }

    public BaseActivity getBaseActivity() {
        return mActivity;
    }

    /**
     * Textwatcher untuk mengakomodir button submit enable disable
     * callback pada method afterText dan beforeText
     */
    protected TextWatcher fragmentTextListener = new TextWatcher() {

        @Override
        public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            beforeText();
        }

        @Override
        public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            changeText(charSequence, i, i1, i2);
        }

        @Override
        public void afterTextChanged(Editable editable) {
            afterText(editable);
        }
    };

    protected void changeText(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);


    }

    /**
     * callback dari textWatcher (fragmentTextListener)
     *
     * @param editable editable dari afterTextChanged
     */
    protected void afterText(Editable editable) {

    }

    protected void beforeText() {

    }

    protected void onAnimatorFadeEnd(String tagIdFade) {

    }

    public void onAnimatorFade(View view, boolean isVisible, String tagIdFade) {
        if (isVisible)
            view.setVisibility(View.VISIBLE);
        else
            view.setVisibility(View.GONE);

        ObjectAnimator anim = ObjectAnimator.ofFloat(view, "alpha", 1f, 0);
        anim.setDuration(700).addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                onAnimatorFadeEnd(tagIdFade);
                view.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                view.setVisibility(View.GONE);
                onAnimatorFadeEnd(tagIdFade);
            }

        });
        anim.start();
    }

    protected void onAnimatorShowEnd(String tagId) {

    }

    public void onAnimatorShow(View view, boolean isVisible, String tagId) {
        if (isVisible)
            view.setVisibility(View.VISIBLE);

        ObjectAnimator anim = ObjectAnimator.ofFloat(view, "alpha", 0, 1f);
        anim.setDuration(700).addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                view.setVisibility(View.VISIBLE);
                onAnimatorShowEnd(tagId);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                onAnimatorShowEnd(tagId);
                view.setVisibility(View.VISIBLE);
            }

        });
        anim.start();
    }


    @Override
    public void onRootedDevice() {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(getActivity())
                .setTitle("Peringatan")
                .setMessage("Maaf, Anda tidak diijinkan untuk masuk ke aplikasi BRImo karena perangkat Anda terindikasi telah di-root")
                .setNeutralButton("Close", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialogInterface, int i) {
                        getActivity().finish();
                    }
                })
                .setIcon(R.drawable.ic_close_black_24dp)
                .setCancelable(false);
        alertDialog.show();
    }

    public void showSnackbarErrorMessage(String message, int messageType, Fragment fragment, boolean isFragment) {
        try {
            switch (messageType) {
                case ALERT_CONFIRM:
                    if (getActivity() != null)
                        GeneralHelper.showSnackBarGreen(getActivity().findViewById(R.id.content), message);
                    break;
                case ALERT_ERROR:
                    if (getActivity() != null)
                        GeneralHelper.showSnackBar(getActivity().findViewById(R.id.content), message);
                    break;
            }
        } catch (Exception e) {
            //do nothing
        }

    }


    public void showSnackbarErrorMessageRevamp(String message, int messageType, Activity activity, boolean isFragment) {
        try {
            switch (messageType) {
                case ALERT_CONFIRM:
                    GeneralHelper.showSnackBarGreenRevamp(getActivity().findViewById(R.id.content), message);
                    break;
                case ALERT_ERROR:
                    GeneralHelper.showSnackBarRevamp(getActivity().findViewById(R.id.content), message);
                    break;
            }
        } catch (Exception e) {
            //do nothing
        }
    }

    public void showSnackbarErrorMessageRevampAkun(String message, int messageType) {
        try {
            switch (messageType) {
                case ALERT_CONFIRM:
                    GeneralHelper.showSnackBarGreenRevamp(getActivity().findViewById(R.id.contentProfile), message);
                    break;
                case ALERT_ERROR:
                    GeneralHelper.showSnackBarRevamp(getActivity().findViewById(R.id.contentProfile), message);
                    break;
                case ALERT_ERROR_AKUN:
                    GeneralHelper.showSnackBarRevampLines(getActivity().findViewById(R.id.contentProfile), message, 3);
                    break;
            }
        } catch (Exception e) {
            //do nothing
        }
    }

    protected void callbackDismissSnackbar() {

    }

    public static boolean hasPermissions(Context context, String... permissions) {
        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && context != null && permissions != null) {
            for (String permission : permissions) {
                if (ActivityCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public void showProgress() {
        GeneralHelper.showDialog(getBaseActivity());
    }

    @Override
    public void hideProgress() {
        GeneralHelper.dismissDialog();
    }

    @Override
    public void onSessionEnd(String message) {
        hideProgress();
//        FastMenuActivity.launchIntentSessionEnd(getBaseActivity(), message);
        FastMenuNewSkinActivity.launchIntentSessionEnd(getBaseActivity(), message);
    }

    @Override
    public void onException(String message) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showBottomDialog(getBaseActivity(), message);
        else
            showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
    }

    @Override
    public void onExceptionRevamp(String message) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showDialogGagalBack(getBaseActivity(), message);
        else
            showSnackbarErrorMessageRevamp(message, ALERT_ERROR, getActivity(), false);
    }

    @Override
    public void onException06(ExceptionResponse response) {
        GeneralHelper.showDialogCustom(getActivity(), response);
    }

    @Override
    public void onException99(String message) {

    }

    public void trackAppsFlyerAnalyticEvent(String eventName, Map<String, Object> eventValue) {
        AppsFlyerLib.getInstance().logEvent(getContext(), eventName, eventValue, new AppsFlyerRequestListener() {
            @Override
            public void onSuccess() {
                if (!GeneralHelper.isProd()) {
                    Log.d(Constant.APPSFLYER_TAG, "Event sent successfully");
                }
            }

            @Override
            public void onError(int i, @NonNull String s) {
                if (!GeneralHelper.isProd()) {
                    Log.d(Constant.APPSFLYER_TAG, "Event failed to be sent:\n" +
                            "Error code: " + i + "\n"
                            + "Error description: " + s);
                }
            }
        });
    }

    @Override
    public void onExceptionFO(EmptyStateResponse response) {
        FragmentBottomDialog fragmentBottomDialog = new FragmentBottomDialog(getActivity(), Constant.IMAGE_SERVER_UNDER_MAINTENANCE, response.getDescription(), response.getSubDescription(), "img_flag_off", true);
        fragmentBottomDialog.setCancelable(false);
        fragmentBottomDialog.show(getFragmentManager(), "");
    }

    protected void disableScreenShoot() {
        if (getActivity() != null && GeneralHelper.isProd()) {
            getActivity().getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }
    }

    protected void crossfadeAnimation(View viewVisible, View viewGone) {
        viewVisible.setAlpha(0f);
        viewVisible.setVisibility(View.VISIBLE);

        viewVisible.animate()
                .alpha(1f)
                .setDuration(shortAnimationDuration)
                .setListener(null);

        viewGone.animate()
                .alpha(0f)
                .setDuration(shortAnimationDuration)
                .setListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        viewGone.setVisibility(View.GONE);
                    }
                });
    }

    @Override
    public void onExceptionLimitExceed(GeneralResponse response) {
        OpenBottomSheetGeneralFragment.INSTANCE.showDialogConfirmation(
                getChildFragmentManager(),
                response.getImagePath(),
                response.getImageName(),
                response.getTitle(),
                response.getDescription(),
                createKotlinFunction0(firstBtnFunction),
                createKotlinFunction0(secondBtnFunction),
                false, GeneralHelper.getString(R.string.see_account_types_and_limit),
                GeneralHelper.getString(R.string.see_account_types_and_limit_second_button), false
        );
    }

    @Override
    public void onExceptionNoBackAction(String message) {

    }

    @Override
    public void onExceptionStatusNotMatch() {
        // do nothing
    }

    public static Function0<Unit> createKotlinFunction0(Runnable action) {
        return () -> {
            action.run();
            return Unit.INSTANCE;
        };
    }

    Runnable firstBtnFunction = () -> {
        Intent newIntent = new Intent(getBaseActivity(), TransactionLimitInformationActivity.class);
        startActivity(newIntent);
    };

    Runnable secondBtnFunction = () -> {
        requireActivity().finish();
    };

    public void updateWidget(Context context) {
        // Send broadcast to update the widget
        Intent intent = new Intent(context, BrimoAppsWidget.class);
        intent.setAction(UPDATE_WIDGET);
        context.sendBroadcast(intent);
    }

    protected void showAlertPermission(String msg) {
        AlertDialog.Builder builder = new AlertDialog.Builder(getActivity());
        builder.setMessage(msg)
                .setPositiveButton(GeneralHelper.getString(R.string.setting), (dialog, which) -> {
                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    Uri uri = Uri.fromParts("package", requireActivity().getPackageName(), null);
                    intent.setData(uri);
                    requireActivity().startActivity(intent);
                })
                .setNegativeButton(GeneralHelper.getString(R.string.batal2), (dialog, which) -> dialog.dismiss())
                .setCancelable(false)
                .show();
    }

    /**
     * Method untuk menampilkan dialog message ketika gps tidak aktif
     */
    public void showDialogEnableLocation(){
        DialogExitCustom dialogExitCustom = new DialogExitCustom(()->{
            Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
            startActivity(intent);
        }, GeneralHelper.getString(R.string.txt_check_gps_title_dialog), GeneralHelper.getString(R.string.txt_check_gps_subtitle_dialog),
                GeneralHelper.getString(R.string.Cancel),GeneralHelper.getString(R.string.Pengaturan_txt));
        FragmentTransaction ft = getParentFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    public void restartActivity() {
        Intent intent = requireActivity().getIntent();
        intent.putExtra(Constant.IS_CHANGE_LANGUAGE, true);
        requireActivity().startActivity(intent);
        requireActivity().finish();
    }

    public void setupTransparentStatusBar() {
        Window window = requireActivity().getWindow();
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        window.getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
        );
        window.setStatusBarColor(Color.TRANSPARENT);
    }

    public void resetTransparentStatusBar() {
        Window window = requireActivity().getWindow();
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        window.setStatusBarColor(ContextCompat.getColor(requireContext(), R.color.colorPrimary));
        window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);
    }

}