package id.co.bri.brimo.ui.activities;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.View;

import androidx.annotation.Nullable;

import com.google.gson.Gson;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ActivityGeneralSyaratBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.cc.DetailCcSofResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.cc_sof.FormAktivasiCcSofActivity;

public class GeneralSyaratActivity extends BaseActivity implements View.OnClickListener {

    private ActivityGeneralSyaratBinding binding;

    protected static final String TAG_CONDITION = "term_condition";
    protected static final String TAG_ARROW = "is_arrow";
    protected static final String TAG_RESPONSE = "response";
    protected static final String TAG_CCSOF = "ccsof";
    private static final String FROMQR = "fromqr";
    protected static final String TAG_ISMULTIPLETNC = "ismultipletnc";
    protected static final String TAG_TNC_POSITION = "tncposition";

    protected String termCond;
    protected boolean isCcSof;
    protected boolean isArrow;
    protected static boolean mIsMultipleTnc = false;
    protected static int mTncPosition;
    private static boolean withBackground;

    protected DetailCcSofResponse detailResponse;

    public static void launchIntent(Activity caller, String termCondition) {
        Intent intent = new Intent(caller, GeneralSyaratActivity.class);
        intent.putExtra(TAG_CONDITION, termCondition);
        caller.startActivityForResult(intent, Constant.REQ_PETTUNJUK1);
    }

    public static void launchIntentNoArrow(Activity caller, String termCondition, boolean isArrow) {
        Intent intent = new Intent(caller, GeneralSyaratActivity.class);
        intent.putExtra(TAG_CONDITION, termCondition);
        intent.putExtra(TAG_ARROW, String.valueOf(isArrow));
        caller.startActivityForResult(intent, Constant.REQ_PETTUNJUK1);
    }

    public static void launchIntent(Activity caller, String termCondition, DetailCcSofResponse detailCcSofResponse, boolean isCcSof) {
        Intent intent = new Intent(caller, GeneralSyaratActivity.class);
        intent.putExtra(TAG_CONDITION, termCondition);
        intent.putExtra(TAG_RESPONSE, new Gson().toJson(detailCcSofResponse));
        intent.putExtra(TAG_CCSOF, String.valueOf(isCcSof));
        caller.startActivityForResult(intent, Constant.REQ_CC_SOF);
    }

    public static void launchIntentFromQr(Activity caller, String termCondition, DetailCcSofResponse detailCcSofResponse, boolean isCcSof) {
        Intent intent = new Intent(caller, GeneralSyaratActivity.class);
        intent.putExtra(TAG_CONDITION, termCondition);
        intent.putExtra(TAG_RESPONSE, new Gson().toJson(detailCcSofResponse));
        intent.putExtra(TAG_CCSOF, String.valueOf(isCcSof));
        intent.putExtra(FROMQR, true);
        caller.startActivityForResult(intent, Constant.REQ_CC_SOF);
    }

    public static void launchIntent(Activity caller, String termCondition, boolean isMultipleTnc, int tncPosition, boolean withBackgrounds) {
        mIsMultipleTnc = isMultipleTnc;
        mTncPosition = tncPosition;
        withBackground = withBackgrounds;
        Intent intent = new Intent(caller, GeneralSyaratActivity.class);
        intent.putExtra(TAG_CONDITION, termCondition);
        caller.startActivityForResult(intent, Constant.REQ_PETTUNJUK1);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityGeneralSyaratBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        if (getIntent().getExtras() != null) {
            if (getIntent().getExtras().getString(TAG_CONDITION) != null)
                termCond = getIntent().getExtras().getString(TAG_CONDITION);

            if (getIntent().getExtras().getString(TAG_ARROW) != null) {
                isArrow = Boolean.parseBoolean(getIntent().getExtras().getString(TAG_ARROW));
            }
        }

        if (getIntent().getExtras().getString(TAG_CONDITION) != null)
            termCond = getIntent().getExtras().getString(TAG_CONDITION);

        if (getIntent().getExtras().getString(TAG_CCSOF) != null)
            isCcSof = Boolean.parseBoolean(getIntent().getExtras().getString(TAG_CCSOF));

        if (getIntent().getExtras().getString(TAG_RESPONSE) != null)
            detailResponse = new Gson().fromJson(getIntent().getExtras().getString(TAG_RESPONSE), DetailCcSofResponse.class);

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.tnc));

        if (withBackground) {
            binding.content.setBackgroundColor(getResources().getColor(R.color.accent3Color));
        }

        GeneralHelper.setWebView(binding.wvSyarat, "", termCond);

        if (!isArrow) {
            binding.scrollview.getViewTreeObserver().addOnScrollChangedListener(() -> {
                int scrollY = binding.scrollview.getScrollY(); //for verticalScrollView
                if (scrollY == 0)
                    binding.imgBawah.setVisibility(View.VISIBLE);
                else
                    binding.imgBawah.setVisibility(View.GONE);
            });
        } else
            binding.imgBawah.setVisibility(View.GONE);

        binding.btSetuju.setOnClickListener(this);
        binding.btBatal.setOnClickListener(this);
        binding.btSetujuBawah.setOnClickListener(this);
        binding.btBatalBawah.setOnClickListener(this);
        binding.imgBawah.setOnClickListener(this);

        DisplayMetrics displayMetrics = new DisplayMetrics();
        getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        int height = displayMetrics.heightPixels;

        if (isArrow) {
            binding.btSetuju.setVisibility(View.GONE);
            binding.btBatal.setVisibility(View.GONE);
            binding.imgBawah.setVisibility(View.GONE);

            binding.btSetujuBawah.setVisibility(View.VISIBLE);
            binding.btBatalBawah.setVisibility(View.VISIBLE);
        } else {
            binding.imgBawah.setVisibility(View.VISIBLE);
            binding.btSetuju.setVisibility(View.VISIBLE);
            binding.btBatal.setVisibility(View.VISIBLE);

            binding.btSetujuBawah.setVisibility(View.GONE);
            binding.btBatalBawah.setVisibility(View.GONE);

        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.bt_setuju:
            case R.id.bt_setuju_bawah:
                if (isCcSof) {
                    if (getIntent().hasExtra(FROMQR)) {
                        FormAktivasiCcSofActivity.launchIntentFromQr(this, detailResponse);
                    } else {
                        FormAktivasiCcSofActivity.launchIntent(this, detailResponse);
                    }

                } else {
                    Intent resultIntentOk = new Intent();
                    if (mIsMultipleTnc)
                        resultIntentOk.putExtra("checkbox" + mTncPosition, String.valueOf(true));
                    else
                        resultIntentOk.putExtra("checkbox", String.valueOf(true));

                    setResult(Activity.RESULT_OK, resultIntentOk);
                    finish();
                }
                break;
            case R.id.bt_batal:
            case R.id.bt_batal_bawah:
                Intent resultIntentGl = new Intent();
                if (mIsMultipleTnc)
                    resultIntentGl.putExtra("checkbox" + mTncPosition, String.valueOf(false));
                else
                    resultIntentGl.putExtra("checkbox", String.valueOf(false));
                setResult(Activity.RESULT_OK, resultIntentGl);
                finish();
                break;
            case R.id.img_bawah:
                binding.scrollview.scrollTo(0, binding.btBatal.getBottom());
                binding.imgBawah.setVisibility(View.GONE);
                break;
            default:
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_CC_SOF) {
            if (resultCode == Activity.RESULT_OK && data != null) {
                setResult(Activity.RESULT_OK, data);
                finish();
            } else if (resultCode == Activity.RESULT_CANCELED && data != null) {
                setResult(Activity.RESULT_CANCELED, data);
                finish();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}