package id.co.bri.brimo.ui.fragments.dashboardhistory

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.bukarekeningnewskin.FilterMonthNewSkinAdapter
import id.co.bri.brimo.adapters.bukarekeningnewskin.ListActivityAdapter
import id.co.bri.brimo.contract.IPresenter.inbox.IInboxPresenter
import id.co.bri.brimo.contract.IView.inbox.IInboxView
import id.co.bri.brimo.databinding.FragmentActivityHistoryBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.calendar.makeGone
import id.co.bri.brimo.domain.helpers.calendar.makeVisible
import id.co.bri.brimo.models.apimodel.response.DataView
import id.co.bri.brimo.models.apimodel.response.DetailListType
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse
import id.co.bri.brimo.models.apimodel.response.FilterAktivityResponse
import id.co.bri.brimo.models.apimodel.response.InboxResponse
import id.co.bri.brimo.models.apimodel.response.InboxResponse.ActivityList
import id.co.bri.brimo.models.apimodel.response.PendingResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptAmkkmResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptInternasionalResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampInboxResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptTravelTrainResponse
import id.co.bri.brimo.models.apimodel.response.esbn.ReceiptResponseNew
import id.co.bri.brimo.payment.app.PaymentActivity
import id.co.bri.brimo.ui.activities.DashboardIBActivity
import id.co.bri.brimo.ui.activities.InboxFilterActivity
import id.co.bri.brimo.ui.activities.ReceiptActivity
import id.co.bri.brimo.ui.activities.ReceiptInboxActivity
import id.co.bri.brimo.ui.activities.ReceiptPendingActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.britamajunio.ReceiptOpenJunioActivity
import id.co.bri.brimo.ui.activities.bukarekening.PendingTabunganActivity.Companion.launchIntent
import id.co.bri.brimo.ui.activities.bukarekening.ReceiptTabunganActivity.Companion.launchIntent
import id.co.bri.brimo.ui.activities.emas.ReceiptGagalAFTActivity.Companion.launchIntent
import id.co.bri.brimo.ui.activities.lifestyle.ReceiptLifestyleActivity.Companion.launchIntent
import id.co.bri.brimo.ui.activities.lifestyle.ReceiptLifestyleActivity.Companion.launchIntentIsFromConfirmation
import id.co.bri.brimo.ui.activities.receipt.ReceiptAbnormalRevampActivity
import id.co.bri.brimo.ui.activities.receipt.ReceiptRevampActivity
import id.co.bri.brimo.ui.activities.sbn.earlyredemption.ReceiptEarlyRedeemActivity.Companion.launchIntent
import id.co.bri.brimo.ui.activities.simpedes.ReceiptAmkkmActivity
import id.co.bri.brimo.ui.activities.transferinternasional.ReceiptTransferInternationalActivity
import id.co.bri.brimo.ui.activities.travel.ReceiptTravelTrainActivity
import id.co.bri.brimo.ui.fragments.NewSkinBaseFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import javax.inject.Inject

class HistoryActivityFragment : NewSkinBaseFragment(), ListActivityAdapter.OnClickItem,
    ListActivityAdapter.OnSelected, FilterMonthNewSkinAdapter.OnItemClickListener, IInboxView, SwipeRefreshLayout.OnRefreshListener {

    private lateinit var binding: FragmentActivityHistoryBinding

    @Inject
    lateinit var inboxPresenter: IInboxPresenter<IInboxView>

    private var listActivityAdapter: ListActivityAdapter? = null
    var activityLists: MutableList<ActivityList?> = ArrayList()
    var layoutManager: LinearLayoutManager? = null
    private var handler: Handler = Handler()
    private var skeletonScreenInbox: SkeletonScreen? = null

    var periode: String = ""
    var status: String = ""
    var fitur: String = ""
    var subFitur: String = ""
    var lastId: String = ""
    var mRefnum: String = ""
    private var isLoading = false
    private var hasShownErrorDialog = false
    private var position: Int? = null
    private var isErrorDetail = false
    private var isLoadPagination = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        injectDependency()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentActivityHistoryBinding.inflate(inflater, container, false)

        binding.swipe.setOnRefreshListener(this)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initiateAdapter()
        setupView()
    }

    private fun injectDependency() {

        activityComponent.inject(this)
        inboxPresenter.view = this
        inboxPresenter.start()
        callService()

    }

    private fun callService() {

        periode = ""
        status = ""
        fitur = ""
        subFitur = ""
        lastId = "0"

        skeletonScreenInbox?.show()

        inboxPresenter.setUrlInbox(GeneralHelper.getString(R.string.url_activity_list))
        inboxPresenter.setUrlDetailInbox(GeneralHelper.getString(R.string.url_activity_detail))

        inboxPresenter.getInbox(periode, status, fitur, subFitur, lastId, true)

    }

    private fun checkRefNumNotif() {
        if (mRefnum.isNotEmpty()) {
            activityLists.forEach { activityList ->
                if (mRefnum == activityList?.referenceNumber) {
                    inboxPresenter.setTrxType(activityList.trxType)
                    inboxPresenter.getInboxDetail(mRefnum)
                    return@forEach
                }
            }
            mRefnum = ""
        }
    }

    private fun setupView() {

        binding.rlTitle.viewResetFilter.tvChip.text = getString(R.string.reset)

        binding.rlTitle.viewFilterIcon.setOnClickListener {
            inboxPresenter.getFilterInbox()
        }

        binding.rlTitle.viewResetFilter.viewConnect.setOnClickListener {

            callService()
            binding.rlTitle.ivBullet.makeGone()
            binding.rlTitle.viewResetFilter.root.makeGone()

        }

    }

    private fun initiateAdapter() {

        listActivityAdapter = ListActivityAdapter(activityLists, requireActivity(), this, this)
        binding.rvDetailLis.layoutManager = LinearLayoutManager(requireActivity(), RecyclerView.VERTICAL, false)
        binding.rvDetailLis.adapter = listActivityAdapter

        binding.rvDetailLis.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                val layoutManager = recyclerView.layoutManager as? LinearLayoutManager ?: return

                val lastVisibleItem = layoutManager.findLastCompletelyVisibleItemPosition()
                val totalItemCount = layoutManager.itemCount

                if (!isLoading && lastVisibleItem == activityLists.size - 1 && totalItemCount >= 10) {
                    loadMoreInbox()
                    isLoading = true
                    isLoadPagination = true
                }
            }
        })

        skeletonScreenInbox =
            Skeleton.bind(binding.content).shimmer(true).angle(20).duration(1200)
                .color(R.color.white).load(R.layout.item_skeleton_transaction_history).show()

    }

    private fun loadMoreInbox() {
        activityLists.add(null)
        listActivityAdapter?.notifyItemInserted(activityLists.size - 1)

        handler.postDelayed({
            activityLists.removeAt(activityLists.size - 1)
            val scrollPosition = activityLists.size
            listActivityAdapter?.notifyItemRemoved(scrollPosition)

            inboxPresenter.getInbox(periode, status, fitur, subFitur, lastId, false)
        }, 1000)
    }

    private fun showError() {

        if (hasShownErrorDialog) return

        hasShownErrorDialog = true

        val firstBtnFunction = Runnable {
            hasShownErrorDialog = false
            isLoadPagination = false

            if (isErrorDetail) {
                position?.let { pos ->
                    activityLists[pos]?.let { item ->
                        inboxPresenter.setTrxType(item.trxType)
                        inboxPresenter.getInboxDetail(item.referenceNumber)
                    }
                }
            } else {
                skeletonScreenInbox?.show()
                binding.rvDetailLis.makeVisible()
                binding.viewEmpty.root.makeGone()
                inboxPresenter.getInbox(periode, status, fitur, subFitur, "", true)
            }
        }
        OpenBottomSheetGeneralNewSkinFragment.showDialogInformation1Button(
            requireActivity().supportFragmentManager,
            "",
            Constant.IC_SAD_NEW_NS,
            getString(R.string.failed_load_page_title),
            getString(R.string.failed_load_page_subtitle),
            BaseActivity.createKotlinFunction0(firstBtnFunction),
            true,
            getString( R.string.refresh),
            true
        )

    }

    override fun onResume() {
        super.onResume()
        hasShownErrorDialog = false
    }

    companion object {
        @JvmStatic
        fun newInstance(): HistoryActivityFragment {
            val fragment = HistoryActivityFragment()
            return fragment
        }

    }

    override fun onClickDetail(position: Int) {
        this.position = position
        hasShownErrorDialog = false
        val type = activityLists[position]?.trxType

        val blockedTypes = listOf(
            "PaymentOpenInsuranceS3f",
            "PurchaseKaiTravel",
            "PaymentTransferInternationalSwift",
            "PaymentPrudential",
            "PaymentAllianz"
        )

        inboxPresenter.setTrxType(type)
        if (type !in blockedTypes) {
            inboxPresenter.getInboxDetail(activityLists[position]?.referenceNumber)
        }
    }

    override fun onSelectedItemId(position: Int) {

        if (position == activityLists.size - 1) {
            activityLists[position]?.id?.let {
                lastId = it
            }
        }

    }

    override fun onItemClick(month: String) {
        //do nothing
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessGetInbox(inboxResponse: InboxResponse?, isRefresh: Boolean) {
        inboxResponse?.let { response ->
            if (isRefresh) {
                activityLists.clear()
            }

            binding.swipe.isRefreshing = false
            skeletonScreenInbox?.hide()

            try {
                validateButton(true)
                binding.apply {
                    viewEmpty.root.makeGone()
                    rvDetailLis.makeVisible()
                    rvDetailLis.smoothScrollToPosition(activityLists.size)
                }

                activityLists.addAll(response.activityLists)
                listActivityAdapter?.setItems(activityLists)
                listActivityAdapter?.notifyDataSetChanged()
                checkRefNumNotif()
            } catch (_: Exception) {
                // Optional: log or handle error silently
            }

            isLoading = false
            isLoadPagination = false
        }

    }

    override fun onInboxEnd(message: String?) {

        if (activityLists.isEmpty()) {
            binding.swipe.isRefreshing = false
            validateButton(true)
            showEmptyState()
        }

    }

    override fun onSuccessGetFilterData(filterAktivityResponse: FilterAktivityResponse?) {
        InboxFilterActivity.launchIntent(resultLauncher, activity, filterAktivityResponse, periode, status)
    }

    override fun onSuccessGetInboxDetail(receiptResponse: ReceiptResponse?) {
        if (receiptResponse!!.pendingResponses.titleImage.equals(
                Constant.RECEIPT68,
                ignoreCase = true
            ) || receiptResponse!!.pendingResponses.titleImage.equals(
                Constant.RECEIPT58,
                ignoreCase = true
            )
        ) {
            val pendingResponse = receiptResponse.pendingResponses.toMappedResponse()
            toReceipt(pendingResponse)
        } else if (receiptResponse.pendingResponses.titleImage.equals(
                Constant.RECEIPT00,
                ignoreCase = true
            )
        ) {
            val pendingResponse = receiptResponse.pendingResponses.toMappedResponse()
            toReceipt(pendingResponse)
        }
    }


    private fun defaultIcon(): Int {
        return R.drawable.bri
    }

    override fun onSuccessGetInboxDetailRevamp(receiptRevampResponse: ReceiptRevampInboxResponse?) {
        if (receiptRevampResponse!!.receiptRevampResponse != null) {
            if (java.lang.Boolean.TRUE == receiptRevampResponse.receiptRevampResponse.isOnProcess) {
                ReceiptAbnormalRevampActivity.launchIntentReceipt(
                    activity,
                    receiptRevampResponse.receiptRevampResponse,
                    false,
                    defaultIcon()
                )
            } else {
                toReceipt(receiptRevampResponse.receiptRevampResponse)
            }
        }
    }

    override fun onSuccessGetInboxAmkkmDetail(amkkmResponse: ReceiptAmkkmResponse?) {
        var flag = false
        if (amkkmResponse!!.dataAlamatResiko[0].name != null) {
            flag = true
        }
        ReceiptAmkkmActivity.launchIntentFromActivity(activity, amkkmResponse, flag)
    }

    override fun onSuccessGetOpenAccountDetail(pendingResponse: PendingResponse?) {
        val pendingResponses = pendingResponse?.toMappedResponse()
        toReceipt(pendingResponses)
    }

    override fun onSuccessGetOpenJunio(pendingResponse: PendingResponse?) {
        val pendingResponses = pendingResponse?.toMappedResponse()
        toReceipt(pendingResponses)
    }

    override fun onSuccessInternasional(receiptInternasionalResponse: ReceiptInternasionalResponse?) {
        ReceiptTransferInternationalActivity.launchIntent(activity, receiptInternasionalResponse)
    }

    override fun onSuccessKai(receiptTravelTrainResponse: ReceiptTravelTrainResponse?) {
        ReceiptTravelTrainActivity.launchIntentReceipt(activity, receiptTravelTrainResponse)
    }

    override fun onSuccessPencairan(responseNew: ReceiptResponseNew?) {
        launchIntent(requireActivity(), responseNew!!)
    }

    fun validateButton(isEnabled: Boolean) {
        binding.rlTitle.viewFilterIcon.isEnabled = isEnabled
    }

    private fun showEmptyState() {

        skeletonScreenInbox?.hide()
        binding.rvDetailLis.makeGone()
        binding.viewEmpty.root.makeVisible()
        binding.viewEmpty.tvTitleEmpty.text = getString(R.string.txt_no_history)
        binding.viewEmpty.tvDescEmpty.text = getString(R.string.txt_no_history_desc)

    }

    override fun onException(message: String) {
        showEmptyState()
        binding.swipe.isRefreshing = false
        isErrorDetail = false
        showError()
    }

    override fun onExceptionNoBackAction(message: String?) { // error get detail receipt
        binding.swipe.isRefreshing = false
        isErrorDetail = true
        showError()
    }

    override fun onSessionEnd(message: String?) {

        binding.swipe.isRefreshing = false

        (activity as DashboardIBActivity).onSessionEndToLogin(message)
        inboxPresenter.stop()

    }

    override fun onException12() {

        binding.swipe.isRefreshing = false
        isErrorDetail = false
        validateButton(false)
        if (!isLoadPagination) {
            showEmptyState()
        }
        showError()

    }

    override fun onException93() {

        binding.swipe.isRefreshing = false
        isErrorDetail = false
        validateButton(false)
        showEmptyState()
        showError()

    }

    override fun onException06(response: ExceptionResponse?) {
        binding.swipe.isRefreshing = false
        isErrorDetail = false
        validateButton(false)
        binding.rvDetailLis.makeGone()
        showEmptyState()
        showError()

    }

    override fun onSuccessGetReceiptRevamp(receiptRevampResponse: ReceiptRevampInboxResponse?) {
        if (receiptRevampResponse!!.receiptRevampResponse.isOnProcess) {
            ReceiptAbnormalRevampActivity.launchIntentReceipt(
                activity,
                receiptRevampResponse.receiptRevampResponse,
                false,
                defaultIcon()
            )
        } else {
            toReceipt(receiptRevampResponse.receiptRevampResponse)
        }
    }

    override fun onSuccessTabunganRevamp(
        pendingResponse: ReceiptRevampResponse?,
        trxType: String?
    ) {
        if (pendingResponse!!.immediatelyFlag) {
            toReceipt(pendingResponse)

        } else launchIntent(
            requireActivity(),
            pendingResponse, GeneralHelper.getString(R.string.url_buka_tabungan_pending_revamp)
        )
    }

    override fun onSuccesTabunganS3fRevamp(pendingResponse: ReceiptRevampResponse?) {
        if (pendingResponse != null) {
            toReceipt(pendingResponse)
        }

    }

    override fun onSuccesOnboardEmas(pendingResponse: ReceiptRevampResponse?) {
        if (pendingResponse?.immediatelyFlag == true) {
            toReceipt(pendingResponse)
        } else {
            if (pendingResponse != null) {
                launchIntent(
                    requireActivity(),
                    pendingResponse,
                    GeneralHelper.getString(R.string.url_buka_tabungan_pending_revamp)
                )
            }
        }
    }

    override fun onSuccessInboxPattern(receiptRevampResponse: ReceiptRevampInboxResponse?) {
        if (receiptRevampResponse!!.receiptRevampResponse != null) {
            if (java.lang.Boolean.TRUE == receiptRevampResponse!!.receiptRevampResponse.isOnProcess) {
                ReceiptAbnormalRevampActivity.launchIntentReceipt(
                    activity,
                    receiptRevampResponse.receiptRevampResponse,
                    false,
                    defaultIcon()
                )
            } else {
                toReceipt(receiptRevampResponse.receiptRevampResponse)
            }
        }
    }

    override fun onSuccessReceiptTicketEvent(receiptRevampResponse: ReceiptRevampInboxResponse?) {
        toReceipt(receiptRevampResponse?.receiptRevampResponse)
    }

    override fun onAbnormalReceipt(receiptRevampResponse: ReceiptRevampInboxResponse?) {
        toReceipt(receiptRevampResponse?.receiptRevampResponse)
    }

    override fun onRefresh() {

        skeletonScreenInbox?.show()
        binding.rvDetailLis.makeVisible()
        binding.viewEmpty.root.makeGone()
        inboxPresenter.getInbox(periode, status, fitur, subFitur, "0", true)

    }

    private val resultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->

        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.let {
                periode = it.getStringExtra("PERIODE") ?: ""
                status = it.getStringExtra("STATUS") ?: ""
                fitur = it.getStringExtra("FITUR") ?: ""
                subFitur = it.getStringExtra("SUBFITUR") ?: ""
                lastId = it.getStringExtra("LASTID") ?: ""

                binding.rlTitle.ivBullet.makeVisible()
                binding.rlTitle.viewResetFilter.root.makeVisible()
                binding.rlTitle.viewResetFilter.viewConnect.backgroundTintList = ContextCompat.getColorStateList(requireActivity(), R.color.primary_ns_100)
                binding.rlTitle.viewResetFilter.tvChip.setTextColor(ContextCompat.getColor(requireActivity(), R.color.primary_ns_600))

                activityLists.clear()
                skeletonScreenInbox?.show()

                inboxPresenter.getInbox(periode, status, fitur, subFitur, lastId, true)
            }
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        activityLists.clear()
    }

    private fun toReceipt(pendingResponse: ReceiptRevampResponse?) {
        val intent = Intent(requireActivity(), PaymentActivity::class.java)
        intent.putExtra("destination", "receipt");
        intent.putExtra("source", "daily_banking");
        intent.putExtra("pending_data", Gson().toJson(pendingResponse));
        startActivity(intent)
    }

    private fun PendingResponse.toMappedResponse(): ReceiptRevampResponse {
        val response = ReceiptRevampResponse()

        response.immediatelyFlag = this.immediatelyFlag
        response.headerDataView = this.headerDataView?.let { ArrayList(it) }
        response.voucherDataView = arrayListOf(this.voucherDataView)
        response.transactionDataView = this.transactionDataView?.let { ArrayList(it) }
        response.minimDataTransaction = this.minimDataTransaction?.let { ArrayList(it) } ?: ArrayList()
        response.detailOpenJunio = this.detailOpenJunio?.let { ArrayList(it) }
        response.amountDataView = this.amountDataView?.let { ArrayList(it) }
        response.kursDataView = this.kursDataView?.let { ArrayList(it) }
        response.totalDataView = this.totalDataView?.let { ArrayList(it) }
        response.streamingId = this.streamingId
        response.title = this.title
        response.subTitle = this.subTitle
        response.description = this.description
        response.titleImage = this.titleImage
        response.referenceNumber = this.referenceNumber
        response.footer = this.footer
        response.footerHtml = this.footerHtml
        response.rowDataShow = this.rowDataShow
        response.transactionDate = this.transactionDate
        response.transactionImage = this.transactionImage
        response.referenceDataView = this.referenceDataView?.let { ArrayList(it) }
        response.mainDataView = this.mainDataView?.let { ArrayList(it) }
        response.subtitle = this.subTitle
        response.transactionSuccess = this.transactionSuccess
        response.trackingDataView = this.trackingDataView?.let { ArrayList(it) }
        response.productDetail = this.productDetail
        response.detailDataView = this.detailDataView?.let { ArrayList(it) }
        response.trxId = this.trxId
        response.paymentDrawer = this.paymentDrawer

        response.billingDetail = DetailListType().apply {
            title = <EMAIL>?.getOrNull(1)?.value.orEmpty()
            subtitle = <EMAIL>?.getOrNull(2)?.value.orEmpty()
        }

        val parts = this.dataViewTransaction?.getOrNull(0)?.value
            ?.split("\n")
            ?.map { it.trim() }
            ?.filter { it.isNotEmpty() }

        val first = parts?.getOrNull(0)
        val second = parts?.getOrNull(1)

        response.sourceAccountDataView = DetailListType().apply {
            title = first.orEmpty()
            subtitle = second.orEmpty()
        }

         return response
    }


}