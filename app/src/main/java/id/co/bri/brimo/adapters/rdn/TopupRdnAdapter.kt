package id.co.bri.brimo.adapters.rdn

import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemTopupRdnAdapterBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.rdn.RdnAccountResponse

class TopupRdnAdapter(private var mContext : Context, private var items : List<RdnAccountResponse.AccountRdn>,private var itemClick : OnCallback): RecyclerView.Adapter<TopupRdnAdapter.ViewHolder>() {
    private var mPosition : Int? = null

    interface OnCallback {
        fun onItemClick(peCode : String, sourceAccount : String)
    }
    inner class ViewHolder(val binding: ItemTopupRdnAdapterBinding) : RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TopupRdnAdapter.ViewHolder {
        val binding = ItemTopupRdnAdapterBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TopupRdnAdapter.ViewHolder, position: Int) {
            GeneralHelper.loadImageUrl(mContext,items[position].iconPath,holder.binding.ivRdn, R.drawable.bri,0)
            holder.binding.tvTitleRdn.text = items[position].peName
            holder.binding.tvSubtitleRdn.text = items[position].accountString
            holder.binding.llRdn.setOnClickListener{
                itemClick.onItemClick( items[position].peCode,items[position].account)
            }
    }

    override fun getItemCount(): Int {
        return items.size
    }
}