package id.co.bri.brimo.domain.firebase;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.ContentResolver;
import android.content.Intent;
import android.media.AudioAttributes;
import android.net.Uri;
import android.os.Build;
import android.util.Log;

import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;
import com.google.gson.Gson;

import androidx.core.app.NotificationManagerCompat;
import id.co.bri.brimo.BaseApp;
import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.firebase.IFirebasePresenter;
import id.co.bri.brimo.contract.IView.firebase.IFirebaseView;
import id.co.bri.brimo.di.components.DaggerServiceComponent;
import id.co.bri.brimo.di.components.ServiceComponent;
import id.co.bri.brimo.di.modules.ServiceModule;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.FirebaseMessageHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.NotifikasiModel;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralResponse;
import id.co.bri.brimo.ui.activities.NotificationRouterActivity;

import javax.inject.Inject;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;
import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

public class BrimoFirebaseMessagingService extends FirebaseMessagingService implements IFirebaseView {

    private static final String TAG = "BrimoFirebase";

    private PendingIntent pendingIntent;
    private final int SUMMARY_ID = 999;


    protected ServiceComponent serviceComponent;

    @Inject
    IFirebasePresenter<IFirebaseView> firebasePresenter;


    @Override
    public void onCreate() {
        super.onCreate();
//        injectServiceDependency();
    }

    private void injectServiceDependency() {
        serviceComponent = DaggerServiceComponent.builder()
                .serviceModule(new ServiceModule(this))
                .applicationComponent(((BaseApp) getApplication()).getComponent())
                .build();
        serviceComponent.inject(this);
    }

    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {

        if (remoteMessage.getData().size() > 0) {
            Gson gson = new Gson();
            sendNotification(remoteMessage, gson.toJson(remoteMessage.getData()));
        }

    }

    /**
     * Broadcast data notifikasi dengan intent filter TAG_NOTIF
     *
     * @param dataNotif berisi JSON String data payload dari Notifikasi
     */
    private void sendBroadcast(String dataNotif) {
        Intent broadcast = new Intent(Constant.TAG_NOTIF);
        broadcast.putExtra(Constant.TAG_NOTIF, dataNotif);

        LocalBroadcastManager.getInstance(this).sendBroadcast(broadcast);

    }


    private void sendNotification(RemoteMessage remoteMessage, String notifikasiString) {

        long idNotif = System.currentTimeMillis();
        NotifikasiModel notifikasiModel = null;

        try {
            //send broadcast
            sendBroadcast(notifikasiString);

            Intent intent;
            notifikasiModel = new Gson().fromJson(notifikasiString, NotifikasiModel.class);

            //set pending intent extra
            intent = new Intent(this, NotificationRouterActivity.class);
            intent.putExtra(Constant.TAG_NOTIF, notifikasiString);
            intent.putExtra(Constant.TAG_NOTIF_BACKGROUND, false);
            int flags = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S ? PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE | PendingIntent.FLAG_ONE_SHOT : PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_ONE_SHOT;
            pendingIntent = PendingIntent.getActivity(this,
                    GeneralHelper.longToInt(idNotif), intent,  flags);
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "sendNotification: ", e);
            }
        }

        //generate notif
        String channelId = getString(R.string.default_notification_channel_id);

        /* Populate title, message, image and sound notification */
        String title;
        String message;
        Uri imageUrl;
        String imageUrlStr = "";
        String sound = "";
        int colorDefault = ContextCompat.getColor(getApplicationContext(), R.color.notif_color);

        title = remoteMessage.getNotification().getTitle();
        message = remoteMessage.getNotification().getBody();
        imageUrl = remoteMessage.getNotification().getImageUrl();
        sound = remoteMessage.getNotification().getSound();

        if (imageUrl != null) {
            imageUrlStr = imageUrl.toString();
        }

        Uri soundUri = Uri.parse(ContentResolver.SCHEME_ANDROID_RESOURCE + "://" + getPackageName() + "/raw/" + sound);

        FirebaseMessageHelper.setHelperContext(this);

        //generate notification per item
        Notification notificationBuilder;

        //generate notification channel
        NotificationManagerCompat notificationManager = NotificationManagerCompat.from(this);

        // Since android Oreo notification channel is needed.
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(channelId,
                    "Notifikasi BRImo",
                    NotificationManager.IMPORTANCE_HIGH);


            AudioAttributes att = new AudioAttributes.Builder()
                    .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                    .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                    .build();

            channel.setSound(soundUri, att);
            channel.enableVibration(true);
            channel.setVibrationPattern(new long[]{400, 400});
            channel.setLockscreenVisibility(NotificationCompat.VISIBILITY_PUBLIC);

            notificationManager.createNotificationChannel(channel);
        }

        if (imageUrlStr.equals("")) {
            notificationBuilder = FirebaseMessageHelper.notificationNonImage(channelId, colorDefault, title, message,
                    soundUri, pendingIntent, notifikasiModel);
        } else {
            notificationBuilder = FirebaseMessageHelper.notificationImage(channelId, colorDefault, title, message,
                    soundUri, pendingIntent, imageUrlStr, notifikasiModel);
        }

        //generate notification summary
//        Notification notificationSummary = FirebaseMessageHelper.notificationSummary(channelId, colorDefault, title, message);

        notificationManager.notify((int) idNotif, notificationBuilder);
//        notificationManager.notify(SUMMARY_ID, notificationSummary);

    }


    @Override
    public void onNewToken(@NonNull String s) {
        super.onNewToken(s);

        if (firebasePresenter != null) {
            firebasePresenter.setView(this);
            firebasePresenter.start();
            firebasePresenter.subscribeTopicAll(s);
            firebasePresenter.updateTokenFirebase(s);
        }

    }

    @Override
    public void onSubscribeTopicSuccess() {
        // do nothing
    }

    @Override
    public void onRootedDevice() {
        // do nothing
    }

    @Override
    public void showProgress() {
        // do nothing
    }

    @Override
    public void hideProgress() {
        // do  nothing
    }

    @Override
    public void onSessionEnd(String message) {
        // do nothing
    }

    @Override
    public void onException(String message) {
        // do nothing
    }

    @Override
    public void onExceptionRevamp(String message) {
        // do nothing
    }

    @Override
    public void onException06(ExceptionResponse response) {
        //TODO : DO NOTHING
    }

    @Override
    public void onException99(String message) {
        // do nothing
    }

    @Override
    public void onExceptionFO(EmptyStateResponse response) {

    }

    @Override
    public void onExceptionLimitExceed(GeneralResponse response) {
        // do nothing
    }

    @Override
    public void onExceptionNoBackAction(String message) {
        // do nothing
    }

    @Override
    public void onExceptionStatusNotMatch() {
        // do nothing
    }


}