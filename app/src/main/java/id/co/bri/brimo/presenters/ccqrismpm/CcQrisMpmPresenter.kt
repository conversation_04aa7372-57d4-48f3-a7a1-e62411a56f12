package id.co.bri.brimo.presenters.ccqrismpm

import android.util.Log
import com.google.gson.Gson
import com.scottyab.rootbeer.Const
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.ccqrismpm.ICcQrisMPMPresenter
import id.co.bri.brimo.contract.IView.ICcQrisMpmView
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.converter.MapperHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.DetailCcSofRequest
import id.co.bri.brimo.models.apimodel.request.ccqrismpm.SetDefaultQrPaymentRequest
import id.co.bri.brimo.models.apimodel.response.cc.DetailCcSofResponse
import id.co.bri.brimo.models.apimodel.response.cc.LimitCcSofResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SaldoReponse
import id.co.bri.brimo.models.apimodel.response.SyaratKetentuanResponse
import id.co.bri.brimo.models.apimodel.response.ccqrismpm.CcQrisMpmResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.observers.DisposableObserver
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class CcQrisMpmPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), ICcQrisMPMPresenter<V> where V : IMvpView, V : ICcQrisMpmView {

    private var urlGetListSof = ""
    private var urlChangeDefaultSof = ""
    private var urlDetailBalanceCc = ""
    private var urlTermSof = ""

    override fun setUrlListSof(url: String) {
        this.urlGetListSof = url
    }

    override fun setUrlChangeSof(url: String) {
        this.urlChangeDefaultSof = url
    }
    override fun setUrlGetBalanceCc(url: String) {
        this.urlDetailBalanceCc = url
    }

    override fun setUrlTerm(url: String) {
        this.urlTermSof = url
    }

    override fun getDataListSof() {
        if (urlGetListSof.isEmpty() || !isViewAttached) return
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getDataTanpaRequest(urlGetListSof, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val data = response.getData(CcQrisMpmResponse::class.java)
                        getView().onSuccess(data)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onApiError(restResponse)
                    }
                })
        )
    }

    override fun setDefaultSof(request: SetDefaultQrPaymentRequest) {
        if (urlChangeDefaultSof.isEmpty() || !isViewAttached) return
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(urlChangeDefaultSof, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().onSuccessChangeSof(
                            restResponse.desc
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onApiError(restResponse)
                    }
                })
        )
    }

    override fun getListBalanceSavings(accountList: ArrayList<CcQrisMpmResponse.AccountList>) {
        compositeDisposable.add(
            Observable.fromIterable(accountList)
                .flatMap { acc -> getBalanceSavingsObservable(acc)}
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableObserver<CcQrisMpmResponse.AccountList>() {
                    override fun onNext(account: CcQrisMpmResponse.AccountList) {
                        val postion: Int = accountList.indexOf(account)
                        if (postion == -1) return
                        accountList[postion] = account
                        view.onResultBalanceSavings(accountList)
                    }

                    override fun onError(e: Throwable) {
                        view.hideProgress()

                    }

                    override fun onComplete() {
                        view.onGetSaldoComplete()
                    }

                })

        )
    }

    override fun getListBalanceCc(accountList: ArrayList<CcQrisMpmResponse.CcList>) {
        compositeDisposable.add(
            Observable.fromIterable(accountList)
                .flatMap { acc -> getBalanceCcObservable(acc)}
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableObserver<CcQrisMpmResponse.CcList>() {
                    override fun onNext(account: CcQrisMpmResponse.CcList) {
                        val postion: Int = accountList.indexOf(account)
                        if (postion == -1) return
                        accountList[postion] = account
                        view.onResultBalanceCc(accountList)
                    }

                    override fun onError(e: Throwable) {
                        view.hideProgress()

                    }

                    override fun onComplete() {
                        view.onGetSaldoComplete()
                    }

                })

        )
    }



    private fun getBalanceCcObservable(account: CcQrisMpmResponse.CcList):Observable<CcQrisMpmResponse.CcList> {
        val cardToken = DetailCcSofRequest(account.cardToken)
        val seqNum = brImoPrefRepository.seqNumber
        return apiSource.getData(urlDetailBalanceCc,cardToken, seqNum)
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .subscribeOn(schedulerProvider.newThread())
            .observeOn(schedulerProvider.mainThread())
            .map { stringResponse ->
                var saldoReponse = DetailCcSofResponse()
                //get checksum response
                val responseCheck = MapperHelper.getIdResponse(stringResponse)

                //jika checksum response kosong
                if (responseCheck.isEmpty()) {
                    saldoReponse.balanceString = RestResponse.ResponseCodeEnum.RC_TO.value
                }

                //coba konversi String ke RestResponse model
                val restResponse = MapperHelper.stringToRestResponse(stringResponse, seqNum)
                if (restResponse!=null){
                    if (GeneralHelper.isContains(  R.array.response_code_success,restResponse.code)) {
                        if (restResponse.desc.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)) {
                            saldoReponse.balanceString = restResponse.desc
                        } else {
                            saldoReponse = restResponse.getData(DetailCcSofResponse::class.java)
                        }
                    }else{
                        when(restResponse.code){
                            RestResponse.ResponseCodeEnum.RC_12.value -> {
                                saldoReponse.balanceString = restResponse.code
                                saldoReponse.name = restResponse.desc
                            }
                            RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> {
                                saldoReponse.balanceString = restResponse.code
                                view.onSessionEnd(restResponse.desc)
                            }
                            else ->{
                                saldoReponse.balanceString = RestResponse.ResponseCodeEnum.RC_TO.value
                            }
                        }
                    }
                } else {
                    saldoReponse = DetailCcSofResponse()
                    saldoReponse.balanceString = RestResponse.ResponseCodeEnum.RC_TO.value
                }
                account.saldoResponse = saldoReponse
                account
            }

    }


    private fun getBalanceSavingsObservable(account : CcQrisMpmResponse.AccountList): Observable<CcQrisMpmResponse.AccountList>? {
        val seqNum = brImoPrefRepository.seqNumber
        return apiSource.getSaldoNormal(account.account, seqNum)
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .subscribeOn(schedulerProvider.newThread())
            .observeOn(schedulerProvider.mainThread())
            .map { stringResponse ->
                var saldoReponse = SaldoReponse()
                //get checksum response
                val responseCheck = MapperHelper.getIdResponse(stringResponse)

                //jika checksum response kosong
                if (responseCheck.isEmpty()) {
                    saldoReponse.balanceString = RestResponse.ResponseCodeEnum.RC_TO.value
                }

                //coba konversi String ke RestResponse model
                val restResponse = MapperHelper.stringToRestResponse(stringResponse, seqNum)
                if (restResponse!=null){
                    if (GeneralHelper.isContains(  R.array.response_code_success,restResponse.code)) {
                        if (restResponse.desc.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)) {
                            saldoReponse.balanceString = restResponse.desc
                        } else {
                            saldoReponse = restResponse.getData(SaldoReponse::class.java)
                        }
                    }else{
                        when(restResponse.code){
                            RestResponse.ResponseCodeEnum.RC_12.value->{
                                saldoReponse.balanceString = restResponse.code
                                saldoReponse.name = restResponse.desc
                            }
                            RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> {
                                saldoReponse.balanceString = restResponse.code
                                view.onSessionEnd(restResponse.desc)
                            }
                            else ->{
                                saldoReponse.balanceString = RestResponse.ResponseCodeEnum.RC_TO.value
                            }
                        }
                    }
                } else {
                    saldoReponse = SaldoReponse()
                    saldoReponse.balanceString = RestResponse.ResponseCodeEnum.RC_TO.value
                }
                account.saldoResponse = saldoReponse
                account
            }
    }

    override fun getDataTerm() {
        if (urlTermSof.isEmpty() || !isViewAttached) return
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable
            .add(
                apiSource.getDataTanpaRequest(urlTermSof, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val syaratKetentuanResponse = response.getData(
                                SyaratKetentuanResponse::class.java
                            )
                            getView().onSuccessTerm(syaratKetentuanResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            onApiError(restResponse)
                        }
                    })
            )
    }
}