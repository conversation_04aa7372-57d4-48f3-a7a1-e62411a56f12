package id.co.bri.brimo.adapters;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.CheckBox;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.R;

import id.co.bri.brimo.databinding.ItemSegmentFilterBinding;
import id.co.bri.brimo.databinding.ItemSegmentSubfilterBinding;
import id.co.bri.brimo.databinding.ItemSubCategoryTransactionBinding;
import id.co.bri.brimo.models.apimodel.response.ActivityGroup;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;

public class ListInboxSubFilterAdapter extends RecyclerView.Adapter<ListInboxSubFilterAdapter.ViewHolder> {


    @Bind(R.id.view_subfilter)
    public LinearLayout llSubFilter;

    private static final String TAG = "ListInboxSubFilterAdapt";

    protected List<ActivityGroup.ActivityType> filterItemList;
    private List<String> selectedCodeTypes = new ArrayList<>();
    protected Context context;
    protected ClickItem clickListener;
    protected boolean onLoop = false;
    protected boolean cbSubAll;
    protected int lastPosition = -1;

    public boolean isCbSubAll() {
        return cbSubAll;
    }

    public void setCbSubAll(boolean cbSubAll) {
        this.cbSubAll = cbSubAll;
    }

    public ListInboxSubFilterAdapter(List<ActivityGroup.ActivityType> filterItemList , Context context, ClickItem clickListener) {
        this.filterItemList = filterItemList;
        this.context = context;
        this.clickListener = clickListener;
    }

    @NonNull
    @Override
    public ListInboxSubFilterAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(ItemSubCategoryTransactionBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false));
    }


    @Override
    public void onBindViewHolder(@NonNull ListInboxSubFilterAdapter.ViewHolder holder, int position) {
        final ActivityGroup.ActivityType objIncome = filterItemList.get(position);

        checkUncheckAll(isCbSubAll());
        holder.binding.cbSubfilter.setText(objIncome.getName());
        holder.binding.cbSubfilter.setOnCheckedChangeListener((buttonView, isChecked) -> {
            objIncome.setSelected(isChecked);
//            if (clickListener != null) {
            clickListener.onClickedSubFilterItem(objIncome);

//            }

/*            try {

                if (objIncome.getName().equalsIgnoreCase("Semua")) {
                    checkUncheckAll(isChecked);
                    notifyDataSetChanged();
                } else {
                    if (position > 0) {
                        if (filterItemList.size() > 1) {
                            if (filterItemList.get(0).isSelected() && !onLoop) {
                                filterItemList.get(0).setSelected(false);
                                notifyDataSetChanged();
                            }
                        }
                    }
                }

            } catch (Exception e) {
                Log.e(TAG, "onBindViewHolder: ", e);
            }*/

        });

        if (!selectedCodeTypes.isEmpty()) {
            // restore checkbox state
//            objIncome.setSelected(selectedCodeTypes.contains(objIncome.getCodeType()));
//            holder.binding.cbSubfilter.setChecked(objIncome.isSelected());
//            holder.binding.cbSubfilter.setText(objIncome.getName());
        } else {
            holder.binding.cbSubfilter.setChecked(objIncome.isSelected());
        }
        setAnimation(holder.itemView, position);
    }

    /**
     * Method untuk check uncheck all item subfilter
     *
     * @param isChecked
     */
    protected void checkUncheckAll(boolean isChecked) {

//        if (filterItemList.size() >= 0) {
        for (int i = 0; i < filterItemList.size(); i++) {
            onLoop = true;
            //set setiap item di check apa uncheck
            filterItemList.get(i).setSelected(isChecked);
//            clickListener.onClickedSubFilterItem(filterItemList.get(i));
//            Log.d(TAG, "checkUncheckAll: " + filterItemList.get(i).getName());
        }
        onLoop = false;
//        }
    }

    /**
     * Here is the key method to apply the animation
     */
    private void setAnimation(View viewToAnimate, int position) {
        // If the bound view wasn't previously displayed on screen, it's animated
        if (position > lastPosition) {
            Animation animation = AnimationUtils.loadAnimation(context, R.anim.fade_in_button);
            animation.setStartOffset((long) lastPosition * 100);
            viewToAnimate.startAnimation(animation);
            lastPosition = position;
        }
    }


    @Override
    public int getItemCount() {
        return (filterItemList != null) ? filterItemList.size() : 0;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ItemSubCategoryTransactionBinding binding;

        public ViewHolder(@NonNull ItemSubCategoryTransactionBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    public void setSubFilter(List<String> newList) {
        if (newList == null) return;
        if (selectedCodeTypes != null) {
            selectedCodeTypes.clear();
            selectedCodeTypes.addAll(newList);
            notifyDataSetChanged();
        }
    }

    public interface ClickItem {
        void onClickedSubFilterItem(ActivityGroup.ActivityType filterModel);
    }
}