package id.co.bri.brimo.ui.fragments;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.FragmentListMonthBinding;
import id.co.bri.brimo.models.YearModel;

public class ListMonthFragment extends BottomSheetDialogFragment {

    private FragmentListMonthBinding binding;
    private SelectMonthYearInterface monthYearInterface;
    private Context context;
    private static List<YearModel> listYear;
    private static List<YearModel.MonthModelData> listMonth;
    private String monthId = null, yearId = null;
    private int defMonth = 0, defYear = 0;

    public static String[] monthTemp = new String[100];

    public ListMonthFragment(SelectMonthYearInterface onClick, Context mContext, List<YearModel> yearModelList, String month, String year) {
        this.monthYearInterface = onClick;
        this.context = mContext;
        this.listYear = yearModelList;
        this.monthId = month;
        this.yearId = year;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentListMonthBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        for (int i = 0; i < listYear.size(); i++) {
            if (yearId.equalsIgnoreCase(listYear.get(i).getYear())) {
                listMonth = listYear.get(i).getListMonth();
            }
        }

        String[] monthValues = new String[listMonth.size()];
        String[] yearValues = new String[listYear.size()];
        monthTemp = new String[listMonth.size()];
        int count = 0;

        for (int i = 0; i < monthValues.length; i++) {
            monthValues[i] = listMonth.get(i).getMonthString();
            monthTemp[i] = listMonth.get(i).getMonthString();
        }

        for (int i = 0; i < yearValues.length; i++) {
            yearValues[i] = listYear.get(i).getYear();
        }

        if (monthId != null && yearId != null) {
            for (String month: monthTemp) {
                if (monthId.equalsIgnoreCase(month)) {
                    for (int a = 0; a < listMonth.size(); a++) {
                        if (month.equalsIgnoreCase(listMonth.get(a).getMonthString())) {
                            defMonth = a;
                        }
                    }
                }
            }

            for (String year: yearValues) {
                if (yearId.equalsIgnoreCase(year)) {
                    for (int b = 0; b < listYear.size(); b++) {
                        if (year.equalsIgnoreCase(listYear.get(b).getYear())) {
                            defYear = b;
                        }
                    }
                }
            }
        }

        binding.numberMonth.setDisplayedValues(null);
        binding.numberMonth.setMinValue(0);
        binding.numberMonth.setMaxValue(monthValues.length - 1);
        binding.numberMonth.setWrapSelectorWheel(false);
        binding.numberMonth.setValue(defMonth);
        binding.numberMonth.setDisplayedValues(monthValues);

        binding.numberYear.setDisplayedValues(null);
        binding.numberYear.setMinValue(0);
        binding.numberYear.setMaxValue(yearValues.length - 1);
        binding.numberYear.setWrapSelectorWheel(false);
        binding.numberYear.setValue(defYear);
        binding.numberYear.setDisplayedValues(yearValues);
        binding.numberYear.setOnValueChangedListener((picker, oldVal, newVal) -> {
            int year = binding.numberYear.getValue();
            String selectedYear = yearValues[year];

            for (int a = 0; a < listYear.size(); a++) {
                if (selectedYear.equalsIgnoreCase(listYear.get(a).getYear())) {
                    listMonth = listYear.get(a).getListMonth();
                }

                monthTemp = new String[listMonth.size()];

                for (int b = 0; b < listMonth.size(); b++) {
                    monthTemp[b] = listMonth.get(b).getMonthString();
                }

                binding.numberMonth.setDisplayedValues(null);
                binding.numberMonth.setMinValue(0);
                binding.numberMonth.setMaxValue(monthTemp.length - 1);
                binding.numberMonth.setWrapSelectorWheel(false);
                binding.numberMonth.setDisplayedValues(monthTemp);
            }
        });

        binding.btnSelect.setOnClickListener(v -> {
            int month = binding.numberMonth.getValue();
            int year = binding.numberYear.getValue();
            String selectedMonth = monthTemp[month];
            String selectedYear = yearValues[year];
            monthYearInterface.onSelectMonth(selectedMonth, selectedYear);
            dismiss();
        });
    }

    public interface SelectMonthYearInterface {
        void onSelectMonth(String month, String year);
    }
}