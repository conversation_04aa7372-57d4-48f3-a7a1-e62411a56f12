package id.co.bri.brimo.ui.fragments.emas

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RadioGroup
import androidx.core.content.ContextCompat
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentFilterRiwayatBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.extension.gone
import id.co.bri.brimo.domain.extension.visible
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelperKt
import id.co.bri.brimo.models.DurasiModel
import id.co.bri.brimo.models.YearModel
import id.co.bri.brimo.models.YearModel.MonthModelData
import id.co.bri.brimo.ui.fragments.CalendarMutationFragment
import id.co.bri.brimo.ui.fragments.ListMonthFragment.SelectMonthYearInterface
import id.co.bri.brimo.ui.fragments.dplkrevamp.FilterPeriodeFragment
import org.threeten.bp.LocalDate

class FilterRiwayatFragment : BottomSheetDialogFragment(), SelectMonthYearInterface,
    CalendarMutationFragment.OnSelectDate, FilterPeriodeFragment.OnCallback {
    private var _binding: FragmentFilterRiwayatBinding? = null
    private val binding get() = _binding!!

    private var localStartDate: LocalDate? = null
    private var localEndDate: LocalDate? = null
    private var listMonth: List<MonthModelData>? = null

    private var selectedMonthInNumber: String? = null
    private var monthNumb: String? = null
    private var convertStartDate: String? = null
    private var convertEndDate: String? = null
    private var startDateRange = ""
    private var endDateRange = ""
    private var startDateRangeString = ""
    private var endDateRangeString = ""

    var onClickButton: (Int, String, String) -> Unit = { type, startate, endDate -> }
    var setDateRangePick: (String, String) -> Unit = { _, _ -> }
    var onClickReset: () -> Unit = { }

    companion object {
        private var currentDuration: DurasiModel? = null
        private var currentDurationTemp: DurasiModel? = null
        private var yearList: List<YearModel> = ArrayList()
        private var selectedFilterIs: String? = null
        private var transactionTypeName: String? = null
        private var transactionTypeId: String? = null
        private var selectedMonth: String = ""
        private var selectedYear: String = ""
        private var startDate: String? = null
        private var endDate: String? = null
        private var isHasSubmit = false

        @JvmStatic
        fun newInstance(
            yearModelList: List<YearModel>,
            selectedFilter: String? = null,
            transactionTypeIds: String? = null,
            month: String = "",
            year: String = "",
            startDates: String? = null,
            endDates: String? = null,
            startDateRange: String = "",
            endDateRange: String = "",
            isHasSubmitIns: Boolean = false
        ) = FilterRiwayatFragment().apply {
            yearList = yearModelList
            currentDuration = DurasiModel()
            selectedFilterIs = selectedFilter
            transactionTypeId = transactionTypeIds
            selectedMonth = month
            selectedYear = year
            startDate = startDates
            endDate = endDates
            this.startDateRange = startDateRange
            this.endDateRange = endDateRange
            isHasSubmit = isHasSubmitIns
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogThemeInput)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = FragmentFilterRiwayatBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupRadioFilter()
        setUpView()
        initListener()
    }

    private fun setupRadioFilter() {
        currentDurationTemp = DurasiModel()
        binding.rgFilter.setOnCheckedChangeListener { _: RadioGroup?, _: Int ->
            if (binding.rbToday.isChecked) {
                binding.rlSelectMonth.gone()
                binding.llSelectDate.gone()
                currentDuration = CalendarHelper.getDurasiHariIni()
                setEnableButton(true)
            } else if (binding.rbSevenDays.isChecked) {
                binding.rlSelectMonth.gone()
                binding.llSelectDate.gone()
                currentDuration = CalendarHelper.getDurasiMingguIni()
                setEnableButton(true)
            } else if (binding.rbSelectMonth.isChecked) {
                binding.rlSelectMonth.visible()
                binding.llSelectDate.gone()
                if (selectedMonth == "") {
                    selectedMonth = CalendarHelper.getNameOfCurrentMonth()
                    selectedYear = CalendarHelper.getCurrentYear()
                    binding.tvMonth.text = CalendarHelper.getNameOfCurrentMonthYear()
                } else {
                    binding.tvMonth.text = selectedMonth + " " + selectedYear
                }
                currentDuration = CalendarHelper.getDurasiBulanIni()
                setEnableButton(true)
            } else if (binding.rbSelectDate.isChecked) {
                binding.llSelectDate.visible()
                binding.rlSelectMonth.gone()
                if (startDate == null && endDate == null) {
                    currentDuration = null
                    if (startDateRange == "") {
                        startDateRangeString = CalendarHelper.getFullDateNow()
                        endDateRangeString = CalendarHelper.getFullDateNow()
                    } else {
                        startDateRangeString = CalendarHelperKt().formatDate(
                            startDateRange,
                            CalendarHelperKt.DATE_PATTERN_STRIP_yyyy_MM_dd,
                            CalendarHelperKt.DATE_PATTERN_dd_MMM_yyyy
                        )
                        endDateRangeString = CalendarHelperKt().formatDate(
                            endDateRange,
                            CalendarHelperKt.DATE_PATTERN_STRIP_yyyy_MM_dd,
                            CalendarHelperKt.DATE_PATTERN_dd_MMM_yyyy
                        )
                    }
                    startDateRange = startDateRangeString
                    endDateRange = endDateRangeString
                    binding.tvStartDate.text = startDateRangeString
                    binding.tvEndDate.text = endDateRangeString
                    convertStartDate =
                        CalendarHelper.convertToStringFormat(CalendarHelper.getFullDateNow())
                    convertEndDate =
                        CalendarHelper.convertToStringFormat(CalendarHelper.getFullDateNow())
                    localStartDate = LocalDate.parse(convertStartDate)
                    localEndDate = LocalDate.parse(convertEndDate)
                    currentDurationTemp = DurasiModel(
                        localStartDate!!.dayOfMonth,
                        localStartDate!!.monthValue,
                        localStartDate!!.year,
                        localEndDate!!.dayOfMonth,
                        localEndDate!!.monthValue,
                        localEndDate!!.year
                    )
                } else {
                    convertStartDate = CalendarHelper.convertToNewFormat(startDate)
                    convertEndDate = CalendarHelper.convertToNewFormat(endDate)
                    localStartDate = LocalDate.parse(convertStartDate)
                    localEndDate = LocalDate.parse(convertEndDate)
                    currentDuration = DurasiModel(
                        localStartDate!!.dayOfMonth,
                        localStartDate!!.monthValue,
                        localStartDate!!.year,
                        localEndDate!!.dayOfMonth,
                        localEndDate!!.monthValue,
                        localEndDate!!.year
                    )
                    val startDateString =
                        currentDuration!!.startDateMutasiStringddMMMyyyy.substring(0, 3) +
                                currentDuration!!.startDateMutasiStringddMMMyyyy.substring(3, 6) +
                                currentDuration!!.startDateMutasiStringddMMMyyyy.substring(6, 11)
                    val endDateString =
                        currentDuration!!.endDateMutasiStringddMMMyyyy.substring(0, 3) +
                                currentDuration!!.endDateMutasiStringddMMMyyyy.substring(6, 11)
                    binding.tvStartDate.text = startDateString
                    binding.tvEndDate.text = endDateString
                }
                setEnableButton(true)
            }
        }
    }

    private fun setUpView() {
        when (selectedFilterIs) {
            Constant.TODAY_FILTER -> {
                binding.rbToday.isChecked = true
            }

            Constant.WEEK_FILTER -> {
                binding.rbSevenDays.isChecked = true
            }

            Constant.MONTH_FILTER -> {
                binding.rbSelectMonth.isChecked = true
                binding.rlSelectMonth.visible()
            }

            Constant.RANGE_FILTER -> {
                binding.rbSelectDate.isChecked = true
                binding.llSelectDate.visible()
            }
        }
        binding.btnReset.visible()
        binding.btnReset.isEnabled = false

        if (selectedMonth.isNotEmpty()) {
            setYear(selectedYear, selectedMonth)
        }

        if (startDate != null && endDate != null) {
            convertStartDate = CalendarHelper.convertToNewFormat(startDate)
            convertEndDate = CalendarHelper.convertToNewFormat(endDate)
            localStartDate = LocalDate.parse(convertStartDate)
            localEndDate = LocalDate.parse(convertEndDate)

            currentDuration = DurasiModel(
                localStartDate!!.dayOfMonth,
                localStartDate!!.monthValue,
                localStartDate!!.year,
                localEndDate!!.dayOfMonth,
                localEndDate!!.monthValue,
                localEndDate!!.year
            )

            val startDateString = currentDuration!!.startDateMutasiStringddMMMyyyy.substring(0, 3) +
                    currentDuration!!.startDateMutasiStringddMMMyyyy.substring(3, 6) +
                    currentDuration!!.startDateMutasiStringddMMMyyyy.substring(6, 11)
            val endDateString = currentDuration!!.endDateMutasiStringddMMMyyyy.substring(0, 3) +
                    currentDuration!!.endDateMutasiStringddMMMyyyy.substring(3, 6) +
                    currentDuration!!.endDateMutasiStringddMMMyyyy.substring(6, 11)
            binding.tvStartDate.text = startDateString
            binding.tvEndDate.text = endDateString
            startDateRange = startDateString
            endDateRange = endDateString
        }

        setEnableButton((selectedFilterIs != null && selectedFilterIs != "") || transactionTypeName != null)

        if (isHasSubmit) {
            binding.btnBlue.setTextColor(
                ContextCompat.getColor(
                    requireContext(), R.color.whiteColor
                )
            )
            binding.btnBlue.background =
                ContextCompat.getDrawable(requireContext(), R.drawable.button_primary_bg)

            binding.btnReset.setTextColor(
                ContextCompat.getColor(
                    requireContext(), R.color.primaryBlue80
                )
            )
            binding.btnReset.isEnabled = true
            binding.btnBlue.isEnabled = true
        }
    }

    private fun setEnableButton(isChecked: Boolean) {
        if (isChecked) {
            binding.btnBlue.setTextColor(
                ContextCompat.getColor(
                    requireContext(), R.color.whiteColor
                )
            )
            binding.btnBlue.background =
                ContextCompat.getDrawable(requireContext(), R.drawable.button_primary_bg)
            binding.btnReset.setTextColor(
                ContextCompat.getColor(
                    requireContext(), R.color.primaryBlue80
                )
            )
            binding.btnReset.isEnabled = true
            binding.btnBlue.isEnabled = true
        }
    }

    private fun selectStartDate() {
        val calendarFragment = CalendarMutationFragment(this)
        val args = Bundle()
        if (currentDuration != null) {
            if (currentDuration!!.startDateString != null && currentDuration!!.startDateString.isNotEmpty() && currentDuration!!.startMonth > 0) {
                args.putString(Constant.TAG_START_DATE, currentDuration!!.startDateString)
            }
            if (currentDuration!!.endDateString != null && currentDuration!!.endDateString.isNotEmpty() && currentDuration!!.endMonth > 0) {
                args.putString(Constant.TAG_END_DATE, currentDuration!!.endDateString)
            }
        }
        if (startDateRange != "") {
            args.putString(Constant.TAG_START_DATE, startDateRange)
            args.putString(Constant.TAG_END_DATE, endDateRange)
        }
        args.putBoolean(Constant.TAG_PICK_START_DATE, true)
        args.putBoolean(Constant.TAG_PICK_DATE, true)
        args.putBoolean(Constant.TAG_MAX_TODAY, true)
        args.putBoolean(Constant.TAG_CUSTOM_CALENDAR, true)
        calendarFragment.arguments = args
        calendarFragment.isCancelable = true
        calendarFragment.show(parentFragmentManager, Constant.REQ_CALENDAR.toString())
    }

    private fun selectEndDate() {
        val calendarFragment = CalendarMutationFragment(this)
        val args = Bundle()
        if (currentDuration != null) {
            if (currentDuration!!.startDateString != null && currentDuration!!.startDateString.isNotEmpty() && currentDuration!!.startMonth > 0) {
                args.putString(Constant.TAG_START_DATE, currentDuration!!.startDateString)
            }
            if (currentDuration!!.endDateString != null && currentDuration!!.endDateString.isNotEmpty() && currentDuration!!.endMonth > 0) {
                args.putString(Constant.TAG_END_DATE, currentDuration!!.endDateString)
            }
        }
        if (startDateRange != "") {
            args.putString(Constant.TAG_START_DATE, startDateRange)
            args.putString(Constant.TAG_END_DATE, endDateRange)
        }
        args.putBoolean(Constant.TAG_PICK_END_DATE, true)
        args.putBoolean(Constant.TAG_PICK_DATE, true)
        args.putBoolean(Constant.TAG_MAX_TODAY, true)
        args.putBoolean(Constant.TAG_CUSTOM_CALENDAR, true)
        calendarFragment.arguments = args
        calendarFragment.isCancelable = true
        calendarFragment.show(parentFragmentManager, Constant.REQ_CALENDAR.toString())
    }

    private fun submitFilter() {
        when {
            binding.rbToday.isChecked -> {
                onClickButton.invoke(1, "", "")
            }

            binding.rbSevenDays.isChecked -> {
                onClickButton.invoke(2, "", "")
            }

            binding.rbSelectMonth.isChecked -> {
                onClickButton.invoke(3, selectedMonth, selectedYear)
            }

            binding.rbSelectDate.isChecked -> {
                onClickButton.invoke(4, startDateRange, endDateRange)
                currentDuration?.startDateString?.let { cvtStartDate ->
                    currentDuration?.endDateString?.let { cvtEndDate ->
                        setDateRangePick.invoke(
                            cvtStartDate, cvtEndDate
                        )
                    }
                }
            }
        }

        val returnIntent = Intent()
        if (binding.rbToday.isChecked) {
            returnIntent.putExtra(Constant.FILTER, Constant.TODAY_FILTER)
        } else if (binding.rbSevenDays.isChecked) {
            returnIntent.putExtra(Constant.FILTER, Constant.SEVEN_DAY_FILTER)
        } else if (binding.rbSelectMonth.isChecked) {
            returnIntent.putExtra(Constant.FILTER, Constant.MONTH_FILTER)
            if (selectedMonthInNumber != null) {
                returnIntent.putExtra(Constant.MONTH, selectedMonthInNumber)
                returnIntent.putExtra(Constant.MONTH_TEXT, selectedMonth)
                returnIntent.putExtra(Constant.YEAR, selectedYear)
            } else {
                returnIntent.putExtra(Constant.MONTH, CalendarHelper.getCurrentMonth())
                returnIntent.putExtra(Constant.MONTH_TEXT, CalendarHelper.getNameOfCurrentMonth())
                returnIntent.putExtra(Constant.YEAR, CalendarHelper.getCurrentYear())
            }
        } else if (binding.rbSelectDate.isChecked) {
            returnIntent.putExtra(Constant.FILTER, Constant.RANGE_FILTER)
            if (currentDuration == null) {
                currentDuration = CalendarHelper.getDurasiHariIni()
            }
            if (currentDuration!!.startDay != 0) {
                if (currentDuration!!.endDay == 0) {
                    returnIntent.putExtra(
                        Constant.TAG_END_DATE, currentDurationTemp!!.endDateFormatRange
                    )
                } else {
                    returnIntent.putExtra(
                        Constant.TAG_END_DATE, currentDuration!!.endDateFormatRange
                    )
                }
                returnIntent.putExtra(
                    Constant.TAG_START_DATE, currentDuration!!.startDateFormatRange
                )
            } else {
                returnIntent.putExtra(
                    Constant.TAG_START_DATE, CalendarHelper.getDateNowFormatRange()
                )
                returnIntent.putExtra(Constant.TAG_END_DATE, CalendarHelper.getDateNowFormatRange())
            }
        }
        returnIntent.putExtra(Constant.TRANSACTION_TYPE_ID, transactionTypeId)
        returnIntent.putExtra(Constant.TRANSACTION_TYPE_NAME, transactionTypeName)
        returnIntent.putExtra(Constant.TAG_VALUE, 0)
        requireActivity().setResult(Activity.RESULT_OK, returnIntent)
        dismiss()
    }

    private fun resetFilter() {
        updateViewAfterReset()
        val returnIntent = Intent()
        returnIntent.putExtra(Constant.TAG_VALUE, 0)
        requireActivity().setResult(Activity.RESULT_OK, returnIntent)
        onClickReset.invoke()
    }

    private fun updateViewAfterReset() {
        binding.apply {
            rgFilter.clearCheck()
            rbToday.isChecked = false
            rbSevenDays.isChecked = false
            rbSelectMonth.isChecked = false
            rbSelectDate.isChecked = false
            transactionTypeName = null

            llSelectDate.gone()
            rlSelectMonth.gone()

            btnBlue.setTextColor(
                ContextCompat.getColor(
                    requireContext(), R.color.whiteColor
                )
            )
            btnBlue.background = ContextCompat.getDrawable(
                requireContext(), R.drawable.rounded_button_disabled_revamp
            )

            btnBlue.isEnabled = false

            btnReset.setTextColor(
                ContextCompat.getColor(
                    requireContext(), R.color.neutralLight60
                )
            )
            btnReset.isEnabled = false

        }
    }

    private fun setYear(year: String, month: String) {
        for (i in yearList.indices) {
            if (year.equals(yearList[i].year, ignoreCase = true)) {
                selectedYear = yearList[i].year
            }
            for (j in yearList[i].listMonth.indices) {
                if (month.equals(yearList[i].listMonth[j].month, ignoreCase = true)) {
                    selectedMonth = yearList[i].listMonth[j].monthString
                    selectedMonthInNumber = yearList[i].listMonth[j].month
                }
            }
        }
        binding.tvMonth.text = "$selectedMonth $selectedYear"
    }

    override fun onSelectStart(dateSelect: LocalDate) {
        currentDuration = if (endDateRange == CalendarHelper.getFullDateNow()) {
            val dateNow = LocalDate.now()
            DurasiModel(
                dateSelect.dayOfMonth,
                dateSelect.monthValue,
                dateSelect.year,
                dateNow.dayOfMonth,
                dateNow.monthValue,
                dateNow.year
            )
        } else {
            DurasiModel(
                dateSelect.dayOfMonth, dateSelect.monthValue, dateSelect.year
            )
        }
        setStart(currentDuration)
        setEnableButton(true)
    }

    private fun setStart(durasi: DurasiModel?) {
        currentDuration = durasi
        val tanggalString = currentDuration!!.startDateMutasiStringddMMMyyyy.substring(
            0,
            3
        ) + currentDuration!!.startDateMutasiStringddMMMyyyy.substring(
            3,
            6
        ) + currentDuration!!.startDateMutasiStringddMMMyyyy.substring(6, 11)
        startDateRange = tanggalString
        binding.tvStartDate.text = tanggalString
    }


    override fun onSelectEnd(dateSelect: LocalDate) {
        currentDuration = DurasiModel(
            dateSelect.dayOfMonth, dateSelect.monthValue, dateSelect.year
        )
        setEnd(currentDuration)
        setEnableButton(true)
    }

    private fun setEnd(durasi: DurasiModel?) {
        currentDuration = durasi
        val tanggalString = currentDuration!!.endDateMutasiStringddMMMyyyy.substring(
            0,
            3
        ) + currentDuration!!.endDateMutasiStringddMMMyyyy.substring(
            3,
            6
        ) + currentDuration!!.endDateMutasiStringddMMMyyyy.substring(6, 11)
        binding.tvEndDate.text = tanggalString
    }


    override fun onSelectRange(startDateSelect: LocalDate, endDateSelect: LocalDate) {
        currentDuration = DurasiModel(
            startDateSelect.dayOfMonth,
            startDateSelect.monthValue,
            startDateSelect.year,
            endDateSelect.dayOfMonth,
            endDateSelect.monthValue,
            endDateSelect.year
        )

        val startDateString = currentDuration!!.startDateMutasiStringddMMMyyyy.substring(0, 3) +
                currentDuration!!.startDateMutasiStringddMMMyyyy.substring(3, 6) +
                currentDuration!!.startDateMutasiStringddMMMyyyy.substring(6, 11)

        val endDateString = currentDuration!!.endDateMutasiStringddMMMyyyy.substring(0, 3) +
                currentDuration!!.endDateMutasiStringddMMMyyyy.substring(3, 6) +
                currentDuration!!.endDateMutasiStringddMMMyyyy.substring(6, 11)

        startDateRange = startDateString
        endDateRange = endDateString
        binding.tvStartDate.text = startDateString
        binding.tvEndDate.text = endDateString

        setEnableButton(true)
    }

    override fun onDismissDate() {
    }

    override fun onSelectMonth(month: String?, year: String?) {
        for (i in yearList.indices) {
            listMonth = yearList[i].listMonth
            for (j in listMonth!!.indices) {
                if (month.equals(listMonth!![j].monthString, ignoreCase = true)) {
                    monthNumb = listMonth!![j].month
                }
            }
        }
        month?.let { valueMonth ->
            year?.let { yearValue ->
                selectedMonth = valueMonth
                selectedYear = yearValue
            }
        }

        selectedMonthInNumber = monthNumb
        binding.tvMonth.text = "$selectedMonth $selectedYear"
    }

    override fun onDismiss() {

    }

    private fun initListener() {
        binding.rlSelectMonth.setOnClickListener {
            val filterPeriod = FilterPeriodeFragment(
                CalendarHelper.TypeFilterPeriode.MONTH_MONTHLY,
                this@FilterRiwayatFragment,
                yearList,
                mSelectedMonth = selectedMonth,
                mSelectedYear = selectedYear
            )
            filterPeriod.show(parentFragmentManager, "")
        }
        binding.llStartDate.setOnClickListener {
            selectStartDate()
        }
        binding.llEndDate.setOnClickListener {
            selectStartDate()
        }
        binding.btnBlue.setOnClickListener {
            submitFilter()
        }
        binding.btnReset.setOnClickListener {
            resetFilter()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && data != null) {
            if (requestCode == Constant.REQ_CALENDAR) {

                // char a = data.getCharExtra(Constant.START_DAY,'a');
                if (data.getIntExtra(Constant.START_DAY, 0) > 0) {
                    currentDuration!!.startDay = data.getIntExtra(Constant.START_DAY, 0)
                    currentDuration!!.startMonth = data.getIntExtra(Constant.START_MONTH, 0)
                    currentDuration!!.startYear = data.getIntExtra(Constant.START_YEAR, 0)
                }
                if (data.getIntExtra(Constant.END_DAY, 0) > 0) {
                    currentDuration!!.endDay = data.getIntExtra(Constant.END_DAY, 0)
                    currentDuration!!.endMonth = data.getIntExtra(Constant.END_MONTH, 0)
                    currentDuration!!.endYear = data.getIntExtra(Constant.END_YEAR, 0)
                }

                //uodate option date Field
                setDefaultDate(currentDuration!!)
            }
        }
    }


    private fun setDefaultDate(durasiModel: DurasiModel) {
        if (durasiModel.startMonth > 0) {
            binding.tvStartDate.text = durasiModel.startDatePfmString
        }
        if (durasiModel.endMonth > 0) {
            binding.tvEndDate.text = durasiModel.endDatePfmString
        }
    }

    override fun onMonthFilter(month: String, year: String, navigation: String) {
        selectedMonth = month
        selectedYear = year
        binding.tvMonth.text = "$selectedMonth $selectedYear"
        setEnableButton(true)
    }

    override fun onPeriodFilter(selectedMonth: String, selectedYear: String, navigation: String) {
    }
}