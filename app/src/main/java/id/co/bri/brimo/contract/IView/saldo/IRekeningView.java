package id.co.bri.brimo.contract.IView.saldo;

import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.BiFastAccountResponse;
import id.co.bri.brimo.models.apimodel.response.ListRekeningResponse;

import java.util.List;

public interface IRekeningView extends IMvpView {

    void onGetSaldo(List<ListRekeningResponse.Account> accountList, boolean isRefreshed);

    void onListRekening(ListRekeningResponse listRekeningResponse);

    void onGetSaldoComplete();

    void onException12(String message);

    void onExceptionTotalSaldo();

    void enableButton(boolean enable);

    void showSkeleton();

    void hideSkeleton();
}
