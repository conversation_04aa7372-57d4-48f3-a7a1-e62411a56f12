package id.co.bri.brimo.ui.activities;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentTransaction;

import org.threeten.bp.LocalDate;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.mutasi.IRequestDownloadMutationPresenter;
import id.co.bri.brimo.contract.IView.mutasi.IRequestDownloadMutationView;
import id.co.bri.brimo.databinding.ActivityRequestDownloadMutationBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.DurasiModel;
import id.co.bri.brimo.models.TransactionTypeModel;
import id.co.bri.brimo.models.YearModel;
import id.co.bri.brimo.models.apimodel.request.DownloadMutationDateRangeRequest;
import id.co.bri.brimo.models.apimodel.request.DownloadMutationMonthRequest;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.response.FormRequestEStatementResponse;
import id.co.bri.brimo.models.apimodel.response.ListRekeningResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import id.co.bri.brimo.ui.fragments.CalendarMutationFragment;
import id.co.bri.brimo.ui.fragments.ListFileTypeFragment;
import id.co.bri.brimo.ui.fragments.ListMonthFragment;
import id.co.bri.brimo.ui.fragments.ListRekeningFragment;
import id.co.bri.brimo.ui.fragments.SumberDanaFragment;
import id.co.bri.brimo.ui.fragments.rdn.CustomBottomDialogFragment;

public class RequestDownloadMutationActivity extends BaseActivity implements IRequestDownloadMutationView, CalendarMutationFragment.OnSelectDate,
        ListMonthFragment.SelectMonthYearInterface, SumberDanaFragment.SelectSumberDanaInterface, DialogExitCustom.DialogClickYesNoListener,
        CustomBottomDialogFragment.DialogDefaultListener, ListFileTypeFragment.SelectFileTypeInterface {

    private ActivityRequestDownloadMutationBinding binding;

    protected static DurasiModel currentDuration = null;
    protected AccountModel model;

    private static List<YearModel> yearList = new ArrayList<>();
    private static ArrayList<TransactionTypeModel> transactionType = new ArrayList<>();
    private static ArrayList<ListRekeningResponse.Account> accountList;
    protected List<Integer> mListFailed;
    private List<YearModel.MonthModelData> listMonth;
    protected List<AccountModel> mListAccountModel = new ArrayList<>();

    private static String selectedMonth = null, selectedYear = null, selectedType = null, startDate = null, endDate = null;
    private String monthNumb, selectedMonthInNumber;
    private int counter = 0;

    private static FormRequestEStatementResponse formRequestEStatement;

    protected DownloadMutationMonthRequest downloadMutationMonthRequest;
    protected DownloadMutationDateRangeRequest downloadMutationDateRangeRequest;

    CustomBottomDialogFragment customBottomDialogFragment;

    @Inject
    IRequestDownloadMutationPresenter<IRequestDownloadMutationView> presenter;

    public static void launchIntent(Activity caller, FormRequestEStatementResponse mFormRequestEStatement) {
        Intent intent = new Intent(caller, RequestDownloadMutationActivity.class);
        formRequestEStatement = mFormRequestEStatement;
        caller.startActivityForResult(intent, Constant.REQ_DOWNLOAD_MUTATION);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityRequestDownloadMutationBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.create_estatement));

        injectDependency();

        if (selectedMonth == null) {
            selectedMonth = CalendarHelper.getNameOfCurrentMonth();
            selectedYear = CalendarHelper.getCurrentYear();
            for (int i = 0; i < yearList.size(); i++) {
                listMonth = yearList.get(i).getListMonth();
                for (int j = 0; j < listMonth.size(); j++) {
                    if (selectedMonth.equalsIgnoreCase(listMonth.get(j).getMonthString())) {
                        monthNumb = listMonth.get(j).getMonth();
                    }
                }
            }
            selectedMonthInNumber = monthNumb;
        }

        accountList = formRequestEStatement.getAccountList();
        yearList = formRequestEStatement.getListYear();

        binding.tvType.setText(formRequestEStatement.getListFileType().get(0).getTypeDesc());
        selectedType = formRequestEStatement.getListFileType().get(0).getType();

        setOnClickListener();

        setupField();

        binding.rgFilter.setOnCheckedChangeListener((group, checkedId) -> {
            if (binding.rbSelectMonth.isChecked()) {
                binding.rlSelectMonth.setVisibility(View.VISIBLE);
                binding.llSelectDate.setVisibility(View.GONE);
                binding.tvMonth.setText(CalendarHelper.getNameOfCurrentMonthYear());
                binding.rlSelectMonth.setOnClickListener(view -> showListMonth());
                currentDuration = CalendarHelper.getDurasiBulanIni();
                selectedMonthInNumber = CalendarHelper.getCurrentMonth();
                setEnableButton(true);
            } else if (binding.rbSelectDate.isChecked()) {
                currentDuration = null;
                binding.llSelectDate.setVisibility(View.VISIBLE);
                binding.rlSelectMonth.setVisibility(View.GONE);
                binding.tvStartDate.setText(CalendarHelper.getFullDateNow());
                binding.tvEndDate.setText(CalendarHelper.getFullDateNow());
                setEnableButton(true);
            }
        });
    }

    private void setOnClickListener() {
        binding.rlSelectType.setOnClickListener(view -> {
            ListFileTypeFragment frg = new ListFileTypeFragment(getApplicationContext(), formRequestEStatement.getListFileType(), this, selectedType);
            frg.show(getSupportFragmentManager(), "");
        });
        binding.rlSelectMonth.setOnClickListener(view -> showListMonth());
        binding.llStartDate.setOnClickListener(view -> selectStartDate());
        binding.llEndDate.setOnClickListener(view -> selectEndDate());
        binding.widgetAccount.llChangeAccount.setOnClickListener(view -> {
            if (accountList == null) {
                GeneralHelper.showToast(this, GeneralHelper.getString(R.string.no_account_list));
            } else {
                ListRekeningFragment fragmentSumberDana = new ListRekeningFragment(mListAccountModel, this, counter, mListFailed);
                fragmentSumberDana.show(getSupportFragmentManager(), "");
            }
        });
    }

    protected void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
        }
    }

    private void showListMonth() {
        ListMonthFragment fragment = new ListMonthFragment(this, getApplicationContext(), yearList, selectedMonth, selectedYear);
        fragment.show(getSupportFragmentManager(), "");
    }

    private void setEnableButton(boolean isChecked) {
        if (isChecked) {
            binding.btnApply.setTextColor(getResources().getColor(R.color.whiteColor));
            binding.btnApply.setBackground(getResources().getDrawable(R.drawable.rounded_button_blue));
            binding.btnApply.setEnabled(true);
            binding.btnApply.setOnClickListener(view -> showDialogRequest());
        }
    }

    private void showDialogRequest() {

        DialogExitCustom dialogExitCustom = new DialogExitCustom(
                this ,GeneralHelper.getString(R.string.title_confirmation),
                GeneralHelper.getString(R.string.desc_confirmation),
                GeneralHelper.getString(R.string.mutation_dialog_text_negative),
                GeneralHelper.getString(R.string.mutation_dialog_text_positive),
                false
        );
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    protected void setupField() {
        model = new AccountModel();
        mListAccountModel.clear();
        mListAccountModel.addAll(fetchAccountModel());

        for (AccountModel accountModel : mListAccountModel) {
            if (accountModel.getIsDefault() == 1) {
                model = accountModel;
                break;
            } else {
                model = mListAccountModel.get(0);
            }
        }

        binding.widgetAccount.tvAccountNumber.setText(model.getAcoountString());
        binding.widgetAccount.tvInitial.setText(GeneralHelper.formatInitialName(model.getName()));
        if (model.getAlias().isEmpty()) {
            binding.widgetAccount.tvUsername.setText(model.getName());
        } else {
            binding.widgetAccount.tvUsername.setText(model.getAlias());
        }
    }

    private List<AccountModel> fetchAccountModel() {
        List<AccountModel> accountModelList = new ArrayList<>();

        if (accountList != null) {
            for (int i = 0; i < accountList.size(); i++) {
                accountModelList.add(new AccountModel(accountList.get(i).getAccount(), accountList.get(i).getAccountString(),
                        accountList.get(i).getName(), accountList.get(i).getCurrency(), accountList.get(i).getCardNumber(), accountList.get(i).getProductType(),
                        accountList.get(i).getAccountType(), accountList.get(i).getScCode(), accountList.get(i).getDefault(),
                        accountList.get(i).getAlias()));
            }
        }
        return accountModelList;
    }

    private void selectStartDate() {
        CalendarMutationFragment calendarFragment;
        calendarFragment = new CalendarMutationFragment(this);
        Bundle args = new Bundle();
        if (currentDuration != null) {
            if (currentDuration.getStartDateString() != null && !currentDuration.getStartDateString().isEmpty() && currentDuration.getStartMonth() > 0) {
                args.putString(Constant.TAG_START_DATE, currentDuration.getStartDateString());
            }

            if (currentDuration.getEndDateString() != null && !currentDuration.getEndDateString().isEmpty() && currentDuration.getEndMonth() > 0) {
                args.putString(Constant.TAG_END_DATE, currentDuration.getEndDateString());
            }
        }

        args.putBoolean(Constant.TAG_PICK_START_DATE, true);
        args.putBoolean(Constant.TAG_PICK_DATE, true);
        args.putBoolean(Constant.TAG_MAX_TODAY, true);
        calendarFragment.setArguments(args);

        calendarFragment.setCancelable(true);
        calendarFragment.show(getSupportFragmentManager(), String.valueOf(Constant.REQ_CALENDAR));
    }

    private void selectEndDate() {
        CalendarMutationFragment calendarFragment;
        calendarFragment = new CalendarMutationFragment(this);
        Bundle args = new Bundle();
        if (currentDuration != null) {
            if (currentDuration.getStartDateString() != null && !currentDuration.getStartDateString().isEmpty() && currentDuration.getStartMonth() > 0) {
                args.putString(Constant.TAG_START_DATE, currentDuration.getStartDateString());
            }

            if (currentDuration.getEndDateString() != null && !currentDuration.getEndDateString().isEmpty() && currentDuration.getEndMonth() > 0) {
                args.putString(Constant.TAG_END_DATE, currentDuration.getEndDateString());
            }
        }

        args.putBoolean(Constant.TAG_PICK_END_DATE, true);
        args.putBoolean(Constant.TAG_PICK_DATE, true);
        args.putBoolean(Constant.TAG_MAX_TODAY, true);
        calendarFragment.setArguments(args);

        calendarFragment.setCancelable(true);
        calendarFragment.show(getSupportFragmentManager(), String.valueOf(Constant.REQ_CALENDAR));
    }

    public void setStart(DurasiModel durasi) {
        currentDuration = durasi;
        String tanggalString = currentDuration.getStartDateMutasiStringddMMMyyyy().substring(0, 3) +
                currentDuration.getStartDateMutasiStringddMMMyyyy().substring(3, 6) +
                currentDuration.getStartDateMutasiStringddMMMyyyy().substring(6, 11);

        binding.tvStartDate.setText(tanggalString);
    }

    public void setEnd(DurasiModel durasi) {
        currentDuration = durasi;
        String tanggalString = currentDuration.getEndDateMutasiStringddMMMyyyy().substring(0, 3) +
                currentDuration.getEndDateMutasiStringddMMMyyyy().substring(3, 6) +
                currentDuration.getEndDateMutasiStringddMMMyyyy().substring(6, 11);

        binding.tvEndDate.setText(tanggalString);
    }

    @Override
    public void onSelectStart(@NonNull LocalDate dateSelect) {
        currentDuration = new DurasiModel(
                dateSelect.getDayOfMonth(),
                dateSelect.getMonthValue(),
                dateSelect.getYear());
        setStart(currentDuration);
    }

    @Override
    public void onSelectEnd(@NonNull LocalDate dateSelect) {
        currentDuration = new DurasiModel(
                dateSelect.getDayOfMonth(),
                dateSelect.getMonthValue(),
                dateSelect.getYear());
        setEnd(currentDuration);
    }

    @Override
    public void onSelectRange(@NonNull LocalDate startDateSelect, @NonNull LocalDate endDateSelect) {
        currentDuration = new DurasiModel(startDateSelect.getDayOfMonth(), startDateSelect.getMonthValue(),
                startDateSelect.getYear(), endDateSelect.getDayOfMonth(), endDateSelect.getMonthValue(), endDateSelect.getYear());

        String startDateString = currentDuration.getStartDateMutasiStringddMMMyyyy().substring(0, 3) +
                currentDuration.getStartDateMutasiStringddMMMyyyy().substring(3, 6) +
                currentDuration.getStartDateMutasiStringddMMMyyyy().substring(6, 11);

        String endDateString = currentDuration.getEndDateMutasiStringddMMMyyyy().substring(0, 3) +
                currentDuration.getEndDateMutasiStringddMMMyyyy().substring(3, 6) +
                currentDuration.getEndDateMutasiStringddMMMyyyy().substring(6, 11);

        binding.tvStartDate.setText(startDateString);
        binding.tvEndDate.setText(endDateString);
    }

    @Override
    public void onSelectMonth(String month, String year) {
        for (int i = 0; i < yearList.size(); i++) {
            listMonth = yearList.get(i).getListMonth();
            for (int j = 0; j < listMonth.size(); j++) {
                if (month.equalsIgnoreCase(listMonth.get(j).getMonthString())) {
                    monthNumb = listMonth.get(j).getMonth();
                }
            }
        }
        selectedMonth = month;
        selectedYear = year;
        selectedMonthInNumber = monthNumb;
        binding.tvMonth.setText(selectedMonth + " " + selectedYear);
    }

    @Override
    public void onSelectSumberDana(AccountModel bankModel) {
        model = bankModel;
        binding.widgetAccount.tvAccountNumber.setText(model.getAcoountString());
        binding.widgetAccount.tvInitial.setText(GeneralHelper.formatInitialName(model.getName()));
        if (model.getAlias().isEmpty()) {
            binding.widgetAccount.tvUsername.setText(model.getName());
        } else {
            binding.widgetAccount.tvUsername.setText(model.getAlias());
        }
    }

    @Override
    public void onSendFailedList(List<Integer> list) {

    }

    private void submitRequest() {
        presenter.setUrlSubmitRequest(GeneralHelper.getString(R.string.url_v1_submit_request_estatement));
        if (binding.rbSelectMonth.isChecked()) {
            downloadMutationMonthRequest = new DownloadMutationMonthRequest(selectedYear, selectedMonthInNumber, model.getAcoount(), selectedType, Constant.MONTH_FILTER);
            presenter.submitRequest(downloadMutationMonthRequest, null);
        } else if (binding.rbSelectDate.isChecked()) {
            if (currentDuration != null && currentDuration.getStartDay() != 0) {
                endDate = currentDuration.getEndDateFormatRange();
                startDate = currentDuration.getStartDateFormatRange();
            } else {
                startDate = CalendarHelper.getDateNowFormatRange();
                endDate = CalendarHelper.getDateNowFormatRange();
            }
            downloadMutationDateRangeRequest = new DownloadMutationDateRangeRequest(startDate, endDate, model.getAcoount(), selectedType, Constant.RANGE_FILTER);
            presenter.submitRequest(null, downloadMutationDateRangeRequest);
        }
    }



    @Override
    public void onClickDialog() {
        Intent intentReturn = new Intent();
        intentReturn.putExtra(Constant.TAG_VALUE, 1);
        setResult(RESULT_OK, intentReturn);
        finish();
    }

    @Override
    public void onSelectType(TransactionTypeModel model) {
        binding.tvType.setText(model.getTypeDesc());
        selectedType = model.getType();
    }

    @Override
    protected void onDestroy() {
        selectedMonth = null;
        selectedYear = null;
        currentDuration = null;
        selectedMonthInNumber = null;
        binding = null;
        super.onDestroy();
    }

    @Override
    public void onSuccessSubmitRequest(EmptyStateResponse response) {
        customBottomDialogFragment = new CustomBottomDialogFragment(this, response.getDescription(), response.getSubDescription(),
                GeneralHelper.getString(R.string.mutation_text_request_download_button_fragment), response.getImageName(), false, this);
        customBottomDialogFragment.show(getSupportFragmentManager(), "");
    }

    @Override
    public void onClickBtnYes() {
        submitRequest();
    }

    @Override
    public void onClickBtnNo() {
        //do nothing
    }
}