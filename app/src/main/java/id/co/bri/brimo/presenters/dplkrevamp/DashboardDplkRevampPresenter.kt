package id.co.bri.brimo.presenters.dplkrevamp


import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IDashboardDplkRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.dplkrevamp.IDashboardDplkRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.DetailBrifineDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.DetailKlaimDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.FormTopupDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.ListDplkRequest
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimo.models.apimodel.response.dplk.DplkBoardingResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.DetailBrifineDplkResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.ListPilihBrifineResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.OnboardingDplkResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.PersonalDataDplkResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.RiwayatClaimDplkResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class DashboardDplkRevampPresenter<V>(schedulerProvider: SchedulerProvider,
                                      compositeDisposable: CompositeDisposable,
                                      mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                      categoryPfmSource: CategoryPfmSource,
                                      transaksiPfmSource: TransaksiPfmSource,
                                      anggaranPfmSource: AnggaranPfmSource)  : MvpPresenter<V>(
        schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
            IDashboardDplkRevampPresenter<V> where V : IMvpView, V : IDashboardDplkRevampView {

    private var urlDashboard: String = ""
    private var urlDetail: String = ""
    private var mUrlListBrifine: String = ""
    private var mUrlListKlaimBrifine: String = ""
    private var urlDataInquiryTopUp: String = ""
    private var mUrlRegisDplk: String = ""
    private var mUrlConfirm: String = ""
    private var mUrlPayment: String = ""
    private var mUrlDetailClaimBrifine = ""
    private var mUrlPersonalDataRegistration: String = ""

    override fun setUrlDashboardDplk(urlDashboard: String) {
        this.urlDashboard = urlDashboard
    }

    override fun setUrlDetailDplk(urlDetail: String) {
        this.urlDetail = urlDetail
    }

    override fun setUrlListBrifine(urlListBrifine: String) {
        this.mUrlListBrifine = urlListBrifine
    }

    override fun seturlDplkRegis(url: String) {
        mUrlRegisDplk = url
    }

    override fun seturlListKlaimBrifine(url: String) {
        mUrlListKlaimBrifine = url
    }

    override fun setUrlPersonalDataRegistration(url: String) {
        mUrlPersonalDataRegistration = url
    }

    override fun setInquiryUrlTopUp(url: String) {
        urlDataInquiryTopUp = url
    }

    override fun getDashboardDplk() {
        if (isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getDataTanpaRequest(urlDashboard, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val data = response.getData(OnboardingDplkResponse::class.java)

                            when {
                                data.returnTotal < 0.0f -> getView().onReturnLoss(data)
                                data.returnTotal == 0.0f -> getView().onReturnStable(data)
                                else -> getView().onReturnProfit(data)
                            }

                            getView().onSuccessGetDashboardDplk(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                getView().onExceptionGeneralError(restResponse.desc)
                            } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)) {
                                getView().onExceptionTrxExpired(restResponse.desc)
                            } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_REFRESH.value)) {
                                getView().onExceptionRefreshCallback()
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDetailDplk(request: DetailBrifineDplkRequest) {
        if (isViewAttached) {
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(urlDetail, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            view.hideProgress()
                            val data = response.getData(DetailBrifineDplkResponse::class.java)
                            getView().onSuccessGetDetailBrfineDplk(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }

                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDataListBrifine(request: ListDplkRequest) {
        if (!isViewAttached) {
            return
        }
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlListBrifine, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView().hideProgress()
                            val data = response.getData(
                                ListPilihBrifineResponse::class.java
                            )
                            if (restResponse.code.equals("00", ignoreCase = true)) {
                                getView().onSuccessListBrifine(data)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun getDplkRegis() {
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(mUrlRegisDplk, "", seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val response1 = response.getData(DplkBoardingResponse::class.java)
                        getView().onSuccessDplkRegis(response1)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                            getView().onExceptionGeneralError(restResponse.desc)
                        } else getView().onException(restResponse.desc)
                    }
                })
        )
    }

    override fun getListKlaimBrifine() {
        if (isViewAttached) {
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getDataTanpaRequest(mUrlListKlaimBrifine, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            view.hideProgress()
                            val data = response.getData(RiwayatClaimDplkResponse::class.java)

                            getView().onSuccessGetListKlaimDplk(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDataInquiry(request: FormTopupDplkRequest) {
        if (urlDataInquiryTopUp.isEmpty() && !isViewAttached) return

        mView.showProgress()

        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(urlDataInquiryTopUp, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val data = response.getData(
                            InquiryDompetDigitalResponse::class.java
                        )
                        getView().onSuccessGetInquiry(
                            data
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onApiError(restResponse)
                    }
                })
        )
    }

    override fun getClaimDplkUrl(url: String) {
        mUrlDetailClaimBrifine = url
    }

    override fun getDetailClaimBrifine(request: DetailKlaimDplkRequest) {
        if (isViewAttached) {
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(mUrlDetailClaimBrifine, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            view.hideProgress()
                            val data = response.getData(ReceiptRevampResponse::class.java)

                            getView().onSuccessGetHistoryDetailClaimDplk(data)

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }

                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getPersonalDataRegistration() {
        if (!isViewAttached) {
            return
        }

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable
            .add(
                apiSource.getDataTanpaRequest(mUrlPersonalDataRegistration, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()

                            val data = response.getData(PersonalDataDplkResponse::class.java)
                            getView().onSuccessPersonalDataDplk(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            )

    }
}