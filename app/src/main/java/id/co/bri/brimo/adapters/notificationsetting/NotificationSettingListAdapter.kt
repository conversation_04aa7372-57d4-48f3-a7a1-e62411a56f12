package id.co.bri.brimo.adapters.notificationsetting

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemTransactionNotificationBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.calendar.makeGone
import id.co.bri.brimo.domain.helpers.calendar.makeVisible
import id.co.bri.brimo.models.apimodel.response.notificationsetting.NotificationOptions

class NotificationSettingListAdapter :
    RecyclerView.Adapter<NotificationSettingListAdapter.Holder>() {
    var notificationOptions = mutableListOf<NotificationOptions>()
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    private var actionListener: (NotificationOptions, Boolean, Int) -> Unit = { _, _, _ -> }
    var onSwitchUnchecked: ((option: NotificationOptions, position: Int) -> Unit)? = null
    private var actionChangeListener: (NotificationOptions, Int) -> Unit = {_, _ ->}
    private var isProgrammaticChange = false

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
        val binding = ItemTransactionNotificationBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return Holder(binding)
    }

    override fun getItemCount(): Int = notificationOptions.size

    override fun onBindViewHolder(holder: Holder, position: Int) {
        holder.bindData(position, notificationOptions[position])
    }

    fun clickChangeListener(callBack: (NotificationOptions, Int) -> Unit) {
        actionChangeListener = callBack
    }

    fun clickTypeListener(callBack: (NotificationOptions, Boolean, Int) -> Unit) {
        actionListener = callBack
    }

    fun updateItem(index: Int, notification: NotificationOptions) {
        notificationOptions[index] = notification
        notifyItemChanged(index)
    }

    inner class Holder(private val binding: ItemTransactionNotificationBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bindData(index: Int, notification: NotificationOptions) {
            binding.apply {
                if (notification.type.equals("whatsapp")) {
                    ivType.setImageResource(R.drawable.ic_wa_icon_ns)
                } else if (notification.type.equals("sms")) {
                    ivType.setImageResource(R.drawable.ic_sms_icon_ns)
                } else {
                    Glide.with(root.context).load(notification.icon).into(ivType)
                }
                tvType.text = notification.label

                binding.swAction.setOnCheckedChangeListener(null)
                isProgrammaticChange = true

                swAction.isChecked = notification.details.status

                if (notification.details.currentAmount != "0") {
                    view.makeVisible()
                    viewNominal.makeVisible()
                    tvNominal.text = GeneralHelper.formatCurrency(notification.details.currentAmount)
                } else {
                    view.makeGone()
                    viewNominal.makeGone()
                }

                isProgrammaticChange = false

                binding.swAction.setOnCheckedChangeListener { _, isChecked ->
                    if (isProgrammaticChange) return@setOnCheckedChangeListener
                    if (!isChecked) {
                        onSwitchUnchecked?.invoke(notification, index)
                    } else {
                        actionListener(notification, true, index)
                    }
                }

                tvChange.setOnClickListener { actionChangeListener(notification, index) }
            }
        }
    }

    fun setOnSwitchUncheckedListener(listener: (option: NotificationOptions, position: Int) -> Unit) {
        onSwitchUnchecked = listener
    }

    fun setSwitchChecked(position: Int, checked: Boolean) {
        notificationOptions[position].isChecked = checked
        notifyItemChanged(position)
    }

}
