package id.co.bri.brimo.contract.IView.ubahpin;

import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.ErrorResponseNewSkin;

public interface IUbahPinView extends IMvpView {

    void onSuccess(String data);

    void onException50(String msg);

    void resetInputPin();

    void onErrorPin(String desc);

    void onExceptionErrorAttemps(ErrorResponseNewSkin resp);

    void onErrorBlock(ErrorResponseNewSkin resp);


}
