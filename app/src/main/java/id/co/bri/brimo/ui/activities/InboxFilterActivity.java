package id.co.bri.brimo.ui.activities;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListInboxFiturFilterAdapter;
import id.co.bri.brimo.adapters.ListInboxPeriodeFilterAdapter;
import id.co.bri.brimo.adapters.ListInboxStatusFilterAdapter;
import id.co.bri.brimo.adapters.ListInboxSubFilterAdapter;
import id.co.bri.brimo.contract.IPresenter.inbox.IInboxFilterPresenter;
import id.co.bri.brimo.contract.IView.inbox.IInboxFilterView;
import id.co.bri.brimo.databinding.ActivityInboxFilterBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.InboxStatusModel;
import id.co.bri.brimo.models.apimodel.response.ActivityGroup;
import id.co.bri.brimo.models.apimodel.response.FilterAktivityResponse;
import id.co.bri.brimo.models.apimodel.response.PeriodeResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class InboxFilterActivity extends BaseActivity implements
        ListInboxPeriodeFilterAdapter.ClickItem,
        ListInboxStatusFilterAdapter.ClickItem,
        ListInboxFiturFilterAdapter.ClickItem,
        ListInboxSubFilterAdapter.ClickItem,
        IInboxFilterView,
        View.OnClickListener {

    private ActivityInboxFilterBinding binding;

    @Inject
    IInboxFilterPresenter<IInboxFilterView> filterPresenter;

    private static final String TAG = "InboxFilterActivity";

    protected ListInboxPeriodeFilterAdapter periodeFilterAdapter;
    protected ListInboxStatusFilterAdapter statusFilterAdapter;
    protected ListInboxFiturFilterAdapter fiturFilterAdapter;
    protected ListInboxSubFilterAdapter subFilterAdapter;

    protected static FilterAktivityResponse filterAktivityResponse;

    protected List<PeriodeResponse> periodeFilterModels;
    protected List<InboxStatusModel> statusModels;
    protected List<InboxStatusModel> fiturModel;
    protected List<ActivityGroup.ActivityType> activityTypeList = null;

    protected String sPeriode = "";
    protected String sStatus = "";
    protected String sFitur = "";
    protected String sSubFitur = "";
    protected String lastId = "0";
    protected List<String> sSubFiturs = new ArrayList<>();

    public static void launchIntent(Activity caller, FilterAktivityResponse filterResponse) {
        Intent intent = new Intent(caller, InboxFilterActivity.class);
        filterAktivityResponse = filterResponse;
        caller.startActivityForResult(intent, Constant.REQ_FILTER_INBOX);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityInboxFilterBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.tbInboxFilter, getTitleBar());

        injectDependency();

        setupView();

        binding.btnReset.setOnClickListener(this);
        binding.btnSubmit.setOnClickListener(this);
        binding.cbSubfilterAll.setOnClickListener(this);
    }

    protected void injectDependency() {
        getActivityComponent().inject(this);
        if (filterPresenter != null) {
            filterPresenter.setView(this);
            filterPresenter.start();
        }
    }

    protected String getTitleBar() {
        return GeneralHelper.getString(R.string.filter_aktivitas);
    }

    /**
     * untuk mengatur adapter
     */
    protected void setupView() {
        dataPeriode();

        binding.rvTimeFilter.setLayoutManager(new LinearLayoutManager(this, RecyclerView.HORIZONTAL, false));
        periodeFilterAdapter = new ListInboxPeriodeFilterAdapter(periodeFilterModels, this, this);
        binding.rvTimeFilter.setAdapter(periodeFilterAdapter);

        if (filterAktivityResponse.getActivityStatusList() != null) {
            binding.rvMainFilter.setLayoutManager(new LinearLayoutManager(this, RecyclerView.HORIZONTAL, false));
            statusFilterAdapter = new ListInboxStatusFilterAdapter(statusModels, this, this);
            binding.rvMainFilter.setAdapter(statusFilterAdapter);

            binding.rvTitleFilter.setLayoutManager(new LinearLayoutManager(this, RecyclerView.HORIZONTAL, false));
            fiturFilterAdapter = new ListInboxFiturFilterAdapter(fiturModel, this, this);
            binding.rvTitleFilter.setAdapter(fiturFilterAdapter);

            binding.rvSubtitleFilter.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        }
    }

    /**
     * merubah data adapter
     */
    private void dataPeriode() {
        periodeFilterModels = new ArrayList<>();
        periodeFilterModels.add(0, new PeriodeResponse("ALL", GeneralHelper.getString(R.string.semua)));
        periodeFilterModels.add(1, new PeriodeResponse("0D", GeneralHelper.getString(R.string.today)));
        periodeFilterModels.add(2, new PeriodeResponse("1D", GeneralHelper.getString(R.string.yesterday)));
        periodeFilterModels.add(3, new PeriodeResponse("1W", GeneralHelper.getString(R.string.this_week)));
        periodeFilterModels.add(4, new PeriodeResponse("1M", GeneralHelper.getString(R.string.this_month)));
        periodeFilterModels.add(5, new PeriodeResponse("7D", GeneralHelper.getString(R.string.last_7_days)));


        if (filterAktivityResponse != null) {
            statusModels = new ArrayList<>();
            for (int i = 0; i < filterAktivityResponse.getActivityStatusList().size(); i++) {
                statusModels.add(i, new InboxStatusModel(filterAktivityResponse.getActivityStatusList().get(i).getCodeGroup(),
                        filterAktivityResponse.getActivityStatusList().get(i).getNameGroup(), false));
            }

            fiturModel = new ArrayList<>();
            for (int i = 0; i < filterAktivityResponse.getActivityGroupList().size(); i++) {
                fiturModel.add(i, new InboxStatusModel(filterAktivityResponse.getActivityGroupList().get(i).getCodeGroup(),
                        filterAktivityResponse.getActivityGroupList().get(i).getNameGroup(), false));
            }
        }
    }

    /**
     * untuk memunculkan data subfilter berdasarkan filter yang dipilih
     */
    public void subFilter() {

        activityTypeList = new ArrayList<>();
        for (int i = 0; i < filterAktivityResponse.getActivityGroupList().size(); i++) {
            for (int j = 0; j < filterAktivityResponse.getActivityGroupList().get(i).getActivityTypeList().size() - 1; j++) {
                if (filterAktivityResponse.getActivityGroupList().get(i).getCodeGroup().equalsIgnoreCase(sFitur)) {
                    activityTypeList.add(j, new ActivityGroup.ActivityType(
                            filterAktivityResponse.getActivityGroupList().get(i).getActivityTypeList().get(j + 1).getCodeType(),
                            filterAktivityResponse.getActivityGroupList().get(i).getActivityTypeList().get(j + 1).getName()));
                }
            }
        }
    }

    /**
     * PeriodeResponse balikan untuk waktu
     */
    @Override
    public void itemClickedPeriode(PeriodeResponse periodeResponseList) {
        clearSelectedPeriode();
        periodeResponseList.setSelected(true);
        sPeriode = periodeResponseList.getCode();
        periodeFilterAdapter.notifyDataSetChanged();
        if (activityTypeList != null) {
            if ((sFitur.equalsIgnoreCase("ALL") || activityTypeList.size() == 0)) {
                enableButton();
            } else {
                validationButton();
            }
        } else {
            validationButton();
        }
    }

    /**
     * @param inboxStatusModel balikan untuk status
     */
    @Override
    public void itemClickedStatus(InboxStatusModel inboxStatusModel) {
        clearSelectedStatus();
        inboxStatusModel.setSelected(true);
        sStatus = inboxStatusModel.getCode();
        statusFilterAdapter.notifyDataSetChanged();
        if (activityTypeList != null) {
            if ((sFitur.equalsIgnoreCase("ALL") || activityTypeList.size() == 0)) {
                enableButton();
            } else {
                validationButton();
            }
        } else {
            validationButton();
        }
    }

    /**
     * @param filterResponses balikan untuk fitur
     */
    @Override
    public void itemClickedFitur(InboxStatusModel filterResponses) {
        clearSelectedFitur();
        filterResponses.setSelected(true);
        sFitur = filterResponses.getCode();
        fiturFilterAdapter.notifyDataSetChanged();
/*
        if (filterResponses.getName().equalsIgnoreCase("Semua") || filterResponses.getName().equalsIgnoreCase("Transfer")) {
            if (!sPeriode.equalsIgnoreCase("") && !sStatus.equalsIgnoreCase(""))
                enableButton();
            else
                disableButton();
        } else {
            sSubFiturs.clear();
            validationButton();
        }

        if (filterResponses.getName().equalsIgnoreCase("Semua") || filterResponses.getName().equalsIgnoreCase("Transfer")) {
            cbSubAll.setVisibility(View.GONE);
        } else {
            cbSubAll.setChecked(false);
            cbSubAll.setVisibility(View.VISIBLE);
        }
*/

        if (sSubFiturs != null) {
            sSubFiturs.clear();
        }

        subFilter();
        binding.rvSubtitleFilter.setVisibility(View.VISIBLE);
        subFilterAdapter = new ListInboxSubFilterAdapter(activityTypeList, this, this);
        binding.rvSubtitleFilter.setAdapter(subFilterAdapter);

        if (activityTypeList.size() == 0) {
            if (!sPeriode.equalsIgnoreCase("") && !sStatus.equalsIgnoreCase(""))
                enableButton();
            else
                disableButton();
        } else {
            sSubFiturs.clear();
            validationButton();
        }

        if (activityTypeList.size() == 0) {
            binding.cbSubfilterAll.setVisibility(View.GONE);
        } else {
            binding.cbSubfilterAll.setChecked(false);
            binding.cbSubfilterAll.setVisibility(View.VISIBLE);
        }

    }


    /**
     * @param filterModel balikan untuk subFitur
     */
    @Override
    public void onClickedSubFilterItem(ActivityGroup.ActivityType filterModel) {
        if (filterModel.isSelected()) {
            sSubFiturs.add(filterModel.getCodeType());

            if (activityTypeList.size() == sSubFiturs.size()) {
                binding.cbSubfilterAll.setChecked(true);
            }

        } else {
            for (int i = 0; i < sSubFiturs.size(); i++) {
                String codeType = filterModel.getCodeType();
                String subFilter = sSubFiturs.get(i);
                if (codeType != null && codeType.equalsIgnoreCase(subFilter))
                    if (!filterModel.isSelected())
                        sSubFiturs.remove(i);

                if (activityTypeList.size() != sSubFiturs.size()) {
                    binding.cbSubfilterAll.setChecked(false);
                }
            }
        }

        validationButton();

    }

    public void validationButton() {

        if (!sPeriode.equalsIgnoreCase("") && !sStatus.equalsIgnoreCase("") &&
                !sFitur.equalsIgnoreCase("") && sSubFiturs.size() != 0) {
            enableButton();
        } else {
            disableButton();
        }
    }

    public void enableButton() {
        binding.btnSubmit.setEnabled(true);
        binding.btnSubmit.setAlpha(1);
    }

    public void disableButton() {
        binding.btnSubmit.setEnabled(false);
        binding.btnSubmit.setAlpha(0.3f);
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btnReset:
                clearAll();
                break;
            case R.id.btnSubmit:
                sSubFitur = String.join(",", sSubFiturs);

                Intent intent = new Intent();
                intent.putExtra("PERIODE", sPeriode);
                intent.putExtra("STATUS", sStatus);
                intent.putExtra("FITUR", sFitur);
                intent.putExtra("SUBFITUR", sSubFitur);
                intent.putExtra("LASTID", lastId);
                setResult(Constant.REQ_FILTER_INBOX, intent);
                finish();
                break;
            case R.id.cb_subfilter_all:
                if (binding.cbSubfilterAll.isChecked()) {
                    subFilterAdapter.setCbSubAll(true);
                } else {
                    subFilterAdapter.setCbSubAll(false);
                }
                if (sSubFiturs != null) {
                    sSubFiturs.clear();
                }
                subFilterAdapter.notifyDataSetChanged();
                break;
        }
    }

    private void clearAll() {
        clearSelectedPeriode();
        clearSelectedStatus();
        clearSelectedFitur();
        sSubFiturs.clear();
        binding.cbSubfilterAll.setVisibility(View.GONE);
        sSubFitur = "";
        disableButton();
        binding.rvSubtitleFilter.setVisibility(View.GONE);
    }

    private void clearSelectedPeriode() {
        sPeriode = "";
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            periodeFilterModels.forEach((p) -> p.setSelected(false));
        } else {
            for (PeriodeResponse tr : periodeFilterModels) {
                tr.setSelected(false);
            }
        }
        periodeFilterAdapter.notifyDataSetChanged();
    }

    private void clearSelectedStatus() {
        sStatus = "";
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            statusModels.forEach((p) -> p.setSelected(false));
        } else {
            for (InboxStatusModel sm : statusModels) {
                sm.setSelected(false);
            }
        }
        statusFilterAdapter.notifyDataSetChanged();
    }

    private void clearSelectedFitur() {
        sFitur = "";
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            fiturModel.forEach((p) -> p.setSelected(false));
        } else {
            for (InboxStatusModel tr : fiturModel) {
                tr.setSelected(false);
            }
        }
        fiturFilterAdapter.notifyDataSetChanged();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}