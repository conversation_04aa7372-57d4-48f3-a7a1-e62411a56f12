package id.co.bri.brimo.ui.activities.inforekeningnewskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.notificationsetting.NotificationSettingListAdapter
import id.co.bri.brimo.contract.IPresenter.notificationsetting.INotificationSettingDetailPresenter
import id.co.bri.brimo.contract.IView.notificationsetting.INotificationSettingDetailView
import id.co.bri.brimo.databinding.ActivityTransactionNotificationBinding
import id.co.bri.brimo.domain.SnackBarType
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.models.apimodel.request.notificationsetting.ChangeStatusNotificationRequest
import id.co.bri.brimo.models.apimodel.request.notificationsetting.GetDetailNotificationSettingRequest
import id.co.bri.brimo.models.apimodel.response.notificationsetting.NotificationDetail
import id.co.bri.brimo.models.apimodel.response.notificationsetting.NotificationOptions
import id.co.bri.brimo.models.apimodel.response.notificationsetting.NotificationSettingResponse
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.newskinonboarding.OnboardingInputNumberForgetPassActivity
import id.co.bri.brimo.ui.fragments.PinFragmentNewSkin
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmationDismissEvent
import javax.inject.Inject

class TransactionNotificationActivity : NewSkinBaseActivity(), INotificationSettingDetailView, PinFragmentNewSkin.SendPin {

    @Inject
    lateinit var presenter: INotificationSettingDetailPresenter<INotificationSettingDetailView>

    private lateinit var binding: ActivityTransactionNotificationBinding
    private val notificationSettingListAdapter = NotificationSettingListAdapter()
    private val gson = Gson()
    private var notification: NotificationSettingResponse? = null
    private lateinit var itemNotification: NotificationOptions
    private lateinit var notificationDetail: NotificationDetail
    private var isStatusChange = false
    private var position = 0
    private var accountNumber: String? = null
    private var alias: String? = null
    private var cardImage: String? = null
    private var name: String? = null

    private var skeletonNotification: SkeletonScreen? = null
    var pinFragment: PinFragmentNewSkin? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTransactionNotificationBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        injectDependency()
        initSkeleton()

        Handler(Looper.getMainLooper()).postDelayed({
            skeletonNotification?.hide()
            binding.viewParent.visibility = View.VISIBLE
            notification = gson.fromJson(
                intent.getStringExtra(Constant.TAG_CONTENT), NotificationSettingResponse::class.java
            )
            accountNumber = intent.getStringExtra(Constant.ACCOUNT_NUM)
            alias = intent.getStringExtra(Constant.USER_ALIAS)
            cardImage = intent.getStringExtra(Constant.IMAGE_LIST_TYPE)
            name = intent.getStringExtra(Constant.NAME)
            setupToolbar()
            setupView()
        }, Constant.SKELETON_DURATION)

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                handleBackPressed()
            }
        })
    }

    private fun setupToolbar()  {

        val title = notification?.title?.let {
            if (it.isNotBlank()) {
                notification?.title
            } else {
                GeneralHelper.getString(R.string.txt_transaction_notification)
            }
        }

        GeneralHelperNewSkin.setToolbar(
            this,
            binding.toolbar.toolbar,
            title
        )

    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
    }

    private fun setupView() {

        val phoneNumberDetails = notification?.notificationOptions
            ?.flatMap { it.details.dataView }
            ?.filter { it.style == "phone_number" }
            ?: emptyList()

        for (detail in phoneNumberDetails) {
            binding.tvPhoneNumber.text = detail.value
        }

        setupAdapter(notification?.notificationOptions)

    }

    private fun setupAdapter(notificationOptions: MutableList<NotificationOptions>?) {

        binding.rvType.apply {
            View.VISIBLE
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            adapter = notificationSettingListAdapter
            if (notificationOptions != null) {
                notificationSettingListAdapter.notificationOptions = notificationOptions
            }
            notificationSettingListAdapter.clickTypeListener { option, _, index ->
                position = index
                if (option.details.currentAmount == "0") {
                    handleToDetails(option)
                } else {
                    val responseJson = gson.toJson(option)
                    itemNotification = gson.fromJson(responseJson, NotificationOptions::class.java)
                    notificationDetail = itemNotification.details
                    isStatusChange = true
                    alertNotification(position,  option.label, true)
                }
            }
            notificationSettingListAdapter.setOnSwitchUncheckedListener { option, index ->
                position = index
                val responseJson = gson.toJson(option)
                itemNotification = gson.fromJson(responseJson, NotificationOptions::class.java)
                notificationDetail = itemNotification.details
                isStatusChange = false
                alertNotification(position, option.label,false)
            }
            notificationSettingListAdapter.clickChangeListener { option, index ->
                position = index
                handleToDetails(option)
            }
        }
    }

    private fun alertNotification (position: Int, label: String, isActive: Boolean) {

        val title = getString(if (isActive) R.string.txt_activate_notifikasi else R.string.txt_non_activate_transaction_title, label)
        val desc = getString(if (isActive) R.string.txt_activate_transaction_subtitle else R.string.txt_non_activate_transaction_subtitle, label)
        val button = getString(if (isActive) R.string.ya_aktifkan else R.string.ya_nonaktifkan)

        val firstBtnFunction = Runnable {
            openPin()
        }

        val secBtnFunction = Runnable {
            notificationSettingListAdapter.setSwitchChecked(position, true)
        }

        val dismissFunction = Runnable {
            notificationSettingListAdapter.setSwitchChecked(position, true)
        }

        showDialogConfirmationDismissEvent(
            supportFragmentManager,
            R.drawable.ic_question_new_ns,
            "",
            title,
            desc,
            createKotlinFunction0(firstBtnFunction),
            createKotlinFunction0(secBtnFunction),
            createKotlinFunction0(dismissFunction),
            false,
            button,
            getString(R.string.btn_cancel),
            false,
            showCloseButton = true,
        )
    }

    private fun handleToDetails(notification: NotificationOptions) {
        val responseJson = gson.toJson(notification)
        val intent = Intent(this, TransactionNotificationSettingActivity::class.java)
        intent.putExtra(Constant.TAG_CONTENT, responseJson)
        intent.putExtra(Constant.ACCOUNT_NUM, accountNumber)
        intent.putExtra(Constant.USER_ALIAS, alias)
        intent.putExtra(Constant.IMAGE_LIST_TYPE, cardImage)
        intent.putExtra(Constant.NAME, name)
        startActivityIntent.launch(intent)
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
//            if (result.resultCode != RESULT_OK) {
//                if (result.data != null) {
//                    setResult(RESULT_CANCELED, result.data)
//                    finish()
//                }
//            }
        }

    private fun initSkeleton() {

        skeletonNotification =
            Skeleton.bind(binding.viewParent).shimmer(true).angle(20).duration(1200)
                .color(R.color.white).load(R.layout.item_skeleton_transaction_notification).show()

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {

            val gson = Gson()
            val updatedItem = gson.fromJson(data?.getStringExtra(Constant.TAG_CONTENT), NotificationOptions::class.java)
            if (updatedItem != null) {
                itemNotification = updatedItem
            }

            val message = data?.getStringExtra(Constant.TAG_MESSAGE)
            val accountNumber = data?.getStringExtra(Constant.ACCOUNT_NUM)
            val type = data?.getStringExtra(Constant.TYPE_FLAG)
            if (message != null) {
                GeneralHelperNewSkin.showCustomSnackBar(binding.content, message, SnackBarType.SUCCESS)
            }
            if (accountNumber != null && type != null) {
                presenter.apply {
                    setUrlGetDetailNotification(getString(R.string.url_v1_get_details_notif))
                    getDetailNotificationSetting(
                        GetDetailNotificationSettingRequest(
                            accountNumber = accountNumber,
                            type = type
                        )
                    )
                }
            }
        } else if (resultCode == Constant.RESULT_BACK) {
            notificationSettingListAdapter.setSwitchChecked(position, true)
        }
    }

    private fun openPin() {
        pinFragment = PinFragmentNewSkin(this, this)
        pinFragment?.show()
        pinFragment?.setOnDismissListenerSuccessOrNo(object : PinFragmentNewSkin.OnDismissListenerSuccessOrNo {
            override fun onDismissListenerSuccessOrNo(isSuccess: Boolean?) {
                notificationSettingListAdapter.setSwitchChecked(position, true)
            }
        })
    }

    private fun changeStatus(pin: String?) {
        presenter.setUrlChangeStatus(getString(R.string.url_v1_registration_notif))
        pin?.let {
            presenter.changeStatus(
                ChangeStatusNotificationRequest(
                    notificationDetail.accountNumber, itemNotification.type, isStatusChange, it
                )
            )
        }
    }

    private fun getDetails() {
        presenter.apply {
            setUrlGetDetailNotification(getString(R.string.url_v1_get_details_notif))
            getDetailNotificationSetting(
                GetDetailNotificationSettingRequest(
                    accountNumber = notificationDetail.accountNumber,
                    type = itemNotification.type
                )
            )
        }
    }

    override fun onSendPinComplete(pin: String?) {
        changeStatus(pin)
    }

    override fun onLupaPin() {
        OnboardingInputNumberForgetPassActivity.launchIntent(this, Constant.REQ_UBAH_PIN)
    }

    override fun onSuccessGetDetailNotification(response: NotificationDetail) {
        itemNotification.details = response
        notificationDetail = response
        notificationSettingListAdapter.updateItem(position, itemNotification)
    }

    override fun onSuccessChangeStatus(desc: String) {
        pinFragment?.dismiss()
        if (isStatusChange) {
            GeneralHelperNewSkin.showCustomSnackBar(binding.content, getString(R.string.txt_success_active_notification), SnackBarType.SUCCESS)
        } else {
            GeneralHelperNewSkin.showCustomSnackBar(binding.content, getString(R.string.txt_success_inactive_notification), SnackBarType.SUCCESS)
        }
        getDetails()
    }

    override fun onExceptionRevamp(message: String?) {
        pinFragment?.onWrongPin(message)
    }


    override fun onException(message: String) {
        if (isStatusChange) {
            GeneralHelperNewSkin.showCustomSnackBar(binding.content, getString(R.string.txt_success_active_failed_notification), SnackBarType.ERROR)
        } else {
            GeneralHelperNewSkin.showCustomSnackBar(binding.content, getString(R.string.txt_success_inactive_failed_notification), SnackBarType.ERROR)
        }
    }

    override fun onSuccessFirstRegistration(desc: String) {
    }

    override fun onSuccessChangeAmount(desc: String) {
    }

    private fun handleBackPressed() {
        val intent = Intent()
        setResult(Constant.RESULT_BACK, intent)
        finish()
    }

    companion object {

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            jsonResponse: String,
            accountNumber: String?,
            alias: String?,
            cardImage: String?,
            name: String?
        ) {
            val intent = Intent(caller, TransactionNotificationActivity::class.java)
            intent.putExtra(Constant.TAG_CONTENT, jsonResponse)
            intent.putExtra(Constant.ACCOUNT_NUM, accountNumber)
            intent.putExtra(Constant.USER_ALIAS, alias)
            intent.putExtra(Constant.IMAGE_LIST_TYPE, cardImage)
            intent.putExtra(Constant.NAME, name)
            caller.startActivityForResult(intent, Constant.RESULT_BACK)
        }
    }

    override fun onSessionEnd(message: String?) {
        GeneralHelperNewSkin.showErrorBlokir(this)
    }
}