package id.co.bri.brimo.ui.activities.dompetdigitalreskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ActivitySuccessBindingDompetDigitalReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.EwalletBindingResponse
import id.co.bri.brimo.models.apimodel.response.EwalletProductResponse
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity

class SuccessBindingDompetDigitalReskinActivity : NewSkinBaseActivity(), View.OnClickListener {

    private lateinit var binding: ActivitySuccessBindingDompetDigitalReskinBinding
    private var selectedWallet: EwalletProductResponse? = null
    private var bindingResponse: EwalletBindingResponse? = null
    private var cellphoneNumber: String = ""

    companion object {
        const val TAG = "SuccessBindingDompetDigitalReskinActivity"
        const val EXTRA_WALLET_JSON = "extra_wallet_json"
        private const val EXTRA_CELLPHONE_NUMBER = "extra_cellphone_number"
        private const val EXTRA_EWALLET_BINDING_RESPONSE = "extra_ewallet_binding_response"

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            wallet: EwalletProductResponse,
            ewalletBindingResponse: EwalletBindingResponse,
            cellphoneNumber: String = ""
        ) {
            val intent = Intent(caller, SuccessBindingDompetDigitalReskinActivity::class.java)
            intent.putExtra(EXTRA_WALLET_JSON, Gson().toJson(wallet))
            intent.putExtra(EXTRA_EWALLET_BINDING_RESPONSE, Gson().toJson(ewalletBindingResponse))
            intent.putExtra(EXTRA_CELLPHONE_NUMBER, cellphoneNumber)
            caller.startActivityForResult(intent, Constant.REQ_WALLET_BINDING_SUCCESS)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySuccessBindingDompetDigitalReskinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        parseDataIntent()
        setupView()
        setupButtonClick()
    }

    private fun parseDataIntent() {
        val bindingJson = intent.getStringExtra(EXTRA_EWALLET_BINDING_RESPONSE)
        bindingResponse = bindingJson?.let {
            Gson().fromJson(it, EwalletBindingResponse::class.java)
        }
        val walletJson = intent.getStringExtra(EXTRA_WALLET_JSON)
        cellphoneNumber = intent.getStringExtra(EXTRA_CELLPHONE_NUMBER) ?: ""
        selectedWallet = walletJson?.let {
            Gson().fromJson(it, EwalletProductResponse::class.java)
        }
    }

    private fun setupView() {
        selectedWallet?.let { wallet ->
            // Set wallet title
            binding.tvTitle.text = wallet.title ?: ""

            // Load wallet icon
            GeneralHelper.loadIconTransaction(
                this,
                wallet.iconPath ?: "",
                wallet.iconName?.split(".")?.get(0) ?: "",
                binding.imgBanner,
                GeneralHelper.getImageId(this, "ic_menu_qna_dompet_digital")
            )
        }
        binding.tvBindingTitle.text = bindingResponse?.extrasResponse?.title ?: ""
        binding.tvBindingDesc.text = bindingResponse?.extrasResponse?.desc ?: ""
        binding.tvNoPelanggan.text = cellphoneNumber
    }

    private fun setupButtonClick() {
        binding.btnSubmit.setOnClickListener(this)
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.btnSubmit -> {
                // Set result to indicate successful binding and return to FormDompetDigitalReskinActivity
                setResult(RESULT_OK)
                finish()
            }
        }
    }
}
