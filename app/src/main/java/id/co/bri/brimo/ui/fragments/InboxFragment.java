package id.co.bri.brimo.ui.fragments;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListInboxAdapter;
import id.co.bri.brimo.contract.IPresenter.inbox.IInboxPresenter;
import id.co.bri.brimo.contract.IView.inbox.IInboxView;
import id.co.bri.brimo.databinding.FragmentInboxBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.FilterAktivityResponse;
import id.co.bri.brimo.models.apimodel.response.InboxResponse;
import id.co.bri.brimo.models.apimodel.response.PendingResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptAmkkmResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptInternasionalResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampInboxResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptTravelTrainResponse;
import id.co.bri.brimo.models.apimodel.response.emas.ReceiptEmasResponse;
import id.co.bri.brimo.models.apimodel.response.esbn.ReceiptResponseNew;
import id.co.bri.brimo.ui.activities.DashboardIBActivity;
import id.co.bri.brimo.ui.activities.InboxFilterActivity;
import id.co.bri.brimo.ui.activities.ReceiptActivity;
import id.co.bri.brimo.ui.activities.ReceiptInboxActivity;
import id.co.bri.brimo.ui.activities.ReceiptPendingActivity;
import id.co.bri.brimo.ui.activities.britamajunio.ReceiptOpenJunioActivity;
import id.co.bri.brimo.ui.activities.bukarekening.PendingTabunganActivity;
import id.co.bri.brimo.ui.activities.bukarekening.ReceiptTabunganActivity;
import id.co.bri.brimo.ui.activities.emas.ReceiptGagalAFTActivity;
import id.co.bri.brimo.ui.activities.lifestyle.ReceiptLifestyleActivity;
import id.co.bri.brimo.ui.activities.receipt.ReceiptAbnormalRevampActivity;
import id.co.bri.brimo.ui.activities.receipt.ReceiptRevampActivity;
import id.co.bri.brimo.ui.activities.sbn.earlyredemption.ReceiptEarlyRedeemActivity;
import id.co.bri.brimo.ui.activities.simpedes.ReceiptAmkkmActivity;
import id.co.bri.brimo.ui.activities.transferinternasional.ReceiptTransferInternationalActivity;
import id.co.bri.brimo.ui.activities.travel.ReceiptTravelTrainActivity;

public class InboxFragment extends BaseFragment implements
        View.OnClickListener,
        IInboxView,
        SwipeRefreshLayout.OnRefreshListener,
        ListInboxAdapter.onClickItem,
        ListInboxAdapter.onSelected {

    private FragmentInboxBinding binding;

    @Inject
    IInboxPresenter<IInboxView> inboxPresenter;

    protected ListInboxAdapter listInboxAdapter;
    protected SkeletonScreen skeletonScreenInbox;
    protected List<InboxResponse.ActivityList> activityLists = new ArrayList<>();
    protected LinearLayoutManager layoutManager;

    protected String periode = "";
    protected String status = "";
    protected String fitur = "";
    protected String subFitur = "";
    protected String lastId = "";

    protected static FilterAktivityResponse saveResponse = null;

    private static final String TAG = "InboxFragment";
    private static final String sRefnum = "refnum";

    private static String mRefnum = "";
    private boolean isLoading = false;
    protected Handler handler = new Handler();

    public InboxFragment() {
    }


    /**
     * @param refnum
     * @return
     */
    public static InboxFragment newInstance(String refnum, String notifikasiString) {
        InboxFragment fragment = new InboxFragment();
        mRefnum = refnum;
        Bundle args = new Bundle();
        args.putString(sRefnum, refnum);
        args.putString(Constant.TAG_NOTIF, notifikasiString);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        injectDependency();

        if (getArguments() != null) {
            if (!getArguments().getString(sRefnum).isEmpty())
                inboxPresenter.getInboxDetail(getArguments().getString(sRefnum));

            if (getArguments().getString(Constant.TAG_NOTIF) != null) {
                String notifikasiString = getArguments().getString(Constant.TAG_NOTIF);
            }
        }

    }

    private void checkRefNumNotif() {
        if (!mRefnum.isEmpty()) {
            if (activityLists != null)
                for (InboxResponse.ActivityList activityList : activityLists) {
                    if (mRefnum.equals(activityList.getReferenceNumber())) {
                        inboxPresenter.setTrxType(activityList.getTrxType());
                        inboxPresenter.getInboxDetail(mRefnum);
                        break;
                    }
                }
            mRefnum = "";
        }
    }

    @SuppressLint("RestrictedApi")
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentInboxBinding.inflate(inflater, container, false);
        binding.tbInbox.canShowOverflowMenu();

        GeneralHelper.setToolbarNoIconBack(getActivity(), binding.tbInbox, getString(R.string.menu_activities));

        binding.ivInbox.setOnClickListener(this);
        binding.swipeRefresh.setOnRefreshListener(this);


        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initiateAdapter();
    }

    public void injectDependency() {
        getActivityComponent().inject(this);

        if (inboxPresenter != null) {
            inboxPresenter.setView(this);
            inboxPresenter.start();
            callService();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    /**
     * inisialisasi adapter
     */
    public void initiateAdapter() {
        layoutManager = new LinearLayoutManager(getBaseActivity(), RecyclerView.VERTICAL, false);
        binding.rvItemInbox.setLayoutManager(layoutManager);
        listInboxAdapter = new ListInboxAdapter(activityLists, getActivity(), this, this);
        binding.rvItemInbox.setAdapter(listInboxAdapter);

        binding.rvItemInbox.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                LinearLayoutManager linearLayoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();

                if (!isLoading) {
                    if (linearLayoutManager != null && linearLayoutManager.findLastCompletelyVisibleItemPosition() == activityLists.size() - 1) {
                        if (linearLayoutManager.getItemCount() >= 10) {
                            loadMoreInbox();
                            isLoading = true;
                        }
                    }
                }
            }
        });

        skeletonScreenInbox = Skeleton.bind(binding.rvItemInbox)
                .adapter(listInboxAdapter)
                .shimmer(true)
                .angle(20)
                .frozen(false)
                .duration(1200)
                .count(7)
                .load(R.layout.item_skeleton_inbox)
                .show();
    }


    /**
     * untuk meload data inbox
     */
    private void loadMoreInbox() {
        activityLists.add(null);
        listInboxAdapter.notifyItemInserted(activityLists.size() - 1);

        handler.postDelayed(() -> {
            activityLists.remove(activityLists.size() - 1);
            int scrollPosition = activityLists.size();
            listInboxAdapter.notifyItemRemoved(scrollPosition);
            inboxPresenter.getInbox(periode, status, fitur, subFitur, lastId, false);
        }, 1000);
    }

    /**
     * @param filterAktivityResponse FilterData Berhasil
     */
    @Override
    public void onSuccessGetFilterData(FilterAktivityResponse filterAktivityResponse) {
        saveResponse = filterAktivityResponse;
//        InboxFilterActivity.launchIntent(getActivity(), filterAktivityResponse);
    }

    /**
     * @param receiptResponse Inbox Detail Berhasil
     */
    @Override
    public void onSuccessGetInboxDetail(ReceiptResponse receiptResponse) {
        if (receiptResponse.getPendingResponses().getTitleImage().equalsIgnoreCase(Constant.RECEIPT68) || receiptResponse.getPendingResponses().getTitleImage().equalsIgnoreCase(Constant.RECEIPT58)) {
            ReceiptPendingActivity.launchIntentInbox(getActivity(), receiptResponse.getPendingResponses());
        } else if (receiptResponse.getPendingResponses().getTitleImage().equalsIgnoreCase(Constant.RECEIPT00)) {
            ReceiptInboxActivity.launchIntent(getActivity(), receiptResponse.getPendingResponses());
        }
    }

    private int defaultIcon() {
        return R.drawable.bri;
    }

    @Override
    public void onSuccessGetInboxDetailRevamp(ReceiptRevampInboxResponse receiptRevampResponse) {
        if (receiptRevampResponse.getReceiptRevampResponse() != null) {
            if (Boolean.TRUE.equals(receiptRevampResponse.getReceiptRevampResponse().isOnProcess())) {
                ReceiptAbnormalRevampActivity.launchIntentReceipt(getActivity(), receiptRevampResponse.getReceiptRevampResponse(), false, defaultIcon());
            } else {
                ReceiptRevampActivity.launchIntentReceipt(getActivity(), receiptRevampResponse.getReceiptRevampResponse(), defaultIcon());
            }
        }
    }

    @Override
    public void onSuccessGetInboxAmkkmDetail(ReceiptAmkkmResponse amkkmResponse) {
        boolean flag = false;
        if (amkkmResponse.getDataAlamatResiko().get(0).getName() != null) {
            flag = true;
        }
        ReceiptAmkkmActivity.launchIntentFromActivity(getActivity(), amkkmResponse, flag);
    }

    @Override
    public void onSuccessGetOpenAccountDetail(PendingResponse pendingResponse) {
        ReceiptActivity.launchIntent(true, getActivity(), pendingResponse, true);
    }

    @Override
    public void onSuccessGetOpenJunio(PendingResponse pendingResponse) {
        ReceiptOpenJunioActivity.launchIntentCatatan(getActivity(), pendingResponse, true);
    }

    @Override
    public void onSuccessInternasional(ReceiptInternasionalResponse receiptInternasionalResponse) {
        ReceiptTransferInternationalActivity.launchIntent(getActivity(), receiptInternasionalResponse);
    }

    @Override
    public void onSuccessKai(ReceiptTravelTrainResponse receiptTravelTrainResponse) {
        ReceiptTravelTrainActivity.launchIntentReceipt(getActivity(), receiptTravelTrainResponse);
    }

    @Override
    public void onSuccessPencairan(ReceiptResponseNew responseNew) {
        ReceiptEarlyRedeemActivity.launchIntent(requireActivity(), responseNew);
    }

    @Override
    public void onException12() {
        if (binding.swipeRefresh != null) {
            binding.swipeRefresh.setRefreshing(false);
        }

        validationButton(false);
        binding.rvItemInbox.setVisibility(View.GONE);
        binding.llDesc12.setVisibility(View.VISIBLE);
    }

    @Override
    public void onException93() {
        if (binding.swipeRefresh != null) {
            binding.swipeRefresh.setRefreshing(false);
        }

        validationButton(false);
        binding.rvItemInbox.setVisibility(View.GONE);
        binding.llDesc12.setVisibility(View.VISIBLE);
    }

    public void onSuccessGetReceiptRevamp(ReceiptRevampInboxResponse receiptRevampResponse) {
        if (receiptRevampResponse.getReceiptRevampResponse().isOnProcess()) {
            ReceiptAbnormalRevampActivity.launchIntentReceipt(getActivity(), receiptRevampResponse.getReceiptRevampResponse(), false, defaultIcon());
        } else {
            ReceiptRevampActivity.launchIntentReceipt(getActivity(), receiptRevampResponse.getReceiptRevampResponse(), defaultIcon());
        }
    }

    @Override
    public void onSuccessTabunganRevamp(ReceiptRevampResponse pendingResponse, String trxType) {
        if (pendingResponse.getImmediatelyFlag()) {
            ReceiptTabunganActivity.Companion.launchIntent(requireActivity(), pendingResponse, true, GeneralHelper.getString(R.string.ok), trxType);
        } else
            PendingTabunganActivity.Companion.launchIntent(requireActivity(), pendingResponse, GeneralHelper.getString(R.string.url_buka_tabungan_pending_revamp));

    }

    @Override
    public void onSuccesTabunganS3fRevamp(ReceiptRevampResponse pendingResponse) {
        ReceiptTabunganActivity.Companion.launchIntent(requireActivity(), pendingResponse, true, GeneralHelper.getString(R.string.ok),"");
    }

    @Override
    public void onSuccesOnboardEmas(ReceiptRevampResponse pendingResponse) {
        if (pendingResponse.getImmediatelyFlag()) {
            ReceiptGagalAFTActivity.launchIntent(requireActivity(), pendingResponse,true);
        }
        else{
            PendingTabunganActivity.Companion.launchIntent(requireActivity(), pendingResponse,GeneralHelper.getString(R.string.url_buka_tabungan_pending_revamp));
        }
    }

    @Override
    public void onSuccessInboxPattern(ReceiptRevampInboxResponse receiptRevampResponse) {
        if (receiptRevampResponse.getReceiptRevampResponse() != null) {
            if (Boolean.TRUE.equals(receiptRevampResponse.getReceiptRevampResponse().isOnProcess())) {
                ReceiptAbnormalRevampActivity.launchIntentReceipt(getActivity(), receiptRevampResponse.getReceiptRevampResponse(), false, defaultIcon());
            } else {
                ReceiptLifestyleActivity.Companion.launchIntent(getActivity(), receiptRevampResponse.getReceiptRevampResponse(), receiptRevampResponse.getReceiptRevampResponse().getReceiptPatternCode(), "");
            }
        }
    }

    @Override
    public void onSuccessReceiptTicketEvent(ReceiptRevampInboxResponse receiptRevampResponse) {
        ReceiptLifestyleActivity.Companion.launchIntentIsFromConfirmation(requireActivity(), receiptRevampResponse.getReceiptRevampResponse(), receiptRevampResponse.getReceiptRevampResponse().getReceiptPatternCode(), "", false);
    }

    @Override
    public void onAbnormalReceipt(ReceiptRevampInboxResponse receiptRevampResponse) {
        ReceiptAbnormalRevampActivity.launchIntentReceipt(requireActivity(), receiptRevampResponse.getReceiptRevampResponse(), false, defaultIcon());
    }

    public void validationButton(boolean isEnable) {
        if (isEnable) {
            binding.ivInbox.setEnabled(true);
            binding.ivInbox.setAlpha(1f);
        } else {
            binding.ivInbox.setEnabled(false);
            binding.ivInbox.setAlpha(0.3f);
        }
    }

    /**
     * @param inboxResponse Inbox Berhasil
     */
    @Override
    public void onSuccessGetInbox(InboxResponse inboxResponse, boolean isRefresh) {
        if (inboxResponse != null) {
            if (isRefresh) {
                if (activityLists != null) {
                    activityLists.clear();
                }
            }
            if (binding.swipeRefresh != null) {
                binding.swipeRefresh.setRefreshing(false);
            }
            if (skeletonScreenInbox != null) {
                skeletonScreenInbox.hide();
            }

            try {
                if (activityLists != null) {
                    validationButton(true);
                    binding.llDesc12.setVisibility(View.GONE);
                    binding.noDataSaved.setVisibility(View.GONE);
                    binding.rvItemInbox.setVisibility(View.VISIBLE);

                    binding.rvItemInbox.smoothScrollToPosition(activityLists.size());
                    activityLists.addAll(inboxResponse.getActivityLists());
                    listInboxAdapter.setItems(activityLists);
                    listInboxAdapter.notifyDataSetChanged();
                    checkRefNumNotif();
                }
            } catch (Exception e) {

            }

            isLoading = false;
        }
    }

    /**
     * @param message data load inbox sudah habis
     */
    @SuppressLint("UseRequireInsteadOfGet")
    @Override
    public void onInboxEnd(String message) {
        if (activityLists.size() == 0) {
            if (binding.swipeRefresh != null) {
                binding.swipeRefresh.setRefreshing(false);
            }
            validationButton(true);
            binding.noDataSaved.setVisibility(View.VISIBLE);
            binding.rvItemInbox.setVisibility(View.GONE);
            binding.llDesc12.setVisibility(View.GONE);
        } else {
//            GeneralHelper.showSnackBar(Objects.requireNonNull(getActivity()).findViewById(R.id.contentInbox), GeneralHelper.getString(R.string.no_inbox_data));
        }
    }

    /**
     * @param message Gagal Mengambil Data
     */
    @SuppressLint("UseRequireInsteadOfGet")
    @Override
    public void onException(String message) {
        if (binding.swipeRefresh != null) {
            binding.swipeRefresh.setRefreshing(false);
        }
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showBottomDialog(getActivity(), message);
        else
            GeneralHelper.showSnackBar(Objects.requireNonNull(getActivity()).findViewById(R.id.contentInbox), message);
    }

    /**
     * @param message
     */
    @Override
    public void onSessionEnd(String message) {
        if (binding.swipeRefresh != null) {
            binding.swipeRefresh.setRefreshing(false);
        }

        ((DashboardIBActivity) getActivity()).onSessionEndToLogin(message);
        inboxPresenter.stop();
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        switch (id) {
            case R.id.iv_inbox:
                inboxPresenter.getFilterInbox();
                break;
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == Constant.REQ_FILTER_INBOX) {
            if (data != null) {
                periode = data.getStringExtra("PERIODE");
                status = data.getStringExtra("STATUS");
                fitur = data.getStringExtra("FITUR");
                subFitur = data.getStringExtra("SUBFITUR");
                lastId = data.getStringExtra("LASTID");

                if (activityLists != null) {
                    activityLists.clear();
                }

                if (skeletonScreenInbox != null) {
                    skeletonScreenInbox.show();
                }
                inboxPresenter.getInbox(periode, status, fitur, subFitur, lastId, true);
            }
        }

        if (requestCode == Constant.REQ_FILTER_INBOX) {
            if (data != null) {

            }
        }
    }

    /**
     * @param position
     */
    @Override
    public void onClickDetail(int position) {
        inboxPresenter.setTrxType(activityLists.get(position).getTrxType());
        inboxPresenter.getInboxDetail(activityLists.get(position).getReferenceNumber());
    }

    /**
     * Merefresh Data inbox
     */
    @Override
    public void onRefresh() {
        if (skeletonScreenInbox != null) {
            binding.rvItemInbox.setVisibility(View.VISIBLE);
            skeletonScreenInbox.show();
            binding.llDesc12.setVisibility(View.GONE);
            binding.noDataSaved.setVisibility(View.GONE);
        }
        inboxPresenter.getInbox(periode, status, fitur, subFitur, "0", true);
    }

    /**
     * @param position mengambil posisi id per inbox
     */
    @Override
    public void onSelectedItemId(int position) {
        if (position == activityLists.size() - 1) {
            lastId = activityLists.get(position).getId();
        }
    }

    /**
     * Call service
     */
    public void callService() {
        if (inboxPresenter != null) {
            periode = "";
            status = "";
            fitur = "";
            subFitur = "";
            lastId = "0";

            if (skeletonScreenInbox != null) {
                skeletonScreenInbox.show();
            }

            inboxPresenter.setUrlInbox(GeneralHelper.getString(R.string.url_activity_list));
            inboxPresenter.setUrlDetailInbox(GeneralHelper.getString(R.string.url_activity_detail));
            inboxPresenter.getInbox(periode, status, fitur, subFitur, lastId, true);
        }
    }

    @Override
    public void onException06(ExceptionResponse response) {
        GeneralHelper.showDialogCustom(getActivity(), response);

        if (binding.swipeRefresh != null) {
            binding.swipeRefresh.setRefreshing(false);
        }

        validationButton(false);
        binding.rvItemInbox.setVisibility(View.GONE);
        binding.llDesc12.setVisibility(View.VISIBLE);

        GeneralHelper.loadIconTransaction(getActivity(), response.getImagePath(), response.getImageName(), binding.ivImage12, GeneralHelper.getImageId(getActivity(), "bri"));
        binding.tvTitle12.setText(response.getTitle());
        binding.tvDesc12.setText(response.getDescription());
    }
}