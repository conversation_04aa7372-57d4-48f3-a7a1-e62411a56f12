package id.co.bri.brimo.domain.helpers.calendar

import java.text.SimpleDateFormat
import java.util.Locale

class CalendarHelperKt {

    companion object {
        const val DATE_PATTERN_yyyy_MM_dd = "yyyyMMdd"
        const val DATE_PATTERN_MMMM_yyyy = "MMMM yyyy"
        const val DATE_PATTERN_dd_MMM_yyyy = "dd MMM yyyy"
        const val DATE_PATTERN_STRIP_yyyy_MM_dd = "yyyy-MM-dd"
        const val DATE_PATTERN_MMMM = "MMMM"
        const val DATE_PATTERN_MM = "MM"
        const val ENGLISH_LOCALE = "English"
        const val INDONESIA_LOCALE = "Indonesia"
    }

    fun formatDate(
        dateStr: String,
        dateTimeFormatInput: String,
        dateTimeFormatOutput: String,
        localeInput: Locale = Locale.getDefault(),
        localeOutput: Locale = Locale.getDefault()
    ): String {
        val inputFormat = SimpleDateFormat(dateTimeFormatInput, localeInput)
        val outputFormat = SimpleDateFormat(dateTimeFormatOutput, localeOutput)
        val date = inputFormat.parse(dateStr)
        return if (date != null) {
            outputFormat.format(date)
        } else {
            outputFormat.format(CalendarHelper.convertToStringFormat(CalendarHelper.getFullDateNow()))
        }
    }
}