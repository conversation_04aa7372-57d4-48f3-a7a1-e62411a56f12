package id.co.bri.brimo.ui.activities.base

import android.app.Activity
import android.app.AlertDialog
import android.app.KeyguardManager
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkRequest
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.widget.AppCompatButton
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.lifecycle.lifecycleScope
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IView.base.INewSkinBaseView
import id.co.bri.brimo.domain.SnackBarType
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.domain.helpers.biometric.BiometricUtility
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.util.SecurityDetector
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

open class NewSkinBaseActivity : BaseActivity(), INewSkinBaseView {
    open fun isScreenshotDisabled(): Boolean = false

    // Idle checker attributes
    private var idleTimeJob: Job? = null
    private var currentIdleTime = 0L
    private var isCurrentIdle = false

    private lateinit var connectivityManager: ConnectivityManager
    private lateinit var networkCallback: ConnectivityManager.NetworkCallback
    private var internetLostDialog: BottomSheetDialogFragment? = null

    companion object {
        private const val MAX_IDLE_TIME = 300000L
        private const val IDLE_CHECK_INTERVAL = 1000L

        const val KEY_BLOCK_BACK_PRESS = "block_back_press"
    }

    private var isSecurityWarningVisible = false
    private lateinit var screenshotDetector: SecurityDetector
    private var securityRootView: View? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val keyguardManager = getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
        val isDeviceLocked = keyguardManager.isDeviceLocked

        if (!isDeviceLocked) {
            // User sudah unlock, aman reset
            BiometricUtility.resetFingerprintLockout()
        }

        if (isScreenshotDisabled()) {
            window.setFlags(
                WindowManager.LayoutParams.FLAG_SECURE,
                WindowManager.LayoutParams.FLAG_SECURE
            )
        }

        val blockBackPress = intent?.getBooleanExtra(KEY_BLOCK_BACK_PRESS, false) == true
        if (blockBackPress) setBlockBackNavigation()

        connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                runOnUiThread {
                    internetLostDialog?.dismiss()
                    internetLostDialog = null
                }
            }

            override fun onLost(network: Network) {
                super.onLost(network)
                runOnUiThread {
                    if (isFinishing || isDestroyed) return@runOnUiThread
                    onInternetLost()
                }
            }
        }
    }

    private fun startIdleTimeChecker() {
        currentIdleTime = 0
        idleTimeJob?.cancel()
        idleTimeJob = lifecycleScope.launch {
            while (true) {
                delay(IDLE_CHECK_INTERVAL)
                currentIdleTime += IDLE_CHECK_INTERVAL
                if (currentIdleTime >= MAX_IDLE_TIME) {
                    onIdleWarning()
                    isCurrentIdle = true
                    break
                }
            }
        }
    }

    private fun stopIdleTimeChecker() {
        idleTimeJob?.cancel()
        idleTimeJob = null
    }

    private fun registerNetworkCallback() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            connectivityManager.registerDefaultNetworkCallback(networkCallback)
        } else {
            val request = NetworkRequest.Builder().build()
            connectivityManager.registerNetworkCallback(request, networkCallback)
        }
    }

    override fun onStart() {
        super.onStart()
        registerNetworkCallback()
    }

    override fun onStop() {
        super.onStop()
        runCatching {
            connectivityManager.unregisterNetworkCallback(networkCallback)
        }
    }

    open fun setupSecurityDetection(rootView: View) {
        this.securityRootView = rootView
        screenshotDetector = SecurityDetector(
            applicationContext,
            onScreenshotTaken = { showSecurityWarning() },
            onScreenRecordingDetected = { showSecurityWarning() }
        )
    }

    private fun showSecurityWarning() {
        if (!isSecurityWarningVisible && securityRootView != null) {
            isSecurityWarningVisible = true
            runOnUiThread {
                GeneralHelperNewSkin.showCustomSnackBar(
                    securityRootView!!,
                    "Aksi ini dibatasi demi keamanan pengguna kami.",
                    SnackBarType.ERROR
                )
                Handler(Looper.getMainLooper()).postDelayed({
                    isSecurityWarningVisible = false
                }, 2000)
            }
        }
    }

    fun showSnackbar(message: String, type: Int) {
        GeneralHelperNewSkin.showSnackBar(findViewById(android.R.id.content),
            message = message, messageType = type
        )
    }

    override fun onResume() {
        super.onResume()
        if (::screenshotDetector.isInitialized) {
            screenshotDetector.start()
        }
        BiometricUtility.tryResetIfFingerprintAvailable(this)
        startIdleTimeChecker()
    }



    override fun onPause() {
        super.onPause()
        if (::screenshotDetector.isInitialized) {
            screenshotDetector.stop()
        }
        stopIdleTimeChecker()
    }

    open fun setBlockBackNavigation() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {

            }
        })
    }

    override fun onException(message: String) {
        GeneralHelperNewSkin.triggerVibration(this, Constant.VIBRATE_ERROR)
        if (GeneralHelperNewSkin.isContains(Constant.LIST_TYPE_GAGAL_GANGGUAN_SISTEM, message)) {
            GeneralHelperNewSkin.showErrorBottomDialog(this, message)
        } else if (message == Constant.AKUN_TERBLOKIR_SALAH_PIN) {
            GeneralHelperNewSkin.showErrorBlokir(this)
        } else {
            GeneralHelperNewSkin.showGeneralErrorBottomDialog(this)
        }
    }

    fun updateButtonState(isEnabled: Boolean, button: AppCompatButton) {
        button.isEnabled = isEnabled
    }

    override fun showSnackbarErrorMessage(
        message: String?,
        messageType: Int,
        activity: Activity?,
        isFragment: Boolean
    ) {
        try {
            // Try to find R.id.content first, if not found, use the root view
            val parentView = findViewById<View>(R.id.content) ?: window.decorView.rootView

            // Use the consolidated snackbar function
            GeneralHelperNewSkin.showSnackBar(parentView, message, messageType)
        } catch (e: Exception) {
            //do nothing
        }
    }

    fun isSafeFragment(): Boolean {
        return !isFinishing && !isDestroyed
    }

    fun setStatusBarAppearance(
        isLightIcons: Boolean
    ) {
        val window = this.window
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        windowInsetsController?.isAppearanceLightStatusBars = isLightIcons
    }

    override fun onIdleWarning() {
//        stopIdleTimeChecker()
//        GeneralHelperNewSkin.showErrorBottomDialog(this, Constant.IDLE_WARNING, onDismiss = {
//            startIdleTimeChecker()
//        })
    }

    override fun showProgress() {
        GeneralHelperNewSkin.showBlur(this)
        GeneralHelperNewSkin.showLoadingDialog(this)
    }

    override fun hideProgress() {
        GeneralHelperNewSkin.dismissLoadingDialog()
        GeneralHelperNewSkin.hideBlur(this)
    }

    override fun showAlertPermission(msg: String?) {
        val builder = AlertDialog.Builder(this)
        builder.setMessage(msg)
            .setPositiveButton(GeneralHelper.getString(R.string.ok)) { _, _ ->
                val intent =
                    Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                val uri = Uri.fromParts("package", packageName, null)
                intent.setData(uri)
                startActivity(intent)
            }
            .setNegativeButton(
                GeneralHelper.getString(R.string.batal2)
            ) { dialog: DialogInterface, _ ->
                dialog.dismiss()
            }
            .setCancelable(false)
            .show()
    }

    open fun onInternetLost() {
        if (supportFragmentManager.isStateSaved) return  // Cegah crash
        if (internetLostDialog?.isAdded == true) return // Dialog sudah tampil

        internetLostDialog = OpenBottomSheetGeneralNewSkinFragment.showDialogConnectivity(
            fragmentManager = supportFragmentManager,
            imgPath = "",
            imgName = "ic_blocked_illustrator",
            titleTxt = "Akses Internetmu Hilang",
            subTitleTxt = GeneralHelper.getString(R.string.desc_koneksi_terputus),
            btnFirstFunction = {
                GeneralHelperNewSkin.openManageCellular(this@NewSkinBaseActivity)
            },
            isClickableOutside = false,
            firstBtnTxt = "Cek Koneksi",
            showCloseButton = false,
            isDismissOnFirstClick = false
        )
    }

    fun showDialogDummyShowCase(
        context: Context,
        title: String,
        message: String,
        positiveText: String,
        neutralText: String,
        negativeText: String,
        onPositive: () -> Unit,
        onNeutral: () -> Unit,
        onNegative: () -> Unit
    ) {
        AlertDialog.Builder(context)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton(positiveText) { dialog, _ ->
                onPositive()
                dialog.dismiss()
            }
            .setNeutralButton(neutralText) { dialog, _ ->
                onNeutral()
                dialog.dismiss()
            }
            .setNegativeButton(negativeText) { dialog, _ ->
                onNegative()
                dialog.dismiss()
            }
            .show()
    }

    protected fun setStatusBarDarkIcons() {
        with(window) {
            setFlags(
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            )
        }
        WindowInsetsControllerCompat(window, window.decorView).isAppearanceLightStatusBars = true
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        currentIdleTime = 0L
        isCurrentIdle = false
        return super.dispatchTouchEvent(event)
    }

    protected fun adjustViewOnKeyboardVisibility(targetView: View, rootView: View) {
        rootView.viewTreeObserver.addOnGlobalLayoutListener {
            val rect = android.graphics.Rect()
            rootView.getWindowVisibleDisplayFrame(rect)
            val screenHeight = rootView.rootView.height
            val keypadHeight = screenHeight - rect.bottom

            if (keypadHeight > screenHeight * 0.15) {
                targetView.translationY = -keypadHeight.toFloat()
            } else {
                targetView.translationY = 0f
            }
        }
    }

}