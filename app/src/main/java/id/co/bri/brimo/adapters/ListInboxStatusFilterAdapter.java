package id.co.bri.brimo.adapters;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ItemSegmentFilterBinding;
import id.co.bri.brimo.databinding.ItemSelectChipBinding;
import id.co.bri.brimo.models.InboxStatusModel;
import id.co.bri.brimo.models.apimodel.response.PeriodeResponse;

import java.util.List;

public class ListInboxStatusFilterAdapter extends RecyclerView.Adapter<ListInboxStatusFilterAdapter.ViewHolder> {

    protected List<InboxStatusModel> statusFilterList;
    protected Context context;
    protected ClickItem clickListener;
    private int selectedPosition = -1;

    public ListInboxStatusFilterAdapter(List<InboxStatusModel> statusFilterList, Context context, ClickItem clickListener) {
        this.statusFilterList = statusFilterList;
        this.context = context;
        this.clickListener = clickListener;

        // Set default value
        for (int i = 0; i < statusFilterList.size(); i++) {
            if ("ALL".equalsIgnoreCase(statusFilterList.get(i).getCode())) {
                statusFilterList.get(i).setSelected(true);
                selectedPosition = i;
                break;
            }
        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(ItemSelectChipBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, @SuppressLint("RecyclerView") int position) {
        if (statusFilterList.get(position).isSelected()) {
            holder.binding.tvChip.setTextColor(ContextCompat.getColor(context, R.color.primary_ns_600));
            holder.binding.viewConnect.setBackgroundTintList(ContextCompat.getColorStateList(context, R.color.primary_ns_100));
        } else {
            holder.binding.tvChip.setTextColor(ContextCompat.getColor(context, R.color.black_ns_main));
            holder.binding.viewConnect.setBackgroundTintList(ContextCompat.getColorStateList(context, R.color.black_ns_100));
        }
        holder.binding.tvChip.setText(statusFilterList.get(position).getName());
        holder.binding.viewConnect.setOnClickListener(view -> {
            if (selectedPosition != -1 && selectedPosition != position) {
                statusFilterList.get(selectedPosition).setSelected(false);
            }
            statusFilterList.get(position).setSelected(true);
            selectedPosition = position;
            clickListener.itemClickedStatus(statusFilterList.get(position));
        });
    }

    @Override
    public int getItemCount() {
        return (statusFilterList != null) ? statusFilterList.size() : 0;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ItemSelectChipBinding binding;

        public ViewHolder(@NonNull ItemSelectChipBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

    public interface ClickItem {
        void itemClickedStatus(InboxStatusModel inboxStatusModel);
    }

    @SuppressLint("NotifyDataSetChanged")
    public void setSelectedStatus(String code) {
        for (int i = 0; i < statusFilterList.size(); i++) {
            InboxStatusModel item = statusFilterList.get(i);
            if (item.getCode().equalsIgnoreCase(code)) {
                item.setSelected(true);
                selectedPosition = i;
            } else {
                item.setSelected(false);
            }
        }
        notifyDataSetChanged();
    }
}