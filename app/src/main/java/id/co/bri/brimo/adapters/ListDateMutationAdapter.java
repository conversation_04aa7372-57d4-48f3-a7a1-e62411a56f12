package id.co.bri.brimo.adapters;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import java.util.ArrayList;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.databinding.ItemDateDownloadMutationBinding;
import id.co.bri.brimo.models.EStatementDataModel;
import id.co.bri.brimo.models.EStatementListModel;

public class ListDateMutationAdapter extends RecyclerView.Adapter<ListDateMutationAdapter.MyViewHolder> {

    private ListDownloadAdapter listDownloadAdapter;
    private RecyclerView.LayoutManager layoutManager;
    private final Activity activity;
    private ArrayList<EStatementDataModel> eStatementDataModels;
    private ArrayList<EStatementListModel> eStatementListModels;
    private ListDownloadAdapter.DownloadInterface downloadListener;


    public ListDateMutationAdapter(ArrayList<EStatementDataModel> mEStatementDataModels, ListDownloadAdapter listDownloadAdapter,
                                   Activity activity, ListDownloadAdapter.DownloadInterface mDownloadListener) {
        this.eStatementDataModels = mEStatementDataModels;
        this.listDownloadAdapter = listDownloadAdapter;
        this.activity = activity;
        this.downloadListener = mDownloadListener;
    }

    @NonNull
    @Override
    public ListDateMutationAdapter.MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new MyViewHolder(ItemDateDownloadMutationBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ListDateMutationAdapter.MyViewHolder holder, int position) {
        holder.binding.tvDate.setText(eStatementDataModels.get(position).getYear());
        eStatementListModels = eStatementDataModels.get(position).geteStatementListModels();

        holder.binding.rvItemDownload.setHasFixedSize(true);
        layoutManager = new LinearLayoutManager(holder.itemView.getContext());
        holder.binding.rvItemDownload.setLayoutManager(layoutManager);

        listDownloadAdapter = new ListDownloadAdapter(activity, eStatementListModels, downloadListener);
        holder.binding.rvItemDownload.setAdapter(listDownloadAdapter);
        holder.binding.rvItemDownload.scrollToPosition(listDownloadAdapter.getItemCount());

        if (position == eStatementDataModels.size() - 1) {
            holder.binding.rvItemDownload.setPadding(0, 0, 0, 28);
        }
    }

    @Override
    public int getItemCount() {
        return eStatementDataModels.size();
    }

    public static class MyViewHolder extends RecyclerView.ViewHolder {
        ItemDateDownloadMutationBinding binding;

        public MyViewHolder(ItemDateDownloadMutationBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }


}