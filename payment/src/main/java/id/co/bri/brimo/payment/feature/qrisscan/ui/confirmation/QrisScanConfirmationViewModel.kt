package id.co.bri.brimo.payment.feature.qrisscan.ui.confirmation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.google.gson.Gson
import id.co.bri.brimo.payment.app.QrisScanConfirmationRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qrisscan.QrisScanPaymentRequest
import id.co.bri.brimo.payment.core.network.response.qrisscan.QrisScanPaymentResponse
import id.co.bri.brimo.payment.feature.qrisscan.data.api.QrisScanRepository
import id.co.bri.brimo.payment.feature.qrisscan.data.model.QrisScanModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class QrisScanConfirmationViewModel(
    private val qrisScanRepository: QrisScanRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val qrisScanConfirmationRoute = savedStateHandle.toRoute<QrisScanConfirmationRoute>()

    val qrisScanModel = runCatching {
        Gson().fromJson(qrisScanConfirmationRoute.data, QrisScanModel::class.java)
    }.getOrNull()

    private val _accountList = MutableStateFlow(qrisScanModel?.qrisScan?.accountList)
    val accountList = _accountList.asStateFlow()

    fun handleEvent(event: QrisScanConfirmationEvent) {
        when (event) {
            is QrisScanConfirmationEvent.Payment -> {
                postQrisScanPay(
                    pin = event.pin,
                    accountNumber = event.accountNumber,
                    note = event.note
                )
            }

            is QrisScanConfirmationEvent.RefreshSaldo -> {
                refreshSaldo(account = event.account)
            }
        }
    }

    private val _qrisScanPayment = MutableSharedFlow<UiState<QrisScanPaymentResponse>>()
    val qrisScanPayment = _qrisScanPayment.asSharedFlow()

    private fun postQrisScanPay(
        pin: String,
        accountNumber: String,
        note: String
    ) {
        viewModelScope.launch {
            _qrisScanPayment.asUiState {
                val request = if (qrisScanModel?.qrisScan != null) {
                    QrisScanPaymentRequest(
                        accountNumber = accountNumber,
                        note = note,
                        pfmCategory = qrisScanModel.qrisScanConfirmation?.pfmCategory,
                        pin = pin,
                        referenceNumber = qrisScanModel.qrisScanConfirmation?.referenceNumber,
                    )
                } else {
                    QrisScanPaymentRequest(
                        accountNumber = qrisScanModel?.accountNumber,
                        note = qrisScanModel?.qrisScanConfirmation?.note,
                        pfmCategory = qrisScanModel?.qrisScanConfirmation?.pfmCategory,
                        pin = pin,
                        referenceNumber = qrisScanModel?.qrisScanConfirmation?.referenceNumber,
                    )
                }
                qrisScanRepository.postQrisScanPay(
                    request = request,
                    fastMenu = qrisScanConfirmationRoute.fastMenu
                )
            }
        }
    }

    private fun refreshSaldo(account: String) {
        viewModelScope.launch {
            updateAccountList(account, true)
            try {
                val saldoNormal = qrisScanRepository.postSaldoNormal(
                    request = SaldoNormalRequest(
                        account = account
                    )
                )
                val accountListUpdated = accountList.value?.map { item ->
                    if (item.account == account) {
                        item.copy(
                            onHold = saldoNormal.onHold,
                            balance = saldoNormal.balance,
                            balanceString = saldoNormal.balanceString,
                            loading = false
                        )
                    } else {
                        item
                    }
                }
                _accountList.update { accountListUpdated }
            } catch (_: Throwable) {
                updateAccountList(account, false)
            }
        }
    }

    private fun updateAccountList(account: String, loading: Boolean) {
        val accountListUpdated = accountList.value?.map { item ->
            if (item.account == account) {
                item.copy(
                    loading = loading
                )
            } else {
                item
            }
        }
        _accountList.update { accountListUpdated }
    }
}
