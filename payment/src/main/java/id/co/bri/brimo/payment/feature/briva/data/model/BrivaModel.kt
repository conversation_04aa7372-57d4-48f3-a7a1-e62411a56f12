package id.co.bri.brimo.payment.feature.briva.data.model

import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.payment.core.network.response.briva.BrivaConfirmationResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaInquiryResponse

internal data class BrivaModel(
    @SerializedName("brivaInquiry") val brivaInquiry: BrivaInquiryResponse? = null,
    @SerializedName("brivaConfirmation") val brivaConfirmation: BrivaConfirmationResponse? = null,
    @SerializedName("accountNumber") val accountNumber: String? = null
)
