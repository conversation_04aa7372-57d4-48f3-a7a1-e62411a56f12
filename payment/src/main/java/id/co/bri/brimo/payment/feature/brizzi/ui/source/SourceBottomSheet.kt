package id.co.bri.brimo.payment.feature.brizzi.ui.source

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.Color_E6EEFF
import id.co.bri.brimo.payment.core.design.theme.Color_E84040
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.response.BrizziSaldoNormalResponse

@Composable
internal fun SourceBottomSheet(
    nominal: String,
    data: List<BrizziSaldoNormalResponse>,
    amount: Int? = 10000,
    onSelect: (BrizziSaldoNormalResponse) -> Unit = {},
    onClose: () -> Unit = {},
    isFastMenu: Boolean = false
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Ganti Sumber Dana",
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 40.dp),
                fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodyLarge
            )

            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = null,
                modifier = Modifier
                    .background(Color_F5F7FB, CircleShape)
                    .clickable {
                        onClose()
                    }
                    .padding(8.dp),
                tint = Color.Black
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        data.forEachIndexed { index, item ->
            val borderColor = if (index == 0) Color_0054F3 else Color.Transparent
            val enabled = !(item.onHold ?: false)
            val alpha = if (enabled) 1f else 0.5f

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .clickable(enabled = enabled) {
                        onSelect(item)
                    }
                    .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                    .border(1.dp, borderColor, RoundedCornerShape(16.dp))
                    .alpha(alpha)
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                AsyncImage(
                    model = item.imagePath.orEmpty(),
                    contentDescription = null,
                    modifier = Modifier
                        .width(58.dp)
                        .height(36.dp),
                    placeholder = painterResource(id = R.drawable.thumbnail),
                    error = painterResource(id = R.drawable.thumbnail),
                    contentScale = ContentScale.Crop
                )

                Spacer(modifier = Modifier.width(16.dp))

                Column(modifier = Modifier.weight(1f)) {
                    Row(modifier = Modifier.fillMaxWidth()) {
                        Text(
                            text = item.name.orEmpty(),
                            style = MaterialTheme.typography.bodySmall
                        )

                        Spacer(modifier = Modifier.weight(1f))

                        if (index == 0) {
                            Text(
                                text = "UTAMA",
                                modifier = Modifier
                                    .background(Color_E6EEFF, RoundedCornerShape(16.dp))
                                    .padding(horizontal = 8.dp, vertical = 2.dp),
                                color = Color_0054F3,
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.labelSmall
                            )
                        }
                    }

                    Text(
                        text = item.accountString.orEmpty(),
                        modifier = Modifier.fillMaxWidth(),
                        style = MaterialTheme.typography.bodySmall
                    )

                    if (!isFastMenu) {
                        Spacer(modifier = Modifier.height(4.dp))

                        val notEnough =
                            (item.balance?.toDoubleOrNull() ?: 0.0) < (nominal.toDoubleOrNull() ?: 0.0)
                        val textColor = if (notEnough) {
                            Color_E84040
                        } else {
                            Color.Black
                        }

                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Text(
                                text = "${item.currency}${item.balanceString}",
                                color = textColor,
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.bodyMedium
                            )

                            if (notEnough) {
                                Spacer(modifier = Modifier.width(8.dp))

                                Text(
                                    text = "Saldo tidak cukup",
                                    color = Color_E84040,
                                    style = MaterialTheme.typography.bodySmall
                                )
                            }
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@Preview
@Composable
fun PreviewSourceBottomSheet() {
    MainTheme {
        SourceBottomSheet(
            nominal = "10000",
            data = listOf(
                BrizziSaldoNormalResponse(
                    account = "***************",
                    accountString = "0230 0100 1674 308",
                    name = "Infinite",
                    currency = "Rp",
                    onHold = false,
                    balance = "*********.69",
                    balanceString = "696.961.830,69",
                    minimumBalance = "0",
                    imagePath = "https://example.com/card1.png"
                ),
                BrizziSaldoNormalResponse(
                    account = "***************",
                    accountString = "0230 0100 1674 309",
                    name = "Premium",
                    currency = "Rp",
                    onHold = false,
                    balance = "150000.00",
                    balanceString = "150.000,00",
                    minimumBalance = "0",
                    imagePath = "https://example.com/card2.png"
                ),
                BrizziSaldoNormalResponse(
                    account = "***************",
                    accountString = "0230 0100 1674 310",
                    name = "Basic",
                    currency = "Rp",
                    onHold = true,
                    balance = "50000.00",
                    balanceString = "50.000,00",
                    minimumBalance = "0",
                    imagePath = "https://example.com/card3.png"
                )
            ),
            amount = 10000,
            onSelect = {},
            onClose = {},
            isFastMenu = false
        )
    }
}

@Preview(name = "Fast Menu Preview")
@Composable
fun PreviewSourceBottomSheetFastMenu() {
    MainTheme {
        SourceBottomSheet(
            nominal = "50000",
            data = listOf(
                BrizziSaldoNormalResponse(
                    account = "***************",
                    accountString = "0230 0100 1674 308",
                    name = "Infinite",
                    currency = "Rp",
                    onHold = false,
                    balance = "*********.69",
                    balanceString = "696.961.830,69",
                    minimumBalance = "0",
                    imagePath = "https://example.com/card1.png"
                ),
                BrizziSaldoNormalResponse(
                    account = "***************",
                    accountString = "0230 0100 1674 309",
                    name = "Premium",
                    currency = "Rp",
                    onHold = false,
                    balance = "150000.00",
                    balanceString = "150.000,00",
                    minimumBalance = "0",
                    imagePath = "https://example.com/card2.png"
                )
            ),
            amount = 50000,
            onSelect = {},
            onClose = {},
            isFastMenu = true
        )
    }
}
