package id.co.bri.brimo.payment.core.network.response.qrshow

import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse

internal data class QrCpmFormResponse(
    @SerializedName("account_list") val accountList: List<AccountResponse>?,
    @SerializedName("reference_number") val referenceNumber: String?,
    @SerializedName("string_qr") val stringQr: String?,
    @SerializedName("expire_time") val expireTime: Int?
)
