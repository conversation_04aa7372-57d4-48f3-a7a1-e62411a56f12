package id.co.bri.brimo.payment.feature.briva.ui.base

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.component.Shimmer
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.Color_E6EEFF
import id.co.bri.brimo.payment.core.design.theme.Color_E84040
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse

@Composable
internal fun BrivaAccountBottomSheet(
    nominal: String,
    data: List<AccountResponse>,
    onSelect: (AccountResponse) -> Unit = {},
    onRefresh: (AccountResponse) -> Unit = {},
    onClose: () -> Unit = {},
    fastMenu: Boolean = false
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Ganti Sumber Dana",
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 40.dp),
                fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodyLarge
            )

            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = null,
                modifier = Modifier
                    .background(Color_F5F7FB, CircleShape)
                    .clickable {
                        onClose()
                    }
                    .padding(8.dp),
                tint = Color.Black
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        data.forEachIndexed { index, item ->
            val isMain = index == 0
            val borderColor = if (isMain) Color_0054F3 else Color.Transparent
            val enabled = !(item.onHold ?: false)
            val alpha = if (enabled) 1f else 0.5f

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .clickable(enabled = enabled) {
                        onSelect(item)
                    }
                    .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                    .border(1.dp, borderColor, RoundedCornerShape(16.dp))
                    .alpha(alpha)
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                AsyncImage(
                    model = item.imagePath.orEmpty(),
                    contentDescription = null,
                    modifier = Modifier
                        .width(58.dp)
                        .height(36.dp),
                    placeholder = painterResource(id = R.drawable.thumbnail),
                    error = painterResource(id = R.drawable.thumbnail),
                    contentScale = ContentScale.Inside
                )

                Spacer(modifier = Modifier.width(16.dp))

                Column(modifier = Modifier.weight(1f)) {
                    Row(modifier = Modifier.fillMaxWidth()) {
                        Text(
                            text = item.alias.orEmpty().ifEmpty { item.name.orEmpty() },
                            style = MaterialTheme.typography.bodySmall
                        )

                        Spacer(modifier = Modifier.weight(1f))

                        if (isMain) {
                            Text(
                                text = "UTAMA",
                                modifier = Modifier
                                    .background(Color_E6EEFF, RoundedCornerShape(16.dp))
                                    .padding(horizontal = 8.dp, vertical = 2.dp),
                                color = Color_0054F3,
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.labelSmall
                            )
                        }
                    }

                    Text(
                        text = item.accountString.orEmpty(),
                        modifier = Modifier.fillMaxWidth(),
                        style = MaterialTheme.typography.bodySmall
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    if (!fastMenu) {
                        val balanceError = item.balance == null
                        val notEnough = (item.balance?.toDoubleOrNull() ?: 0.0) <
                            (nominal.toDoubleOrNull() ?: 0.0)
                        val textColor = if (balanceError || notEnough) {
                            Color_E84040
                        } else {
                            Color.Black
                        }
                        val text = if (!enabled) {
                            "Rekening tidak aktif"
                        } else if (balanceError) {
                            "Gagal memuat saldo"
                        } else if (notEnough) {
                            "Saldo tidak cukup"
                        } else {
                            ""
                        }

                        if (item.loading == true) {
                            Shimmer(
                                modifier = Modifier
                                    .width(144.dp)
                                    .height(12.dp)
                            )
                        } else {
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                if (!balanceError) {
                                    Text(
                                        text = "${item.currency}${item.balanceString}",
                                        color = textColor,
                                        fontWeight = FontWeight.SemiBold,
                                        style = MaterialTheme.typography.bodyMedium
                                    )

                                    Spacer(modifier = Modifier.width(8.dp))
                                }

                                if (!enabled || balanceError || notEnough) {
                                    Text(
                                        text = text,
                                        color = Color_E84040,
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                }

                                if (balanceError) {
                                    Spacer(modifier = Modifier.weight(1f))

                                    Image(
                                        painter = painterResource(R.drawable.icon_circle_reload),
                                        contentDescription = null,
                                        modifier = Modifier
                                            .size(24.dp)
                                            .clickable {
                                                onRefresh(item)
                                            },
                                        contentScale = ContentScale.Fit
                                    )
                                }
                            }
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@Preview
@Composable
private fun PreviewBrivaAccountBottomSheet() {
    MainTheme {
        BrivaAccountBottomSheet(
            nominal = "",
            data = listOf(
                AccountResponse(
                    account = "***************",
                    accountString = "0230 0113 7115 507",
                    name = "ADIXXXXXXXXXXXXXXLTI",
                    currency = "Rp",
                    cardNumber = "5221XXXXXXXX7777",
                    cardNumberString = "5221 XXXX XXXX 7777",
                    productType = "BritAma",
                    accountType = "SA",
                    scCode = "TA",
                    default = 0,
                    alias = "",
                    minimumBalance = "50000",
                    limit = "-1",
                    limitString = "",
                    imageName = "",
                    imagePath = "",
                    onHold = false,
                    balance = "9085000",
                    balanceString = "9.085.000,00"
                )
            )
        )
    }
}
