package id.co.bri.brimo.payment.core.design.component

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import kotlinx.coroutines.DisposableHandle
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun BottomSheet(
    showBottomSheet: Boolean,
    onShowBottomSheet: (Boolean) -> Unit,
    onDismiss: () -> Unit = {},
    content: @Composable ColumnScope.((() -> Unit) -> DisposableHandle) -> Unit
) {
    val coroutineScope = rememberCoroutineScope()
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    val dismiss = { action: () -> Unit ->
        coroutineScope.launch { sheetState.hide() }.invokeOnCompletion {
            if (!sheetState.isVisible) {
                onShowBottomSheet(false)
                action()
            }
        }
    }

    if (showBottomSheet) {
        ModalBottomSheet(
            onDismissRequest = {
                onShowBottomSheet(false)
                onDismiss()
            },
            sheetState = sheetState,
            containerColor = Color.White,
            dragHandle = { DragHandle() }
        ) {
            content(dismiss)
        }
    }
}


@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun BasicBottomSheet(
    data: List<String>,
    showBottomSheet: Boolean,
    onShowBottomSheet: (Boolean) -> Unit,
    onSelect: (String) -> Unit = {},
    onDismiss: () -> Unit = {},
    enableSearch: Boolean = false
) {
    var searchField by rememberSaveable { mutableStateOf("") }

    val filteredData = remember(searchField, data) {
        if (searchField.isEmpty()) {
            data
        } else {
            val query = searchField.trim().lowercase()
            data.filter { it.lowercase().contains(query) }
        }
    }

    if (showBottomSheet) {
        BottomSheet(
            showBottomSheet = showBottomSheet,
            onShowBottomSheet = onShowBottomSheet,
            onDismiss = onDismiss
        ) { dismiss ->
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.8f)
                    .padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Pilih Item",
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = 40.dp)
                            .padding(horizontal = 12.dp),
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyLarge
                    )

                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = null,
                        modifier = Modifier
                            .background(Color.White, CircleShape)
                            .clickable {
                                dismiss { onDismiss() }
                            }
                            .padding(8.dp),
                        tint = Color.Black
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                if (enableSearch) {
                    OutlinedTextFieldCustom(
                        value = searchField,
                        onValueChange = { searchField = it },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(44.dp),
                        textStyle = MaterialTheme.typography.bodyMedium,
                        placeholder = {
                            Text(
                                text = "Cari",
                                color = Color_7B90A6,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Search,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp),
                            )
                        },
                        trailingIcon = if (searchField.isNotEmpty()) {
                            {
                                Icon(
                                    painter = painterResource(R.drawable.icon_close),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .size(16.dp)
                                        .clickable {
                                            searchField = ""
                                        }
                                )
                            }
                        } else {
                            null
                        },
                        singleLine = true,
                        shape = RoundedCornerShape(24.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedContainerColor = Color_F5F7FB,
                            unfocusedContainerColor = Color_F5F7FB,
                            disabledContainerColor = Color_F5F7FB,
                            errorContainerColor = Color_F5F7FB,
                            focusedBorderColor = Color_0054F3,
                            unfocusedBorderColor = Color_F5F7FB,
                            disabledBorderColor = Color_F5F7FB
                        ),
                        contentPadding = PaddingValues(horizontal = 16.dp)
                    )
                }

                if (filteredData.isEmpty()) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .padding(vertical = 32.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "Hasil Tidak Ditemukan",
                            style = MaterialTheme.typography.titleLarge,
                            color = Color_7B90A6
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "Coba gunakan kata kunci lainnya, ya.",
                            style = MaterialTheme.typography.bodyLarge,
                            color = Color_7B90A6
                        )
                    }
                } else {
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                    ) {
                        items(filteredData) { item ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable {
                                        onSelect(item)
                                        dismiss { onDismiss() }
                                    }
                                    .padding(vertical = 20.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = item,
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.SemiBold,
                                    modifier = Modifier.weight(1f)
                                )
                            }
                            HorizontalDivider()
                        }
                    }
                }
            }
            DisposableHandle { }
        }
    }
}
