package id.co.bri.brimo.payment.feature.briva.ui.nominal

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.google.gson.Gson
import id.co.bri.brimo.payment.app.BrivaNominalRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.briva.BrivaConfirmationRequest
import id.co.bri.brimo.payment.core.network.response.briva.BrivaConfirmationResponse
import id.co.bri.brimo.payment.feature.briva.data.api.BrivaRepository
import id.co.bri.brimo.payment.feature.briva.data.model.BrivaModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class BrivaNominalViewModel(
    private val brivaRepository: BrivaRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val brivaNominalRoute = savedStateHandle.toRoute<BrivaNominalRoute>()

    val brivaModel = runCatching {
        Gson().fromJson(brivaNominalRoute.data, BrivaModel::class.java)
    }.getOrNull()

    private val _accountList = MutableStateFlow(brivaModel?.brivaInquiry?.accountList)
    val accountList = _accountList.asStateFlow()

    fun handleEvent(event: BrivaNominalEvent) {
        when (event) {
            is BrivaNominalEvent.Confirmation -> {
                postBrivaConfirmation(
                    accountNumber = event.accountNumber,
                    amount = event.amount,
                    note = event.note
                )
            }

            is BrivaNominalEvent.RefreshSaldo -> {
                refreshSaldo(account = event.account)
            }
        }
    }

    private val _brivaConfirmation = MutableSharedFlow<UiState<BrivaConfirmationResponse>>()
    val brivaConfirmation = _brivaConfirmation.asSharedFlow()

    private fun postBrivaConfirmation(accountNumber: String, amount: String, note: String) {
        viewModelScope.launch {
            _brivaConfirmation.asUiState {
                brivaRepository.postBrivaConfirmation(
                    request = BrivaConfirmationRequest(
                        accountNumber = accountNumber,
                        amount = amount,
                        note = note,
                        referenceNumber = brivaModel?.brivaInquiry?.referenceNumber.orEmpty(),
                        saveAs = ""
                    ),
                    fastMenu = brivaNominalRoute.fastMenu
                )
            }
        }
    }

    private fun refreshSaldo(account: String) {
        viewModelScope.launch {
            updateAccountList(account, true)
            try {
                val saldoNormal = brivaRepository.postSaldoNormal(
                    request = SaldoNormalRequest(
                        account = account
                    )
                )
                val accountListUpdated = accountList.value?.map { item ->
                    if (item.account == account) {
                        item.copy(
                            onHold = saldoNormal.onHold,
                            balance = saldoNormal.balance,
                            balanceString = saldoNormal.balanceString,
                            loading = false
                        )
                    } else {
                        item
                    }
                }
                _accountList.update { accountListUpdated }
            } catch (_: Throwable) {
                updateAccountList(account, false)
            }
        }
    }

    private fun updateAccountList(account: String, loading: Boolean) {
        val accountListUpdated = accountList.value?.map { item ->
            if (item.account == account) {
                item.copy(
                    loading = loading
                )
            } else {
                item
            }
        }
        _accountList.update { accountListUpdated }
    }
}
