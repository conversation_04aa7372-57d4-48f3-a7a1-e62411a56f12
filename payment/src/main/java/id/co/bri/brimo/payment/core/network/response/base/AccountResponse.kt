package id.co.bri.brimo.payment.core.network.response.base

import com.google.gson.annotations.SerializedName

internal data class AccountResponse(
    @SerializedName("account") val account: String?,
    @SerializedName("account_string") val accountString: String?,
    @SerializedName("name") val name: String?,
    @SerializedName("currency") val currency: String?,
    @SerializedName("card_number") val cardNumber: String?,
    @SerializedName("card_number_string") val cardNumberString: String?,
    @SerializedName("product_type") val productType: String?,
    @SerializedName("account_type") val accountType: String?,
    @SerializedName("sc_code") val scCode: String?,
    @SerializedName("default") val default: Int?,
    @SerializedName("alias") val alias: String?,
    @SerializedName("minimum_balance") val minimumBalance: String?,
    @SerializedName("limit") val limit: String?,
    @SerializedName("limit_string") val limitString: String?,
    @SerializedName("image_name") val imageName: String?,
    @SerializedName("image_path") val imagePath: String?,
    @SerializedName("on_hold") val onHold: Boolean?,
    @SerializedName("balance") val balance: String?,
    @SerializedName("balance_string") val balanceString: String?,
    @SerializedName("loading") val loading: Boolean? = false
)
