package id.co.bri.brimo.payment.feature.briva.ui.nominal

import id.co.bri.brimo.payment.app.BrivaNominalRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaConfirmationResponse
import id.co.bri.brimo.payment.feature.briva.data.model.BrivaModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow

internal data class BrivaNominalState(
    val brivaNominalRoute: BrivaNominalRoute,
    val brivaModel: BrivaModel,
    val accountList: List<AccountResponse> = emptyList(),
    val brivaConfirmation: SharedFlow<UiState<BrivaConfirmationResponse>> = MutableSharedFlow()
)

internal sealed class BrivaNominalEvent {
    data class Confirmation(
        val accountNumber: String,
        val amount: String,
        val note: String
    ) : BrivaNominalEvent()

    data class RefreshSaldo(
        val account: String
    ) : BrivaNominalEvent()
}

internal sealed class BrivaNominalNavigation {
    object Back : BrivaNominalNavigation()
    data class Confirmation(val brivaData: String, val fastMenu: Boolean) : BrivaNominalNavigation()
}
