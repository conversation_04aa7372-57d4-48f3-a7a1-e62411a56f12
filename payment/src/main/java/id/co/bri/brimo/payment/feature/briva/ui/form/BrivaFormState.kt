package id.co.bri.brimo.payment.feature.briva.ui.form

import id.co.bri.brimo.payment.app.BrivaFormRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.base.FavoriteResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaFormResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaInquiryResponse
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow

internal data class BrivaFormState(
    val brivaFormRoute: BrivaFormRoute,
    val brivaForm: BrivaFormResponse,
    val brivaInquiry: SharedFlow<UiState<BrivaInquiryResponse>> = MutableSharedFlow(),
    val pinFavorite: SharedFlow<UiState<Unit>> = MutableSharedFlow(),
    val deleteFavorite: SharedFlow<UiState<Unit>> = MutableSharedFlow(),
    val favorites: List<FavoriteResponse> = listOf()
)

internal sealed class BrivaFormEvent {
    object RefreshBrivaForm : BrivaFormEvent()
    data class GetBrivaData(val number: String) : BrivaFormEvent()
    data class PinFavorite(val savedId: String, val isPin: Boolean) : BrivaFormEvent()
    data class DeleteFavorite(val savedId: String) : BrivaFormEvent()
    data class EditFavorite(val savedId: String, val name: String) : BrivaFormEvent()
}

internal sealed class BrivaFormNavigation {
    object Back : BrivaFormNavigation()
    data class EditFavorite(
        val number: String,
        val name: String,
        val savedId: String,
        val subtitle: String
    ) : BrivaFormNavigation()

    data class Nominal(val brivaData: String, val fastMenu: Boolean) : BrivaFormNavigation()
    data class Confirmation(val brivaData: String, val fastMenu: Boolean) : BrivaFormNavigation()
}
