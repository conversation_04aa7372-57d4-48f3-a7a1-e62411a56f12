package id.co.bri.brimo.payment.core.design.helper

import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3

internal fun highlightedText(
    highlight: String,
    label: String
): AnnotatedString {
    return buildAnnotatedString {
        val input = highlight.trim()
        if (input.isNotEmpty()) {
            val matchIndex = label.indexOf(input, ignoreCase = true)
            if (matchIndex in 0 until label.length) {
                // Before match
                append(label.substring(0, matchIndex))

                // Highlighted match
                withStyle(style = SpanStyle(color = Color_0054F3)) {
                    append(
                        label.substring(
                            matchIndex,
                            matchIndex + input.length.coerceAtMost(label.length - matchIndex)
                        )
                    )
                }

                // After match
                val afterMatchStart = matchIndex + input.length
                if (afterMatchStart < label.length) {
                    append(label.substring(afterMatchStart))
                }
            } else {
                // No match
                append(label)
            }
        } else {
            // Empty input
            append(label)
        }
    }
}
