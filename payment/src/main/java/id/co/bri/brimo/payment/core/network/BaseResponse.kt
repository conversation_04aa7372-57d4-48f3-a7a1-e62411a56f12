package id.co.bri.brimo.payment.core.network

import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken

data class BaseResponse<T>(
    @SerializedName("code") val code: String?,
    @SerializedName("description") val description: String?,
    @SerializedName("data") val data: T?
)

data class ErrorResponse(
    @SerializedName("image_path") val image: String?,
    @SerializedName("title") val title: String?,
    @SerializedName("description") val description: String?
)

class MessageException(
    val code: String,
    val description: String
) : Exception()

class DataException(
    val code: String,
    val image: String,
    val title: String,
    val description: String
) : Exception()

internal fun Throwable.errorSnackbar(): Boolean {
    return this is MessageException && this.code !in listOf("05", "61", "93")
}

internal suspend fun <T> processResponse(action: suspend () -> BaseResponse<T>?): T {
    val response = action()
    return if (response?.code == "00") {
        response.data ?: throw Exception("${response.code}:${response.description}")
    } else {
        throw Exception("${response?.code}:${response?.description}")
    }
}

internal suspend inline fun <reified T> processApi(action: suspend () -> String?): T {
    val response = action()
    val type = object : TypeToken<BaseResponse<T>>() {}.type
    val model = Gson().fromJson<BaseResponse<T>>(response, type)
    return if (model.code == "00" || model.code == "01") {
        model.data ?: throw MessageException(
            code = model.code,
            description = model.description.orEmpty()
        )
    } else {
        val errorType = object : TypeToken<BaseResponse<ErrorResponse>>() {}.type
        val errorModel = Gson().fromJson<BaseResponse<ErrorResponse>>(response, errorType)
        if (errorModel.data != null) {
            throw DataException(
                code = errorModel.code.orEmpty(),
                image = errorModel.data.image.orEmpty(),
                title = errorModel.data.title.orEmpty(),
                description = errorModel.data.description.orEmpty()
            )
        } else {
            throw MessageException(
                code = errorModel.code.orEmpty(),
                description = errorModel.description.orEmpty()
            )
        }
    }
}

internal suspend fun processApiUnit(action: suspend () -> String?) {
    val response = action()
    val type = object : TypeToken<BaseResponse<Any>>() {}.type
    val model = Gson().fromJson<BaseResponse<Any>>(response, type)
    return if (model.code == "00") {
        Unit
    } else {
        val errorType = object : TypeToken<BaseResponse<ErrorResponse>>() {}.type
        val errorModel = Gson().fromJson<BaseResponse<ErrorResponse>>(response, errorType)
        if (errorModel.data != null) {
            throw DataException(
                code = errorModel.code.orEmpty(),
                image = errorModel.data.image.orEmpty(),
                title = errorModel.data.title.orEmpty(),
                description = errorModel.data.description.orEmpty()
            )
        } else {
            throw MessageException(
                code = errorModel.code.orEmpty(),
                description = errorModel.description.orEmpty()
            )
        }
    }
}
