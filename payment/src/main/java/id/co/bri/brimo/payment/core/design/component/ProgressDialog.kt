package id.co.bri.brimo.payment.core.design.component

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.theme.MainTheme

@Composable
internal fun ProgressDialog() {
    val dialogProperties =
        DialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false)
    val loadingComposition by rememberLottieComposition(
        spec = LottieCompositionSpec.RawRes(R.raw.loading_animation)
    )

    Dialog(
        onDismissRequest = {},
        properties = dialogProperties
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            LottieAnimation(
                composition = loadingComposition,
                modifier = Modifier.size(200.dp),
                iterations = LottieConstants.IterateForever
            )
        }
    }
}

@Preview
@Composable
private fun PreviewProgressDialog() {
    MainTheme {
        ProgressDialog()
    }
}
