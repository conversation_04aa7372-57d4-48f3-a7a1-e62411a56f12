package id.co.bri.brimo.payment.feature.briva.ui.favorite

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LocalLifecycleOwner
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.app.BrivaFavoriteRoute
import id.co.bri.brimo.payment.core.common.launchAndCollectIn
import id.co.bri.brimo.payment.core.common.onError
import id.co.bri.brimo.payment.core.common.onLoading
import id.co.bri.brimo.payment.core.common.onSuccess
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.PrimaryButton
import id.co.bri.brimo.payment.core.design.component.ProgressDialog
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.component.TextFieldCustom
import id.co.bri.brimo.payment.core.design.component.textFieldColors
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel

@Composable
internal fun BrivaFavoriteScreen(
    navigation: (BrivaFavoriteNavigation) -> Unit = {},
    brivaFavoriteViewModel: BrivaFavoriteViewModel = koinViewModel()
) {
    BrivaFavoriteContent(
        state = BrivaFavoriteState(
            brivaFavoriteRoute = brivaFavoriteViewModel.brivaFavoriteRoute,
            editFavorite = brivaFavoriteViewModel.editFavorite,
        ),
        event = brivaFavoriteViewModel::handleEvent,
        navigation = navigation
    )
}

@Composable
private fun BrivaFavoriteContent(
    state: BrivaFavoriteState,
    event: (BrivaFavoriteEvent) -> Unit = {},
    navigation: (BrivaFavoriteNavigation) -> Unit = {}
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    val scope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current

    // Global
    var numberField by rememberSaveable { mutableStateOf(state.brivaFavoriteRoute.number) }
    var nameField by rememberSaveable { mutableStateOf(state.brivaFavoriteRoute.name) }
    val enableButton by remember {
        derivedStateOf {
            nameField.isNotEmpty() && nameField != state.brivaFavoriteRoute.name
        }
    }

    // Loading
    var progressDialog by rememberSaveable { mutableStateOf(false) }
    if (progressDialog) ProgressDialog()

    // Snackbar
    val snackbarHostState = remember { SnackbarHostState() }

    // Favorite
    LaunchedEffect(Unit) {
        state.editFavorite.launchAndCollectIn(lifecycleOwner) { event ->
            event
                .onLoading {
                    progressDialog = true
                }
                .onSuccess {
                    progressDialog = false
                    navigation(
                        BrivaFavoriteNavigation.Edit(
                            savedId = state.brivaFavoriteRoute.savedId,
                            name = nameField
                        )
                    )

                }
                .onError { error ->
                    progressDialog = false
                    scope.launch {
                        snackbarHostState.showSnackbar("Gagal menyimpan, silakan coba lagi, ya.")
                    }
                }
        }
    }

    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .imePadding()
            .pointerInput(Unit) {
                detectTapGestures {
                    focusManager.clearFocus()
                }
            },
        bottomBar = {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
            ) {
                DividerHorizontal()

                PrimaryButton(
                    label = "Simpan",
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    enabled = enableButton
                ) {
                    event(BrivaFavoriteEvent.EditFavorite(name = nameField))
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = SnackbarType.ERROR
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .paint(
                        painter = painterResource(R.drawable.form_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .padding(innerPadding)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.icon_back),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clickable {
                                navigation(BrivaFavoriteNavigation.Back)
                            },
                        contentScale = ContentScale.Fit
                    )

                    Text(
                        text = "Ubah Nama",
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 16.dp)
                            .padding(end = 32.dp),
                        color = Color.White,
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleLarge
                    )
                }

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Color.White,
                            RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                        )
                        .padding(horizontal = 16.dp)
                ) {
                    Spacer(modifier = Modifier.height(24.dp))

                    TextFieldCustom(
                        value = numberField,
                        onValueChange = {
                            if (it.length <= 26) {
                                numberField = it
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(64.dp),
                        enabled = false,
                        textStyle = MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.SemiBold
                        ),
                        label = {
                            Text(text = "Nomor Virtual Account")
                        },
                        singleLine = true,
                        shape = RoundedCornerShape(16.dp),
                        colors = textFieldColors(),
                        contentPadding = TextFieldDefaults.contentPaddingWithLabel(top = 12.dp)
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    var isFocused by rememberSaveable { mutableStateOf(false) }
                    val borderColor = if (isFocused) {
                        Color_0054F3
                    } else {
                        Color.Transparent
                    }

                    TextFieldCustom(
                        value = nameField,
                        onValueChange = { nameField = it },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(64.dp)
                            .onFocusChanged { state ->
                                isFocused = state.isFocused
                            }
                            .border(1.dp, borderColor, RoundedCornerShape(16.dp)),
                        textStyle = MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.SemiBold
                        ),
                        label = {
                            Text(text = "Nama Tersimpan")
                        },
                        trailingIcon = if (nameField.isNotEmpty()) {
                            {
                                Image(
                                    painter = painterResource(R.drawable.icon_close),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .size(16.dp)
                                        .clickable {
                                            nameField = ""
                                        },
                                    contentScale = ContentScale.Fit
                                )
                            }
                        } else {
                            null
                        },
                        singleLine = true,
                        shape = RoundedCornerShape(16.dp),
                        colors = textFieldColors(),
                        contentPadding = TextFieldDefaults.contentPaddingWithLabel(top = 12.dp)
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewBrivaFavorite() {
    MainTheme {
        BrivaFavoriteContent(
            state = BrivaFavoriteState(
                brivaFavoriteRoute = BrivaFavoriteRoute(
                    number = "",
                    name = "",
                    savedId = "",
                    subtitle = ""
                )
            )
        )
    }
}
