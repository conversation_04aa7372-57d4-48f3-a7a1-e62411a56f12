package id.co.bri.brimo.payment.feature.briva.ui.base

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.EmptyState
import id.co.bri.brimo.payment.core.design.component.ImageAsync
import id.co.bri.brimo.payment.core.design.helper.highlightedText
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.response.base.FavoriteResponse

@Composable
internal fun BrivaFavoriteSection(
    data: List<FavoriteResponse>,
    showOption: Boolean = true,
    onOption: (FavoriteResponse) -> Unit = {},
    onSelect: (FavoriteResponse) -> Unit = {}
) {
    if (data.isEmpty()) {
        Box(modifier = Modifier.fillMaxSize()) {
            EmptyState(
                modifier = Modifier.align(Alignment.Center),
                title = "Belum Ada Daftar Favorit",
                description = "Yuk, tambah favorit biar transaksi berikutnya lebih cepat."
            )
        }
    } else {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            itemsIndexed(data) { index, item ->
                ItemBrivaFavorite(
                    item = item,
                    showOption = showOption,
                    onOption = onOption,
                    onSelect = onSelect
                )

                Spacer(modifier = Modifier.height(16.dp))

                if (index < data.size - 1) {
                    DividerHorizontal()
                }
            }
        }
    }
}

@Composable
internal fun ItemBrivaFavorite(
    highlight: String = "",
    item: FavoriteResponse,
    showOption: Boolean = true,
    onOption: (FavoriteResponse) -> Unit = {},
    onSelect: (FavoriteResponse) -> Unit = {}
) {
    val title = item.title.orEmpty()
    val highlightedTitle = highlightedText(highlight, title)
    val value = "${item.subtitle.orEmpty()} - ${item.description.orEmpty()}"
    val highlightedValue = highlightedText(highlight, value)

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable {
                onSelect(item)
            },
        verticalAlignment = Alignment.CenterVertically
    ) {
        ImageAsync(
            context = LocalContext.current,
            url = item.iconPath.orEmpty(),
            initial = item.title.orEmpty(),
            size = 32,
            color = Color_F5F7FB
        )

        Spacer(modifier = Modifier.width(12.dp))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = highlightedTitle,
                modifier = Modifier.fillMaxWidth(),
                fontWeight = FontWeight.SemiBold,
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.height(2.dp))

            Text(
                text = highlightedValue,
                modifier = Modifier.fillMaxWidth(),
                style = MaterialTheme.typography.bodySmall
            )
        }

        if (item.favorite == true) {
            Spacer(modifier = Modifier.width(12.dp))

            Image(
                painter = painterResource(R.drawable.icon_pin),
                contentDescription = null,
                modifier = Modifier.size(24.dp),
                contentScale = ContentScale.Fit
            )
        }

        if (showOption) {
            Spacer(modifier = Modifier.width(12.dp))

            Image(
                painter = painterResource(R.drawable.icon_option),
                contentDescription = null,
                modifier = Modifier
                    .size(24.dp)
                    .clickable {
                        onOption(item)
                    },
                contentScale = ContentScale.Fit
            )
        }
    }
}

@Preview
@Composable
private fun PreviewBrivaFavorite() {
    MainTheme {
        BrivaFavoriteSection(
            data = listOf(
                FavoriteResponse(
                    listType = "",
                    iconName = "",
                    iconPath = "",
                    title = "Title",
                    subtitle = "Subtitle",
                    description = "Description",
                    value = "",
                    favorite = false,
                    keyword = ""
                )
            )
        )
    }
}
