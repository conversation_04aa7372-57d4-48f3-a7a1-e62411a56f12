package id.co.bri.brimo.payment.feature.qrisscan.ui.nominal

import id.co.bri.brimo.payment.app.QrisScanNominalRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.qrisscan.QrisScanConfirmationResponse
import id.co.bri.brimo.payment.feature.qrisscan.data.model.QrisScanModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow

internal data class QrisScanNominalState(
    val qrisScanNominalRoute: QrisScanNominalRoute,
    val qrisScanModel: QrisScanModel,
    val accountList: List<AccountResponse> = emptyList(),
    val qrisScanConfirmation: SharedFlow<UiState<QrisScanConfirmationResponse>> = MutableSharedFlow()
)

internal sealed class QrisScanNominalEvent {
    data class Confirmation(
        val accountNumber: String,
        val amount: String,
        val tipAmount: String,
        val note: String
    ) : QrisScanNominalEvent()

    data class RefreshSaldo(
        val account: String
    ) : QrisScanNominalEvent()
}

internal sealed class QrisScanNominalNavigation {
    object Back : QrisScanNominalNavigation()
    data class Confirmation(
        val qrisScanData: String,
        val fastMenu: Boolean
    ) : QrisScanNominalNavigation()
}
