package id.co.bri.brimo.payment.feature.briva.ui.confirmation

import id.co.bri.brimo.payment.app.BrivaConfirmationRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaPaymentResponse
import id.co.bri.brimo.payment.feature.briva.data.model.BrivaModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow

internal data class BrivaConfirmationState(
    val brivaConfirmationRoute: BrivaConfirmationRoute,
    val brivaModel: BrivaModel,
    val accountList: List<AccountResponse> = emptyList(),
    val brivaPayment: SharedFlow<UiState<BrivaPaymentResponse>> = MutableSharedFlow(),
)

internal sealed class BrivaConfirmationEvent {
    data class Payment(
        val pin: String,
        val saveAs: String,
        val accountNumber: String,
        val note: String
    ) : BrivaConfirmationEvent()


    data class RefreshSaldo(
        val account: String
    ) : BrivaConfirmationEvent()
}

internal sealed class BrivaConfirmationNavigation {
    object Back : BrivaConfirmationNavigation()
    data class Payment(val data: String) : BrivaConfirmationNavigation()
}
