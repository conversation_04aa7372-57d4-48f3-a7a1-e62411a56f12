package id.co.bri.brimo.payment.feature.transfer.ui.component

import android.util.Log
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.EmptyState
import id.co.bri.brimo.payment.core.design.component.OutlinedTextFieldCustom
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.network.response.BankItem

@Composable
fun BankListBottomSheet(
    data: List<BankItem>,
    onSelect: (BankItem) -> Unit = {},
    onClose: () -> Unit = {}
) {

    var searchField by rememberSaveable { mutableStateOf("") }

    val filteredBanks = remember(searchField, data) {
        if (searchField.isEmpty()) {
            data
        } else {
            val query = searchField.trim().lowercase()
            data.filter { bank ->
                bank.name.lowercase().contains(query) ||
                        bank.code.lowercase().contains(query)
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .fillMaxHeight(0.95f)
            .background(Color.White)
            .padding(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()

        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Pilih Bank",
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 40.dp)
                        .padding(horizontal = 12.dp),
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyLarge
                )

                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = null,
                    modifier = Modifier
                        .background(Color_F5F7FB, CircleShape)
                        .clickable {
                            onClose()
                        }
                        .padding(8.dp),
                    tint = Color.Black
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            OutlinedTextFieldCustom(
                value = searchField,
                onValueChange = { searchField = it },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(44.dp),
                textStyle = MaterialTheme.typography.bodyMedium,
                placeholder = {
                    Text(
                        text = "Cari bank",
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.bodyMedium
                    )
                },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp),
                    )
                },
                trailingIcon = if (searchField.isNotEmpty()) {
                    {
                        Icon(
                            painter = painterResource(R.drawable.icon_close),
                            contentDescription = null,
                            modifier = Modifier
                                .size(16.dp)
                                .clickable {
                                    searchField = ""
                                }
                        )
                    }
                } else {
                    null
                },
                singleLine = true,
                shape = RoundedCornerShape(24.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedContainerColor = Color_F5F7FB,
                    unfocusedContainerColor = Color_F5F7FB,
                    disabledContainerColor = Color_F5F7FB,
                    errorContainerColor = Color_F5F7FB,
                    focusedBorderColor = Color_0054F3,
                    unfocusedBorderColor = Color_F5F7FB,
                    disabledBorderColor = Color_F5F7FB
                ),
                contentPadding = PaddingValues(horizontal = 16.dp)
            )
        }

        if (filteredBanks.isEmpty()) {
            Box(modifier = Modifier.fillMaxSize()) {
                EmptyState(
                    modifier = Modifier.align(Alignment.Center),
                    title = "Hasil Tidak Ditemukan",
                    description = "Coba gunakan kata kunci lainnya, ya."
                )
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                items(filteredBanks) { item ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable {
                                onSelect(item)
                            }
                            .padding(vertical = 20.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Log.d("BankListBottomSheet", "Loading image from URL: ${item.imageUrl}")
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(item.imageUrl)
                                .crossfade(true)
                                .decoderFactory(SvgDecoder.Factory())
                                .listener(
                                    onError = { _, result ->
                                        Log.e(
                                            "BankListBottomSheet",
                                            "Error loading image: ${result.throwable.message}"
                                        )
                                    },
                                    onSuccess = { _, _ ->
                                        Log.d("BankListBottomSheet", "Image loaded successfully")
                                    }
                                )
                                .build(),
                            contentDescription = null,
                            modifier = Modifier
                                .size(32.dp)
                                .clip(CircleShape)
                                .background(Color_F5F7FB),
                            contentScale = ContentScale.Crop,
                            error = painterResource(R.drawable.thumbnail),
                            placeholder = painterResource(R.drawable.thumbnail)
                        )

                        Spacer(modifier = Modifier.width(16.dp))

                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = item.name,
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.SemiBold
                            )
                        }
                    }

                    DividerHorizontal()
                }
            }
        }
    }
}