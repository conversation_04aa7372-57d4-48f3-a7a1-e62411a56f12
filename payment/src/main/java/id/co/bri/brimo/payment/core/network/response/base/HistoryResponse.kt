package id.co.bri.brimo.payment.core.network.response.base

import com.google.gson.annotations.SerializedName

data class HistoryResponse(
    @SerializedName("list_type") val listType: String?,
    @SerializedName("icon_name") val iconName: String?,
    @SerializedName("icon_path") val iconPath: String?,
    @SerializedName("title") val title: String?,
    @SerializedName("subtitle") val subtitle: String?,
    @SerializedName("description") val description: String?,
    @SerializedName("value") val value: String?
)
