package id.co.bri.brimo.payment.feature.qriscrossborder.ui.confirmation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.google.gson.Gson
import id.co.bri.brimo.payment.app.QrisCrossConfirmationRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qriscrossborder.QrisCrossPaymentRequest
import id.co.bri.brimo.payment.core.network.response.qriscrossborder.QrisCrossPaymentResponse
import id.co.bri.brimo.payment.feature.qriscrossborder.data.api.QrisCrossBorderRepository
import id.co.bri.brimo.payment.feature.qriscrossborder.data.model.QrisCrossModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class QrisCrossConfirmationViewModel(
    private val qrisCrossRepository: QrisCrossBorderRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    private val qrisCrossConfirmationRoute = savedStateHandle.toRoute<QrisCrossConfirmationRoute>()

    val qrisCrossModel = runCatching {
        Gson().fromJson(qrisCrossConfirmationRoute.data, QrisCrossModel::class.java)
    }.getOrNull()

    private val _accountList = MutableStateFlow(qrisCrossModel?.qrisScan?.accountList)
    val accountList = _accountList.asStateFlow()

    fun handleEvent(event: QrisCrossConfirmationEvent) {
        when (event) {
            is QrisCrossConfirmationEvent.Payment -> {
                postQrisCrossPay(
                    pin = event.pin,
                    note = event.note
                )
            }

            is QrisCrossConfirmationEvent.RefreshSaldo -> {
                refreshSaldo(account = event.account)
            }
        }
    }

    private val _qrisCrossPayment = MutableSharedFlow<UiState<QrisCrossPaymentResponse>>()
    val qrisCrossPayment = _qrisCrossPayment.asSharedFlow()

    private fun postQrisCrossPay(
        pin: String,
        note: String
    ) {
        viewModelScope.launch {
            _qrisCrossPayment.asUiState {
                val request = if (qrisCrossModel?.qrisScan != null) {
                    QrisCrossPaymentRequest(
                        note = note,
                        pfmCategory = qrisCrossModel.qrisCrossConfirmation?.pfmCategory,
                        pin = pin,
                        referenceNumber = qrisCrossModel.qrisCrossConfirmation?.referenceNumber
                    )
                } else {
                    QrisCrossPaymentRequest(
                        note = qrisCrossModel?.qrisCrossConfirmation?.note.orEmpty(),
                        pfmCategory = qrisCrossModel?.qrisCrossConfirmation?.pfmCategory,
                        pin = pin,
                        referenceNumber = qrisCrossModel?.qrisCrossConfirmation?.referenceNumber,
                    )
                }
                qrisCrossRepository.postQrisCrossPay(
                    request = request,
                    fastMenu = qrisCrossConfirmationRoute.fastMenu
                )
            }
        }
    }

    private fun refreshSaldo(account: String) {
        viewModelScope.launch {
            updateAccountList(account, true)
            try {
                val saldoNormal = qrisCrossRepository.postSaldoNormal(
                    request = SaldoNormalRequest(
                        account = account
                    )
                )
                val accountListUpdated = accountList.value?.map { item ->
                    if (item.account == account) {
                        item.copy(
                            onHold = saldoNormal.onHold,
                            balance = saldoNormal.balance,
                            balanceString = saldoNormal.balanceString,
                            loading = false
                        )
                    } else {
                        item
                    }
                }
                _accountList.update { accountListUpdated }
            } catch (_: Throwable) {
                updateAccountList(account, false)
            }
        }
    }

    private fun updateAccountList(account: String, loading: Boolean) {
        val accountListUpdated = accountList.value?.map { item ->
            if (item.account == account) {
                item.copy(
                    loading = loading
                )
            } else {
                item
            }
        }
        _accountList.update { accountListUpdated }
    }
}
