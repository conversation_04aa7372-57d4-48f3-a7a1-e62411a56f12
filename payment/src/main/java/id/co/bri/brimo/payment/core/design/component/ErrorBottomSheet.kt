package id.co.bri.brimo.payment.core.design.component

import androidx.activity.ComponentActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.DataException
import id.co.bri.brimo.payment.core.network.MessageException
import id.co.bri.brimo.payment.dependency.PaymentDependency
import java.io.IOException

@Composable
internal fun ErrorBottomSheet(
    error: Throwable = Throwable(),
    showBottomSheet: Boolean = true,
    onShowBottomSheet: (Boolean) -> Unit = {},
    onDismiss: () -> Unit = {},
    onClose: () -> Unit = {},
    onRetry: () -> Unit = {},
    isRetry: Boolean = true
) {
    val context = LocalContext.current
    val onPin = {
        PaymentDependency.getPaymentApi()?.onPin(context as ComponentActivity)
        (context as ComponentActivity).finish()
    }
    val onSession = {
        PaymentDependency.getPaymentApi()?.onSession(context as ComponentActivity)
        (context as ComponentActivity).finish()
    }
    val onFinish = {
        PaymentDependency.getFinish()
    }
    val onCloseCustom = when (error) {
        is MessageException -> {
            when {
                error.code == "05" &&
                    error.description.contains("pin", true) -> {
                    { onPin() }
                }

                error.code == "05" -> {
                    { onSession() }
                }

                error.code == "93" -> {
                    { onFinish() }
                }

                else -> {
                    { onClose() }
                }
            }
        }

        else -> {
            { onClose() }
        }
    }
    val onDismissCustom = when (error) {
        is MessageException -> {
            when {
                error.code == "05" &&
                    error.description.contains("pin", true) -> {
                    { onPin() }
                }

                error.code == "05" -> {
                    { onSession() }
                }

                error.code == "93" -> {
                    { onFinish() }
                }

                else -> {
                    { onDismiss() }
                }
            }
        }

        else -> {
            { onDismiss() }
        }
    }

    BottomSheet(
        showBottomSheet = showBottomSheet,
        onShowBottomSheet = onShowBottomSheet,
        onDismiss = onDismissCustom
    ) { dismiss ->
        ErrorContent(
            error = error,
            onClose = {
                dismiss {
                    onCloseCustom()
                }
            },
            onRetry = {
                dismiss {
                    onRetry()
                }
            },
            onPin = {
                dismiss {
                    onPin()
                }
            },
            onSession = {
                dismiss {
                    onSession()
                }
            },
            onFinish = {
                dismiss {
                    onFinish()
                }
            }
        )
    }
}

@Composable
private fun ErrorContent(
    error: Throwable = Throwable(),
    onClose: () -> Unit = {},
    onRetry: () -> Unit = {},
    onPin: () -> Unit = {},
    onSession: () -> Unit = {},
    onFinish: () -> Unit = {}
) {
    val title = when (error) {
        is MessageException -> {
            when {
                error.code == "05" &&
                    error.description.contains("pin", true) ->
                    "Akun Kamu Terkunci Sementara"

                error.code == "05" -> "Sesi Kamu Telah Berakhir"
                error.code == "61" -> "Aktivitas Mencapai Limit Harian"
                // error.code == "93" -> "Transaksi Gagal"
                else -> "Terjadi Kesalahan"
            }
        }

        is DataException -> error.title.ifEmpty { "Terjadi Kesalahan" }
        is IOException -> "Internet Tidak Terhubung"
        else -> "Terjadi Kesalahan"
    }
    val description = when (error) {
        is MessageException -> {
            when {
                error.code == "05" &&
                    error.description.contains("pin", true) ->
                    "Akun kamu terkunci karena terlalu banyak percobaan yang gagal. Yuk, atur ulang akses kamu untuk melanjutkan aktivitas di Qitta."

                error.code == "05" -> error.description.ifEmpty { "Untuk alasan keamanan, kamu perlu login kembali sebelum mengakses fitur ini." }
                error.code == "61" -> error.description.ifEmpty { "Akun Qitta yang kamu gunakan saat ini sedang dalam Safety Mode. Silakan coba lagi besok, ya." }
                // error.code == "93" -> error.description.ifEmpty { "Terjadi kendala saat mengakses halaman ini. Silakan coba lagi nanti atau kunjungi Pusat Bantuan untuk info lebih lanjut." }
                else -> "Transaksi belum berhasil karena ada kendala saat memuat data. Tidak ada perubahan pada saldo."
            }
        }

        is DataException -> error.description.ifEmpty { "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya." }
        is IOException -> "Terjadi kendala pada koneksi internet. Silakan cek koneksi internet kamu dan coba lagi, ya."
        else -> "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya."
    }
    val button = when (error) {
        is MessageException -> {
            when {
                error.code == "05" &&
                    error.description.contains("pin", true) ->
                    "Atur Ulang PIN Sekarang"

                error.code == "05" -> "Login Ulang"
                error.code == "61" -> "Mengerti"
                // error.code == "93" -> "Kembali ke Beranda"
                else -> "Coba Lagi"
            }
        }

        is DataException -> error.title.ifEmpty { "Coba Lagi" }
        is IOException -> "Coba Lagi"
        else -> "Coba Lagi"
    }
    val action = when (error) {
        is MessageException -> {
            when {
                error.code == "05" &&
                    error.description.contains("pin", true) -> {
                    { onPin() }
                }

                error.code == "05" -> {
                    { onSession() }
                }

                error.code == "61" -> {
                    { onClose() }
                }

                error.code == "93" -> {
                    { onFinish() }
                }

                else -> {
                    { onRetry() }
                }
            }
        }

        is DataException -> {
            { onRetry() }
        }

        is IOException -> {
            { onRetry() }
        }

        else -> {
            { onRetry() }
        }
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Box(modifier = Modifier.fillMaxWidth()) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = null,
                modifier = Modifier
                    .background(Color_F5F7FB, CircleShape)
                    .clickable {
                        onClose()
                    }
                    .padding(8.dp)
                    .align(Alignment.TopEnd),
                tint = Color.Black
            )

            Image(
                painter = painterResource(R.drawable.image_error),
                contentDescription = null,
                modifier = Modifier
                    .size(200.dp)
                    .align(Alignment.TopCenter),
                contentScale = ContentScale.Fit
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = title,
            modifier = Modifier.fillMaxWidth(),
            fontWeight = FontWeight.SemiBold,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.bodyLarge
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = description,
            modifier = Modifier.fillMaxWidth(),
            color = Color_7B90A6,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.bodyMedium
        )

        Spacer(modifier = Modifier.height(32.dp))

        PrimaryButton(
            label = button,
            modifier = Modifier.fillMaxWidth(),
            onClick = {
                action()
            }
        )

        /*Spacer(modifier = Modifier.height(12.dp))

        SecondaryButton(
            label = "Tutup",
            modifier = Modifier.fillMaxWidth(),
            onClick = onClose
        )*/
    }
}

@Preview
@Composable
private fun PreviewErrorBottomSheet() {
    MainTheme {
        ErrorContent()
    }
}
