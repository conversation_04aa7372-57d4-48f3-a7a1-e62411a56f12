package id.co.bri.brimo.payment.dependency

object PaymentDependency {

    private var paymentApi: PaymentApi? = null
    private var finish: () -> Unit = {}

    fun setPaymentApi(api: PaymentApi) {
        paymentApi = api
    }

    internal fun getPaymentApi(): PaymentApi? {
        return paymentApi
    }

    fun setFinish(action: () -> Unit) {
        finish = action
    }

    internal fun getFinish() {
        finish()
    }
}
