package id.co.bri.brimo.payment.core.network.response.briva

import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.payment.core.network.response.base.FavoriteResponse
import id.co.bri.brimo.payment.core.network.response.base.HistoryResponse

internal data class BrivaFormResponse(
    @SerializedName("top_briva") val topBriva: List<TopBriva>?,
    @SerializedName("history") val history: List<HistoryResponse>?,
    @SerializedName("saved") val saved: List<FavoriteResponse>?,
    @SerializedName("reference_number") val referenceNumber: String?
) {
    data class TopBriva(
        @SerializedName("code") val code: String?,
        @SerializedName("name") val name: String?,
        @SerializedName("icon_name") val iconName: String?,
        @SerializedName("icon_path") val iconPath: String?
    )
}
