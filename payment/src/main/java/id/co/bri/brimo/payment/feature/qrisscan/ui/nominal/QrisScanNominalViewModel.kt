package id.co.bri.brimo.payment.feature.qrisscan.ui.nominal

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.google.gson.Gson
import id.co.bri.brimo.payment.app.QrisScanNominalRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qrisscan.QrisScanConfirmationRequest
import id.co.bri.brimo.payment.core.network.response.qrisscan.QrisScanConfirmationResponse
import id.co.bri.brimo.payment.feature.qrisscan.data.api.QrisScanRepository
import id.co.bri.brimo.payment.feature.qrisscan.data.model.QrisScanModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class QrisScanNominalViewModel(
    private val qrisScanRepository: QrisScanRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val qrisScanNominalRoute = savedStateHandle.toRoute<QrisScanNominalRoute>()

    val qrisScanModel = runCatching {
        Gson().fromJson(qrisScanNominalRoute.data, QrisScanModel::class.java)
    }.getOrNull()

    private val _accountList = MutableStateFlow(qrisScanModel?.qrisScan?.accountList)
    val accountList = _accountList.asStateFlow()

    fun handleEvent(event: QrisScanNominalEvent) {
        when (event) {
            is QrisScanNominalEvent.Confirmation -> {
                postQrisScanConfirmation(
                    accountNumber = event.accountNumber,
                    amount = event.amount,
                    tipAmount = event.tipAmount,
                    note = event.note
                )
            }

            is QrisScanNominalEvent.RefreshSaldo -> {
                refreshSaldo(account = event.account)
            }
        }
    }

    private val _qrisScanConfirmation =
        MutableSharedFlow<UiState<QrisScanConfirmationResponse>>()
    val qrisScanConfirmation = _qrisScanConfirmation.asSharedFlow()

    private fun postQrisScanConfirmation(
        accountNumber: String,
        amount: String,
        tipAmount: String,
        note: String
    ) {
        viewModelScope.launch {
            _qrisScanConfirmation.asUiState {
                qrisScanRepository.postQrisScanConfirmation(
                    request = QrisScanConfirmationRequest(
                        accountNumber = accountNumber,
                        amount = amount,
                        inputTipAmount = tipAmount.takeIf { it.isNotEmpty() },
                        note = note,
                        referenceNumber = qrisScanModel?.qrisScan?.referenceNumber.orEmpty(),
                        saveAs = ""
                    ),
                    fastMenu = qrisScanNominalRoute.fastMenu
                )
            }
        }
    }

    private fun refreshSaldo(account: String) {
        viewModelScope.launch {
            updateAccountList(account, true)
            try {
                val saldoNormal = qrisScanRepository.postSaldoNormal(
                    request = SaldoNormalRequest(
                        account = account
                    )
                )
                val accountListUpdated = accountList.value?.map { item ->
                    if (item.account == account) {
                        item.copy(
                            onHold = saldoNormal.onHold,
                            balance = saldoNormal.balance,
                            balanceString = saldoNormal.balanceString,
                            loading = false
                        )
                    } else {
                        item
                    }
                }
                _accountList.update { accountListUpdated }
            } catch (_: Throwable) {
                updateAccountList(account, false)
            }
        }
    }

    private fun updateAccountList(account: String, loading: Boolean) {
        val accountListUpdated = accountList.value?.map { item ->
            if (item.account == account) {
                item.copy(
                    loading = loading
                )
            } else {
                item
            }
        }
        _accountList.update { accountListUpdated }
    }
}
